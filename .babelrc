{"presets": [["@babel/preset-react", {"runtime": "automatic", "development": false}], ["@babel/preset-env", {"modules": false, "debug": false, "corejs": 3, "useBuiltIns": "usage", "targets": {"browsers": [">0.2%", "Chrome >= 72", "Safari >= 12"]}, "exclude": ["transform-typeof-symbol"]}]], "plugins": [["import", {"libraryName": "antd", "libraryDirectory": "es", "style": true}], ["@babel/plugin-transform-runtime", {"corejs": 3, "helpers": true, "regenerator": true, "useESModules": true}], "@babel/plugin-syntax-dynamic-import", "@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-nullish-coalescing-operator", "@babel/plugin-proposal-optional-chaining"], "env": {"development": {"presets": [["@babel/preset-react", {"runtime": "automatic", "development": true}]]}}}