#!/bin/bash

# 最终修复所有 kebab-case 类名的脚本

set -e

echo "🔄 最终修复所有 kebab-case 类名..."

# 函数：为文件添加 classnames/bind 导入和绑定
add_classnames_bind() {
    local file="$1"
    local var_name="$2"
    local bind_name="$3"
    
    # 检查是否已经有 classnames/bind 导入
    if ! grep -q "import.*classBind.*from 'classnames/bind'" "$file" && ! grep -q "import.*classNames.*from 'classnames/bind'" "$file"; then
        # 在 styles 导入后添加 classnames/bind 导入
        sed -i '' "/import \* as $var_name from.*\.less/a\\
import classBind from 'classnames/bind';\\
\\
const $bind_name = classBind.bind($var_name);" "$file"
        echo "  - 添加了 classnames/bind 导入到 $file"
    fi
}

# 函数：修复文件中的类名使用
fix_classnames_usage() {
    local file="$1"
    local var_name="$2"
    local bind_name="$3"
    
    # 将 styles['kebab-case'] 替换为 cx('kebab-case')
    sed -i '' "s/${var_name}\[['\"]\([^'\"]*\)['\"\]/${bind_name}('\1')/g" "$file"
    
    # 将 styles.camelCase 替换为 cx('kebab-case') - 这需要手动处理复杂情况
    echo "  - 修复了 $file 中的类名使用"
}

echo "📝 处理所有使用 CSS 模块的文件..."

# 处理使用 styles 变量的文件
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
    if grep -q "import \* as styles from.*\.less" "$file"; then
        echo "处理文件: $file"
        
        # 检查是否已经有绑定函数
        if grep -q "classBind\.bind(styles)" "$file" || grep -q "classNames\.bind(styles)" "$file"; then
            # 已经有绑定，找到绑定变量名
            bind_var=$(grep -o "[a-zA-Z_][a-zA-Z0-9_]*\s*=.*\.bind(styles)" "$file" | cut -d'=' -f1 | tr -d ' ')
            if [ -n "$bind_var" ]; then
                fix_classnames_usage "$file" "styles" "$bind_var"
            fi
        else
            # 没有绑定，添加绑定
            add_classnames_bind "$file" "styles" "cx"
            fix_classnames_usage "$file" "styles" "cx"
        fi
    fi
done

# 处理使用 style 变量的文件
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
    if grep -q "import \* as style from.*\.less" "$file"; then
        echo "处理文件 (style): $file"
        
        # 检查是否已经有绑定函数
        if grep -q "classBind\.bind(style)" "$file" || grep -q "classNames\.bind(style)" "$file"; then
            # 已经有绑定，找到绑定变量名
            bind_var=$(grep -o "[a-zA-Z_][a-zA-Z0-9_]*\s*=.*\.bind(style)" "$file" | cut -d'=' -f1 | tr -d ' ')
            if [ -n "$bind_var" ]; then
                fix_classnames_usage "$file" "style" "$bind_var"
            fi
        else
            # 没有绑定，添加绑定
            add_classnames_bind "$file" "style" "cx"
            fix_classnames_usage "$file" "style" "cx"
        fi
    fi
done

# 处理其他变量名
for var_name in "stylesCommon" "styleCommon" "commonStyle" "commonStyles" "addMemberBtnStyles" "styleAside"; do
    find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
        if grep -q "import \* as $var_name from.*\.less" "$file"; then
            echo "处理文件 ($var_name): $file"
            
            # 检查是否已经有绑定函数
            if grep -q "classBind\.bind($var_name)" "$file" || grep -q "classNames\.bind($var_name)" "$file"; then
                # 已经有绑定，找到绑定变量名
                bind_var=$(grep -o "[a-zA-Z_][a-zA-Z0-9_]*\s*=.*\.bind($var_name)" "$file" | cut -d'=' -f1 | tr -d ' ')
                if [ -n "$bind_var" ]; then
                    fix_classnames_usage "$file" "$var_name" "$bind_var"
                fi
            else
                # 没有绑定，添加绑定
                bind_name="c$(echo ${var_name:0:1} | tr '[:lower:]' '[:upper:]')$(echo ${var_name:1})"
                add_classnames_bind "$file" "$var_name" "$bind_name"
                fix_classnames_usage "$file" "$var_name" "$bind_name"
            fi
        fi
    done
done

echo "✅ 所有 kebab-case 类名修复完成！"

# 显示修复统计
echo "📊 修复统计："
total_cx_files=$(find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "\.bind(" | wc -l | tr -d ' ')
echo "- 使用 classnames/bind 的文件: $total_cx_files 个"

echo "🧪 测试构建..."
