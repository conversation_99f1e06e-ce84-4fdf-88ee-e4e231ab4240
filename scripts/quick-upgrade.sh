#!/bin/bash

# 快速升级脚本 - 只升级最关键的依赖
# 基于 npm outdated 分析，专注于性能和安全

set -e

echo "⚡ 快速依赖升级 - 只升级关键依赖"
echo ""

# 创建备份
echo "📦 创建备份..."
cp package.json package.json.backup

# 显示将要升级的包
echo "📋 将要升级的关键依赖："
echo ""
echo "🔒 安全更新："
echo "  axios: 0.21.1 → 1.10.0 (修复安全漏洞)"
echo "  core-js: 3.26.1 → 3.44.0"
echo ""
echo "🚀 性能优化："
echo "  css-loader: 6.11.0 → 7.1.2"
echo "  postcss-loader: 7.3.4 → 8.1.1"
echo "  @babel/plugin-transform-runtime: 7.16.7 → 7.28.0"
echo ""

read -p "是否继续升级？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 升级已取消"
    exit 1
fi

echo "🚀 开始升级..."

# 安全更新
echo "🔒 安全更新..."
npm install axios@^1.10.0 core-js@^3.44.0

# 性能优化
echo "🚀 性能优化..."
npm install --save-dev css-loader@^7.1.0 postcss-loader@^8.1.0
npm install @babel/plugin-transform-runtime@^7.28.0

# 测试构建
echo "🧪 测试构建..."
npm run build:prod > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 升级成功！构建测试通过"
    
    # 运行性能测试
    if [ -f "performance-logs/test-optimization.sh" ]; then
        echo "📊 运行性能测试..."
        ./performance-logs/test-optimization.sh > performance-logs/quick-upgrade-result.log 2>&1 || true
        echo "📊 性能测试结果保存到: performance-logs/quick-upgrade-result.log"
    fi
    
    echo ""
    echo "🎉 快速升级完成！"
    echo ""
    echo "📊 升级内容："
    echo "- ✅ 安全漏洞修复"
    echo "- ✅ CSS 处理性能提升"
    echo "- ✅ Babel 运行时优化"
    echo ""
    echo "📈 预期性能提升: 10-15%"
    echo ""
    echo "📝 下一步："
    echo "1. 测试应用功能是否正常"
    echo "2. 如需更多优化，运行: ./scripts/upgrade-dependencies.sh"
    echo "3. 如有问题，恢复备份: cp package.json.backup package.json && npm install"
    
else
    echo "❌ 构建测试失败，正在回滚..."
    cp package.json.backup package.json
    npm install > /dev/null 2>&1
    echo "🔄 已回滚到升级前状态"
    exit 1
fi
