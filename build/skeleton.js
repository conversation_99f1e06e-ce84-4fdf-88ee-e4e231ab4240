process.env.BABEL_ENV = 'production';
process.env.NODE_ENV = 'production';
process.env.GENERATE_SOURCEMAP = 'true';
process.env.APP_ENV = process.env.APP_ENV;

const fs = require('fs-extra');
const path = require('path');
const webpack = require('webpack');
const prodWebpackConfig = require('../config/webpack.skeleton');
const paths = require('../config/paths');
const chalk = require('chalk');
// 移除: const formatWebpackMessages = require('react-dev-utils/formatWebpackMessages');
// 移除: const FileSizeReporter = require('react-dev-utils/FileSizeReporter');
// 移除: const printBuildError = require('react-dev-utils/printBuildError');
// 用fs.statSync和console.error替代文件大小统计和错误输出。

// 移除FileSizeReporter、printBuildError、formatWebpackMessages相关代码
// These sizes are pretty large. We'll warn for bundles exceeding them.
const WARN_AFTER_BUNDLE_GZIP_SIZE = 512 * 1024;
const WARN_AFTER_CHUNK_GZIP_SIZE = 1024 * 1024;

const isInteractive = process.stdout.isTTY;

// 移除checkBrowsers后的Promise链，直接执行主逻辑
fs.emptyDirSync(paths.appBuild);
copyPublicFolder();
build()
  .then(({ stats, warnings }) => {
    if (warnings.length) {
      console.log(chalk.yellow('Compiled with warnings.\n'));
    } else {
      console.log(chalk.green('Compiled successfully.\n'));
    }
  })
  .catch(err => {
    console.log(chalk.red('Failed to compile.\n'));
    console.error(err);
    process.exit(1);
  });

function build() {
  console.log('Creating an optimized skeleton build...');
  const compiler = webpack(prodWebpackConfig);
  return new Promise((resolve, reject) => {
    compiler.run((err, stats) => {
      if (err) {
        if (!err.message) {
          return reject(err);
        }
        let errMessage = err.message;
        if (Object.prototype.hasOwnProperty.call(err, 'postcssNode')) {
          errMessage +=
            '\nCompileError: Begins at CSS selector ' +
            err['postcssNode'].selector;
        }
        return reject(new Error(errMessage));
      }
      const info = stats.toJson();
      if (stats.hasErrors()) {
        return reject(new Error(info.errors.map(e => (typeof e === 'string' ? e : e.message || JSON.stringify(e))).join('\n\n')));
      }
      if (stats.hasWarnings()) {
        console.warn('Compiled with warnings.');
        info.warnings.forEach(w => console.warn(typeof w === 'string' ? w : w.message || JSON.stringify(w)));
      } else {
        console.log('Compiled successfully.');
      }
      const resolveArgs = {
        stats,
        warnings: info.warnings || [],
      };
      return resolve(resolveArgs);
    });
  });
}

function copyPublicFolder() {
  fs.copySync(paths.appPublic, paths.appBuild, {
    dereference: true,
    filter: file => file !== paths.cooperAppHtml && file !== paths.knowledgeHtml,
  });
}
