process.env.NODE_ENV = "development";
process.env.BABEL_ENV = "development";
process.env.GENERATE_SOURCEMAP = "true";
const targetPort = process.env.TARGET_PORT;

const webpack = require("webpack");
const WebpackDevServer = require("webpack-dev-server");
const chalk = require("chalk");
const open = require("open");
const paths = require("../config/paths");
const cooperDevWebpackConfig = require("../config/webpack.cooper.dev");
const konwledgeDevWebpackConfig = require("../config/webpack.konwledge.dev");
const devServerConfig = require("../config/webpack.devServer");

const entryServerArr = [
  {
    config: cooperDevWebpackConfig,
    HOST: devServerConfig.host,
    PORT: 4001,
    appName: require(paths.appPackageJson).name,
    devServerConfig: {
      ...devServerConfig,
      ...{
        port: 4001,
        before: (app, server) => {
          app.use((req, res, next) => {
            if (req.url.startsWith("/knowledge")) {
              const newUrl = "http://localhost:4002" + req.url;
              res.redirect(newUrl);
            } else {
              next();
            }
          });
        },
      },
    },
    path: "/",
  },
  {
    config: konwledgeDevWebpackConfig,
    HOST: devServerConfig.host,
    PORT: 4002,
    appName: require(paths.appPackageJson).name,
    devServerConfig: { ...devServerConfig, ...{ port: 4002, before: (app) => {
      app.use((req, res, next) => {
        if (req.url === "/") {
          const newUrl = "http://localhost:4001" + req.url;
          res.redirect(newUrl);
        } else {
          next();
        }
      });
    }} },
    path: "/knowledge",
  },
];

const startServer = (port) => {
  const item = entryServerArr.find(item => item.PORT === port);
  if (!item) {
    console.log(chalk.red(`No server configuration found for port: ${port}`));
    return;
  }

  const { config, HOST, PORT, appName, devServerConfig, path } = item;
  const urls = {
    localUrlForBrowser: `http://${HOST}:${PORT}${path}`,
    lanUrlForTerminal: `http://${HOST}:${PORT}${path}`,
    lanUrlForTerminal: `http://${HOST}:${PORT}${path}`,
  };
  const devSocket = {
    warnings: (warnings) =>
      devServer.sockWrite(devServer.sockets, "warnings", warnings),
    errors: (errors) =>
      devServer.sockWrite(devServer.sockets, "errors", errors),
  };
  const compiler = webpack(config);
  const devServer = new WebpackDevServer(compiler, devServerConfig);
  devServer.listen(PORT, HOST, (err) => {
    if (err) {
      return console.log(err);
    }

    console.log(chalk.cyan("Starting the development server...\n"));
    open(urls.localUrlForBrowser);
  });
};

// 移除checkBrowsers后的Promise链，直接执行主逻辑
const serversToStart = targetPort ? [targetPort] : [3000, 4001, 4002];
serversToStart.forEach(port => {
  startServer(port);
});
