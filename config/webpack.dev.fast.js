const paths = require('./paths');
const { merge } = require('webpack-merge');
const common = require('./webpack.common');
const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin');
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');

// 快速开发模式配置 - 牺牲一些功能换取极致的构建速度
module.exports = [
  merge(common, {
    mode: 'development',
    bail: false,
    // 使用最快的 devtool
    devtool: 'eval',
    entry: [paths.knowledgeAppIndexJs],
    output: {
      path: paths.appBuild,
      pathinfo: false, // 关闭路径信息，提升构建速度
      filename: 'static/js/[name].js',
      chunkFilename: 'static/js/[name].chunk.js',
      publicPath: '/',
    },
    optimization: {
      // 开发环境关闭优化，提升构建速度
      minimize: false,
      removeAvailableModules: false,
      removeEmptyChunks: false,
      splitChunks: false,
      usedExports: false,
      concatenateModules: false,
      flagIncludedChunks: false,
    },
    module: {
      rules: [
        // 覆盖样式处理规则，移除 thread-loader 和 cache-loader
        {
          test: /\.less$/,
          use: [
            'style-loader',
            {
              loader: 'css-loader',
              options: {
                sourceMap: false,
                modules: {
                  auto: true,
                  localIdentName: '[local]',
                },
              },
            },
            {
              loader: 'less-loader',
              options: {
                sourceMap: false,
                lessOptions: {
                  javascriptEnabled: true,
                },
              },
            },
          ],
        },
      ],
    },
    plugins: [
      new HtmlWebpackPlugin({
        template: paths.knowledgeHtml,
        inject: true,
      }),
      new CaseSensitivePathsPlugin(),
      new ReactRefreshWebpackPlugin({
        overlay: false, // 关闭错误覆盖层，提升性能
      }),
    ],
    // 关闭性能提示
    performance: false,
    // 优化文件系统缓存
    cache: {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename],
      },
    },
  }),
];
