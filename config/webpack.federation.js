const ModuleFederationPlugin = require('@module-federation/webpack');
const paths = require('./paths');

// 模块联邦配置 - 用于大型应用拆分
const federationConfig = {
  name: 'knowledgeForge',
  filename: 'remoteEntry.js',
  exposes: {
    // 暴露公共组件
    './Button': './src/components/common/Button',
    './Modal': './src/components/common/Modal',
    './Upload': './src/components/Upload',
    // 暴露业务模块
    './CooperModule': './src/pages/cooper',
    './KnowledgeModule': './src/pages/knowledge',
  },
  shared: {
    react: {
      singleton: true,
      requiredVersion: '^17.0.0',
    },
    'react-dom': {
      singleton: true,
      requiredVersion: '^17.0.0',
    },
    'react-router-dom': {
      singleton: true,
      requiredVersion: '^6.0.0',
    },
    antd: {
      singleton: true,
      requiredVersion: '^4.21.0',
    },
  },
};

module.exports = {
  plugins: [
    new ModuleFederationPlugin(federationConfig),
  ],
};
