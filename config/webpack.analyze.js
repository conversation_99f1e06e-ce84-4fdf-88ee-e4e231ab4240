const paths = require('./paths');
const { merge } = require('webpack-merge');
const prodConfig = require('./webpack.prod');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');

const smp = new SpeedMeasurePlugin({
  outputFormat: 'human',
  outputTarget: 'performance-logs/speed-analysis.txt',
});

// 移除原有的 SpeedMeasurePlugin 包装
const baseConfig = prodConfig[0]; // 获取基础配置，去掉 smp.wrap

const analyzeConfig = merge(baseConfig, {
  plugins: [
    new BundleAnalyzerPlugin({
      analyzerMode: 'static',
      openAnalyzer: false,
      reportFilename: '../performance-logs/bundle-report.html',
      generateStatsFile: true,
      statsFilename: '../performance-logs/bundle-stats.json',
    }),
  ],
});

// 使用新的 SpeedMeasurePlugin 包装
module.exports = smp.wrap(analyzeConfig);
