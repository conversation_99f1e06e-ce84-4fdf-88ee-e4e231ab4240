const paths = require('./paths');
const { merge } = require('webpack-merge');
const common = require('./webpack.common');
const TerserPlugin = require('terser-webpack-plugin');

const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
// const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const ScriptExtHtmlWebpackPlugin = require('script-ext-html-webpack-plugin');


const shouldUseSourceMap = process.env.GENERATE_SOURCEMAP !== 'false';

// gift空间申请完成后修改
const assetsPublicPath = process.env.APP_ENV !== 'prod' ? '/' : '//img-ys011.didistatic.com/static/cooper_cn/cooper/';


module.exports = [
  merge(common, {
    mode: 'production',
    bail: true,
    devtool: shouldUseSourceMap ? 'source-map' : false,
    entry: {
      cooper: paths.cooperAppIndexJs,
      knowledge: paths.knowledgeAppIndexJs,
    },
    output: {
      path: paths.appBuild,
      filename: 'static/js/[name].[contenthash:8].js',
      chunkFilename: 'static/js/[name].[contenthash:8].chunk.js',
      publicPath: assetsPublicPath,
    },
    optimization: {
      // tree shaking
      usedExports: true,
      // ！！开启scope  hosting
      concatenateModules: true,
      minimize: true,
      minimizer: [
        // 压缩转换js
        new TerserPlugin({
          parallel: true,
          terserOptions: {
            parse: {
              ecma: 8,
            },
            compress: {
              ecma: 5,
              warnings: false,
              comparisons: false,
              inline: 2,
            },
            mangle: {
              safari10: true,
            },
            keep_classnames: true,
            keep_fnames: true,
            output: {
              ecma: 5,
              comments: false,
              ascii_only: true,
            },
          },
        }),
        // 压缩css
        new CssMinimizerPlugin({
          parallel: true,
          minimizerOptions: {
            preset: [
              'default',
              {
                minifyFontValues: { removeQuotes: false },
              },
            ],
          },
        }),
      ],
      runtimeChunk: {
        name: 'runtime',
      },
      splitChunks: {
        chunks: 'all',
        maxAsyncRequests: Infinity, // 按需加载时并行请求的最大数目。
        maxInitialRequests: Infinity, // 入口点的最大并行请求数
        minSize: 100 * 1024, // 模块大于minSize时才会被分割出来。默认100k
        // maxSize: 0, // 生成的块的最大大小，如果超过了这个限制，大块会被拆分成多个小块。
        // minChunks: 1, // 拆分前必须共享模块的最小块数。
        cacheGroups: {
          // lib: {
          //   name: 'chunk-vendors',
          //   test: /[\\/]node_modules[\\/]/,
          //   priority: 10,
          //   chunks: 'initial',
          // },
          commons: { // 公共模块包
            name: 'chunk-commons',
            minChunks: 3,
            priority: 5,
            reuseExistingChunk: true,
          },
          vendor: {
            test(module, chunk) {
              return module.resource
                && module.resource.includes('node_modules')
                && (module.resource.indexOf('halo') === -1);
            },
            // ！！配置依赖包单独打包
            chunks: 'all',
            name(module) {
              const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/-]|$)/)[1];
              return `splitChunk.${packageName.replace('@', '')}`;
            },
            priority: 15,
            minChunks: 1,
            maxSize: 3000 * 1024,
            reuseExistingChunk: true,
          },
        },
      },
    },
    plugins: [
      new MiniCssExtractPlugin({
        filename: 'static/css/[name].[contenthash:8].css',
        chunkFilename: 'static/css/[name].[contenthash:8].chunk.css',
      }),
      new ScriptExtHtmlWebpackPlugin({
        inline: /runtime~.+\.js$/, // 运行时加载逻辑和模块依赖关系始终与页面加载的其他资源保持一致
      }),
      // new BundleAnalyzerPlugin({
      //   // analyzerMode: 'disabled', // 不启动展示打包报告的http服务器
      //   generateStatsFile: false, // 是否生成stats.json文件
      //   analyzerPort: 9090,
      // }),
    ].filter(Boolean),
  }),

];
