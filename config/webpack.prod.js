const paths = require('./paths');
const { merge } = require('webpack-merge');
const common = require('./webpack.common');
const TerserPlugin = require('terser-webpack-plugin');
const safePostCssParser = require('postcss-safe-parser');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
// const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
// 移除 const ScriptExtHtmlWebpackPlugin = require('script-ext-html-webpack-plugin');
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');

const smp = new SpeedMeasurePlugin();

// 生产环境默认关闭 source map 以提升构建速度
const shouldUseSourceMap = process.env.GENERATE_SOURCEMAP === 'true';

// gift空间申请完成后修改
const assetsPublicPath = process.env.APP_ENV !== 'prod' ? '/' : '//img-ys011.didistatic.com/static/cooper_cn/cooper/';


module.exports = smp.wrap([
  merge(common, {
    mode: 'production',
    bail: true,
    devtool: shouldUseSourceMap ? 'source-map' : false,
    entry: {
      cooper: paths.cooperAppIndexJs,
      knowledge: paths.knowledgeAppIndexJs,
    },
    output: {
      path: paths.appBuild,
      filename: 'static/js/[name].[contenthash:8].js',
      chunkFilename: 'static/js/[name].[contenthash:8].chunk.js',
      publicPath: assetsPublicPath,
    },
    optimization: {
      // tree shaking
      usedExports: true,
      // ！！开启scope  hosting
      concatenateModules: true,
      minimize: true,
      minimizer: [
        // JS 压缩
        new TerserPlugin({
          terserOptions: {
            parse: { ecma: 8 },
            compress: { ecma: 5, warnings: false, comparisons: false, inline: 2 },
            mangle: { safari10: true },
            keep_classnames: true,
            keep_fnames: true,
            output: { ecma: 5, comments: false, ascii_only: true },
          },
          parallel: true,
          extractComments: false,
        }),
        // CSS 压缩
        new CssMinimizerPlugin({
          parallel: true,
        }),
      ],
      runtimeChunk: {
        name: 'runtime',
      },
      splitChunks: {
        chunks: 'all',
        maxAsyncRequests: 30, // 优化：减少并行请求数，避免过多小文件
        maxInitialRequests: 30, // 优化：减少初始请求数
        minSize: 50 * 1024, // 优化：降低最小分割大小，更细粒度分割
        maxSize: 2000 * 1024, // 优化：设置最大大小，避免单个文件过大
        cacheGroups: {
          // 框架核心库
          framework: {
            test: /[\\/]node_modules[\\/](react|react-dom|react-router|react-router-dom)[\\/]/,
            name: 'framework',
            priority: 40,
            chunks: 'all',
            reuseExistingChunk: true,
          },
          // UI库
          ui: {
            test: /[\\/]node_modules[\\/](antd|@ant-design)[\\/]/,
            name: 'ui-lib',
            priority: 30,
            chunks: 'all',
            reuseExistingChunk: true,
          },
          // 工具库
          utils: {
            test: /[\\/]node_modules[\\/](lodash|ramda|dayjs|moment|axios)[\\/]/,
            name: 'utils-lib',
            priority: 25,
            chunks: 'all',
            reuseExistingChunk: true,
          },
          // 编辑器相关
          editor: {
            test: /[\\/]node_modules[\\/](prosemirror|@didi\/didoc)[\\/]/,
            name: 'editor-lib',
            priority: 35,
            chunks: 'all',
            reuseExistingChunk: true,
          },
          // 公共模块包
          commons: {
            name: 'chunk-commons',
            minChunks: 3,
            priority: 5,
            reuseExistingChunk: true,
          },
          // 其他第三方库
          vendor: {
            test(module, chunk) {
              return module.resource
                && module.resource.includes('node_modules')
                && (module.resource.indexOf('halo') === -1);
            },
            chunks: 'all',
            name(module) {
              const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/-]|$)/)[1];
              return `vendor.${packageName.replace('@', '')}`;
            },
            priority: 15,
            minChunks: 1,
            maxSize: 1500 * 1024, // 优化：减小vendor包大小
            reuseExistingChunk: true,
          },
        },
      },
    },
    plugins: [
      new MiniCssExtractPlugin({
        filename: 'static/css/[name].[contenthash:8].css',
        chunkFilename: 'static/css/[name].[contenthash:8].chunk.css',
        // v4不支持parallel参数，v5支持。此处注释说明：如升级v5可加parallel: true
      }),
      // 移除 ScriptExtHtmlWebpackPlugin
      // new ScriptExtHtmlWebpackPlugin({
      //   inline: /runtime~.+\.js$/, // 运行时加载逻辑和模块依赖关系始终与页面加载的其他资源保持一致
      // }),
      // html-webpack-plugin 5.x 支持 scriptLoading: 'blocking'，可提升 runtime 执行时机
      // 该配置已在 common.js 里传递给 HtmlWebpackPlugin
      // new BundleAnalyzerPlugin({
      //   // analyzerMode: 'disabled', // 不启动展示打包报告的http服务器
      //   generateStatsFile: false, // 是否生成stats.json文件
      //   analyzerPort: 9090,
      // }),
    ].filter(Boolean),
  }),

]);
