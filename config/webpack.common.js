const paths = require('./paths');
const webpack = require('webpack');
const ESLintPlugin = require('eslint-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { getThemeVariables } = require('antd/dist/theme');
const getLessVariables = require('../src/utils/theme');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
// const HtmlWebpackExternalsPlugin = require('html-webpack-externals-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin')
const HtmlWebpackInjectPlugin = require('@didi/html-webpack-inject-plugin');
const skeleton = require('../src/components/SkeletonPage/totalSkeleton');
const path = require('path');

const isDev = process.env.NODE_ENV === 'development';
const isProd = process.env.NODE_ENV === 'production';
const shouldUseSourceMap = process.env.GENERATE_SOURCEMAP !== 'false';


const definedEnv = {
  'process.env': {
    APP_ENV: `"${process.env.APP_ENV}"`,
    // 区分本地环境和生产环境
    NODE_ENV: `"${process.env.NODE_ENV}"`,
    PUBLIC_URL: '" "',
    FAST_REFRESH: isDev ? 'true' : 'false',
  },
};

module.exports = {
  output: {
    // futureEmitAssets: true,
    globalObject: 'this',
  },
  cache: { type: 'filesystem' },
  resolve: {
    alias: {
      '@': paths.appSrc,
      '@/': 'src/',
    },
    modules: [paths.appNodeModules],
    // 移除: plugins: [new ModuleScopePlugin(paths.appSrc, [paths.appPackageJson])],
  },
  module: {
    strictExportPresence: true,
    rules: [
      { parser: { requireEnsure: false }},
      {
        oneOf: [
          {
            test: /\.(xml)$/,
            type: 'asset/resource',
          },
          {
            test: /\.(?:png|jpe?g|gif|svg|woff|woff2|eot|ttf)\??.*$/,
            type: 'asset',
            parser: {
              dataUrlCondition: {
                maxSize: 8 * 1024, // 8KB以下的文件转为base64，减少HTTP请求
              },
            },
            generator: {
              filename: 'static/img/[name].[hash:8][ext]',
            },
          },
          {
            test: /\.(js|jsx)$/,
            include: [
              paths.appSrc,
              /node_modules\/react-draggable/,
            ],
            use: [
              {
                loader: 'thread-loader',
                options: {
                  workers: 8,
                  workerParallelJobs: 50, // 增加并行任务数
                  poolTimeout: isDev ? Infinity : 2000, // 开发环境保持worker池，生产环境及时释放
                },
              },
              {
                loader: 'babel-loader',
                options: {
                  plugins: [
                    isDev && require.resolve('react-refresh/babel'),
                    require.resolve('@babel/plugin-proposal-optional-chaining'),
                    require.resolve('@babel/plugin-proposal-nullish-coalescing-operator'),
                    require.resolve('@babel/plugin-proposal-class-properties'),
                    require.resolve('@babel/plugin-syntax-dynamic-import'),
                    [require.resolve('babel-plugin-import'), { libraryName: 'antd', libraryDirectory: 'es', style: true }],
                  ].filter(Boolean),
                  cacheDirectory: true,
                  cacheCompression: false,
                  compact: isProd, // 生产环境启用压缩
                  sourceMaps: shouldUseSourceMap && isDev, // 生产环境关闭sourceMap
                  inputSourceMap: shouldUseSourceMap && isDev,
                },
              },
            ],
          },
          // {
          //   test: /\.(js)$/,
          //   exclude: /@babel(?:\/|\\{1,2})runtime/,
          //   loader: require.resolve('babel-loader'),
          //   options: {
          //     babelrc: false,
          //     configFile: false,
          //     compact: false,
          //     cacheDirectory: true,
          //     cacheCompression: false,
          //     sourceMaps: shouldUseSourceMap,
          //     inputSourceMap: shouldUseSourceMap,
          //   },
          // },
          {
            test: /\.css$/,
            sideEffects: true,
            use: [
              // 添加 thread-loader 用于CSS处理
              {
                loader: 'thread-loader',
                options: {
                  workers: 2, // CSS处理相对简单，使用较少worker
                  workerParallelJobs: 50,
                  poolTimeout: 2000,
                },
              },
              isDev ? 'style-loader' : MiniCssExtractPlugin.loader,
              {
                loader: 'css-loader',
                options: {
                  esModule: false,
                  importLoaders: 2,
                  sourceMap: shouldUseSourceMap && isDev, // 生产环境关闭sourceMap
                },
              },
              {
                loader: 'postcss-loader',
                options: {
                  sourceMap: shouldUseSourceMap && isDev, // 生产环境关闭sourceMap
                  postcssOptions: {
                    plugins: [
                      require('autoprefixer')({ remove: false }),
                    ],
                  },
                },
              },
            ],
          },
          {
            test: /\.less$/,
            sideEffects: true,
            use: [
              // 添加缓存层提升样式处理速度
              {
                loader: 'cache-loader',
                options: {
                  cacheDirectory: 'node_modules/.cache/cache-loader/less',
                },
              },
              // 添加 thread-loader 用于样式处理
              {
                loader: 'thread-loader',
                options: {
                  workers: 4, // 样式处理使用较少的worker避免内存占用过高
                  workerParallelJobs: 50,
                  poolTimeout: 2000,
                },
              },
              isDev ? 'style-loader' : MiniCssExtractPlugin.loader,
              {
                loader: 'css-loader',
                options: {
                  esModule: false,
                  importLoaders: 2,
                  sourceMap: shouldUseSourceMap && isDev, // 生产环境关闭sourceMap
                  modules: {
                    auto: true,
                    localIdentName: '[local]--[hash:base64:5]',
                  },
                },
              },
              {
                loader: 'postcss-loader',
                options: {
                  sourceMap: shouldUseSourceMap && isDev, // 生产环境关闭sourceMap
                  postcssOptions: {
                    plugins: [
                      require('autoprefixer')({ remove: false }),
                    ],
                  },
                },
              },
              {
                loader: 'less-loader',
                options: {
                  sourceMap: shouldUseSourceMap && isDev, // 生产环境关闭sourceMap
                  lessOptions: {
                    javascriptEnabled: true,
                  },
                  additionalData: (content, loaderContext) => {
                    const path = require('path');
                    const fs = require('fs');
                    const glob = require('glob');
                    const globalLessFiles = glob.sync(path.resolve(__dirname, '../src/assets/style/global/*.less'));
                    const imports = globalLessFiles.map((file) => `@import "${file.replace(/\\/g, '/')}";`).join('\n');
                    return `${imports}\n${content}`;
                  },
                },
              },
            ],
          },
          {
            test: /\.scss$/,
            sideEffects: true,
            use: [
              // 添加 thread-loader 用于SCSS处理
              {
                loader: 'thread-loader',
                options: {
                  workers: 4, // SCSS处理使用中等数量的worker
                  workerParallelJobs: 50,
                  poolTimeout: 2000,
                },
              },
              isDev ? 'style-loader' : MiniCssExtractPlugin.loader,
              {
                loader: 'css-loader',
                options: {
                  esModule: false,
                  importLoaders: 2,
                  sourceMap: shouldUseSourceMap && isDev, // 生产环境关闭sourceMap
                  modules: {
                    compileType: 'module',
                    localIdentName: '[local]--[hash:base64:5]',
                  },
                },
              },
              {
                loader: 'postcss-loader',
                options: {
                  sourceMap: shouldUseSourceMap && isDev, // 生产环境关闭sourceMap
                  postcssOptions: {
                    plugins: [
                      require('autoprefixer')({ remove: false }),
                    ],
                  },
                },
              },
              {
                loader: 'sass-loader',
                options: {
                  sourceMap: shouldUseSourceMap && isDev, // 生产环境关闭sourceMap
                },
              },
            ],
            exclude: [paths.appCommon],
          },
        ],
      },
      {
        test: /\.mjs$/,
        include: /node_modules/,
        type: 'javascript/auto',
      },
    ],
  },
  plugins: [
    new HtmlWebpackPlugin(
      {
        env_my: process.env.APP_ENV,
        inject: true,
        template: paths.cooperAppHtml,
        chunks: ['cooper'],
        filename: 'index.html',
        ...(isProd
          ? {
            minify: { // 默认开启html-minifier-terser
              removeComments: true,
              collapseWhitespace: true,
              removeRedundantAttributes: true,
              useShortDoctype: true,
              removeEmptyAttributes: true,
              removeStyleLinkTypeAttributes: true,
              keepClosingSlash: true,
              minifyJS: true,
              minifyCSS: true,
              minifyURLs: true,
            },
            // inject: 'body', // js插入到body下面
          }
          : undefined),
      },
    ),
    new HtmlWebpackPlugin(
      {
        env_my: process.env.APP_ENV,
        inject: true,
        template: paths.knowledgeHtml,
        chunks: ['knowledge'],
        filename: 'knowledge.html',
        ...(isProd
          ? {
            minify: { // 默认开启html-minifier-terser
              removeComments: true,
              collapseWhitespace: true,
              removeRedundantAttributes: true,
              useShortDoctype: true,
              removeEmptyAttributes: true,
              removeStyleLinkTypeAttributes: true,
              keepClosingSlash: true,
              minifyJS: true,
              minifyCSS: true,
              minifyURLs: true,
            },
            // inject: 'body', // js插入到body下面
          }
          : undefined),
      },
    ),
    // new HtmlWebpackInjectPlugin(
    //   {
    //     dom: [{ reg: /id="root-skeleton">/, str: skeleton }],
    //   },
    // ),
    new CopyWebpackPlugin({
      patterns: [
        { from: paths.codeWorker, to: paths.publicJs },
      ],
    }),
    new webpack.DefinePlugin(definedEnv),
    new webpack.ProvidePlugin({
      React: 'react',
    }),
    // 生产环境启用CDN外部依赖以减少打包体积
    // new HtmlWebpackExternalsPlugin({
    //   externals: [
    //     {
    //       module: 'react',
    //       entry: 'https://img-ys011.didistatic.com/static/cooper_cn/<EMAIL>',
    //       global: 'React',
    //     },
    //     {
    //       module: 'react-dom',
    //       entry: 'https://img-ys011.didistatic.com/static/cooper_cn/<EMAIL>',
    //       global: 'ReactDOM',
    //     },
    //     {
    //       module: 'react-redux',
    //       entry: 'https://img-ys011.didistatic.com/static/cooper_cn/<EMAIL>',
    //       global: 'ReactRedux',
    //     },
    //   ],
    // }),
    // 移除: new ModuleNotFoundPlugin(paths.appPath),
    //  TODO:要不要开启esLint 配置
    // new ESLintPlugin({
    //   extensions: ['js', 'jsx'],
    //   formatter: require.resolve('react-dev-utils/eslintFormatter'),
    //   eslintPath: require.resolve('eslint'),
    //   context: paths.appSrc,
    //   cwd: paths.appPath,
    //   resolvePluginsRelativeTo: __dirname,
    // }),
  ],
  performance: false,
};

