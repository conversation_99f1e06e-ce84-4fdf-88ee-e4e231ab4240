import { get, post } from '@/utils/request/other';
import api from '@/utils/request/api/DkApi';

const didocUrlHost = {
  dev: 'didoc-qa.didichuxing.com',
  test: 'didoc-qa.didichuxing.com',
  qa: 'didoc-qa.didichuxing.com',
  production: 'didoc.didichuxing.com',
  prod: 'didoc.didichuxing.com',
}[process.env.APP_ENV];

export function uploadCover(params) {
  return post(api.UPLOAD_COVER, params, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export function getDidocDetail(guid) {
  return get(api.DIDOC_DETAIL.replace(':guid', guid));
}

export function getDidocHistory(guid) {
  return get(api.DIDOC_HISTORY.replace(':guid', guid).replace(':timestamp', Date.now()))
}

export function renameDoc({ docId, title }) {
  const params = {
    title,
    noHistory: true,
  };
  return post(api.RENAME_DOC.replace(':docId', docId), params);
}

export function editorUpdateCheck({ docId, publishVersion }) {
  return get(`//${didocUrlHost}/api/doc/migration/check?docId=${docId}&publishVersion=${publishVersion}`)
}

export function checkDocStatus({ docId }) {
  return get(`//${didocUrlHost}/api/doc/check/status?docId=${docId}`)
}

export const getDidocToken = async (params) => {
  const tokenResp = await post(`//${didocUrlHost}/api/oauth/getToken`, params)
  return tokenResp;
}

export async function setPublishVersion(params) {
  const { guid } = params;
  const res = await post(`//${didocUrlHost}/collab/doc/${guid}/version/publish`, params);
  return res;
}

export async function getNewEditorVersionContent({ guid, version }) {
  const res = await get(`//${didocUrlHost}/api/doc/version/${guid}_${version}`);
  return res;
}

export async function getNewVersionList({ guid }) {
  const res = await get(`//${didocUrlHost}/api/doc/publish/versions?docId=${guid}`);
  return res;
}

export async function getDidocTitle({ guid }) {
  const res = await get(`//${didocUrlHost}/api/docs/info/${guid}`);
  return res;
}

export async function updateDidocTitle({ docId, title }) {
  const params = {
    title,
    noHistory: true,
  };
  return post(`//${didocUrlHost}/api/docs/title/update/${docId}`, params);
}