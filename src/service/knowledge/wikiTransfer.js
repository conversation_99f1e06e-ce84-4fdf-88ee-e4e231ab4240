import { get, post } from '@/utils/request';
import Dk<PERSON>pi from '@/utils/request/api/DkApi';

export async function wikiTaskCheck(params) {
  try {
    console.log('wikiTaskCheck', params)
    const res = await post(DkApi.WIKI_TASK_CHECK, {
      ...params,
      includeSpacePerm: false,
    });
    return res;
  } catch (error) {
    console.log(error);
  }
}

export async function wikiTaskCreate(params) {
  try {
    const res = await post(DkApi.WIKI_TASK_CREATE, params);
    return res;
  } catch (error) {
    console.log(error);
  }
}

export async function wikiTaskSubnode(params) {
  try {
    const res = await post(DkApi.WIKI_TASK_SUBNODE, params);
    return res;
  } catch (error) {
    console.log(error);
  }
}

export async function wikiTaskSubnodeCheck(params) {
  try {
    const res = await post(DkApi.WIKI_TASK_SUBNODE_CHECK, params);
    return res;
  } catch (error) {
    console.log(error);
  }
}

export async function wikiTaskSubnodeLink(params) {
  try {
    const res = await post(DkApi.WIKI_TASK_LINK_VALID, params);
    return res;
  } catch (error) {
    console.log(error);
    return error
  }
}

export async function wikiTaskSubnodeCreatOwner(params) {
  try {
    const res = await post(DkApi.WIKI_TASK_CREATE_OWNER_VALID, params);
    return res;
  } catch (error) {
     console.log(error)
  }
}


export async function wikiTaskList(params) {
  try {
    const res = await get(DkApi.WIKI_TASK_LIST, { params });
    return res;
  } catch (error) {
    console.log(error);
  }
}

export async function wikiTaskProgress(params) {
  try {
    const res = await get(DkApi.WIKI_TASK_PROGRESS, { params });
    return res;
  } catch (error) {
    console.log(error);
  }
}

export async function wikiTaskJump(params) {
  try {
    const res = await post(DkApi.WIKI_TASK_JUMP, params);
    return res;
  } catch (error) {
    console.log(error);
  }
}
