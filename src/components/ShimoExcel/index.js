import { PageNotPublishIcon } from '@/assets/icon';
import ErrorTips from '@/components/ErrorTips';
import { intl } from 'di18n-react';
import { useEffect, useState, useContext, useRef } from 'react';
import { message } from 'antd';
import classNames from 'classnames/bind';
import { connect } from 'react-redux';
import { fetchSignatureAndToken, initShimoExcel } from '@/utils/shimo-excel';
import { inPhone } from '@/utils';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { useNavigate } from 'react-router-dom';
import * as styles from './style.module.less';

// 知识库和石墨保存状态映射
const SaveStatusMap = {
  saving: 'sending',
  saved: 'success',
  error: 'failed',
}

const cx = classNames.bind(styles);

const ShimoExcel = (props) => {
  const { changeInitLoading, changeSaveStatus, changeView, docInfo, isResizingAside } = props;
  const { knowledgeId, pageId } = useContext(LayoutContext);
  const containerRef = useRef(null);
  const [shimoSDK, setShimoSDK] = useState(null);
  const [historyMode, setHistoryMode] = useState(false);
  const navigate = useNavigate();
  const isInPhone = inPhone();

  useEffect(() => {
    if (isInPhone) {
      changeInitLoading(false);
    }
  }, []);

  if (isInPhone) {
    return (
      <ErrorTips
        img={PageNotPublishIcon}
        title={
          <div className={cx('no-permission-tip')}>
            {intl.t('暂不支持移动端访问表格')}
          </div>
        }
      />
    );
  }

  const handleSaveStatus = ({ status }) => {
    const result = SaveStatusMap[status] || '';
    changeSaveStatus(result);
  }

  const handleParamsChange = async (instance) => {
    const Editor = instance.getEditor();
    const sheetId = await Editor.getActiveSheetId();
    navigate(`${window.location.pathname}?sheetId=${sheetId}`)
  }

  const initEditor = async () => {
    const container = containerRef.current;
    const data = {
      fileId: docInfo.guid,
      signature: docInfo.accessToken,
      token: docInfo.uploadToken,
      id: pageId,
      container,
    };
    const shimoSDKCurr = await initShimoExcel(data);
    const Editor = shimoSDKCurr.getEditor();

    Editor.on('saveStatusChanged', handleSaveStatus);
    Editor.on('paramsChanged', handleParamsChange.bind(null, shimoSDKCurr));

    container.addEventListener('keydown', Editor.print);
    setShimoSDK(shimoSDKCurr);
    changeView(shimoSDKCurr);
  };

  // 定时刷新token
  const refreshTimer = (wait = 60000) => {
    return setTimeout(async () => {
      console.log('*** refreshTimer');
      const { signature, token, ttl } = await fetchSignatureAndToken();
      shimoSDK.setCredentials({
        signature,
        // token,
      });
      refreshTimer(Math.max(0, ttl - 1000 * 60 * 60));
    }, wait);
  };

  const showHistoryList = () => {
    containerRef.current.querySelector('iframe')?.contentWindow.postMessage({
      type: 'showHistoryList',
    }, '*');
    setHistoryMode(true);
    shimoSDK.getEditor().showHistory();
  }

  const closeHistoryList = () => {
    containerRef.current.querySelector('iframe')?.contentWindow.postMessage({
      type: 'hideHistoryList',
    }, '*');
    setHistoryMode(false);
    shimoSDK.getEditor().hideHistory();
  }

  useEffect(() => {
    if (docInfo.pageName) {
      window.document.title = docInfo.pageName;
    }
  }, [docInfo.pageName]);

  useEffect(() => {
    let timer = shimoSDK ? refreshTimer() : null;

    return () => {
      console.log('*** refreshTimer -- clear');
      clearTimeout(timer);
    };
  }, [shimoSDK]);

  useEffect(() => {
    initEditor(pageId)
      .catch((e) => {
        message.error('文档初始化失败');
        console.error('*** initShimo', e);
      })
      .finally(() => {
        const iframe = containerRef.current.querySelector('iframe');
        iframe?.contentWindow.postMessage({ type: 'TYPE', text: intl.t('本表格能力由石墨提供') }, '*');
        changeInitLoading(false);
        iframe?.contentWindow.postMessage({
          type: 'inKnowledge',
          inEdit: true,
        }, '*');
      });
  }, []);

  return (
    <div className={cx('shimo-excel')}>
      {/* <div className={cx('shimo-excel-history')}>
        <button onClick={showHistoryList}>历史记录</button>
      </div> */}
      {
        isInPhone && <h2 className={cx('shimo-excel-title')}>{docInfo.pageName}</h2>
      }
      <div className={cx('shimo-excel-content', { 'history-list-mode': historyMode })}>
        {
          historyMode && <button
            className={cx('hide-excel-history')}
            onClick={closeHistoryList}>关闭历史记录</button>
        }
        <div ref={containerRef} id='shimo-excel-container' className={cx('shimo-excel-container', { 'stop-event': isResizingAside })}/>
      </div>
    </div>
  );
};

function mapStateToProps({ pageDetail, asideDK }) {
  const { docInfo } = pageDetail;
  const { isResizingAside } = asideDK;
  return {
    docInfo,
    isResizingAside,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const { changeInitLoading, changeSaveStatus, changeView } = pageDetail;
  return {
    changeInitLoading,
    changeSaveStatus,
    changeView,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(ShimoExcel);
