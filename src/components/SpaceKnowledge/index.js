import classBind from 'classnames/bind';
import { useEffect, useState, useCallback } from 'react';
import { computeTimeInterval } from '@/utils';
import { Modal, Checkbox, Row, Button } from 'antd';
import { TEAM_ADMIN, TEAM_OWNER } from '@/utils/cooperutils';
import { intl } from 'di18n-react';
import { getSpaceKnowledgeList } from '@/service/cooper/teamDetail';
import { useParams } from 'react-router-dom';
import styles from './style.module.less';
import { useSelector } from 'react-redux';

const cx = classBind.bind(styles);

function SpaceRecommend(props) {
  const { knowledge, role, saveKnowledge } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [checkedValue, setCheckedValue] = useState([]);
  const [allKnowledgeList, setKnowledgeList] = useState([]);
  const [isHover, saveIsHover] = useState(false);
  const { teamId } = useParams();
  const { teamDynamic } = useSelector((state) => state.TeamData);

  useEffect(() => {
    if (knowledge?.value) {
      setCheckedValue(knowledge?.value);
    } else {
      setCheckedValue([]);
    }
  }, [knowledge?.value, isModalOpen]);

  useEffect(() => {
    if (teamId) {
      getSpaceKnowledgeList(teamId).then((res) => {
        setKnowledgeList(res);
      });
    }
  }, [teamId]);

  const handleOk = useCallback(() => {
    saveKnowledge(checkedValue);
    setIsModalOpen(false);
  }, [checkedValue, saveKnowledge]);

  const handleCancel = useCallback(() => {
    setIsModalOpen(false);
  }, []);

  const handleClick = useCallback(
    (item) => {
      const checkedSet = new Set(checkedValue);
      if (checkedSet.has(item.knowledgeId)) {
        checkedSet.delete(item.knowledgeId);
      } else {
        if (checkedSet.size >= 2) return;
        checkedSet.add(item.knowledgeId);
      }
      setCheckedValue(Array.from(checkedSet));
    },
    [checkedValue],
  );

  const gotoKnowlegePage = (knowledgeId, resourceId) => {
    window.open(`/knowledge/${knowledgeId}/${resourceId}`);
  };

  return (
    <div
      className={cx('space-detail-card')}
      onMouseEnter={() => saveIsHover(true)}
      onMouseLeave={() => saveIsHover(false)}
    >
      <div className={cx('card-title')}>
        <span>{intl.t('知识库')}</span>
        {isHover && (role === TEAM_OWNER || role === TEAM_ADMIN) && (
          <span
            className={cx('card-title-edit')}
            onClick={() => setIsModalOpen(true)}
          >
            <i className={cx('dk-iconfont', 'dk-icon-shezhi4', 'edit-icon')} />
            <span>{intl.t('设置')}</span>
          </span>
        )}
      </div>
      {teamDynamic && teamDynamic.length > 0 ? (
        <div className={cx('card-content')}>
          {teamDynamic.map((item, idx) => {
            return (
              <div
                className={cx('card-item')}
                key={idx}>
                <div
                  className={cx('card-item-title')}
                  onClick={() => gotoKnowlegePage(item.id, 'home')}
                >
                  <img
                    src={item.logo}
                    alt="logo"
                    className={cx('card-item-knowledge-logo')}
                  />

                  <span className={cx('card-item-knowledge-name')}>
                    {item?.name}
                  </span>
                </div>
                {item.list.slice(0, 3).length > 0 ? (
                  item.list.slice(0, 3).map((val, i) => {
                    return (
                      <div
                        key={i}
                        className={cx('card-item-list')}
                        onClick={() => gotoKnowlegePage(val.knowledgeId, val.resourceId)
                        }
                      >
                        <div className={cx('card-item-name')}>
                          {val?.operateContent?.name}
                        </div>
                        <div className={cx('card-item-time')}>
                          {computeTimeInterval(val.operateTime)}
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className={cx('knowledge-no-data')}>
                    {intl.t('暂无内容')}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      ) : (
        <div className={cx('card-no-data')}>{intl.t('暂无知识库')}</div>
      )}

      <Modal
        width={620}
        centered={true}
        className={cx('modal-setting')}
        title={intl.t('设置推荐知识库')}
        visible={isModalOpen}
        closeIcon={
          <i
            className={cx(
              'dk-iconfont',
              'dk-icon-guanbi',
              'modal-setting-close',
            )}
            onClick={handleCancel}
          />
        }
        footer={
          <div className={cx('modal-footer')}>
            <div>
              {intl.t('已选择:{number}/2个', { number: checkedValue.length })}
            </div>
            <div>
              <Button onClick={handleCancel}>{intl.t('取消')}</Button>
              <Button
                type="primary"
                onClick={handleOk}>
                {intl.t('确认')}
              </Button>
            </div>
          </div>
        }
      >
        <div className={cx('modal-content')}>
          <div className={cx('modal-tip')}>
            {intl.t(
              '最多显示2个知识库的内容。知识库必须为当前空间所属的知识库。',
            )}
          </div>
          <div className={cx('modal-list')}>
            {allKnowledgeList.length > 0 ? (
              <Checkbox.Group value={checkedValue}>
                {allKnowledgeList.map((item, idx) => {
                  return (
                    <Row
                      className={cx('modal-list-item', {
                        'modal-list-item-disabled':
                          checkedValue.length >= 2
                          && !checkedValue.includes(item.knowledgeId),
                      })}
                      key={idx}
                      onClick={() => {
                        handleClick(item);
                      }}
                    >
                      <Checkbox
                        value={item.knowledgeId}
                        className={cx('modal-list-label')}
                      />

                      <img
                        src={item.picture}
                        className={cx('modal-list-knowledge-logo')}
                      />

                      <span className={cx('modal-list-knowledge-name')}>
                        {item.name}
                      </span>
                    </Row>
                  );
                })}
              </Checkbox.Group>
            ) : (
              <div className={cx('empty-box')}>
                <img
                  src={require('@/assets/icon/empty12.png')}
                  className={cx('empty-img')}
                />

                <div className={cx('empty-text')}>
                  {intl.t('空间下暂无知识库，请先添加知识库')}
                </div>
              </div>
            )}
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default SpaceRecommend;
