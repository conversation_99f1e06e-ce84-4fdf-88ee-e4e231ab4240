import { intl } from 'di18n-react';
import classNames from 'classnames/bind';
import { useMemo, useState } from 'react';
import { useSelector, connect } from 'react-redux';
import { blurTime, getFileSuffix, bytesToSize } from '@/utils';
import styles from './style.module.less';

const cls = classNames.bind(styles);

function WkiMetaInfo(props) {
  const { docInfo = {}, view } = props;
  const { creator, createOn, pageId } = docInfo;
  const [latestVersion, setLatestVersion] = useState({});
  const [owner, setOwner] = useState(0);

  const writer = useMemo(() => {
    return latestVersion?.createdBy
      ? decodeURI(latestVersion?.createdBy?.chineseName)
      : creator;
  }, [creator, latestVersion]);

  const updateTime = useMemo(() => {
    return latestVersion?.createdOn || createOn;
  }, [createOn, latestVersion]);

  const count = view.state ? view.state.doc.textContent.length : 0;

  return (
    <div className={cls('meta-info')}>
      <div className={cls('character-count')} />
      <div className={cls('info')}>
        <span className={cls('username')}>{creator}</span>
        <span className={cls('time')}>
          {intl.t('创建于')}
          {blurTime(createOn)}
        </span>
      </div>
      <div className={cls('info')}>
        <span className={cls('username')}>{decodeURI(writer)}</span>
        <span className={cls('time')}>
          {intl.t('最后修改于')}
          {blurTime(updateTime)}
        </span>
      </div>
    </div>
  );
}
function mapStateToProps({ pageDetail }) {
  const { docInfo, view } = pageDetail;

  return {
    docInfo,
    view,
  };
}

export default connect(mapStateToProps)(WkiMetaInfo);
