.share-popover {
  z-index: 1000 !important; 

  .share-content {
    width: 540px;
    padding: 20px;
    :global{
      .ant-tabs-nav{
        &::before{
          margin: 0 -20px;
          border-bottom: 1px solid #EEEEEF !important;
        }
      }
    }
    .title {
      font-size: 16px;
      font-weight: 500;
      color: @blueGray-1;
    }

    .divider {
      margin: 10px -20px 0;
      height: 1px;
      background: #E5E5E5;
    }

    .switch-global {
      display: flex;
      align-items: center;
      font-size: 14px;
      padding-top: 18px;
      line-height: 22px;
    }

    .share-main {
      border-radius: 6px;
      background: #F4F6F8;
      padding: 16px 16px 20px 16px;
      margin-top: 20px;
    }

    .share-publish {
      display: flex;
    }

    .share-icon {
      color: #2ECDA4;
      font-size: 18px;
      margin-top: 4px;
    }

    .publish-content {
      flex: 1;
      margin-right: 24px;
      margin-left: 8px;
    }

    .publish-status {
      font-size: 16px;
      font-weight: 500;
      color: @blueGray-1;
    }

    .publish-desc {
      color: @blueGray-4;
      margin-top: 2px;
      font-size: 12px;
    }

    .publish-cancel {
      margin-top: 4px;
      // padding: 0 12px !important;

      // :global {
      //   .ant-btn {
      //     padding: 0 12px !important;
      //   }
      // }
    }

    .share-divider {
      margin: 16px 0;
      height: 1px;
      background: @blueGray-10;
    }

    .share-setting {
      .setting-titles {
        font-size: 14px;
        font-weight: 500;
        color: @blueGray-1;
        line-height: 20px;
        margin-bottom: 8px;
      }
      .setting-content {
        font-size: 14px;
        font-weight: 400;
        color: @blueGray-1;
        line-height: 20px;

        .setting-item {
          margin-bottom: 8px;

          .setting-text-disabled {
            color: @blueGray-6;
            cursor: not-allowed;
          }

          .disabled-desc {
            margin-left: 10px;
            font-size: 12px;
            color: @blueGray-8;
            line-height: 22px;
            display: inline-block;
          }

          :global {
            .ant-checkbox-wrapper {
              margin-right: 4px !important;
            }
          }
        }

        .setting-item:last-child {
          margin-bottom: 0;
        }
      }
    }

    .share-setting:last-child {
      margin-top: 16px;
    }
    
    .main {
      margin-top: 20px;
      background: #F7F8F9;
      border-radius: 7px;
      color: #444B4F;
      line-height: 30px;
      font-size: 16px;
      padding: 18px 20px;
    }
  }

  .share-content-tab {
    padding-top: 20px;
  }

  .share-close {
    padding-bottom: 20px;
  }

  .share-collaborator {
    width: 540px;
    padding: 20px 20px 0;
  }
  
  .add-collaborator {
    width: 540px;
    padding: 20px 20px 0;
    height: 580px;
  }

  .share-content-desc {
    margin-top: 48px;
    text-align: center;
  }

  .share-content-img {
    width: 76px;
  }

  .share-content-text {
    margin-top: 12px;
    color: @blueGray-1;
    padding: 0 32px;
    text-align: left;
  }

  .share-content-link {
    color: @primary-color;
    cursor: pointer;
  }

  .share-content-link:hover {
    border-bottom: 1px solid #047FFE;
  }

  .share-content-bottom {
    margin-top: 68px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .share-content-tip {
    color: @blueGray-6;
    font-size: 12px;
    text-align: left;
  }

  .safe-need {
    display: block;
    height: 20px;
    line-height: 1;
    margin-bottom: 6px;
  }

  .safe-need:last-child {
    margin-bottom: 0;
  }

  .safe-text {
    color: @blueGray-1;
  }

  .safe-text-disabled {
    color: @blueGray-6;
  }

  .safe-tip {
    color: @blueGray-6;
    font-size: 12px;
    margin-left: 8px;
  }

  .safe-tip-disabled {
    color: @blueGray-8;
  }

  :global {
    .ant-popover-inner-content {
      padding: 0 !important;
    }
    .ant-popover-inner {
      border-radius: 8px !important;
    }
    .ant-tabs-nav {
      margin-bottom: 0 !important;
    }
    .ant-tabs-tab {
      line-height: 32px !important;
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      margin-top: -8px !important;
    }
    .ant-tabs-tab-active {
      position: relative;

      &::before {
        content: '';
        width: 24px;
        height: 2px;
        background-color: #047FFE;
        left: calc(50% - 12px);
        bottom: 0px;
        z-index: 100;
        position: absolute;
      }
    }
  }
}

.share-switch {
  margin-right: 6px;
}

.confirm-modal {
  width: 480px;
  margin: auto;

  .content {
    font-size: 16px;
    font-weight: 400;
    color: #444B4F;
    line-height: 24px;
  }

  :global {
    .ant-modal-content {
      border-radius: 6px;
      overflow: hidden;

      .ant-modal-header {
        padding: 32px 28px 10px;
        border-bottom: none;

        .ant-modal-title {
          height: 33px;
          line-height: 33px;
          font-size: 24px;
          font-weight: 500;
          color: @text-color;
        }
      }

      .ant-modal-body {
        padding: 0 28px;
      }

      .ant-modal-footer {
        border-top: none;
        padding: 28px 28px 24px;

        .ant-btn {
          width: 96px;
          height: 44px;
          border-radius: 4px;
          color: #444B4F;
          background: #F6F6F6;
          border: none;
          font-size: 16px;

          &.ant-btn-primary {
            color: #FFFFFF;
            background: #0B83FF;
            margin-left: 16px;
          }
        }
      }
    }
  }
}

.second-floor-modal {
  z-index: 1031 !important;
}

.origin-share-content {
  position: relative;
  min-height: 200px;
}

.list-wrap-loading {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: absolute !important;
}
