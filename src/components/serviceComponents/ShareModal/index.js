import { intl } from 'di18n-react';
import { useEffect, useMemo, useState, useContext, useRef } from 'react';
import { Popover } from 'antd';
import classNames from 'classnames/bind';
import { useDispatch, useSelector } from 'react-redux';
import { SHARE_TYPE, SHARE_MODAL_TEXT } from '@/constants/index';
import ShareEntryDK from './ShareEntryDK';
import ShareEntryPage from './ShareEntryPage';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import Share from '@/components/serviceComponents/Share';
import MemberListOfDK from '@/components/serviceComponents/MemberListOfDK';
import AddNewCollaborator from '../AddNewCollaborator';
import { CooperTabs, CooperTabsPane } from '@/components/common/CooperTabs';
import ShareContent from './ShareContent';
import { SHARE_STATUS } from '@/constants/setup';
import { getType } from '@/utils/type'
import styles from './style.module.less';

const cls = classNames.bind(styles);

function ShareModal({ resourceId, shareType, hasPermission, resourceName }) {
  const { knowledgeId, pageId } = useContext(LayoutContext);
  const [ sharePopoverVisible, setSharePopoverVisible ] = useState(false);
  const [ step, setStep ] = useState(0);
  const [ searchValue, setSearchValue ] = useState('');
  const { shareModalSetting } = useSelector((state) => state.SharePage);
  const { docInfo } = useSelector((state) => state.pageDetail);
  const dispatch = useDispatch();
  const { getShareModalSetting } = dispatch.SharePage;
  const { cooperLinkConf } = useSelector((state) => state.GlobalData);
  const [ hasChildModal, setHasChildModal ] = useState(false);
  const [firstToThird, setFirstToThird] = useState(false);
  const shareComponentRef = useRef(null);

  const isSharing = useMemo(() => {
    return shareModalSetting?.status === SHARE_STATUS.OPEN; // 0:关闭 1:开启
  }, [shareModalSetting]);

  useEffect(() => {
    if (!sharePopoverVisible) {
      setSearchValue('');
    }
  }, [sharePopoverVisible]);

  useEffect(() => {
    if (step === 0 || step === 1) {
      setSearchValue('');
    }
  }, [step]);

  useEffect(() => {
    if (shareType === SHARE_TYPE.DK) {
      getShareModalSetting({
        resourceType: shareType,
        resourceId,
      });
    }
  }, [shareType])

  const onPopoverVisibleChange = (value) => {
    // 判断子组件是否有其他弹出层，如果有失焦不关闭popover
    if (hasChildModal) return;

    if (value) {
      if (shareType === SHARE_TYPE.DK) {
        window.__OmegaEvent('ep_dkpc_share_ck');
      } else {
        const isType = [ 'docs', 'sheets', 'slides', 'flowchart', 'xmind', 'pages' ].includes(getType(docInfo.type));
        window.__OmegaEvent('ep_detail_share_ck', '', {
          source: isType ? getType(docInfo.type) : '',
        });
      }
    } else {
      setStep(0);
    }
    setSharePopoverVisible(value);
  };

  const triggerSearch = (value) => {
    setSearchValue(value);
    setStep(2);
    setFirstToThird(true)
  }

  const toggleChildVisible = (isHas) => {
    setHasChildModal(isHas);
  };

  const handleThirdBack = () => {
    const setpHistory = firstToThird ? 0 : 1
    setStep(setpHistory)
    firstToThird && setFirstToThird(false)
  }

  // 知识库页面新分享
  const shareTab = (
    <>
      {
        step === 0 && (
          <div
            id='tooltip-wrap'
            className={cls('share-content', { 'share-close': !isSharing })}
          >
            <CooperTabs>
              <CooperTabsPane key='1' tab={intl.t('分享')} className={cls('share-content-tab')}>
                <Share
                  ref={shareComponentRef}
                  isInDK={true}
                  isDkPage={true}
                  resourceId={pageId}
                  info={{
                    sourceId: knowledgeId,
                    fileType: docInfo?.type
                  }}
                  cooperLinkConf={cooperLinkConf}
                  gotoNext={() => setStep(1)}
                  triggerSearch={triggerSearch}
                  closeShare={() => {
                    setSharePopoverVisible(false);
                    setSearchValue('');
                  }}
                  toggleChildVisible={toggleChildVisible}
                />
              </CooperTabsPane>
              <CooperTabsPane key='2' tab={typeof SHARE_MODAL_TEXT[shareType].title === 'function' ? SHARE_MODAL_TEXT[shareType].title() : SHARE_MODAL_TEXT[shareType].title } >
                <ShareContent
                  resourceId={resourceId}
                  shareType={shareType}
                  hasPermission={hasPermission}
                  resourceName={resourceName}
                  sharePopoverVisible={sharePopoverVisible}
                />
              </CooperTabsPane>
            </CooperTabs>
          </div>
        )
      }
      {
        step === 1 && (
          <div className={cls('share-collaborator')}>
            <MemberListOfDK
              isDkPage={true}
              needPerm={true}
              resourceId={pageId}
              teamId={knowledgeId}
              teamIdAllTeam={knowledgeId}
              permission={docInfo.permission}
              roleKey={docInfo.roleKey}
              title={intl.t('协作者')}
              showBack={true}
              handleBack={() => setStep(0)}
              gotoNext={() => setStep(2)}
              permissionsPopHover={true}
              info={{ fileType: docInfo.type }}
            />
          </div>
        )
      }
      {
        step === 2 && (
          <div className={cls('add-collaborator')}>
            <AddNewCollaborator
              isDkPage={true}
              resourceId={pageId}
              teamId={knowledgeId}
              showBack={true}
              fileType={docInfo.type}
              searchValue={searchValue}
              backToFirstStep={() => {
                setStep(0);
                setFirstToThird(false)
                setTimeout(() => {
                  if (shareComponentRef.current) {
                    shareComponentRef.current.focusInput();
                  }
                }, 1000);
              }}
              handleBack={() => { handleThirdBack() }}
              closeShare={() => {
                setStep(0);
                setSharePopoverVisible(false);
              }}
            />
          </div>
        )
      }
    </>
  );

  // 知识库原分享
  const shareTabOnly = (
    <div
      id='tooltip-wrap'
      className={cls('share-content', { 'share-close': !isSharing })}
    >
      <CooperTabs>
        <CooperTabsPane tab={typeof SHARE_MODAL_TEXT[shareType].title === 'function' ? SHARE_MODAL_TEXT[shareType].title() : SHARE_MODAL_TEXT[shareType].title } >
          <ShareContent
            resourceId={resourceId}
            shareType={shareType}
            hasPermission={hasPermission}
            resourceName={resourceName}
            sharePopoverVisible={sharePopoverVisible}
          />
        </CooperTabsPane>
      </CooperTabs>
    </div>
  );

  const getContent = () => {
    if (shareType === SHARE_TYPE.DK) {
      return shareTabOnly;
    }
    if (shareType === SHARE_TYPE.PAGE) {
      return shareTab;
    }
  }

  return (
    <div className={cls('share')}>
      <Popover
        trigger={['click']}
        content={getContent()}
        placement={'bottomRight'}
        destroyTooltipOnHide={true}
        onVisibleChange={onPopoverVisibleChange}
        visible={sharePopoverVisible}
        overlayClassName={cls('share-popover', 'dk-ant-popover__reset',{'dk-ant-popover_share': shareType ===  SHARE_TYPE.PAGE})}
      >
        {shareType === SHARE_TYPE.DK && <ShareEntryDK isSharing={isSharing} />}
        {shareType === SHARE_TYPE.PAGE && <ShareEntryPage isSharing={isSharing} />}
      </Popover>
    </div>
  );
}

export default ShareModal;
