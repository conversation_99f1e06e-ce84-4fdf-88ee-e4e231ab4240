import { Button } from 'antd';
import { intl } from 'di18n-react';
import classNames from 'classnames/bind';
import styles from './style.module.less';

const cls = classNames.bind(styles);

function ShareEntryPage() {
  return (
    <Button className={cls('share-trigger')}>
      <i className={cls('dk-iconfont', 'dk-icon-fenxiang', 'status-icon')}/>
      {intl.t('分享')}
    </Button>
  );
}

export default ShareEntryPage;
