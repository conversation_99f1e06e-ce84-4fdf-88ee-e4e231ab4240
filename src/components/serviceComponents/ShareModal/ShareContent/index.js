
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames/bind';
import { Tooltip, Radio, Button, Checkbox, Spin } from 'antd';
import { intl } from 'di18n-react';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { getShareLink } from '@/utils/index';
import { SHARE_TYPE, SHARE_MODAL_TEXT } from '@/constants/index';
import TextOnlyModal from '@/components/TextOnlyModal';
import { SAFE_SETTING_VALUE, SHARE_STATUS } from '@/constants/setup';
import useNotification from '@/hooks/useNotification';
import { gainSetting } from '@/service/knowledge/setup';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import ShareLink from '../ShareLink';
import NotificationStatus from '@/constants/notification';

import styles from '../style.module.less';

const cls = classNames.bind(styles);
const CAN_OPERATE = 1;

const ShareContent = ({ resourceId, shareType, hasPermission, resourceName, sharePopoverVisible }) => {
  const { knowledgeId, pageId } = useContext(LayoutContext);
  const dispatch = useDispatch();
  const [ loading, setLoading ] = useState(false);
  const { getShareModalSetting, editShareModalSetting } = dispatch.SharePage;
  const { shareModalSetting } = useSelector((state) => state.SharePage);
  const { operatePerm } = useSelector((state) => state.Setting);
  const { changeOperatePerm } = dispatch.Setting;
  const notification = useNotification();
  const { cooperLinkConf } = useSelector((state) => state.GlobalData);

  const isSharing = shareModalSetting?.status === SHARE_STATUS.OPEN;

  const downloadValue = shareModalSetting?.sharePermList?.download;

  const commentValue = shareModalSetting?.sharePermList?.comment;

  const openSubPageValue = shareModalSetting?.isOpenSubPage;

  const shareLink = getShareLink({ shareId: shareModalSetting?.shareId, shareType });

  const reqShareSetting = () => {
    setLoading(true)
    getShareModalSetting({
      resourceType: shareType,
      resourceId,
    });

    // 获取高级设置中安全设置中是否允许操作
    gainSetting({ knowledgeId }).then((res) => {
      setLoading(false);
      changeOperatePerm({
        isAllowDownload: res?.switchSetting?.shareLinkDownloadConfig, // 1:允许 2.不允许
        isAllowComment: res?.switchSetting?.shareLinkCommentConfig,
      });
    });
  };

  let uniqueId = pageId || knowledgeId;

  useEffect(() => {
    if (sharePopoverVisible) {
      reqShareSetting();
      return;
    }
    setLoading(false);
    reqShareSetting();
  }, [uniqueId, sharePopoverVisible]);

  const getDesc = (value, diffDesc) => {
    return intl.t('{slot0}后，获得{slot1}的人都将{slot2}{diffDesc}', {
      slot0: value === SAFE_SETTING_VALUE.OPEN ? intl.t('关闭') : intl.t('开启'),
      slot1: typeof SHARE_MODAL_TEXT[shareType].title === 'function' ? SHARE_MODAL_TEXT[shareType].title() : SHARE_MODAL_TEXT[shareType].title,
      slot2: value === SAFE_SETTING_VALUE.OPEN ? intl.t('无法') : intl.t('可以'),
      diffDesc,
    });
  };

  const MODAL_DATA_MAP = {
    closeShare: {
      title: intl.t('确定取消发布？'),
      cancelText: intl.t('取消'),
      okText: intl.t('确定'),
      content: intl.t(
        '一旦取消发布，之前获得分享链接的用户将无法访问。',
        { shareTypeText },
      ),
      onOk: () => {
        doControlSetting({
          control: SHARE_STATUS.CLOSE,
        }).then(() => {
          notification(
            NotificationStatus.SUCCESS,
            intl.t('{shareTypeText}已取消发布', {
              shareTypeText,
            }),
          );
        });
      },
    },
    download: {
      title: intl.t('权限变更提示'),
      cancelText: intl.t('取消'),
      okText: intl.t('确认变更'),
      content: getDesc(downloadValue, intl.t('下载页面内的附件')),
      onOk: () => {
        onOkDownload();
      },
    },
    comment: {
      title: intl.t('权限变更提示'),
      cancelText: intl.t('取消'),
      okText: intl.t('确认变更'),
      content: getDesc(commentValue, intl.t('查看和添加评论')),
      onOk: () => {
        onOkComment();
      },
    },
    subScope: {
      title: intl.t('更改分享范围'),
      cancelText: intl.t('取消'),
      okText: intl.t('确认变更'),
      content: (
        <p>
          {intl.t('选择分享“当前页面及子页面”，你为')}
          <b>{intl.t('管理员')}</b>
          {intl.t('角色的子页面会同时开启分享')}
        </p>
      ),
      onOk: () => {
        window.__OmegaEvent('ep_dkpc_pagedetail_share_includesubpages_ck');
        onOkShareScope();
      },
    },
    onlySelfScope: {
      title: intl.t('更改分享范围'),
      cancelText: intl.t('取消'),
      okText: intl.t('确认变更'),
      content: (
        <p>
          {intl.t(
            '选择分享“仅当前页面”,获得分享链接的用户将无法再访问当前页面的子页面，但不会更改子页面的分享状态',
          )}
        </p>
      ),
      onOk: () => {
        window.__OmegaEvent('ep_dkpc_pagedetail_share_current_ck');
        onOkShareScope();
      },
    },
  };

  const permRenderArray = [
    {
      text: intl.t('下载页面附件'),
      disableDesc: intl.t('知识库-安全设置-已关闭-下载'),
      overAllAllow: operatePerm.isAllowDownload,
      allow: downloadValue,
      clickPermSwitch: () => showConfirm('download'),
    },
    {
      text: intl.t('查看并添加划线评论、全文评论'),
      disableDesc: intl.t('知识库-安全设置-已关闭-评论'),
      overAllAllow: operatePerm.isAllowComment,
      allow: commentValue,
      clickPermSwitch: () => showConfirm('comment'),
    },
  ];

  // const isSharing = useMemo(() => {
  //   return shareModalSetting?.status === SHARE_STATUS.OPEN; // 0:关闭 1:开启
  // }, [shareModalSetting]);

  // const downloadValue = useMemo(() => {
  //   return shareModalSetting?.sharePermList?.download;
  // }, [shareModalSetting]);

  // const commentValue = useMemo(() => {
  //   return shareModalSetting?.sharePermList?.comment;
  // }, [shareModalSetting]);

  // const openSubPageValue = useMemo(() => {
  //   return shareModalSetting?.isOpenSubPage;
  // }, [shareModalSetting]);

  // const shareLink = useMemo(() => {
  //   return getShareLink({ shareId: shareModalSetting?.shareId, shareType });
  // }, [shareModalSetting]);

  const shareTypeText = useMemo(() => {
    return shareType === SHARE_TYPE.DK ? intl.t('知识库') : intl.t('页面');
  }, [shareType]);

  const isPermCheck = useCallback((overAllVal, val) => {
    return overAllVal === SAFE_SETTING_VALUE.OPEN && val;
  }, []);

  const showConfirm = (key) => {
    const item = MODAL_DATA_MAP[key];
    TextOnlyModal.confirm({
      title: item.title,
      wrapClassName: cls('second-floor-modal'),
      getContainer: () => document.querySelector('#tooltip-wrap'),
      content: item.content,
      onOk: item.onOk,
    });
  };

  const onOkDownload = () => {
    let newValue = Math.abs(downloadValue - 1);
    doControlSetting({ download: newValue, control: 2 })
      .then(() => {
        notification(NotificationStatus.SUCCESS, intl.t('变更成功'));
      })
      .catch(() => {
        notification(NotificationStatus.ERROR, intl.t('变更失败，请稍后再试'));
      });

    if (newValue) {
      if (shareType === SHARE_TYPE.DK) {
        window.__OmegaEvent('ep_dkpc_share_closedownload_ck');
      } else {
        window.__OmegaEvent('ep_dkpc_pagedetail_share_closedownload_ck');
      }
    } else if (shareType === SHARE_TYPE.DK) {
      window.__OmegaEvent('ep_dkpc_share_opendownload_ck');
    } else {
      window.__OmegaEvent('ep_dkpc_pagedetail_share_opendownload_ck');
    }
  };

  const onOkComment = () => {
    const newValue = Math.abs(commentValue - 1);

    doControlSetting({ comment: Math.abs(commentValue - 1), control: 2 })
      .then(() => {
        notification(NotificationStatus.SUCCESS, intl.t('变更成功'));
      })
      .catch(() => {
        notification(NotificationStatus.ERROR, intl.t('变更失败，请稍后再试'));
      });

    if (newValue === CAN_OPERATE) {
      shareType === SHARE_TYPE.DK
        && window.__OmegaEvent('ep_dkpc_dkpcvistor_allowcomment_ck');
      shareType === SHARE_TYPE.PAGE
        && window.__OmegaEvent('ep_dkpc_pagevistor_allowcomment_ck');
    } else {
      shareType === SHARE_TYPE.DK
        && window.__OmegaEvent('ep_dkpc_dkpcvistor_forbidcomment_ck');
      shareType === SHARE_TYPE.PAGE
        && window.__OmegaEvent('ep_dkpc_pagevistor_forbidcomment_ck');
    }
  };

  const onOkShareScope = () => {
    let newValue = Math.abs(openSubPageValue - 1);
    doControlSetting({
      scopeValue: newValue,
      control: 2,
    }).then(() => {
      notification(NotificationStatus.SUCCESS, intl.t('更改分享范围成功'));
    });
  };

  const copyCallBack = () => {
    notification(NotificationStatus.SUCCESS, intl.t('链接复制成功'));

    if (shareType === SHARE_TYPE.DK) {
      window.__OmegaEvent('ep_dkpc_share_copylink_ck');
    } else {
      window.__OmegaEvent('ep_dkpc_pagedetail_share_copylink_ck');
    }
  };

  const doControlSetting = async ({
    control,
    download,
    comment,
    scopeValue,
  }) => {
    // 注意:control: 0: 关闭分享 1:开启分享 2:修改除分享状态之外的其他操作
    let params = {
      resourceId,
      resourceType: shareType,
      control: control ?? isSharing + 0,
      isOpenSubPage: scopeValue ?? openSubPageValue,
      sharePermList: {
        download: download ?? downloadValue,
        comment: comment ?? commentValue,
      },
    };

    const data = editShareModalSetting(params);
    return data;
  };

  const clickShareSwitch = (checked) => {
    if (checked) {
      doControlSetting({
        control: SHARE_STATUS.OPEN,
      }).then(() => {
        notification(
          NotificationStatus.SUCCESS,
          intl.t('{shareTypeText}已发布', { shareTypeText }),
        );
      });
    } else {
      showConfirm('closeShare');
    }

    if (checked) {
      if (shareType === SHARE_TYPE.DK) {
        window.__OmegaEvent('ep_dkpc_share_begin_ck');
      } else {
        window.__OmegaEvent('ep_dkpc_pagedetail_share_begin_ck');
      }
    } else if (shareType === SHARE_TYPE.DK) {
      window.__OmegaEvent('ep_dkpc_share_stop_ck');
    } else {
      window.__OmegaEvent('ep_dkpc_pagedetail_share_stop_ck');
    }
  };

  const isPermDisable = useCallback(
    (val) => {
      if (!val) return !hasPermission;
      return !hasPermission || val === SAFE_SETTING_VALUE.CLOSE;
    },
    [hasPermission],
  );

  return (
    <div className={cls('origin-share-content')}>
      {
        loading ? <Spin loading={true} className={cls('list-wrap-loading')} /> : (
          <>
            {isSharing ? (
              <>
                <ShareLink
                  shareLink={shareLink}
                  shareTypeText={shareTypeText}
                  resourceName={resourceName}
                  copyCallBack={copyCallBack}
                  shareType={shareType}
                />

                <div className={cls('share-main')}>
                  <div className={cls('share-publish')}>
                    <i className={cls('dk-iconfont', 'dk-icon-chenggong', 'share-icon')} />
                    <div className={cls('publish-content')}>
                      <div className={cls('publish-status')}>
                        {intl.t('已发布')}
                      </div>
                      <div className={cls('publish-desc')}>
                        {intl.t('公司内获得链接的人都可访问（注：该链接与文档网址不一致，且该链接内容不支持被公开搜索）')}
                      </div>
                    </div>
                    {
                      hasPermission ? (
                        <Button
                          className={cls('publish-cancel')}
                          disabled={!hasPermission}
                          onClick={() => clickShareSwitch(false)}
                        >
                          {intl.t('取消发布')}
                        </Button>
                      ) : (
                        <Tooltip title={typeof SHARE_MODAL_TEXT[shareType].cancleDisabled === 'function' ? SHARE_MODAL_TEXT[shareType].cancleDisabled() : SHARE_MODAL_TEXT[shareType].cancleDisabled}>
                          <Button
                            className={cls('publish-cancel')}
                            disabled={!hasPermission}
                            onClick={() => clickShareSwitch(false)}
                          >
                            {intl.t('取消发布')}
                          </Button>
                        </Tooltip>
                      )
                    }
                  </div>
                  <div className={cls('share-divider')}></div>
                  {shareType === SHARE_TYPE.PAGE && (
                    <div className={cls('share-setting')}>
                      <div className={cls('setting-titles')}>{intl.t('分享范围')}</div>
                      <div className={cls('setting-content')}>
                        <Radio.Group
                          disabled={!hasPermission}
                          onChange={(e) => {
                            showConfirm(e.target.value ? 'subScope' : 'onlySelfScope');
                          }}
                          value={openSubPageValue}
                          className={cls('safe-radio', 'dk-ant-radio_reset')}
                        >
                          <Radio
                            className={cls('safe-need')}
                            value={0}
                          >
                            <span className={cls({
                              'safe-text': true,
                              'safe-text-disabled': !hasPermission,
                            })}>
                              {intl.t('仅当前页面')}
                            </span>
                          </Radio>
                          <Radio
                            className={cls('safe-need')}
                            value={1}
                          >
                            <span className={cls({
                              'safe-text': true,
                              'safe-text-disabled': !hasPermission,
                            })}>
                              {intl.t('当前页面及子页面')}
                            </span>
                            <span className={cls({
                              'safe-tip': true,
                              'safe-tip-disabled': !hasPermission,
                            })}>
                              {intl.t('若新增子页面，子页面分享状态需手动开启')}
                            </span>
                          </Radio>
                        </Radio.Group>
                      </div>
                    </div>
                  )}

                  <div className={cls('share-setting')}>
                    <div className={cls('setting-titles')}>{intl.t('链接权限')}</div>
                    <div className={cls('setting-content')}>
                      {permRenderArray.map(
                        ({
                          text,
                          overAllAllow,
                          allow,
                          disableDesc,
                          clickPermSwitch,
                        }) => (
                          <div
                            className={cls('setting-item')}
                            key={text}
                          >
                            <Checkbox
                              disabled={isPermDisable(overAllAllow)}
                              checked={isPermCheck(overAllAllow, allow)}
                              className={cls('share-switch')}
                              onChange={clickPermSwitch}
                            >
                              <span className={cls({
                                'setting-text-disabled': isPermDisable(overAllAllow),
                              })}>
                                {text}
                              </span>
                              {overAllAllow !== 1 && (
                                <span className={cls('disabled-desc')}>
                                  {intl.t('知识库安全设置中{disableDesc}权限已关闭', {
                                    disableDesc,
                                  })}
                                </span>
                              )}
                            </Checkbox>
                          </div>
                        ),
                      )}
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className={cls('share-content-desc')}>
                  <img 
                    className={cls('share-content-img')} 
                    src={SHARE_MODAL_TEXT[shareType].image}
                    alt='图片' 
                  />
                  <div className={cls('share-content-text')}>
                    {typeof SHARE_MODAL_TEXT[shareType].text === 'function' ? SHARE_MODAL_TEXT[shareType].text() : SHARE_MODAL_TEXT[shareType].text }
                    <a className={cls('share-content-link')} href={cooperLinkConf.origin_publish} target='_blank'>
                      {intl.t('示例说明')}
                    </a>
                  </div>
                  <div className={cls('share-content-bottom')}>
                    <div className={cls('share-content-tip')}>
                      {intl.t('发布生成的链接与文档网址无法互通使用，且该链接内容不支持被公开搜索')}
                    </div>
                    {
                      hasPermission ? (
                        <Button
                          disabled={!hasPermission}
                          type='primary'
                          onClick={() => clickShareSwitch(true)}
                        >
                          {intl.t('发布')}
                        </Button>
                      ) : (
                        <Tooltip title={typeof SHARE_MODAL_TEXT[shareType].disabled === 'function' ? SHARE_MODAL_TEXT[shareType].disabled() : SHARE_MODAL_TEXT[shareType].disabled}>
                          <Button
                            disabled={!hasPermission}
                            type='primary'
                            onClick={() => clickShareSwitch(true)}
                          >
                            {intl.t('发布')}
                          </Button>
                        </Tooltip>
                      )
                    }
                  </div>
                </div>
              </>
            )}
          </>
        )
      }
    </div>
  )
};

export default ShareContent;
