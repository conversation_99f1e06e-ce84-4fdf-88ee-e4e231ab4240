/* eslint-disable react/jsx-max-props-per-line */
/* eslint-disable operator-linebreak */
import { Modal, Spin } from 'antd';
import { useEffect, useState, useRef, useCallback } from 'react';
import styles from './style.module.less';
import classBind from 'classnames/bind';
import noTransferTask from '@/assets/icon/noTransferTask.png';
import { wikiTaskList } from '@/service/knowledge/wikiTransfer';
import InfiniteScroll from 'react-infinite-scroller';
import ModalWrapper from '../components/ModalWrapper';
import DetailModal, { Status as ProgressStatus } from '../components/DetailModal';

const cx = classBind.bind(styles);

const statusDesc = [
  { /* Queue */
    text: '排队中',
    color: '#656A72',
  },
  { /* Active */
    text: '迁移中',
    color: '#656A72',
  },
  { /* Success */
    text: '迁移成功',
    color: '#2ECDA4',
  },
  { /* Interrupted */
    text: '部分失败',
    color: '#FF563B',
  },
]

const listPageSize = 10;

const TransferList = ({ onClose }) => {
  const [visible] = useState(true);
  const [pending, setPending] = useState(false);

  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [taskId, setTaskId] = useState(null);
  const onCloseDetailModal = ({ id, status }) => {
    setDetailModalVisible(false);
    needRefresh && refreshStatus(id, status);
  }

  const gotoDetail = (id, status) => {
    setNeedRefresh(status != ProgressStatus.Success);
    setDetailModalVisible(true);
    setTaskId(id);
  }

  const [data, setData] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);
  const getListData = useCallback(() => {
    if (!hasMore || pending) return;
    setPending(true);
    wikiTaskList({
      pageNum: page,
      pageSize: listPageSize,
    })
      .then((res) => {
        if (res !== undefined) {
          setData((v) => [...v, ...res]);
          if (res.length >= listPageSize) {
            setPage(page + 1);
            setHasMore(true);
          } else {
            setHasMore(false);
          }
          setPending(false);
        }
      }).finally(() => {
        setPending(false);
      });
  }, [pending, hasMore]);

  useEffect(() => {
    getListData();
  }, [])

  const [needRefresh, setNeedRefresh] = useState(false);
  const refreshStatus = (id, status) => {
    const index = data.findIndex((item) => item.taskId === id);
    // bug修复 - 弹窗状态是空间初始化(4)时，列表数据还是'迁移中'，不需要更新
    if (status !== 4) {
      setData([
        ...data.slice(0, index),
        { ...data[index], status },
        ...data.slice(index + 1),
      ]);
    }
  };

  return (
    <Modal
      visible={visible}
      onCancel={onClose}
      closable={false}
      maskClosable={false}
      footer={null}
      wrapClassName={cx('importProcess')}
      bodyStyle={{ padding: '24px', height: '600px' }}
      classNames={{ content: cx('modal-content') }}
      width={800}
      style={{
        borderRadius: '8px',
        overflow: 'hidden',
        padding: 0,
        minWidth: '800px',
      }}
    >
      <ModalWrapper
        title={'Wiki空间迁移进度列表'}
        onCancel={onClose}
        style={{ height: '100%' }}
        showFeedback={true}
      >
        <div className={cx('content')}>
          {
            data.length > 0 ?
              <div className={cx('list')}>
                <div className={cx('list-header')}>
                  <div className={cx('wikiSpaceName-header', 'wikiSpaceName-base', 'ellipsis')}>Wiki空间</div>
                  <div className={cx('transferLocation-header', 'transferLocation-base', 'ellipsis')}>迁移后的知识库</div>
                  <div className={cx('transferResult-header', 'transferResult-base', 'ellipsis')}>迁移状态</div>
                  <div className={cx('operation-header', 'operation-base', 'ellipsis')}>操作</div>
                </div>
                <div className={cx('list-body')}>
                  <InfiniteScroll
                    initialLoad={false}
                    pageStart={0}
                    loadMore={getListData}
                    hasMore={hasMore}
                    useWindow={false}
                    loader={<div key={0} style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}><Spin /></div>}
                  >
                    {
                      data.map(({ taskId, spaceName, spaceLink, wikiName, wikiLink, status }) => (
                        <div
                          key={taskId}
                          className={cx('list-item')}
                        >
                          <div className={cx('wikiSpaceName-item', 'wikiSpaceName-base', 'ellipsis')}>
                            <a href={wikiLink} target="_blank">{wikiName}</a>
                          </div>
                          <div className={cx('transferLocation-item', 'transferLocation-base', 'ellipsis')}>
                            <a href={spaceLink} target="_blank">{spaceName}</a>
                          </div>
                          <div
                            className={cx('transferResult-item', 'transferResult-base', 'ellipsis')}
                            style={{ color: statusDesc[status].color }}
                          >
                            {statusDesc[status].text}
                          </div>
                          <div
                            className={cx('operation-item', 'operation-base', 'ellipsis')}
                            onClick={() => gotoDetail(taskId, status)}
                          >
                            查看
                          </div>
                        </div>
                      ))
                    }
                  </InfiniteScroll>
                </div>
              </div> :
              <div className={cx('fallback-wrapper')}>
                {
                  pending
                    ? <Spin /> :
                    <>
                      <img src={noTransferTask} />
                      <span>暂无任务</span>
                    </>
                }
              </div>
          }
        </div>
      </ModalWrapper>
      {detailModalVisible && <DetailModal onClose={onCloseDetailModal} taskId={taskId} />}
    </Modal>
  )
}

export default TransferList;
