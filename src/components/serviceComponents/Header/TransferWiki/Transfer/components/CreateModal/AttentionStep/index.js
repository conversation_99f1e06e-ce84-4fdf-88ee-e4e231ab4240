/* eslint-disable react/jsx-max-props-per-line */
import styles from './style.module.less';
import classBind from 'classnames/bind';
import { Checkbox } from 'antd';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

const cx = classBind.bind(styles);

const AttentionStep = ({ check, data: { checked }, setData }) => {
  const { cooperLinkConf } = useSelector((state) => state.GlobalData);

  const onChange = (e) => {
    setData((v) => ({ ...v, checked: e.target.checked }));
  }

  useEffect(() => {
    check(checked);
  }, [checked]);

  return (
    <div className={cx('container')}>
      <div className={`${cx('desc')}`}>
        <div className={cx('label')}>
          <Checkbox
            defaultChecked={checked}
            onChange={onChange}
          >
            我已知晓以下注意事项
          </Checkbox>
        </div>
        <div className='custom-rich-text'>
          <ul className='text-sm text-color-para gap-6'>
            <li>
              一旦开始迁移，原Wiki空间将转为「只读」状态，所有空间成员只能查看不能编辑空间内的页面；
            </li>
            <li>
              空间迁移完成前，请勿对新知识库进行操作（如修改权限、增加/删除页面、编辑页面等），否则会导致迁移失败；
            </li>
            <li>
              空间迁移完成后，页面样式跟从Wiki导出html保持一致，因Wiki系统导出能力限制，存在部分内容丢失情况，请注意检查核实，
              <a href={cooperLinkConf.wiki_task_content_loss} target="_blank" className='underline'>详见说明</a>；
            </li>
            <li>
              Wiki和知识库非同一产品，在产品功能设计上有所差异，因此知识库在产品功能、页面布局、协作权限等内容上无法实现 100% 还原。编辑迁移到知识库的页面时存在部分内容和样式丢失的情况，请注意检查核实，
              <a href={cooperLinkConf.wiki_task_content_incompatible} target="_blank" className='underline'>详见说明</a>。
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default AttentionStep;
