import UrlStep from './UrlStep';
import RangeStep from './RangeStep';
import AttentionStep from './AttentionStep';
import Pageowner from './Pageowner';
import MemberStep from './MemberStep';
import TargetPosition from './TargetPosition';
import NotificationStatus from '@/constants/notification';
import { wikiTaskCheck, wikiTaskSubnodeCheck, wikiTaskSubnodeCreatOwner } from '@/service/knowledge/wikiTransfer';

export default [
  {
    title: '粘贴迁移网址',
    component: UrlStep,
    beforeNext: async ({ url }) => {
      const res = await wikiTaskCheck({
        wikiUrl: url,
      });
      if (!res.wikiUrlValid) {
        return {
          canNext: false,
          notification: {
            status: NotificationStatus.ERROR,
            msg: '该空间链接无效，可定位到空间内具体页面后重新复制粘贴',
          },
        }
      }
      if (!res.wikiSpaceAdmin) {
        return {
          canNext: false,
          notification: {
            status: NotificationStatus.ERROR,
            msg: '你无权迁移，请联系Wiki空间管理员为你设置“管理员”和“导出”权限后重试',
          },
        }
      }
      // if (res.taskResubmit) {
      //   return {
      //     canNext: false,
      //     notification: {
      //       status: NotificationStatus.WARN,
      //       msg: '已新建该空间的迁移任务，不得重复提交',
      //     },
      //   }
      // }
      return {
        canNext: true,
        wikiSpace: {
          name: res.wikiSpaceName,
          url: res.wikiSpaceUrl,
          taskResubmit: res.taskResubmit
        },
      };
    },
  },
  {
    title: '确认成员及权限',
    component: MemberStep,
  },
  {
    title: '选择迁移范围',
    component: RangeStep,
    beforeNext: async ({ urlrange, member }) => {
      const res = await wikiTaskSubnodeCheck({
        wikiUrl: urlrange,
        includeSpacePerm: member,
      });
      if (!res.wikiUrlValid) {
        return {
          canNext: false,
          notification: {
            status: NotificationStatus.ERROR,
            msg: '该空间链接无效，可定位到空间内具体页面后重新复制粘贴',
          },
        }
      }
      if (!res.wikiSpaceAdmin) {
        return {
          canNext: false,
          notification: {
            status: NotificationStatus.ERROR,
            msg: '你无权迁移，请联系Wiki空间管理员为你设置“管理员”和“导出”权限后重试',
          },
        }
      }
      if (res.taskResubmit) {
        return {
          canNext: false,
          notification: {
            status: NotificationStatus.WARN,
            msg: '已新建该空间的迁移任务，不得重复提交',
          },
        }
      }
      return {
        canNext: true,
        // wikiSpace: {
        //   name: res.wikiSpaceName,
        //   url: res.wikiSpaceUrl,
        // },
      };
    },
  },
  {
    title: '选择目标位置',
    component: TargetPosition,
  },
  {
    title: '指定页面所有者',
    component: Pageowner,
      beforeNext: async ({ userlap, knowledgeId }) => {
      const res = await wikiTaskSubnodeCreatOwner({
        owner: userlap,
        knowledgeId: knowledgeId
      });
      if (!res) {
        return {
          canNext: false,
          notification: {
            status: NotificationStatus.ERROR,
            msg: '未找到该用户',
          },
        }
      }
      if (!res.ownerValid) {
        return {
          canNext: false,
          notification: {
            status: NotificationStatus.ERROR,
            msg: res.detail,
          },
        }
      }
      return {
        canNext: true,
      };
    },
  },
  {
    title: '明确注意事项',
    component: AttentionStep,
  },
];
