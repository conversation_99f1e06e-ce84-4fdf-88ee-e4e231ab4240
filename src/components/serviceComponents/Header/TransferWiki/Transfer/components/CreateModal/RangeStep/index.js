/* eslint-disable react/jsx-max-props-per-line */
/* eslint-disable operator-linebreak */
import styles from './style.module.less';
import classBind from 'classnames/bind';
import { Radio } from 'antd';
import { useEffect, useState, useCallback} from 'react';
import { useSelector } from 'react-redux';

const cx = classBind.bind(styles);
const urlPattern = /^https?:\/\/.+/;

const RangeStep = ({ check, data: { rangValue, urlrange }, setData }) => {
  const checkUrlValid = useCallback((v) => {
    return urlPattern.test(v);
  }, []);

  const handChange = useCallback((e) => {
    const { value } = e.target;
    setData((v) => ({ ...v, urlrange: value }));
    const isValid = checkUrlValid(value);
    check(isValid);
  }, [check]);

  const onChange = (e) => {
    const { value } = e.target;
    const isAllContent = value === '全部空间内容';
    setData((v) => ({ 
      ...v, 
      rangValue: isAllContent,
      urlrange: isAllContent ? '' : v.urlrange
    }));
  }

  useEffect(() => {
     const isValid = checkUrlValid(urlrange);
     check(rangValue || isValid);
  }, [rangValue, urlrange]);

  return (
    <div className={cx('container')}>
      <div className={cx('label')}>
        选择迁移内容范围
      </div>
      <div className={cx('radio')}>
        <Radio.Group
          defaultValue={rangValue ? '全部空间内容' : '部分空间内容'}
          onChange={onChange}
        >
          <Radio
            value='全部空间内容'
            style={{ position: 'relative' }}
          >
            全部空间内容
          </Radio>
          <Radio
            value='部分空间内容'
            style={{ position: 'relative' }}
          >
            部分空间内容
          </Radio>
        </Radio.Group>
      </div>
      {!rangValue && <div className={cx('urlIpt')}>
        <span className={cx('label')}>输入您要迁移的wiki空间节点链接，或复制链接粘贴至当前输入框</span>
        <div className={cx('ipt')}>
          <input
            autoFocus
            placeholder='粘贴您要迁移的wiki空间节点链接'
            value={urlrange}
            onChange={handChange}
          />
        </div>
      </div>}
    </div>
  )
}

export default RangeStep;
