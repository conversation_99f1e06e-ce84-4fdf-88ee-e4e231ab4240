.importCreate {
  .steps {
    padding: 24px 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
  }

  .steps-hidden {
    padding: 0 !important;
    margin-bottom: 16px;
  }

  .content {
    
  }

  .space {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .label {
      font-weight: 500;
    }

    .content {
      height: 48px;
      display: flex;
      border: 1px solid #e8e9ea;
      border-radius: 4px;
      align-items: center;
      
      .icon-wrapper {
        background-color: #e8f2fe;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 14px;

        &>img {
          width: 20px;
          height: 20px;
        }
      }

      .url-wrapper {
        padding: 0 10px;
        cursor: pointer;
        flex: 1;
        height: 100%;
        transition: all .2s;
        display: flex;
        align-items: center;
        overflow: hidden;

        &>div {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &:hover {
          background-color: #F4F6F8;
        }
      }
    }
  }

  .stepController {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 30px;

    .pre {
      border: 1px solid #EEEEEF;

      &:hover {
        background: #F2F3F3;
      }
    }
  }
}