/* eslint-disable react/jsx-max-props-per-line */
/* eslint-disable operator-linebreak */
import { Modal, Button } from 'antd';
import { useCallback, useMemo, useRef, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './style.module.less';
import classBind from 'classnames/bind';
import '../../text.less';
import wikiIcon from '@/assets/icon/wikiIcon.png';
import steps from './steps';
import useNotification from '@/hooks/useNotification';
import { wikiTaskCreate, wikiTaskSubnode } from '@/service/knowledge/wikiTransfer';
import ModalWrapper from '../ModalWrapper';
import { getUserNameFromCookie } from '@/utils/cooperutils';
import Steps from '../Steps';
import NotificationStatus from '@/constants/notification';

const cx = classBind.bind(styles);
const STEP_INDEXES = {
  RANGE_STEP: 2,      // 选择迁移范围
  TARGET_STEP: 3,     // 选择目标位置
  PAGE_OWNER_STEP: 4, // 指定页面所有者
};
const TransferCreate = ({ onClose, onComplete }) => {
  const notification = useNotification();

  const stepControllerRef = useRef(null);
 
  const [data, setData] = useState({
    url: '',
    member: true,
    checked: false,
    rangValue: true,// 全部空间内容
    urlrange: '',
    targetValue: true,// 新建同名知识库并迁移
    urlTarget: '',
    userlap: '',
    pageValue: true,//当前用户（我）
    calculatedCapacity: '待计算',
    isUrlValidated: false,
    knowledgeId: null,
    taskId: null
  })

  const [pending, setPending] = useState(false);

  const [wikiSpace, setWikiSpace] = useState({ name: '', url: '',taskResubmit: false });
  
  const onStepFinish = ({ url, member, userlap, knowledgeId, rangValue, targetValue, pageValue, urlrange, taskId}) => {
    setPending(true);
  // 提取公共参数
  const commonParams = {
    wikiUrl: rangValue ? url : urlrange,
    includeSpacePerm: member,
  };

  // 判断是否所有选项都是默认值（全部空间内容 + 新建同名知识库并迁移 + 当前用户）,走线上逻辑
  const isAllDefault = rangValue && targetValue && pageValue;

  const params = isAllDefault
    ? commonParams
    : {
        ...commonParams,
        owner: pageValue ? getUserNameFromCookie() : userlap,
        knowledgeId: knowledgeId,
        subNodeImportType: rangValue ? 'space': 'node',
        taskId: taskId
      };

  const executeTask = isAllDefault ? wikiTaskCreate : wikiTaskSubnode;
 
  // 执行任务
  return executeTask(params)
    .then((res) => {
          onComplete(res);
        })
    .finally(() => setPending(false));
};

  const [step, setStep] = useState(0);
  const [canNext, setCanNext] = useState(false);
  const toNext = useCallback(async () => {
    if (!canNext) return;
    const { beforeNext } = steps[step];
    // 第三步特殊检查：如果选择全部空间内容且taskResubmit为true，则阻止进入下一步
    if (step === STEP_INDEXES.RANGE_STEP && data.rangValue && wikiSpace.taskResubmit === true) {
      notification(NotificationStatus.ERROR, '已新建该空间的迁移任务，不得重复提交');
      blurStepBtns();
      return;
    }
    
    // 检查是否需要跳过 beforeNext 校验
    const shouldSkipValidation = (
       // 第3步：选择"全部空间内容"时跳过校验
      (step === STEP_INDEXES.RANGE_STEP && data.rangValue) ||
      // 第4步：选择"新建同名知识库并迁移"时跳过校验
      (step === STEP_INDEXES.TARGET_STEP && data.targetValue) ||
      // 第5步：选择"当前用户（我）"时跳过校验
      (step === STEP_INDEXES.PAGE_OWNER_STEP && data.pageValue)
    );
    if (shouldSkipValidation) {
      // 跳过校验，直接进入下一步
      if (step < steps.length - 1) {
        setStep(step + 1);
      } else {
        onStepFinish(data);
      }
      return;
    }
    
    if (beforeNext) {
      setPending(true);
      const res = await beforeNext(data);
      setPending(false);
      if (!res.canNext) {
        const { notification: { status, msg } } = res;
        notification(status, msg);
        blurStepBtns();
        return;
      }
      res.wikiSpace && setWikiSpace(res.wikiSpace);
    }

    if (step < steps.length - 1) {
      setStep(step + 1);
    } else {
      onStepFinish(data);
    }
  }, [step, canNext, data, wikiSpace.taskResubmit]);

  const toPre = useCallback(() => {
    step > 0 && setStep(step - 1);
  }, [step]);

  const content = useMemo(() => {
    const s = steps[step];
    return (
      <s.component
        check={setCanNext}
        data={data}
        setData={setData}
      />
    )
  }, [step, data]);

  const blurStepBtns = useCallback(() => {
    if (stepControllerRef.current) {
      const btns = stepControllerRef.current.children;
      for (let i = 0; i < btns.length; i++) {
        btns.item(i).blur && btns.item(i).blur();
      }
    }
  }, []);

  useEffect(() => {
    blurStepBtns();
  }, [step]);

  return (
    <Modal
      visible={true}
      onCancel={onClose}
      closable={false}
      maskClosable={false}
      footer={null}
      wrapClassName={cx('importCreate')}
      bodyStyle={{ padding: '24px' }}
      classNames={{ content: cx('modal-content') }}
      width={580}
      style={{
        borderRadius: '8px',
        overflow: 'hidden',
        padding: 0,
        minWidth: '500px',
      }}
    >
      <ModalWrapper
        title={'迁移Wiki空间'}
        onCancel={onClose}
      >
        <div className={cx('steps', { 'steps-hidden': step === 0 })}>
          {step > 0 && (
            <Steps
              titles={steps.slice(1).map(({ title }) => title)}
              step={step - 1}
            />
          )}
        </div>
        {
          step > 0 &&
            <div className={cx('space')}>
              <div className={cx('label')}>待迁移空间</div>
              <div className={cx('content')}>
                <div className={cx('icon-wrapper')}>
                  <img src={wikiIcon} />
                </div>
                <div
                  className={cx('url-wrapper')}
                  onClick={() => window.open(wikiSpace.url, '_blank')}
                >
                  <div>{wikiSpace.name}</div>
                </div>
              </div>
            </div>
        }
        <div className={cx('content')}>
          {content}
        </div>
        <div className={cx('stepController')} ref={stepControllerRef}>
          {
            step !== 0 &&
              <Button
                onClick={toPre}
                type='text'
                className={cx('pre')}
              >
                上一步
              </Button>
          }
          <Button
            type="primary"
            loading={pending}
            onClick={toNext}
            disabled={!canNext}
            className={cx('next')}
          >
            {step < steps.length - 1 ? '下一步' : '开始迁移'}
          </Button>
        </div>
      </ModalWrapper>
    </Modal>
  )
}

export default TransferCreate;
