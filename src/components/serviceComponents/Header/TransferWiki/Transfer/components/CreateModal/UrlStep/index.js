/* eslint-disable react/jsx-max-props-per-line */
import styles from './style.module.less';
import classBind from 'classnames/bind';
import { useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';

const cx = classBind.bind(styles);

const urlPattern = /^https?:\/\/.+/;

const UrlStep = ({ check, data: { url }, setData }) => {
  const { cooperLinkConf } = useSelector((state) => state.GlobalData);
  const checkUrlValid = useCallback((v) => {
    return urlPattern.test(v);
  }, []);

  const onChange = useCallback((e) => {
    const { value } = e.target;
    setData((v) => ({ ...v, url: value }));
    const isValid = checkUrlValid(value);
    check(isValid);
  }, [check]);

  useEffect(() => {
    const isValid = checkUrlValid(url);
    check(isValid);
  }, []);

  return (
    <div className={cx('container')}>
      <div className={cx('urlIpt')}>
        <span className={cx('label')}>打开需要迁移的Wiki空间，复制页面网址并粘贴至此</span>
        <div className={cx('ipt')}>
          <input
            autoFocus
            placeholder='Wiki空间网址：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=……'
            defaultValue={url}
            onChange={onChange}
          />
        </div>
      </div>
      <div className={cx('tip')}>
        <div className={cx('icon')}>
          <i className='dk-iconfont dk-icon-tishi-01' />
        </div>
        <div className='custom-rich-text'>
          <p className='text-sm'>
            只能迁移「你是空间管理员且有导出权限」的Wiki空间<br />
            <a href={cooperLinkConf.wiki_task_auth_check} target="_blank" className='underline'>如何辨别是否具备权限？</a>
          </p>
        </div>
      </div>
    </div>
  )
}

export default UrlStep;
