/* eslint-disable operator-linebreak */
/* eslint-disable react/jsx-max-props-per-line */
import { useEffect, useRef } from 'react';
import styles from './style.module.less';
import classBind from 'classnames/bind';

const cx = classBind.bind(styles);

const Steps = ({ titles, step, hideFirstLine = false }) => {
  const wrapperRef = useRef(null);
  const titleRefs = useRef([]);

  useEffect(() => {
    const wrapper = wrapperRef.current;
    if (wrapper !== null) {
      const h = titleRefs.current.reduce((v, el) => Math.max(v, el.clientHeight), 0);
      wrapper.style.setProperty('padding-bottom', `${h + 8}px`);
    }
  }, []);

  return (
    <div className={cx('wrapper')} ref={wrapperRef}>
      {
        titles.map((title, index) => (
          <div key={index} className={cx('item', index === step ? 'process' : (index < step ? 'finish' : 'wait'))}>
            <div className={cx('display')}>
              {
                index < step ?
                  <i className='dk-iconfont dk-icon-gouxuan-01 icon' /> :
                  <span className={cx('stepNum')}>{index + 1}</span>
              }
              {index < titles.length - 1 && !(hideFirstLine && index === 0) && <div className={cx('line')} />}
            </div>
            <div className={cx('title')} ref={(el) => titleRefs.current.push(el)}>{title}</div>
          </div>
        ))
      }
    </div>
  )
}

export default Steps;
