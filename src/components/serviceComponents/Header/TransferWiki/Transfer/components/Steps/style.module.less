.wrapper {
  display: flex;
  gap: calc(60px + 12px * 2);
  width: fit-content;

  * {
    transition: all .2s;
  }

  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    position: relative;

    .display {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;

      .line {
        position: absolute;
        width: 60px;
        height: 0;
        border-bottom: 1px solid #e4e5e6;
        right: -12px;
        top: 50%;
        transform: translate(100%, -50%);
      }

      &>i {
        font-size: 16px;
        color: #047FFE;
      }

      .stepNum {
        font-size: 14px;
      }
    }

    .title {
      font-size: 12px;
      font-weight: normal;
      color: #656A72;
      position: absolute;
      left: 50%;
      bottom: -8px;
      transform: translate(-50%, 100%);
      white-space: nowrap;
    }


    &.process {
      .display {
        background-color: #047FFE;

        .stepNum {
          font-weight: 500;
          color: #ffffff;
        }
      }
      
      .title {
        font-weight: 500;
        color: #222A35;
      }
    }

    &.finish {
      .display {
        background-color: rgba(4, 127, 254, 0.1);

        .line {
          border-bottom: 1px solid rgba(4, 127, 254, 0.4);
        }
      }
    }

    &.wait {
      .display {
        background-color: #F2F3F3;
        
        .stepNum {
          color: #656A72;
        }
      }
    }
  }
}
