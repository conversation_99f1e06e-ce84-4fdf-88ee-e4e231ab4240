.content {
  padding-bottom: 12px;
  position: relative;

  .pending-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
    background-color: #fff;
  }

  .info {
    margin-bottom: 20px;
    background-color: #f4f6f8;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    padding: 16px;
    gap: 20px;

    .left {

      img {
        width: 125px;
        height: 104px;
      }
    }

    .right {
      display: flex;
      flex-direction: column;
      gap: 18px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
      padding: 4px 0;
    }
  }

  .status {
    margin-bottom: 34px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;

    .label {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 16px;
      font-weight: 700;
      line-height: 1.2em;

      .icon {
        display: flex;
        justify-content: center;
        align-items: center;

        &>i {
          font-size: 20px
        }
      }
    }

    .desc {
      text-align: center;
    }
  }

  .progress {
    display: flex;
    flex-direction: column;
    gap: 14px;

    .bar {
      height: 3px;
      margin-top: 13px;

      :global(.ant-progress-bg) {
        height: 3px !important;
      }

      :global(.ant-progress) {
        display: flex;
      }

      :global(.ant-progress-outer) {
        display: flex;
      }
    }
  }
  .number_ {
    display: flex;
    justify-content: space-around;
    font-family: PingFang SC;
    .pagecount {

      font-size: 12px;
      color: #909499;
      text-align: center;
       .count {
        font-size: 14px;
        font-weight: 600;
        color: #4E555D;
       }
       .count_ {
        font-size: 14px;
        font-weight: 600;
        color: #FF563B;
       }
    }
  }
}
