/* eslint-disable react/jsx-max-props-per-line */
/* eslint-disable operator-linebreak */
/* eslint-disable no-mixed-operators */
import styles from './style.module.less';
import classBind from 'classnames/bind';
import { useState, useEffect, useMemo, useContext, useRef, useCallback } from 'react';
import { Progress, Tooltip, Modal, Spin } from 'antd';
import wiki2knowledgeForge from '@/assets/icon/wiki2knowledgeForge.png';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import '../../text.less';
import { wikiTaskProgress } from '@/service/knowledge/wikiTransfer';
import useNotification from '@/hooks/useNotification';
import NotificationStatus from '@/constants/notification';
import { useSelector } from 'react-redux';
import ModalWrapper from '../ModalWrapper';

const cx = classBind.bind(styles);

export const Status = {
  Queue: 0,
  Active: 1,
  Success: 2,
  Interrupted: 3,
  Waiting: 4, // 全都迁移完成但是状态非 Success
};
const statusDesc = [
  { /* Queue */
    icon: <i className='dk-iconfont dk-icon-qianyizhong' style={{ color: '#4f555c' }} />,
    text: '排队等待中，请稍等',
  },
  { /* Active */
    icon: <i className='dk-iconfont dk-icon-qianyizhong' style={{ color: '#4f555c' }} />,
    text: '迁移中',
  },
  { /* Success */
    icon: <i className='dk-iconfont dk-icon-wancheng' style={{ color: '#65caa6' }} />,
    text: '迁移成功',
    desc: (spacePageProgress, includeSpacePerm, spacePermProgress) => (includeSpacePerm ?
      `已完成${spacePageProgress.totalCount}个页面迁移，${spacePermProgress.totalCount}个成员及权限迁移` :
      `已完成${spacePageProgress.totalCount}个页面迁移`),
  },
  // { /* Interrupted */
  //   icon: <i className='dk-iconfont dk-icon-tishi-01' style={{ color: '#f2a93e' }} />,
  //   text: '迁移中断',
  //   desc: (spacePageProgress, includeSpacePerm, spacePermProgress) => (
  //     includeSpacePerm ?
  //       (
  //         spacePageProgress.finishCount < spacePageProgress.totalCount ?
  //           `已完成${spacePageProgress.finishCount}个页面迁移，剩余${spacePageProgress.totalCount - spacePageProgress.finishCount}个页面、全部空间成员及权限均未迁移。` :
  //           `已完成${spacePageProgress.finishCount}个页面、${spacePermProgress.finishCount}个成员及权限迁移，剩余${spacePermProgress.totalCount - spacePermProgress.finishCount}个成员及权限未迁移。`
  //       ) :
  //       `已完成${spacePageProgress.finishCount}个页面迁移，剩余${spacePageProgress.totalCount - spacePageProgress.finishCount}个页面未迁移。`
  //   ),
  // },
  {
    icon: <i className='dk-iconfont dk-icon-tishi-01' style={{ color: '#f2a93e' }} />,
    text: '迁移任务完成，部分页面迁移失败',
  },
  { /* Waiting */
    icon: <i className='dk-iconfont dk-icon-qianyizhong' style={{ color: '#4f555c' }} />,
    text: '空间初始化中',
    desc: (spacePageProgress, includeSpacePerm, spacePermProgress) => `当前空间页面${includeSpacePerm ? '及成员权限' : ''}已完成迁移，正在处理迁移数据整理，请等待迁移成功后在知识库中进行查询`,
  },
]

const getProgressPercentage = ({ finishCount, totalCount }) => {
  if (totalCount === 0) return 0;
  return Math.min(100, Math.floor(finishCount / totalCount * 100));
}

const progressHasFinished = ({ finishCount, totalCount }) => totalCount > 0 && finishCount >= totalCount;

const transferItemQueueText = '未迁移，排队等待中...';
const pageActiveText = (progress) => `已完成${progress.finishCount}（总页面量：${progress.totalCount}）个页面迁移，进度${getProgressPercentage(progress)}%`;
const permActiveText = (progress) => `已完成${progress.finishCount}（总成员量：${progress.totalCount}）个成员迁移，进度${getProgressPercentage(progress)}%`;

const ItemStatus = {
  Active: 0,
  Success: 1,
  Exception: 2,
};

const progressColor = [
  /* Active */ 'linear-gradient(90deg, #047FFE 82%, #04BFFE 100%)',
  /* Success */ 'linear-gradient(90deg, #2ECDA4 82%, #1AEEB8 100%)',
  /* Exception */ 'linear-gradient(90deg, #FF563B 82%, #FFA03B 100%)',
]

const POLLING_INTERVAL = 10 * 1000; // 轮询间隔

const TransferDetail = ({ onClose, taskId }) => {
  const { cooperLinkConf } = useSelector((state) => state.GlobalData);

  const notification = useNotification();

  const [status, setStatus] = useState(Status.Active); // active, queue, success, interrupted
  const [spacePageProgress, setSpacePageProgress] = useState({
    totalCount: 0,
    finishCount: 0,
  });
  const spacePageStatus = useMemo(() => {
    if (progressHasFinished(spacePageProgress) || status === Status.Success) return ItemStatus.Success;
    if (status === Status.Interrupted) return ItemStatus.Exception;
    return ItemStatus.Active;
  }, [spacePageProgress, status]);
  const [includeSpacePerm, setIncludeSpacePerm] = useState(true);
  const [spacePermProgress, setSpacePermProgress] = useState({
    totalCount: 0,
    finishCount: 0,
  });
  const spacePermStatus = useMemo(() => {
    if (progressHasFinished(spacePermProgress) || status === Status.Success) return ItemStatus.Success;
    if (status === Status.Interrupted) return ItemStatus.Exception;
    return ItemStatus.Active;
  }, [spacePermProgress, status]);

  const [wikiSpace, setWikiSpace] = useState({ name: '', url: '' });
  const [space, setSpace] = useState({ name: '', url: '' });

  const pollingInterval = useRef(null);

  const clearPolling = useCallback(() => {
    pollingInterval.current && clearInterval(pollingInterval.current);
    pollingInterval.current = null;
  }, [])

  useEffect(() => {
    if (status === Status.Interrupted || status === Status.Success) {
      clearPolling();
    }
  }, [status]);

  const doPolling = useCallback(async () => {
    try {
      const res = await wikiTaskProgress({ taskId });
      if (res !== undefined) {
        setWikiSpace({ name: res.wikiSpaceName, url: res.wikiSpaceUrl });
        setSpace({ name: res.spaceName, url: res.spaceUrl });
        setStatus(res.taskStatus);
        setIncludeSpacePerm(res.includeSpacePerm);
        setSpacePageProgress(res.spacePageProgress);
        if (res.includeSpacePerm) {
          setSpacePermProgress(res.spacePermProgress);
        }
        if (res.taskId === '') { // 可能为用户自己输入的任意id导致返回无效数据
          clearPolling();
          notification(NotificationStatus.ERROR);
        }

        // 「空间初始化」状态切换逻辑
        if (
          // 当前状态为「迁移中」并且进度都已经完成，则切换为「空间初始化」状态（Status.Waiting）
          res.taskStatus === Status.Active &&
          progressHasFinished(res.spacePageProgress) &&
          (!res.includeSpacePerm || res.includeSpacePerm && progressHasFinished(res.spacePermProgress))
        ) {
          setStatus(Status.Waiting);
        }

        setPending(false);
      } else {
        setStatus(Status.Interrupted);
        notification(NotificationStatus.ERROR);
      }
    } catch (error) {
      setStatus(Status.Interrupted);
      notification(NotificationStatus.ERROR);
    }
  }, [taskId]);

  const [pending, setPending] = useState(false);
  // 每 10 秒轮询进度
  useEffect(() => {
    setPending(true);
    doPolling();

    pollingInterval.current = setInterval(() => {
      doPolling();
    }, POLLING_INTERVAL);

    return () => {
      clearPolling();
    }
  }, [doPolling]);
  
  const renderFailStats = () => (
    <div className={cx('number_')}>
      <div className={cx('pagecount')}>
        <div>总页面量</div>
        <p className={cx('count')}>{spacePageProgress.totalCount}</p>
      </div>
      <div className={cx('pagecount')}>
        <div>成功数量</div>
        <p className={cx('count')}>{spacePageProgress.finishCount}</p>
      </div>
      <div className={cx('pagecount')}>
        <div>失败数量</div>
        <p className={cx('count_')}>{spacePageProgress.totalCount - spacePageProgress.finishCount}</p>
      </div>
    </div>
  );

  return (
    <Modal
      visible={true}
      onCancel={() => onClose({ id: taskId, status })}
      closable={false}
      maskClosable={false}
      footer={null}
      wrapClassName={cx('importCreate')}
      bodyStyle={{ padding: '24px' }}
      classNames={{ content: cx('modal-content') }}
      width={500}
      style={{
        borderRadius: '8px',
        overflow: 'hidden',
        padding: 0,
        minWidth: '500px',
      }}
    >
      <ModalWrapper
        title={'迁移Wiki空间'}
        onCancel={() => onClose({ id: taskId, status })}
      >
        <div className={cx('content')}>
          {
            pending &&
            <div className={cx('pending-wrapper')}>
              <Spin />
            </div>
          }
          <div className={cx('info')}>
            <div className={cx('left')}>
              <img src={wiki2knowledgeForge} />
            </div>
            <div className={cx('right')}>
              <div className='custom-rich-text'>
                <p>Wiki空间：</p>
                <p>
                  <Tooltip title={wikiSpace.name}>
                    <a href={wikiSpace.url} target="_blank" className='ellipsis'>{wikiSpace.name}</a>
                  </Tooltip>
                </p>
              </div>
              <div className='custom-rich-text'>
                <p>迁至知识库：</p>
                <p>
                  <Tooltip title={space.name}>
                    <a href={space.url} target="_blank" className='ellipsis'>{space.name}</a>
                  </Tooltip>
                </p>
              </div>
            </div>
          </div>
          {status && Number(status) === 3 ?
            <div>
              <div className={cx('status')}>
                <div className={cx('label')}>
                  <div className={cx('icon')}>{statusDesc[status].icon}</div>
                  <div className={cx('text')}>{statusDesc[status].text}</div>
                </div>
                {
                  statusDesc[status] &&
                  <div className={`${cx('desc')} custom-rich-text`}>
                    <p>
                      如需修复，请 <nbsp></nbsp>
                      {/* {status === Status.Interrupted && <a href='https://im.xiaojukeji.com/channel?uid=616708&token=7a1bc9635c1d1564505f3956cdc2b812&id=1171568080658147328' target="_blank">联系我们</a>} */}
                      <a href='https://im.xiaojukeji.com/channel?uid=616708&token=7a1bc9635c1d1564505f3956cdc2b812&id=1171568080658147328' target="_blank">联系我们</a>
                    </p>
                  </div>
                }
              </div>
              {renderFailStats()}
            </div> :
            <div>
              <div className={cx('status')}>
                <div className={cx('label')}>
                  <div className={cx('icon')}>{statusDesc[status].icon}</div>
                  <div className={cx('text')}>{statusDesc[status].text}</div>
                </div>
                {
                  statusDesc[status].desc &&
                  <div className={`${cx('desc')} custom-rich-text`}>
                    <p>
                      {statusDesc[status].desc(spacePageProgress, includeSpacePerm, spacePermProgress)}
                      {status === Status.Interrupted && <a href='https://im.xiaojukeji.com/channel?uid=616708&token=7a1bc9635c1d1564505f3956cdc2b812&id=1171568080658147328' target="_blank">反馈咨询</a>}
                    </p>
                  </div>
                }
              </div>
              <div className={cx('progress')}>
                <div className={cx('page')}>
                  <div
                    className='custom-rich-text'
                    style={{ fontSize: '12px' }}
                  >
                    <p style={{ color: '#4E555D' }}>
                      <strong>页面迁移：</strong>
                      {
                        spacePageProgress.finishCount > 0 ?
                          pageActiveText(spacePageProgress) :
                          transferItemQueueText
                      }
                    </p>
                  </div>
                  <div className={cx('bar')}>
                    <Progress
                      showInfo={false}
                      size='small'
                      strokeColor={progressColor[spacePageStatus]}
                      percent={getProgressPercentage(spacePageProgress)}
                    />
                  </div>
                </div>
                {
                  includeSpacePerm &&
                  <div className={cx('member')}>
                    <div
                      className='custom-rich-text'
                      style={{ fontSize: '12px' }}
                    >
                      <p className='mb-2' style={{ color: '#4E555D' }}>
                        <strong>成员及权限迁移：</strong>
                        {
                          spacePermProgress.finishCount > 0 ?
                            permActiveText(spacePermProgress) :
                            transferItemQueueText
                        }
                      </p>
                      <p style={{ color: '#909499' }}>
                        全部用户迁移到知识库后，离职用户会被移出知识库
                      </p>
                    </div>
                    <div className={cx('bar')}>
                      <Progress
                        showInfo={false}
                        size='small'
                        strokeColor={progressColor[spacePermStatus]}
                        percent={getProgressPercentage(spacePermProgress)}
                      />
                    </div>
                  </div>
                }
              </div>
            </div>
          }
        </div>
      </ModalWrapper>
    </Modal>
  )
}

export default TransferDetail;
