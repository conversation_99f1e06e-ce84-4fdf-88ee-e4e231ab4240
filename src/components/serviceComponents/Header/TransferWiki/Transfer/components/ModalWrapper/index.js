/* eslint-disable operator-linebreak */
/* eslint-disable react/jsx-max-props-per-line */
import styles from './style.module.less';
import classBind from 'classnames/bind';
import { intl } from 'di18n-react';
import TransferFeedback from '@/assets/icon/transferFeedback.svg'

const cx = classBind.bind(styles);

const ModalWrapper = ({ title, onCancel, children, showFeedback = false, ...props }) => {
  return (
    <div className={cx('wrapper')} {...props}>
      <div className={cx('header')}>
        <div className={cx('title')}>
          {title}
        </div>
        <div className={cx('header-content')}>
          {
            showFeedback &&
              <div
                className={cx('feedback')}
                onClick={() => window.open('https://page.xiaojukeji.com/active/ddpage_0ieWPuOF.html', '_blank')}
              >
                <div className={cx('icon')}><img src={TransferFeedback} alt="" /></div>
                <span className={cx('text')}>{intl.t('问卷反馈')}</span>
              </div>
          }
        </div>
        <div
          className={cx('closeIcon')}
          onClick={onCancel}
        >
          <i className='dk-iconfont dk-icon-guanbi1' />
        </div>
      </div>
      {children}
    </div>
  )
}

export default ModalWrapper;
