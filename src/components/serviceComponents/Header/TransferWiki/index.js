

import classBind from 'classnames/bind';
import styles from './style.module.less';
import { Popover } from 'antd';
import wikiIcon from '@/assets/icon/wikiIcon.png';
import { intl } from 'di18n-react';
import TransferCreate from './Transfer/Create';
import TransferList from './Transfer/List';
import { useMemo, useState } from 'react';

const cx = classBind.bind(styles);

const transferDict = {
  create: TransferCreate,
  list: TransferList,
}

const TransferWiki = () => {
  const [process, setProcess] = useState(null); // null | 'create' | 'list';

  const RenderedModal = useMemo(() => {
    if (process === null) return null;
    return transferDict[process];
  }, [process]);

  const onClose = () => {
    setProcess(null);
  }

  return (
    <>
      <Popover
        trigger="hover"
        overlayInnerStyle={{ width: '160px' }}
        overlayClassName={cx('popover-wrapper')}
        destroyTooltipOnHide={true}
        content={
          <ul className={cx('list')}>
            <li
              className={cx('item')}
              onClick={() => setProcess('create')}
            >
              <i className='dk-iconfont dk-icon-daoru4' />
              <span>{intl.t('新建迁移任务')}</span>
            </li>
            <li
              className={cx('item')}
              onClick={() => setProcess('list')}
            >
              <i className='dk-iconfont dk-icon-yemian' />
              <span>{intl.t('查看迁移进度')}</span>
            </li>
            <li
              className={cx('item')}
              onClick={() => window.open('https://im.xiaojukeji.com/channel?uid=616708&token=7a1bc9635c1d1564505f3956cdc2b812&id=1171568080658147328', '_blank')}
            >
              <i className='dk-iconfont dk-icon-kefu-01' />
              <span>{intl.t('反馈咨询')}</span>
            </li>
          </ul>
        }
      >
        <div className={cx('transferDk')}>
          <img
            className={cx('icon')}
            src={wikiIcon}
          />
          <span className={cx('text')}>{intl.t('迁移Wiki空间')}</span>
        </div>
      </Popover>
      {RenderedModal && <RenderedModal onClose={onClose} />}
    </>
  );
};

export default TransferWiki;
