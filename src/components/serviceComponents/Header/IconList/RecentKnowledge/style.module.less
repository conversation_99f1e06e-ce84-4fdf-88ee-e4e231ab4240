.recentKnowledge {
  width: 390px;
  position: relative;

  :global {
    .ant-tabs-nav {
      margin-left: 20px !important;
      margin-right: 20px !important;
      margin-top: 6px !important;
      margin-bottom: 6px !important;
    }
  }

  .toggle-switch {
    position: absolute;
    top: 14px;
    right: 10px;
    z-index: 1;
    display: flex;
    align-items: center;

    &-text {
      margin-left: 6px;
      font-size: 12px;
      font-weight: 400;
      color: #7C7C7C;
    }
  }

  .more {
    position: absolute;
    width: calc(100% + 12px);
    height: 32px;
    line-height: 32px;
    background: #F7F8F9;
    padding-left: 19px;
    font-size: 12px;
    font-weight: 400;
    color: #7D7D7D;
    border-radius: 0px 0px 6px 6px;
    bottom: -6px;
    left: -6px;

    &-click-area {
      cursor: pointer;
    }

    &-icon {
      margin-left: 6px;
      font-size: 12px;
      color: #9E9F9F;
    }
  }
}