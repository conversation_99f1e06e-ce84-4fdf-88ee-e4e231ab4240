import { intl } from 'di18n-react';
import ReactDOM from 'react-dom';
import { inPhone } from '@/utils';
import { DKLogoIcon, gotoDkhomeIcon } from '@/assets/icon';
import classBind from 'classnames/bind';
import styles from './style.module.less';

const cx = classBind.bind(styles);

const isInPhone = inPhone();

const GotoDkhome = () => {
  const getEmptyNode = () => {
    let wrapEla = document.getElementById('dk-editor-bottom-wrap-gotoDkhome');
    if (!wrapEla) {
      const divNode = window.document.createElement('div');
      divNode.id = 'dk-editor-bottom-wrap-gotoDkhome';
      divNode.style = 'display:none';
      window.document.body.appendChild(divNode);
      return divNode;
    }
    return wrapEla;
  };

  return ReactDOM.createPortal(
    <div className={cx('gotoDkhome')}>
      {intl.t('源自')}

      <img
        src={DKLogoIcon}
        className={cx('dk-logo')}
        alt={intl.t('知识库')} />
      {!isInPhone && (
        <>
          {intl.t('，前往使用')}

          <a href="/knowledge">
            <span className={cx('goto-icon-wrap')}>
              <img
                className={cx('goto-icon')}
                src={gotoDkhomeIcon} />
            </span>
          </a>
        </>
      )}
    </div>,
    getEmptyNode(),
  );
};

export default GotoDkhome;
