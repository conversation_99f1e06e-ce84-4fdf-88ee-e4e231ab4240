/*
 * @Author: guanzhong
 * @Date: 2023-11-09 16:00:00
 * @LastEditors: guanzhong
 * @LastEditTime: 2023-11-10 11:11:59
 * @FilePath: /knowledgeforge/src/components/Comment/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useContext, useEffect } from 'react';
// import { CooperParcelUrl } from '@/constants/cooperComponents';
import { useSelector, useDispatch } from 'react-redux';
import ReactDOM from 'react-dom';
import { reportMeasure } from '@/utils/performance';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
// import { MrParcel } from '@/utils/tenon.js';
import CommentContent from '@/components/CommentContent';
import classNames from 'classnames/bind';
import cls from 'classnames';
import { getCookie, parseUrlSearch } from '@/utils';
// eslint-disable-next-line camelcase
import { Share_Type } from '@/constants/page';
import styles from './style.module.less';

const cx = classNames.bind(styles);

// const debugComment = getCookie('debugComment');

function Comment() {
  const dispatch = useDispatch();
  const { pageId, shareId } = useContext(LayoutContext);
  const { openGuide } = useSelector((state) => state.guide);
  const { profile } = useSelector((state) => state.CooperIndex);
  const { setTreeFn } = dispatch.CommentTree;

  useEffect(() => {
    window.performance.mark(`comment-start${pageId}`);
  }, [pageId]);


  const handleClick = () => {
    openGuide && openGuide('1-4-1029-comment');
  };

  const scrollToPoint = () => {
    let msgId = parseUrlSearch('msgId');
    if (msgId) {
      let operateDiv = document.getElementById(msgId);
      operateDiv?.scrollIntoView(true);
    }
  };

  const getEmptyNode = () => {
    let wrapEla = document.getElementById('dk-editor-bottom-wrap');
    if (!wrapEla) {
      const divNode = window.document.createElement('div');
      divNode.id = 'dk-editor-bottom-wrap';
      divNode.style = 'display:none';
      window.document.body.appendChild(divNode);
      return divNode;
    }
    return wrapEla;
  };


  return (
    ReactDOM.createPortal(<>
      <div
        className={cx('comment-content')}
        id={cls('comment-guide')}
        onClick={handleClick}>

        <CommentContent
          pageId={pageId}
          isShare={window.location.href.indexOf('share') !== -1}
          shareId={shareId}
          shareType={window.location.href.indexOf('book') !== -1 ? Share_Type.Dk_Share : Share_Type.Page_Share}
          user={{
            ldap: profile.username,
            name: profile.username_zh,
            avatar: profile.avatar,
          }}
          eventHandler={{
            handleMounted: () => {
              scrollToPoint();
              reportMeasure({
                startName: `comment-start${pageId}`,
                endName: `comment-end${pageId}`,
                measureName: `load-comment${pageId}`,
              });
            },
            initTreeFn: (tree) => {
              setTreeFn({ ...tree }); // 收到长链消息时，调用传过来的tree方法操作commentlist
            },
          }}
        />
      </div>
    </>, getEmptyNode())

  );
}

export default Comment;
