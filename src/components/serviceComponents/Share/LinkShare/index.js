import { Popover, Input, Select, Tooltip } from 'antd';
import classNames from 'classnames/bind';
import { intl } from 'di18n-react';
import { useEffect, useMemo, useState } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import LinkShareSetting from '../LinkShareSetting';
import dclogo from './dchat.svg';
import { createShareLink, modifyShareLink } from '@/service/knowledge/share';
import { createShareLinkCooper, modifyShareLinkCooper } from '@/service/cooper/home';
import { formatDatetimeNew, getUserNameFromCookie } from '@/utils/cooperutils';
import { ROLE_TYPE_DOC } from '../../MemberListOfDK/constants';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import usePermission from '@/hooks/usePermission';
import pathUtil from '@/utils/path';
import { getType, sendFileTypeEvent } from '@/utils/type'
import NewLinkShareSetting from '../NewLinkShareSetting';

import styles from './style.module.less';

const cx = classNames.bind(styles);
const { Option } = Select;

const DisabledTip = (props) => {
  return (
    <Tooltip
      title={props.title}
      placement={props.placement || 'top'}
    >
      {props.children}
    </Tooltip>
  )
}

const LinkShare = (props) => {
  const { resourceId, disabled, linkInfo, refreshLinkInfo, isDkPage, isInDK, info, cooperLinkConf, toggleChildVisible, isModalShare } = props;
  const [ selectedValue, setSelectedValue ] = useState(0);
  const [openSelect,setOpenSelect]= useState(false);
  const [ modalOpen, setModalOpen ] = useState(false);
  const { accessRole, permission, linkSelectType, expire_time, password, expiration, spaceType } = linkInfo;
  const notification = useNotification();
  const { checkOperationPermission } = usePermission();

  const adminPerm = useMemo(() => {
    if (isDkPage) {
      return checkOperationPermission('MANAGE_PAGE_MEMBER', linkInfo.currentUserPerm);
    } else {
      return [ROLE_TYPE_DOC.Owner, ROLE_TYPE_DOC.Admin].includes(linkInfo.currentUserRole);
    }
  }, [linkInfo.currentUserPerm]);

  useEffect(() => {
    setSelectedValue(linkSelectType);
  }, [linkSelectType]);

  const generatePwd = () => {	
    return Math.floor(100000 + Math.random() * 900000);	
  };

  const getOpenDefaultParam = () => {
    if (spaceType === 'IN_OUT') {
      return {
        'resource_id': resourceId,
        'permission': 1,  //权限类型：查看/评论，编辑/下载
        'expiration': 3,  // 1: 永久优先； 2: 1个月； 3: 1周； 4: 1天
        'accessRole': 0,  // 0: 成为文档协作者; 1: 不成为文档协作者
        'access_type': 'Secret',
        password: generatePwd(),
      }
    }

    return {
      'resource_id': resourceId,
      'permission': 1,  //权限类型：查看/评论，编辑/下载
      'expiration': 3,  // 1: 永久优先； 2: 1个月； 3: 1周； 4: 1天
      'accessRole': 0,  // 0: 成为文档协作者; 1: 不成为文档协作者
      'access_type': 'Public',
    }
  }

  const getCloseDefaultParam = () => {
    return {
      'resource_id': resourceId,
      'linkSelectType': 0,
    }
  }

  const linkShareContent = () => {
    return (
      <div className={cx('popover-container')}>
        <span>
          {intl.t('可设置链接分享范围为企业员工可访问，')}
        </span>
        <a href={isDkPage ? cooperLinkConf.link_share_dk : cooperLinkConf.link_share_doc} target='_blank'>
          {intl.t('查看详细说明')}
        </a>
      </div>
    )
  }
  const handleChange = async(value) => {
    // 开启链接分享
    if (value === 1 && !linkInfo?.invite_link_id) {
      (isDkPage ? createShareLink : createShareLinkCooper)(getOpenDefaultParam())
      .then(() => {
        notification(NotificationStatus.SUCCESS, intl.t('修改成功'));
        refreshLinkInfo();
      }).catch(() => {
        notification(NotificationStatus.ERROR, intl.t('修改失败'));
      })
    }

    // 关闭链接分享
    if (value === 0) {
      (isDkPage ? modifyShareLink : modifyShareLinkCooper)({
        ...getCloseDefaultParam(),
        'invite_link_id': linkInfo.invite_link_id
      }).then(() => {
        notification(NotificationStatus.SUCCESS, intl.t('修改成功'));
        refreshLinkInfo();
      }).catch(() => {
        notification(NotificationStatus.ERROR, intl.t('修改失败'));
      });
    }
    const params = {};
    params.scope = value ? 'shared' : 'unshared';
    const isType = [
      'docs',
      'sheets',
      'slides',
      'flowchart',
      'xmind',
      'pages',
    ].includes(getType(info?.fileType));
    params.source = isType ? getType(info.fileType) : '';
    window.__OmegaEvent('ep_share_scope_ck', '', { ...params });
  };

  const getCopyText = () => {
    let copyPath = '';
    let url = location.origin + location.pathname;
    let name = document.title;

    // isInDK 在知识库页面中点击的分享，使用document.title即可，否则需要从接口信息中获取titile
    if (!isInDK) {
      url = pathUtil.getDkPageUrl(info.sourceId , resourceId);
      name = info.fileName || info.objectName;
    }

    if (isDkPage) {
      url = pathUtil.getDkPageUrl(info.sourceId , resourceId);
    }

    // 有密码，使用文件名称、链接、密码的形式
    // 无密码，使用链接#标题的形式
    if (password && adminPerm) {
      if (isDkPage) {
        copyPath = `${intl.t('文件名称')}：${name}\n${intl.t('链接')}：${url}\n${intl.t('密码')}：` + password;
      } else {
        copyPath = `${intl.t('文件名称')}：${name}\n${intl.t('链接')}：${location.origin}${pathUtil.getCoopPath(resourceId, info.mime_type || info.mimeType)}\n${intl.t('密码')}：` + password;
      }
    } else {
      if (isDkPage) {
        copyPath = `${intl.t('文件名称')}：${name}\n${intl.t('链接')}：${url}`;
      } else {
        copyPath = `${intl.t('文件名称')}：${name}\n${intl.t('链接')}：${location.origin}${pathUtil.getCoopPath(resourceId, info.mime_type || info.mimeType)}`;
      }
    }

    return copyPath;
  }

  return (
    <div className={cx({ 'link-share-disabled': disabled , 'link-no-share': selectedValue === 0})}>
      <div className={cx('link-share')}>
        <div className={cx('link-share-title')}>
          <span>{intl.t('链接分享1')}</span>
          <Popover
            trigger='hover'
            placement='top'
            content={linkShareContent}
            zIndex={1001}
            getPopupContainer={(e) => e.parentNode}
            overlayClassName={isModalShare ? 'link-share-tip-placement-top_modal__reset' : 'link-share-tip-placement-top__reset'}
          >
            <i className={cx('dk-iconfont', 'dk-icon-tishi-xianxing', 'icon-tip')} />
          </Popover>
        </div>
        {
          /* 联系所有者按钮 - 优化条件判断逻辑 */
          (!adminPerm && (
            // 如果是知识库页面(isDkPage)，则只有当用户不是所有者时才显示
            !isDkPage || getUserNameFromCookie() !== linkInfo?.ownerLdap?.ldap
          )) && (
            <a
              href={`dchat://im/start_conversation?name=${linkInfo?.ownerLdap?.ldap}&team_id=${linkInfo?.ownerLdap?.organizationId}`}
              className={cx('contact-owner')}
              target='_blank'
              onClick={() => {
                sendFileTypeEvent('ep_share_link_contact_ck', info?.fileType);
              }}
            >
              <img src={dclogo} className={cx('contact-owner-dc')} />
              <span>
                {intl.t('联系所有者')}
              </span>
            </a>
          )
        }
      </div>
      <div className={cx('share-option')}>
        <div 
          className={cx(
            {
              'share-option-left': true,
              'no-share': selectedValue === 0,
              'intern-share': selectedValue === 1
            }
          )}
        >
          {
            selectedValue === 0
              ? <i className='dk-iconfont dk-icon-suo-01 icon-no-share' />
              : <i className='dk-iconfont dk-icon-qiye-01 icon-intern-share' />
          }
        </div>
        <div className={cx('share-option-right')}>
          <div>
            <DisabledTip placement='topLeft' title={disabled ? intl.t('仅文档管理员可修改设置') : ''}>
              <Input.Group compact className={cx('option')}>
                <Select 
                  value={selectedValue}
                  dropdownMatchSelectWidth={false}
                  bordered={false}
                  style={{width: 'auto'}}
                  disabled={disabled}
                  onChange={handleChange}
                  suffixIcon={openSelect ? <i className='dk-iconfont dk-icon-shouqi-01 dk-icon-customize' />: <i className='dk-iconfont dk-icon-zhankai-01 dk-icon-customize' />}
                  onDropdownVisibleChange={(open) => { setOpenSelect(open) }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  optionLabelProp='label'
                >
                  <Option value={0} label={intl.t('未开启分享')}>
                    <div className={cx('dropdown-option')}>
                      <div className={cx('dropdown-option-left')}>
                        <div className={cx('dropdown-option-item')}>{intl.t('未开启分享')}</div>
                        <div className={cx('dropdown-option-item-tip')}>{intl.t('仅文档协作者可通过链接访问，非文档协作者打开链接需要申请权限')}</div>
                      </div>
                      {
                        selectedValue === 0 && (
                          <div className={cx('dropdown-option-right')}>
                            <i className='dk-iconfont dk-icon-gou' />
                          </div>
                        )
                      }
                    </div>
                  </Option>
                  <Option value={1} label={intl.t('企业员工可访问')}>
                    <div className={cx('dropdown-option')}>
                      <div className={cx('dropdown-option-left')}>
                        <div className={cx('dropdown-option-item')}>{intl.t('企业员工可访问')}</div>
                        {
                          password 
                            ? <div className={cx('dropdown-option-item-tip')}>{intl.t('获得链接和密码的人都可访问')}</div>
                            : <div className={cx('dropdown-option-item-tip')}>{intl.t('获得链接的人都可访问')}</div>
                        }
                      </div>
                      {
                        selectedValue === 1 && (
                          <div className={cx('dropdown-option-right')}>
                            <i className='dk-iconfont dk-icon-gou' />
                          </div>
                        )
                      }
                    </div>
                  </Option>
                </Select>
              </Input.Group>
            </DisabledTip>
          </div>
          <div className={cx('option-desc')}>
            {
              selectedValue === 0
                ? intl.t('仅文档协作者可通过链接访问，非文档协作者打开链接需要申请权限')
                : (
                  password ? intl.t('获得链接和密码的人都可访问') : intl.t('获得链接的人都可访问')
                )
            }
          </div>
        </div>
        <CopyToClipboard
          text={getCopyText()}
          onCopy={() => {
            if (password && adminPerm) {
              notification(NotificationStatus.SUCCESS, intl.t('已复制链接和密码'));
            } else {
              notification(NotificationStatus.SUCCESS, intl.t('已复制链接'));
            }
            sendFileTypeEvent('ep_share_link_copylink_ck',info?.fileType);
          }}
        >
          <div className={cx('share-operate-copy')}>
            <i className='dk-iconfont dk-icon-fuzhilianjie3' />
            {
              password ? (
                adminPerm
                  ? intl.t('复制链接和密码') : intl.t('复制文档链接')
              ) : intl.t('复制文档链接')
            }
          </div>
        </CopyToClipboard>
      </div>
      { selectedValue === 1 && ( <NewLinkShareSetting disabled={disabled} isDkPage={isDkPage} linkInfo={linkInfo} refreshLinkInfo={refreshLinkInfo} info={info} adminPerm={adminPerm} /> ) }
   </div>
  )
}

export default LinkShare;
