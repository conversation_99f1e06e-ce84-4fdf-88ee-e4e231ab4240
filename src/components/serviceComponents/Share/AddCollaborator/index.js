import { forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Popover, Input, Button, Tooltip, message } from 'antd';
import classNames from 'classnames/bind';
import { intl } from 'di18n-react';
import { debounce } from 'lodash-es';
// import { BatchAdd } from '@didi/add-member-search-new';
import { batchAddPageMember } from '@/service/knowledge/page';
import { batchAddPageMemberDoc } from '@/service/cooper/home';
import { getPageRole } from '@/utils';
import { PAGE_MEMBER_TYPE } from '@/constants/setup';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import { sendFileTypeEvent } from '@/utils/type'

import styles from './style.module.less';

const cx = classNames.bind(styles);

const DisabledTip = (props) => {
  return (
    <Tooltip
      title={props.title}
      placement='top'
    >
      {props.children}
    </Tooltip>
  )
}

export const EpClickAddMemberBatchAddFns = {
  clickBatchAdd: () => {},
  notify: () => {},
  return: () => {},
  modifyPermission: () => {},
  modify: () => {},
  confirm: () => {},
  copy: () => {},
};


const AddCollaborator = forwardRef((props, ref) => {
  const { resourceId, gotoNext, disabled, triggerSearch, closeShare, isDkPage, cooperLinkConf, isModalShare, info } = props;

  const [batchAddVisible, setBatchAddVisible] = useState(false);
  const [cnInputting, setCnInputting] = useState(false);
  const [value, setValue] = useState('');
  const notification = useNotification();
  const inputRef = useRef(null);

  useImperativeHandle(ref, () => ({
    focusInput: () => {
      inputRef.current.focus();
    },
  }));

  const triggerContent = () => {
    return (
      <div className={cx('popover-container')}>
        <span>
          {intl.t('为文档协作者设置查看/编辑/下载权限，')}
        </span>
        <a
          href={isDkPage ? cooperLinkConf.add_collaborator_dk : cooperLinkConf.add_collaborator_doc}
          target='_blank'>
          {intl.t('查看详细说明')}
        </a>
      </div>
    )
  }

  const handleNext = () => {
    gotoNext();
    sendFileTypeEvent('ep_share_manage_ck', info?.fileType);
  }

  const onInputChange = useMemo(() => {
    return debounce((v) => {
      triggerSearch(v);
    }, 400);
  }, []);

  const onCompositionStart = () => {
    setCnInputting(true);
  };

  const onCompositionEnd = (e) => {
    setCnInputting(false);
    setValue(e.target.value);
    onInputChange(e.target.value);
  };

  const batchAdd = (invitees, perm, checkInvitees, sendNotification) => {
    const params = {
      invitees,
      limitCount: 1000,
      checkInvitees,
      notifyDc: sendNotification,
    };

    if (isDkPage) {
      params.role = getPageRole(perm, PAGE_MEMBER_TYPE)
      params.pageId = resourceId;
    } else {
      params.resourceId = resourceId;
      params.permission = perm;
    }
    return (isDkPage ? batchAddPageMember : batchAddPageMemberDoc)(params).then((res) => {
      if (res?.addStatus === 1) {
        notification(NotificationStatus.SUCCESS, intl.t('添加成功'));
        setBatchAddVisible(false);
        closeShare();
      }
      return res;
    });
  };
  return (
    <div className={cx({ 'share-disabled': disabled })}>
      <div className={cx('add-collaborator')}>
        <div className={cx('add-collaborator-title')}>
          <span>{intl.t('添加协作者')}</span>
          <Popover
            trigger='hover'
            content={triggerContent}
            placement='top'
            zIndex={1001}
            getPopupContainer={(e) => e.parentNode}
            overlayClassName={isModalShare ? 'add-collaborator-tip-placement-top_modal__reset' : 'add-collaborator-tip-placement-top__reset'}
          >
            <i className={cx('dk-iconfont', 'dk-icon-tishi-xianxing', 'icon-tip')} />
          </Popover>
        </div>
        <div className={cx('batch-add-auth-control')}>
          {
            disabled ? (
              <DisabledTip title={intl.t('仅文档管理员可邀请协作者')}>
                <Button
                  className={cx('batch-add', { 'batch-add-disabled': disabled })}
                  disabled={disabled}>
                  <i className={cx('dk-iconfont', 'dk-icon-icon-test')} />
                  {intl.t('批量添加')}
                </Button>
              </DisabledTip>
            ) : (
              <Button
                className={cx('batch-add')}
                onClick={() => {
                  setBatchAddVisible(true);
                  sendFileTypeEvent('ep_share_batchadd_ck', info?.fileType);
                }}>
                <i className={cx('dk-iconfont', 'dk-icon-icon-test')} />
                {intl.t('批量添加')}
              </Button>
            )
          }
          {
            disabled ? (
              <DisabledTip title={intl.t('仅文档管理员可操作')}>
                <div className={cx('auth-control')}>
                  <span>{intl.t('权限管理1')}</span>
                  <i className={cx('dk-iconfont', 'dk-icon-youjiantou', 'icon-auth')} />
                </div>
              </DisabledTip>
            ) : (
              <div
                className={cx('auth-control')}
                onClick={handleNext}>
                <span>{intl.t('权限管理1')}</span>
                <i className={cx('dk-iconfont', 'dk-icon-youjiantou', 'icon-auth')} />
              </div>
            )
          }
        </div>
      </div>
      <div className={cx('add-search')}>
        {
          disabled ? (
            <DisabledTip title={intl.t('仅文档管理员可邀请协作者')}>
              <Input
                disabled={disabled}
                className={cx('search-input')}
                placeholder={intl.t('搜索并添加联系人、DC群、部门')}
              />
            </DisabledTip>
          ) : (
            <Input
              value={value}
              ref={inputRef}
              className={cx('search-input')}
              onChange={(e) => {
                setValue(e.target.value);
                if (cnInputting) {
                  return;
                }
                onInputChange(e.target.value);
                sendFileTypeEvent('ep_share_search_ck', info?.fileType);
              }}
              placeholder={intl.t('搜索并添加联系人、DC群、部门')}
              onCompositionStart={onCompositionStart}
              onCompositionEnd={onCompositionEnd}
            />
          )
        }

      </div>
      {/* <BatchAdd
        show={batchAddVisible}
        batchAdd={batchAdd}
        closeBatchAdd={() => {
          setBatchAddVisible(false);
        }}
        onClose={() => {}}
        singleCheck={!!isDkPage}
        permissionType={isDkPage ? 'DkPage' : 'doc'}
        message={message}
        // permissionFilter={permissionFilter}
        defaultPermis={isDkPage ? [1] : [1, 2]}
        EpClickBatchAddFns={EpClickAddMemberBatchAddFns}
        isShowOutDes={'false'}
        showNotificationCheckbox={true}
      /> */}
    </div>
  )
});

export default AddCollaborator;
