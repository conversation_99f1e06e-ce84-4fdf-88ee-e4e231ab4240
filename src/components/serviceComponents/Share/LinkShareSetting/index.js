import { useEffect, useState } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import { Divider, message, Modal, Switch, Tooltip } from 'antd';
import classNames from 'classnames/bind';
import { intl } from 'di18n-react';
import DropdownCheckbox from '@/components/common/DropdownCheckbox';
import DropdownRadio from '@/components/common/DropdownRadio';
import { modifyShareLink } from '@/service/knowledge/share';
import { modifyShareLinkCooper } from '@/service/cooper/home';
import cooperConfirm from '@/components/common/CooperConfirm';
import { PERM_DEFAULT_LIST_KNOWLEDGE, PERM_DEFAULT_LIST_SHARE } from '@/components/CooperFoldAuth/contants';
import { IN_OUT } from '@/constants/space';
import { getType, sendFileTypeEvent } from '@/utils/type'

import styles from './style.module.less';

const cx = classNames.bind(styles);

const permissionOptions = function () {
  return [
    {
      label: intl.t('查看'),
      id: 0,
      value: 1,
      perm: 1,
      desc: intl.t('可查看/评论'),
      disabled: true,
    },
    {
      label: intl.t('编辑'),
      id: 1,
      value: 34,
      perm: 34,
      desc: intl.t('可编辑/下载'),
    },
  ];
};

export const permissionOptionsDoc = () => {
  return [
    {
      label: intl.t('查看'),
      id: 0,
      value: 1,
      perm: 1,
      desc: intl.t('可查看/评论，不能编辑'),
      disabled: true,
    },

    {
      label: intl.t('编辑'),
      id: 1,
      value: 2,
      perm: 2,
      desc: intl.t('可查看/评论/编辑，不能分享给他人'),
    },
    {
      label: intl.t('下载'),
      id: 2,
      value: 32,
      perm: 32,
      desc: intl.t('可查看/下载，不能编辑'),
    },
  ];
};

const LinkShareSetting = (props) => {
  const {
    modalOpen,
    linkInfo,
    setModalOpen,
    refreshLinkInfo,
    isDkPage,
    info,
    toggleChildVisible,
  } = props;
  const { accessRole, permission, password, expiration, resource_id, invite_link_id, access_type, spaceType } = linkInfo;
  const [ batchValue, setBatchVaulue ] = useState([0]);
  const [ batchValueDoc, setBatchVaulueDoc ] = useState([0]);
  const [ expireValue, setExpireValue ] = useState(3);      // 有效期
  const [ newPassword, setPassword ] = useState(password);  // 新密码
  const [ needPassword, setNeedPassword ] = useState(access_type === 'Secret' );  // 密码访问是否打开
  const [ needRole, setNeedRole ] = useState(accessRole === 0);  // 成为协作者是否打开
  const [ perm, setPerm ] = useState(permission);

  useEffect(() => {
    toggleChildVisible && toggleChildVisible(modalOpen);
  }, [modalOpen]);

  const resetState = () => {
    setExpireValue(expiration);
    setPassword(password);
    setNeedPassword(access_type === 'Secret');
    setNeedRole(accessRole === 0);
    setPerm(permission);

    let perms = [];
    
    if (isDkPage) {
      for (let i = 0; i < PERM_DEFAULT_LIST_KNOWLEDGE.length; i++) {
        if (permission & PERM_DEFAULT_LIST_KNOWLEDGE[i]) {
          perms.push(i);
        }
      }
      setBatchVaulue(perms);
    } else {
       for (let i = 0; i < PERM_DEFAULT_LIST_SHARE.length; i++) {
        if (permission & PERM_DEFAULT_LIST_SHARE[i]) {
          perms.push(i);
        }
      }
      setBatchVaulueDoc(perms);
    }
  };

  useEffect(() => {
    setNeedRole(accessRole === 0);
  }, [accessRole]);

  useEffect(() => {
    setPerm(permission);
  }, [permission]);

  useEffect(() => {
    setExpireValue(expiration);
  }, [expiration]);

  useEffect(() => {
    let perms = [];
    
    if (isDkPage) {
      for (let i = 0; i < PERM_DEFAULT_LIST_KNOWLEDGE.length; i++) {
        if (permission & PERM_DEFAULT_LIST_KNOWLEDGE[i]) {
          perms.push(i);
        }
      }
      setBatchVaulue(perms);
    } else {
       for (let i = 0; i < PERM_DEFAULT_LIST_SHARE.length; i++) {
        if (permission & PERM_DEFAULT_LIST_SHARE[i]) {
          perms.push(i);
        }
      }
      setBatchVaulueDoc(perms);
    }
  }, [permission]);

  useEffect(() => {
    setPassword(password);
  }, [password]);

  useEffect(() => {
    setNeedPassword(access_type === 'Secret')
  }, [access_type]);

  const changeBatchPermis = (value) => {
    const values = value[0] === 0 ? value : [0].concat(value);
    let newPerm = 0;
    values.forEach((v) => {
      newPerm += permissionOptions()[v].perm;
    });
    setPerm(newPerm);
    setBatchVaulue(values);
  };

  const changeBatchPermisDoc = (value) => {
    const values = value[0] === 0 ? value : [0].concat(value);
    let newPerm = 0;
    values.forEach((v) => {
      newPerm += permissionOptionsDoc()[v].perm;
    });
    setPerm(newPerm);
    setBatchVaulueDoc(values);
  };

  const handleExpireChange = (value) => {
    setExpireValue(value);
    const ExpireTime = ['permanently', 'month', 'week', 'day'];
    const isType = new Set([
             'docs',
             'sheets',
             'slides',
             'flowchart',
             'xmind',
             'pages',
     ]).has(getType(info?.fileType));
    window.__OmegaEvent('ep_share_link_manage_period_ck', '', {
      period: ExpireTime[value - 1],
      source: isType ? getType(info?.fileType) : ''
    });
  };

  const generatePwd = () => {
    return Math.floor(100000 + Math.random() * 900000);
  }

  const resetPwd = () => {
    setPassword(generatePwd());
  }

  const handleChangePwd = (checked) => {
    setNeedPassword(checked);
    if (checked) {
      setPassword(generatePwd());
      sendFileTypeEvent('ep_share_link_manage_password_ck', info?.fileType);
    } else {
      setPassword('');
      sendFileTypeEvent('ep_share_link_manage_unpassword_ck', info?.fileType);
    }
  }
  // 检查配置是否被修改
  const isConfigChanged = () => {
    // 检查权限设置变化
    const permissionChanged = permission !== perm;
    
    // 检查密码访问设置变化
    const passwordSettingChanged = (access_type === 'Secret') !== needPassword;
    // 如果开启了密码访问，还需要检查密码是否变化
    const passwordValueChanged = needPassword && password !== newPassword;
    
    // 检查有效期变化
    const expirationChanged = expiration !== expireValue;
    
    // 检查访问者角色设置变化
    const accessRoleChanged = (accessRole === 0) !== needRole;

    return permissionChanged || 
           passwordSettingChanged ||
           passwordValueChanged ||
           expirationChanged || 
           accessRoleChanged;
  };

  // 保存配置
  const handleSave = () => {
    (isDkPage ? modifyShareLink : modifyShareLinkCooper)({
      'resource_id': resource_id,
      'invite_link_id': invite_link_id,
      permission: perm,
      password: newPassword,
      expiration: expireValue,
      accessRole: needRole ? 0 : 1,
      'access_type': needPassword ? 'Secret' : 'Public',
      linkSelectType: 1
    }).then(() => {
      setModalOpen(false);
      message.success(intl.t('修改成功'));
      refreshLinkInfo();
    });
    sendFileTypeEvent('ep_share_link_manage_permission_confirm_ck', info?.fileType);
  }

  const handleCancel = () => {
    if (!isConfigChanged()) {
      setModalOpen(false);
      return;
    } else {
      cooperConfirm({
        title: (
          <span>
            <i className={cx('dk-iconfont', 'dk-icon-tishi-01', 'icon-warn')} />
            {intl.t('是否修改设置')}
          </span>
        ),
        type: 'warn',
        content: (
          <span className={cx('modify-modal-content')}>
            {intl.t('链接分享设置已被修改，需确认修改后，以上设置才会生效。')}
          </span>
        ),
        okText: intl.t('确认修改'),
        cancelText: intl.t('暂不修改'),
        className: cx('modify-modal'),
        closable: true,
        zIndex: 9999,
        closeIcon: <i className={cx('dk-iconfont', 'dk-icon-guanbi', 'confirm-modal-close')} />,
        autoFocusButton: null,
        onOk() {
          handleSave();
        },
        onCancel() {
          setModalOpen(false);
          resetState();
        }
      });
    }
  }

  return (
    <Modal
      width={540}
      centered={true}
      maskClosable={false}
      destroyOnClose={true}
      className={cx('modal-setting')}
      title={intl.t('链接分享设置')}
      visible={modalOpen}
      onOk={handleSave}
      afterClose={resetState}
      onCancel={() => {
        setModalOpen(false);
        resetState();
      }}
      okButtonProps={{
        disabled: !isConfigChanged(),
      }}
      okText={intl.t('确认修改')}
      cancelText={intl.t('取消')}
      closeIcon={
        <i
          className={cx(
            'dk-iconfont',
            'dk-icon-guanbi',
            'modal-setting-close',
          )}
          onClick={(e) => {
            if (isConfigChanged()) {
              e.preventDefault();
              e.stopPropagation();
              handleCancel();
            } else {
              handleCancel();
            }
          }}
        />
      }
    >
      <div className={cx('setting-title')}>{intl.t('“企业员工可访问”的链接设置如下')}</div>

      <div className={cx('setting-card')}>
        <div className={cx('setting-card-left')}>
          {intl.t('企业内获得链接的人拥有以下权限')}
        </div>
        <div className={cx('setting-card-right')}>
          {
            isDkPage ? (
              <DropdownCheckbox
                showDesc
                options={permissionOptions()}
                value={batchValue}
                onChange={(value, newOptions) => {
                  changeBatchPermis(value, newOptions);
                  sendFileTypeEvent('ep_share_link_manage_permission_ck', info?.fileType);
                }}
                defaultOptions={permissionOptions()}
                splitLine={true}
                isSharedropdownCheckbox={true}
              />
            ) : (
              <DropdownCheckbox
                showDesc
                options={permissionOptionsDoc()}
                value={batchValueDoc}
                onChange={(value, newOptions) => {
                  changeBatchPermisDoc(value, newOptions)
                  sendFileTypeEvent('ep_share_link_manage_permission_ck', info?.fileType);
                }}
                defaultOptions={permissionOptionsDoc()}
                splitLine={true}
              />
            )
          }
        </div>
      </div>

      <div className={cx('setting-card')}>
        <div className={cx('setting-card-left')}>
          <div>
            {intl.t('链接有效期限')}
          </div>
          <div className={cx('setting-card-left-desc')}>
            {intl.t('过期后，重置为“未开启分享”')}
          </div>
        </div>
        <div className={cx('setting-card-right')}>
          <DropdownRadio
            options={[
              {
                id: 1,
                label: intl.t('永久有效'),
                disabled: (info?.relationTypeTags || []).includes(IN_OUT),
              },

              {
                id: 2,
                label: `1${intl.t('个月1')}`,
              },

              {
                id: 3,
                label: `1${intl.t('周1')}`,
              },

              {
                id: 4,
                label: `1${intl.t('天1')}`,
              },
            ]}
            onChange={handleExpireChange}
            value={expireValue}
            isShareDropdownRadio={true}
          />
        </div>
      </div>

      <div className={cx('setting-card', 'setting-pwd')}>
        <div className={cx('setting-pwd-top')}>
          <div className={cx('setting-card-left')}>
            <div>{intl.t('启用密码')}</div>
            <div className={cx('setting-card-left-desc')}>
              {intl.t('文档协作者无需密码，企业内其他获得链接的人需密码访问')}
            </div>
          </div>
          <div className={cx('setting-card-right')}>
            {
              spaceType === 'IN_OUT' ? (
                <Tooltip title={intl.t('外部空间的文档必须设置密码')}>
                  <Switch checked={needPassword} disabled />
                </Tooltip>
              ) : (
                <Switch checked={needPassword} onChange={handleChangePwd} />
              )
            }
          </div>
        </div>
        {
          newPassword && (
            <>
              <Divider />
              <div className={cx('setting-pwd-content')}>
                <div className={cx('setting-pwd-left')}>
                  { intl.t('密码') }
                </div>

                <div className={cx('setting-pwd-right')}>
                  <div className={cx('setting-pwd-text')}>
                    <span>{newPassword}</span>
                  </div>
                  <CopyToClipboard
                    text={newPassword}
                    onCopy={() => {
                      message.success(intl.t('已复制密码'))
                    }}
                  >
                    <div className={cx('setting-pwd-btn')}>
                      <i className='dk-iconfont dk-icon-lianjie-01' />
                      <span>
                        {intl.t('复制')}
                      </span>
                    </div>
                  </CopyToClipboard>
                  <div className={cx('setting-pwd-btn')} onClick={resetPwd}>
                    <i className='dk-iconfont dk-icon-zhongxinshengcheng' />
                    <span>{intl.t('重置')}</span>
                  </div>
                </div>
              </div>
            </>
          )
        }
      </div>

      <div className={cx('setting-card')}>
        <div className={cx('setting-card-left')}>
          <div>
            {intl.t('访问者默认成为文档协作者')}
          </div>
          <div className={cx('setting-card-left-desc')}>
            {intl.t('通过链接快速邀请批量协作者，请开启该设置。如开启，访问者默认成为协作者，初始权限为链接权限')}
          </div>
        </div>
        <div className={cx('setting-card-right')}>
          <Switch 
            checked={needRole}
            onChange={() => {
              setNeedRole(!needRole);
              if (needRole) {
                sendFileTypeEvent('ep_share_link_manage_uninvite_ck', info?.fileType);
              } else {
                sendFileTypeEvent('ep_share_link_manage_invite_ck', info?.fileType);
              }
            }}
          />
        </div>
      </div>

    </Modal>
  )
}

export default LinkShareSetting;