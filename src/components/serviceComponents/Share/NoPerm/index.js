import React, { useEffect, useState, useMemo } from "react";
import { intl } from "di18n-react";
import { CreateLinkSelectTypeData } from "../constants";
import { sendFileTypeEvent } from "@/utils/type";
import { CopyToClipboard } from "react-copy-to-clipboard";
import pathUtil from '@/utils/path';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import classNames from "classnames/bind";
import * as styles from "./style.module.less";

const cx = classNames.bind(styles);
const NoPerm = ({ linkInfo, info, isDkPage, isInDK, resourceId }) => {
  const { linkSelectType, access_type } = linkInfo;
  const notification = useNotification();
  const linkData = useMemo(() => {
    return CreateLinkSelectTypeData(access_type, linkSelectType).filter(
      (i) => i.check
    )[0];
  }, [linkSelectType, access_type]);

  const getCopyText = (isUrlOnly = false) => {
    let copyPath = "";
    let url = location.origin + location.pathname;
    let name = document.title;

    // isInDK 在知识库页面中点击的分享，使用document.title即可，否则需要从接口信息中获取titile
    if (!isInDK) {
      url = pathUtil.getDkPageUrl(info.space_id || info.spaceId || info.sourceId, resourceId);
      name = info.fileName || info.objectName;
    }

    if (isDkPage) {
      url = pathUtil.getDkPageUrl(info.space_id || info.spaceId || info.sourceId, resourceId);
    }
    if(isUrlOnly) {
      if (isDkPage) {
        copyPath = `${url}`;
      } else {
        copyPath = `${location.origin}${pathUtil.getCoopPath(
          resourceId,
          info.mime_type || info.mimeType
        )}`;
      }
    }else {
      if (isDkPage) {
        copyPath = `${intl.t('文件名称')}：${name}\n${intl.t('链接')}：${url}`;
      } else {
        copyPath = `${intl.t('文件名称')}：${name}\n${intl.t('链接')}：${location.origin}${pathUtil.getCoopPath(
          resourceId,
          info.mime_type || info.mimeType
        )}`;
      }
    }
    

    return copyPath;
  };

  return (
    <div className={cx("no-perm")}>
      <div className={cx("label")}>{intl.t("当前文档的链接分享设置为")}</div>
      <div className={cx("link-selecttype-item")}>
        <img src={require(`../${linkData.icon}.svg`)} />
        <div className={cx("context-box")}>
          <div className={cx("title")}>{linkData.label}</div>
          <div className={cx("des")}>{linkData.extra}</div>
        </div>
      </div>
      {linkSelectType === 1 && access_type === "Public" && (
        <div className={cx("link-label")}>
          {intl.t("分享者需对分享对象负责，")}
          <span>{intl.t("请谨慎分享")}</span>
        </div>
      )}
      {linkSelectType === 1 && access_type === "Secret" && (
        <div className={`${cx("link-label")} ${cx("link-label-secret")}`}>
          {intl.t("如需获取密码，请")}
          <a
            href={`dchat://im/start_conversation?name=${linkInfo?.ownerLdap?.ldap}&team_id=${linkInfo?.ownerLdap?.organizationId}`}
            onClick={() => {
              sendFileTypeEvent("ep_share_contactowner_ck", info?.fileType);
            }}
            target="_blank"
          >
            <span>{intl.t("联系所有者")}</span>
            <img src={require("../dc.svg")} />
          </a>
        </div>
      )}
      <div className={cx("url-box")}>
        <div className={cx("url")}><input type='text' value={getCopyText(true)} /></div>
        <CopyToClipboard
          text={getCopyText()}
          onCopy={() => {
            notification(NotificationStatus.SUCCESS, intl.t("已复制链接"));
            sendFileTypeEvent("ep_share_link_copylink_ck", info?.fileType);
          }}
        >
          <div className={cx("btn")}>{intl.t("复制链接")}</div>
        </CopyToClipboard>
      </div>
    </div>
  );
};

export default NoPerm;
