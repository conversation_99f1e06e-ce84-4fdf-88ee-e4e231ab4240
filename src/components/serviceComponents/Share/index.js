import classNames from 'classnames/bind';
import { Spin } from 'antd';
import { useEffect, useMemo, useState, useRef, useImperativeHandle, forwardRef } from 'react';
import NewAddCollaborator from './NewAddCollaborator';
import NewLinkShare from './NewLinkShare';
import AddCollaborator from './AddCollaborator';
import LinkShare from './LinkShare';
import { getShareLink } from '@/service/knowledge/share';
import { getShareLinkCooper } from '@/service/cooper/home'
import usePermission from '@/hooks/usePermission';
import { ROLE_TYPE_DOC } from '../MemberListOfDK/constants';
import NoPerm from './NoPerm';
import { apolloSwitch } from '@/utils/ab';

import styles from './style.module.less';

const cx = classNames.bind(styles);

const Share = forwardRef((props, ref) => {
  const { gotoNext, triggerSearch, closeShare, resourceId, isDkPage, isInDK, info, cooperLinkConf, toggleChildVisible, isModalShare = false } = props;
  const [ linkInfo, setLinkInfo ] = useState({});
  const [ loading, setLoading ] = useState(false);
  const [isNewLinkShare, setIsNewLinkShare] = useState(false);
  const { checkOperationPermission } = usePermission();
  const addCollaboratorRef = useRef(null);


  // 暴露focusInput方法给父组件
  useImperativeHandle(ref, () => ({
    focusInput: () => {
      if (addCollaboratorRef.current) {
        addCollaboratorRef.current.focusInput();
      }
    }
  }));

  const adminPerm = useMemo(() => {
    if (isDkPage) {
      return checkOperationPermission('MANAGE_PAGE_MEMBER', linkInfo.currentUserPerm);
    } else {
      return [ROLE_TYPE_DOC.Owner, ROLE_TYPE_DOC.Admin].includes(linkInfo.currentUserRole);
    }
  }, [linkInfo.currentUserPerm]);

  const fetchShareLink = async () => {
    try {
      let res;
      if (isDkPage) {
        res = await getShareLink({ resourceId });
      } else {
        res = await getShareLinkCooper({ resourceId });
      }
      const data = await apolloSwitch('link-share-update-new');
      setIsNewLinkShare(data)
      setLinkInfo(res);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    setLoading(true);
    fetchShareLink();
  }, []);
  
  const refreshLinkInfo = () => {
    fetchShareLink();
  };
  let content = null;
  if (loading) {
    content = <Spin loading={true} className={cx("list-wrap-loading")} />;
  } else if(!isNewLinkShare) {
    content = <>
      <AddCollaborator
        ref={addCollaboratorRef}
        resourceId={resourceId}
        isDkPage={isDkPage}
        gotoNext={gotoNext}
        linkInfo={linkInfo}
        disabled={!adminPerm}
        triggerSearch={triggerSearch}
        closeShare={closeShare}
        cooperLinkConf={cooperLinkConf}
        isModalShare={isModalShare}
        info={info}
      />
      <LinkShare
        info={info}
        isInDK={isInDK}
        isDkPage={isDkPage}
        resourceId={resourceId}
        linkInfo={linkInfo}
        disabled={!adminPerm}
        cooperLinkConf={cooperLinkConf}
        refreshLinkInfo={refreshLinkInfo}
        toggleChildVisible={toggleChildVisible}
        isModalShare={isModalShare}
      />
    </>
  } else {
    if (!adminPerm) {
      content = (
        <NoPerm
          info={info}
          linkInfo={linkInfo}
          isInDK={isInDK}
          resourceId={resourceId}
          isDkPage={isDkPage}
        />
      );
    } else {
      content = (
        <>
          <NewAddCollaborator
            ref={addCollaboratorRef}
            resourceId={resourceId}
            isDkPage={isDkPage}
            gotoNext={gotoNext}
            linkInfo={linkInfo}
            disabled={!adminPerm}
            triggerSearch={triggerSearch}
            closeShare={closeShare}
            cooperLinkConf={cooperLinkConf}
            isModalShare={isModalShare}
            info={info}
          />
          <NewLinkShare
            info={info}
            isInDK={isInDK}
            isDkPage={isDkPage}
            resourceId={resourceId}
            linkInfo={linkInfo}
            disabled={!adminPerm}
            cooperLinkConf={cooperLinkConf}
            refreshLinkInfo={refreshLinkInfo}
            toggleChildVisible={toggleChildVisible}
            isModalShare={isModalShare}
          />
        </>
      );
    }
  }
  
  

  return (
     <div className={cx('share')}>
      {content}
    </div> 
  )
});

export default Share;
