import { intl } from 'di18n-react';
import { Modal, Input, Button } from 'antd';
import { useContext, useMemo, useState } from 'react';
import classNames from 'classnames/bind';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import TextOnlyModal from '@/components/TextOnlyModal';
import * as service from '@/service/knowledge/setup';
import { openNewWindow } from '@/utils/index';
import styles from './style.module.less';

const { TextArea } = Input;
const cx = classNames.bind(styles);

const ApplyOwner = ({ onClose, visible, isDK, onApplyCallback }) => {
  const [text, setText] = useState('');
  const [verifyModalVisible, setVerifyModalVisible] = useState(false);
  const [bpmUrl, setBpmUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const desc = useMemo(() => {
    let text = isDK ? intl.t('申请弹窗-描述-知识库') : intl.t('申请弹窗-描述-页面');
    return intl.t(
      '该申请会提交给{text}所有者进行审批；如{text}所有者已离职，会提交给原{text}所有者上级进行审批。审批通过后变更生效',
      { text },
    );
  }, [isDK]);

  const { knowledgeId, pageId } = useContext(LayoutContext);

  const onApply = async () => {
    setLoading(true);
    const params = isDK ? knowledgeId : pageId;
    const res = await service[isDK ? 'applySetting' : 'applyPage'](params, {
      reason: text,
    });
    const { approver, bpmUrl: url } = res || {};
    const user = <a
      style={{ color: '#127FF0' }}
      target='_blank'
      href={`dchat://im/start_conversation?name=${approver?.ldap}`}
    >
      {approver?.cnName}
    </a>
    setLoading(false);
    setBpmUrl(url);
    handleClose();
    setVerifyModalVisible({
      title: intl.t('申请已提交'),
      tip: <span>{intl.t('你的申请已提交，将由')}{user}{intl.t('进行审批。审批完成后，你将会收到消息通知')}</span>
      ,
    });
    isDK && window.__OmegaEvent('ep_dkpc_advancedsetting_applyownership_confirm_ck');
    !isDK && window.__OmegaEvent('ep_dkpc_pagedetail_applyownship_confirm_ck');
  };

  const handleVerify = () => {
    setVerifyModalVisible(false);
    onApplyCallback && onApplyCallback();
  };

  const handleOpen = () => {
    openNewWindow(bpmUrl);
    onApplyCallback && onApplyCallback();
  };
  const handleClose = () => {
    setText('');
    onClose();
  };
  return (
    <div>
      <Modal
        visible={visible}
        maskClosable={false}
        wrapClassName={cx('apply-owner-modal', 'dk-ant-modal_reset')}
        title={intl.t('申请{slot0}所有权', { slot0: isDK ? intl.t('申请弹窗-知识库') : intl.t('申请弹窗-页面') })}
        footer={
          <>
            <Button onClick={handleClose}>{intl.t('取消')}</Button>
            <Button
              className={cx('btn-blue')}
              style={{ marginLeft: 20 }}
              type="primary"
              onClick={onApply}
              disabled={!text}
              loading={loading}
            >
              {intl.t('申请所有权弹窗-申请')}
            </Button>
          </>
        }
        onCancel={handleClose}
        confirmLoading={loading}
      >
        <p className={cx('apply-body-desc')}>{desc}</p>
        <p className={cx('apply-body-reason')}>
          {intl.t('申请理由（必填）：')}
        </p>
        <TextArea
          onChange={(e) => setText(e.target.value)}
          placeholder={intl.t('请输入申请理由，最多200字')}
          maxLength={200}
          className={cx('apply-input')}
          value={text}
        />
      </Modal>
      <TextOnlyModal
        cancelText={intl.t('知道了')}
        okText={intl.t('查看详情')}
        customClass={cx('departMentModal')}
        handleOk={handleOpen}
        handleCancel={handleVerify}
        modalVisible={verifyModalVisible}
        title={verifyModalVisible?.title}
        tip={verifyModalVisible?.tip}
      />
    </div>
  );
};

export default ApplyOwner;
