import { useEffect } from 'react';
import { Input, Button } from 'antd';
import { getLocale, intl } from 'di18n-react';
import classBind from 'classnames/bind';
import Header from '@/components/serviceComponents/Header';
import { inPhone } from '@/utils';

import styles from './style.module.less';

const cx = classBind.bind(styles);

const LimitedShare = ({ code, changeCode, checkCode, setInitLoading, setIsFromPwd, ownerInfo }) => {
  const isInPhone = inPhone();

  useEffect(() => {
    setInitLoading(false);
  }, []);

  const getClassName = () => {
    let className = [];
    if (getLocale() === 'en-US') {
      className.push('eng-trick');
    }
    if (code.length == 0) {
      className.push('button-default');
    }
    return className.join(' ');
  }

  const gotoPwd = () => {
    setIsFromPwd(true);
  }

  return (
    <div className={cx('limited-share')}>
      {
        !isInPhone && (
          <Header
            configSet={{
              showProjectJump: true,
              showChangeKnowledge: false,
              showSearchContent: false,
              showCreateKnowledge: false,
              showIconList: true,
              showUserInfo: true,
              showSafeTip: false,
            }}
          />
        )
      }

      {
        isInPhone ? (
          <div className={cx('docs-password')}>
            <div className={cx('limited-share')}>
              <div className={cx('img-wrapper')}>
                <img src={'https://img-ys011.didistatic.com/static/cooper_cn/dkpage.png'} alt='' />
              </div>
              <p className={cx('tip-user')}>
                {
                  ownerInfo && ownerInfo.name && ownerInfo.ldap && (
                    <a className={cx('tip-user-name')} target='_blank' href={`dchat://im/start_conversation?name=${ownerInfo?.ldap}`}>{ownerInfo?.name?.chineseName + '  '}</a>
                  )
                }
                {intl.t('分享给你一个加密文档')}
              </p>
              <div className={cx('tip-body')}>
                <div className={cx('tip-wrapper')}>
                  <input
                    placeholder={intl.t('请输入密码')}
                    type='password'
                    value={code}
                    onKeyUp={(e) => {
                      if (e.keyCode === 13) {
                        checkCode();
                      }
                    }}
                    onChange={e => changeCode(e.target.value)}
                  />
                  {/* {errTip && <div className='tip-err'>{errTip}</div>} */}
                </div>
                <button style={{backgroundColor: code ? '#3D3D3D' : 'rgba(56, 59, 67, 0.4)'}} type='button' onClick={checkCode}>
                  {intl.t('访问1')}
                </button>
              </div>
              <div className={cx('apply-join')}>{intl.t('不知道密码？')}<span onClick={gotoPwd}>{intl.t('申请权限')}</span></div>
            </div>
          </div>
        ) : (
          <div className={cx('panel', {
            'panel-inphone': isInPhone
          })}>
            <div className={cx('head')}>
              <img src={ownerInfo?.avatar} />
              <span className={cx('name')}>{ownerInfo?.name?.chineseName}</span>
              {intl.t('邀请您一起协作加密文件')}
            </div>
            <div className={cx('body')}>
              <div className={cx('password')}>{intl.t('输入密码')}：</div>
              <div className={cx("flex-center")}>
              <Input
                type='password'
                value={code}
                onChange={e => changeCode(e.target.value)}
                placeholder={intl.t('请输入密码')}
                autoComplete='off'
                onPressEnter={() => checkCode()}
              />
              <Button type='primary' onClick={() => checkCode()}  className={ getClassName && getClassName()}>
                {intl.t('确认')}
                </Button>
                </div>
              <p className={cx('tips')}>
                {intl.t('不知道密码？') + ' '}
                <a onClick={gotoPwd}>{intl.t('申请权限')}</a>
              </p>
            </div>
          </div>
        )
      }
    </div>
  );
}

export default LimitedShare;
