import React from 'react';
import { connect } from 'react-redux';
import classnames from 'classnames/bind';
import { Modal, Spin } from 'antd';
import Header from './Header';
import Editor from './Editor';
import styles from './style.module.less';

const cls = classnames.bind(styles);

function HistoryRecordModal(props) {
  const {
    initLoading,
    historyRecordVisible,
    toggleHistoryRecordVisible,
  } = props;

  return (
    <Modal
      width={'auto'}
      wrapClassName={ cls('history-record-modal') }
      title={null}
      visible={historyRecordVisible}
      onCancel={toggleHistoryRecordVisible}
      footer={null}
      centered
      destroyOnClose
      closeIcon={(<i className={cls('dk-iconfont', 'dk-icon-guanbi', 'close-icon')}/>)}
    >
      <div className={cls('history-record')}>
        {
          initLoading && (
            <div className={'page-detail-loading'}>
              <Spin/>
            </div>
          )
        }
        <Header/>
        <div className={cls('history-container')}>
          <Editor/>
        </div>
      </div>
    </Modal>
  );
}

function mapStateToProps({ historyRecord }) {
  const { initLoading, historyRecordVisible } = historyRecord;
  return {
    initLoading,
    historyRecordVisible,
  };
}

function mapDispatchToProps({ historyRecord }) {
  const { changeInitLoading, toggleHistoryRecordVisible } = historyRecord;
  return {
    changeInitLoading,
    toggleHistoryRecordVisible,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(HistoryRecordModal);
