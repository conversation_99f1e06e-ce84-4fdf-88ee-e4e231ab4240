import { intl } from 'di18n-react';
import { connect } from 'react-redux';
import { useEffect, useState } from 'react';
import classNames from 'classnames/bind';
import { Button, Modal } from 'antd';
import useNotification from '@/hooks/useNotification';
import usePermission from '@/hooks/usePermission';
import NotificationStatus from '@/constants/notification';
import { blurTime } from '@/utils';
import styles from './style.module.less';

const cls = classNames.bind(styles);

function Header(props) {
  const {
    view,
    currentRecord,
    toggleHistoryRecordVisible,
    refreshEditorKey,
    changeInitLoading,
    docInfo,
  } = props;
  const { collabEditorRangeHash, id, showDetail } = currentRecord;

  const { permission } = docInfo;
  const [hasRevertPermission, setHasRevertPermission] = useState(false);
  const notification = useNotification();
  const { checkOperationPermission } = usePermission();

  useEffect(() => {
    if (permission) {
      setHasRevertPermission(
        checkOperationPermission('MANAGE_PAGE_CONTEXT', permission),
      );
    }
  }, [permission]);

  const handleRevert = () => {
    const [docId] = collabEditorRangeHash.split('_');
    view.historyRevert({ historyId: id, docId }).then(() => {
      notification(NotificationStatus.SUCCESS, intl.t('已恢复记录'));
      toggleHistoryRecordVisible();
      setTimeout(() => {
        changeInitLoading(true);
        refreshEditorKey();
      }, 1000);
    });
  };

  const confirmRevert = async () => {
    const content = (
      <div className={cls('tip-content')}>
        {intl.t('⻚面将恢复为')}

        <span className={cls('datetime')}>
          {' '}
          {blurTime(currentRecord.updateTime)}{' '}
        </span>
        {intl.t('的历史记录。当前未发布内容将被覆盖。')}
      </div>
    );

    Modal.confirm({
      width: 480,
      icon: null,
      centered: true,
      title: intl.t('确认恢复历史记录吗？'),
      content,
      okText: intl.t('确认恢复'),
      cancelText: intl.t('取消'),
      className: cls('revert-history-modal'),
      onOk: handleRevert,
    });
  };

  return (
    <div className={cls('header')}>
      <div className={cls('left')}>
        <span className={cls('title')}>{intl.t('历史记录')}</span>
      </div>
      <div className={cls('right')}>
        {hasRevertPermission && (
          <Button
            className={cls('revert-btn')}
            onClick={confirmRevert}
            disabled={!showDetail}
          >
            {intl.t('恢复至此记录')}
          </Button>
        )}
        <span className={cls('badge')}>
          <i className={cls('add')} />
          {intl.t('新增')}
        </span>
        <span className={cls('badge')}>
          <i className={cls('del')} />
          {intl.t('删除')}
        </span>
      </div>
    </div>
  );
}

function mapStateToProps({ historyRecord, pageDetail }) {
  const { view, currentRecord } = historyRecord;
  const { docInfo } = pageDetail;
  return {
    view,
    currentRecord,
    docInfo,
  };
}

function mapDispatchToProps({ historyRecord, pageDetail }) {
  const { toggleHistoryRecordVisible } = historyRecord;
  const { refreshEditorKey, changeInitLoading } = pageDetail;

  return {
    toggleHistoryRecordVisible,
    refreshEditorKey,
    pageDetail,
    changeInitLoading,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Header);
