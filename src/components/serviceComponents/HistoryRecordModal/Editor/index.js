import { intl } from 'di18n-react';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { connect } from 'react-redux';
import { MrParcel } from '@didi/mf-tenon';
import classnames from 'classnames/bind';
import { EditorConfig, EditorMicroUrl, EditorName } from '@/constants/editor';
import styles from './style.module.less';

const cls = classnames.bind(styles);

window.msContainer = true;
function Editor(props) {
  const {
    changeView,
    docInfo,
    changeInitLoading,
    changeCurrentRecord,
    profile,
  } = props;
  const { guid, accessToken } = docInfo;
  const userInfo = {
    username: profile.username,
    username_zh: profile.username_zh,
    avatar: profile.avatar,
  };
  const editorConfig = {
    ...EditorConfig,
    menu: {
      floatingMenuContent: false,
      menuBarContent: null,
    },
    eventHandler: {
      init: (state, view) => {
        changeInitLoading(false);
        changeView(view);
      },
      onChange: (state, view, action) => {
        const { type, value } = action;
        if (type === 'historyChange') {
          changeCurrentRecord(value);
        }
      },
    },
    title: {
      value: docInfo.pageName || intl.t('无标题页面'),
      withTitle: true,
      placeholder: intl.t('无标题页面'),
      maxLength: 200,
    },
    app: {
      sourceId: guid,
      accessToken,
      sourceType: '6',
      content: '',
      dataLocation: 'self',
    },
    placeholder: {
      content: intl.t('直接输入内容，或选取模板进行新建'),
      switch: true,
    },
    comment: {
      show: false,
      editable: false,
    },
    dictionary: {
      show: false,
    },
    editable: false,
    history: true,
    // mode: 'history',
    user: {
      ...userInfo,
      nameCn: userInfo.username_zh,
    },
  };

  const editorShow = useMemo(() => {
    return guid && userInfo.username;
  }, [guid, userInfo]);

  useEffect(() => {
    if (docInfo.pageName) {
      window.document.title = docInfo.pageName;
    }
  }, [docInfo.pageName]);

  useEffect(() => {
    return () => {
      // 页面切换时卸载编辑器
      const TenonEditor = window[EditorName];
      TenonEditor && TenonEditor.unmount();
    };
  }, []);

  return (
    <div
      id="knowledge_editor_box"
      className={cls('editor')}
    >
      <div className={cls('editor-container')}>
        {editorShow && (
          <MrParcel
            entry={EditorMicroUrl}
            name={EditorName}
            appProps={editorConfig}
          />
        )}
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail, CooperIndex }) {
  const { docInfo } = pageDetail;
  const { profile } = CooperIndex;
  return {
    docInfo,
    profile,
  };
}

function mapDispatchToProps({ historyRecord }) {
  const { changeView, changeInitLoading, changeCurrentRecord } = historyRecord;
  return {
    changeView,
    changeInitLoading,
    changeCurrentRecord,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Editor);
