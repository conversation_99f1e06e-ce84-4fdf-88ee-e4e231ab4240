import { intl } from 'di18n-react';
/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-11-08 15:17:24
 * @LastEditTime: 2023-11-15 11:23:03
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/components/serviceComponents/DKList/index.js
 *
 */
import { useMemo } from 'react';
import { Spin } from 'antd';
import classBind from 'classnames/bind';
import ErrorTips from '@/components/ErrorTipsDk';
import { NoMyDkIcon, NoMyJoinDkIcon } from '@/assets/icon';
import { isDesktopDC } from '@/utils';
import DKCard from '@/pages/knowledge/MenHu/MyKnowledge/DKCard';
import MHCard from '@/pages/knowledge/MenHu/MenhuList/MHCard';
import styles from './style.module.less';

const cx = classBind.bind(styles);

const DKList = ({
  dkData,
  errorData,
  isOwn,
  initLoading,
  isMyKnowledge,
  changeKey,
  activeOwnerType,
}) => {
  const noData = useMemo(() => {
    return Array.isArray(dkData) ? dkData.length === 0 : [];
  }, [dkData]);
  const tipsDesc = useMemo(() => {
    if (errorData) return intl.t('数据获取失败，请稍后再试');
    if (noData) {
      const { key } = changeKey;
      if (isOwn) {
        if (isMyKnowledge && activeOwnerType === '0') {
          return intl.t('赶紧创建一个知识库，开启你的知识创作之旅');
        }
        if (isMyKnowledge && activeOwnerType !== '0') {
          return intl.t('暂无数据');
        }
        if (!isMyKnowledge) {
          return intl.t('赶紧创建一个知识门户，开启你的知识创作之旅');
        }
      }
      return intl.t('你还没有被邀请进入他人的知识{slot0}～', {
        slot0: isMyKnowledge ? intl.t('你还没有被邀请-知识库') : intl.t('你还没有被邀请-知识门户'),
      });
    }
    return '';
  });

  return (
    <div
      className={cx('dk-list-wrap', {
        'dk-list-wrap-dc': isDesktopDC,
      })}
     >
       {initLoading && (
         <div className={`page-detail-loading ${cx('loading-blank')}`}>
            <Spin />
          </div>
        )}
      

      {noData ? (
        <div className={cx('no-list')}>
          {isOwn ? (
            <ErrorTips
              desc={tipsDesc}
              overlayClassName={cx('dk-and-mh-list')}
              img={NoMyDkIcon}
            />
          ) : (
            <ErrorTips
              desc={tipsDesc}
              img={NoMyJoinDkIcon}
              overlayClassName={cx('dk-and-mh-list')}
             
            />
             
          )}
        </div>
      ) : (
        <ul
          className={cx({
            'team-dk-list': isMyKnowledge,
            'team-dk-list-mh': !isMyKnowledge,
            'team-dk-list-dc': isMyKnowledge && isDesktopDC,
            'team-dk-list--mh-dc': !isMyKnowledge && isDesktopDC,
          })}
        >
          {dkData.map((item) => {
            return isMyKnowledge ? (
              <DKCard
                key={item.id}
                dkInfo={item}
                isOwn={isOwn} />
            ) : (
              <MHCard
                key={item.id}
                mhInfo={item}
                isOwn={isOwn} />
            );
          })}
        </ul>
      )}
    </div>
  );
};

export default DKList;
