import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import { Modal } from 'antd';
import { RecycleBinEmptyIcon } from '@/assets/icon';
import styles from './style.module.less';

const cx = classBind.bind(styles);

const DeleteModal = ({ modalVisible, onOk, onCancel }) => {
  return (
    <Modal
      centered
      keyboard={true}
      visible={modalVisible}
      onOk={onOk}
      okText={intl.t('删除')}
      cancelText={intl.t('取消')}
      width={580}
      closable={false}
      wrapClassName={cx('delete-confirm-container', 'dk-ant-modal_reset')}
      onCancel={onCancel}
    >
      <div className={cx('delete-modal-content')}>
        <img
          src={RecycleBinEmptyIcon}
          className={cx('delete-confirm-icon')}
        />

        <div className={cx('delete-text')}>
          <h1 className={cx('text-h1')}>{intl.t('确认删除此页面吗？')}</h1>
          <p className={cx('text-p')}>
            {intl.t(
              '删除后，该页面及其子页面都将一并删除，30天内你仍可在「回收站」中恢复它',
            )}
          </p>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteModal;
