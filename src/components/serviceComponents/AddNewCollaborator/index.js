import classBind from 'classnames/bind';
import { intl } from 'di18n-react';
import { useState } from 'react';
import { DirectAdd } from '@didi/add-member-search-new';
import { searchMembersWhenAddMemberV2 } from '@/service/knowledge/setup';
import { addPageMembersV2, batchAddPageMember } from '@/service/knowledge/page';
import { addPageMembersV2Doc, batchAddPageMemberDoc, searchMembersWhenAddMemberV2Doc } from '@/service/cooper/home';
import { getPageRole } from '@/utils';
import { PAGE_MEMBER_TYPE } from '@/constants/setup';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import { sendFileTypeEvent } from '@/utils/type'

import styles from './style.module.less';

const cx = classBind.bind(styles);

export const EpClickAddMemberBatchAddFns = {
  clickBatchAdd: (fileType) => {
    sendFileTypeEvent('ep_addcollaborator_batchadd_ck', fileType);
  },
  notify: () => {},
  return: () => {},
  modifyPermission: () => {},
  modify: () => {},
  confirm: () => {},
  copy: () => {},
};

const AddNewCollaborator = (props) => {
  const { showBack, handleBack, backToFirstStep, searchValue, closeShare, resourceId, teamId, isDkPage, fileType} = props;
  const [ batchAddVisible, setBatchAddVisible ] = useState(false);
  const notification = useNotification();
  // 创建一个包装函数，将fileType传递给EpClickAddMemberBatchAddFns.clickBatchAdd
  const handleClickBatchAdd = () => {
    EpClickAddMemberBatchAddFns.clickBatchAdd(fileType);
  };
  
  // 创建一个新的对象，包含包装后的函数
  const localBatchAddFns = {
    ...EpClickAddMemberBatchAddFns,
    clickBatchAdd: handleClickBatchAdd
  };

  const doInvite = async (invitees,{ notifyDc }) => {
    const inviteesRes = invitees.map((item) => {

      if (isDkPage) {
        return {
          id: item.id,
          inviteeType: item.inviteeType,
          role: getPageRole(item.permis[0], PAGE_MEMBER_TYPE),
          searchType: item.searchType,
        };
      } else {
        return {
          id: item.id,
          inviteeType: item.inviteeType,
          permis: item.permis,
          permission: item.permission,
          searchType: item.searchType,
        };
      }
    });

    const params = {
      invitees: inviteesRes,
      notifyDc
    }

    if (isDkPage) {
      params.metisId = teamId;
    }

    return (isDkPage ? addPageMembersV2 : addPageMembersV2Doc)(resourceId, params).then(() => {
      notification(NotificationStatus.SUCCESS, intl.t('添加成功'));
      closeShare();
    });
  }

  const doSearch = async (params) => {
    const newParam = {
      ...params
    };

    if (isDkPage) {
      newParam.pageId = params.resourceId;
      newParam.knowledgeId = teamId;
    }

    const data = await (isDkPage ? searchMembersWhenAddMemberV2 : searchMembersWhenAddMemberV2Doc)(newParam);
    return data;
  }

  const epClickSearchTab = () => {}
  const epClickSeeMore = () => {}
  const handleChangeBatch = (value) => {
    setBatchAddVisible(value);
  }

  const batchAdd = (invitees, perm, checkInvitees, sendNotification) => {
    const params = {
      invitees,
      limitCount: 1000,
      checkInvitees,
      notifyDc: sendNotification
    };

    if (isDkPage) {
      params.role = getPageRole(perm, PAGE_MEMBER_TYPE)
      params.pageId = resourceId;
    } else {
      params.resourceId = resourceId;
      params.permission = perm;
    }

    return (isDkPage ? batchAddPageMember : batchAddPageMemberDoc)(params).then((res) => {
      if (res?.addStatus === 1) {
        notification(NotificationStatus.SUCCESS, intl.t('添加成功'));
        setBatchAddVisible(false);
        closeShare();
      }
      return res;
    });
  };

  const clearSearch = () => {
    if (searchValue !== '') {
      backToFirstStep();
    }
  }

  return (
    <div className={cx('add-collab')}>
      <div className={cx('title')}>
        { showBack && <i className={cx('dk-iconfont dk-icon-fanhuiyemian')} onClick={handleBack} />}
        {intl.t('添加协作者')}
      </div>
      <div className={cx('content')}>
        <DirectAdd 
          currId={resourceId}
          searchValue={searchValue}
          clearSearch={clearSearch}
          isHiddenKnowldege={isDkPage ? false : true}
          singleCheck={isDkPage ? true : false}
          onClose={handleBack}
          onOk={doInvite}
          defaultPermis={isDkPage ? [1] : [1,2]}
          searchFun={doSearch}
          permissionType={isDkPage ? 'DkPage' : 'Doc'}
          epClickSearchTab={epClickSearchTab}
          epClickSeeMore={epClickSeeMore}
          isHiddenTeam={false}
          showBatchAdd={batchAddVisible}
          batchAdd={batchAdd}
          setShowBatchAdd={handleChangeBatch}
          EpClickBatchAddFns={localBatchAddFns}
          epClickChangePermis={() => {}}
          toggleChildVisible={() => {}}
          showNotificationCheckbox={true}
        />
      </div>
    </div>
  );
}

export default AddNewCollaborator;
