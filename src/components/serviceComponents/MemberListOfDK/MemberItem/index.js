import React, { useContext } from 'react';
import { intl } from 'di18n-react';
import { highlight } from '@/utils';
import classBind from 'classnames/bind';
// import DepartContent from '../../departContent';
import UserPermSelectNew from '../UserPermChangeNew';
import TeamContext from '../TeamContext';
import { TagRoleMap } from '@/components/serviceComponents/PageMemberList/config';
import { ADD_GROUP_TYPE } from '@/constants/setup';
import MemberEmail from '../../MemberComponent/MemberEmail';

import styles from './style.module.less';

const cx = classBind.bind(styles);

const Tag = ({ content, className = '' }) => {
  return (
    <span className={cx('tag', className)}>
      <span className={cx('word')}>{intl.t(content)}</span>
    </span>
  );
};
        
const TagText = ({ tagRole, resourceType }) => {
  if (TagRoleMap[tagRole]) {
    return <Tag content={TagRoleMap[tagRole]} />
  }
  return '';
};

const filterRoles = (roles) => {
  if (roles && roles.includes('PAGE_OWNER') && roles.includes('DK_OWNER')) {
    return ['PAGE_OWNER'];
  }
  return roles;
}

const MemberItem = ({
  memberInfo,
  userRole,
  refresh,
  hideChangePerm,
  spaceRole,
  onRemoved,
  onPermChange,
  toggleChildVisible,
  keyword,
}) => {
  const {
    tag,
    email,
    avatar,
    cooperate_with_cn: name,
    id,
  } = memberInfo;

  const { hrStatus, groupType } = memberInfo;
  const { resourceId, resourceType } = useContext(TeamContext);

  const getFromText = () => {
    switch (groupType) {
      case ADD_GROUP_TYPE.METIS_CUSTOM: return intl.t('来自：自定义成员组');
      case ADD_GROUP_TYPE.DEPARTMENT: return intl.t('来自：组织架构');
      case ADD_GROUP_TYPE.COOPER: return intl.t('来自：Cooper团队');
      default: return '';
    }
  }

  return (
    <div
      key={id}
      className={cx('cooperFoldAuthm-member-list', {
        'quit-member': hrStatus === 'I',
      })}
    >
      <img className={cx('pic')} src={avatar} />
      <div className={cx('info')}>
        <div className={cx('nameArea')}>
          <div className={cx('name')}>{highlight(name)}</div>
          {
            filterRoles(tag)?.map((item) => {
              return <TagText tagRole={item} resourceType={resourceType} />
            })
          }
          {
            hrStatus === 'I' && (
              <Tag
                content={intl.t('已离职')}
                className={cx('tag-quit-self')} />
            )
          }
        </div>
        {
          keyword ? (
            <>
              {(memberInfo.groupUsers || memberInfo.email) && (
                <MemberEmail
                  groupUsers={memberInfo.groupUsers}
                  keyword={keyword}
                  memberEmail={memberInfo.email}
                  maxlength={24}
                  customClass={cx('mail')}
                />
              )}
            </>
          ) : (
            <>
              {
                email 
                  ? <p className={cx('mail')} >{ highlight(email) }</p> 
                  : <p className={cx('mail')} >{ getFromText() }</p> 
              }
            </>
          )
        }
      </div>
      {!hideChangePerm && (
        <UserPermSelectNew
          memberInfo={memberInfo}
          toggleChildVisible={toggleChildVisible}
          onRemoved={onRemoved}
          onPermChange={onPermChange}
          onRefresh={refresh}
          userRole={userRole}
          spaceRole={spaceRole}
          resourceId={resourceId}
          resourceType={resourceType}
        />
      )}
    </div>
  );
};

export default MemberItem;
