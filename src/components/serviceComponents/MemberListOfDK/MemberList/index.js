import React from 'react';
import MemberItem from '../MemberItem';
import classBind from 'classnames/bind';
import styles from './style.module.less';

const cx = classBind.bind(styles);

const MemberList = ({ list, refresh, keyWord, userRole, spaceRole, onRemoved, onPermChange, toggleChildVisible, keyword }) => {
  return (
    <div className={cx('memberList')}>
      {list && list.map((item, index) => {
        return (
          <div
            className={cx('memberItem')}
            key={item.id || item.userGroupId || item.principalMemberId || index}
          >
            <MemberItem
              keyword={keyword}
              toggleChildVisible={toggleChildVisible}
              memberInfo={item}
              refresh={refresh}
              keyWord={keyWord}
              userRole={userRole}
              spaceRole={spaceRole}
              onRemoved={onRemoved}
              onPermChange={onPermChange}
            />
          </div>
        );
      }) }
    </div>
  );
};

export default MemberList;
