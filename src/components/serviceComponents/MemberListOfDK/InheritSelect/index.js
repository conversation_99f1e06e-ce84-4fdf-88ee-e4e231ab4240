import classNames from 'classnames/bind';
import { Popover } from 'antd';
// import { MemberRoleMap } from '../config.js';
import { throttle } from 'lodash-es';
import { useEffect, useState, useContext, useCallback } from 'react';
// import * as PageService from '@/service/knowledge/page';
import { MemberRoleMap } from '@/components/serviceComponents/PageMemberList/config';
import inheritCheck from '../inheritCheck.png';
import inheritNoCheck from '../inheritNoCheck.png';
import { openNewWindow } from '@/utils';
import TeamContext from '../TeamContext';
import styles from './style.module.less';

const cls = classNames.bind(styles);
const cx = classNames.bind(styles);

const InheritSelect = ({
  inheritValue,
  InheritConfig: Config = {},
  inheritValueSource,
  itemMessage,
  bottomLink,
  changePermExplainText,
  isRootNode,
  knowledgeMember,
  changeInherit = () => {},
  getAlonePerm,
}) => {
  const [InheritConfig, setInheritConfig] = useState(Config);
  const [popoverVisible, setPopoverVisible] = useState(false);
  const [chooseTitle, setChooseTitle] = useState();
  const [inheritMsg, setInheritMsg] = useState({});
  // const { globalOutsideChain } = useSelector((state) => state.CooperIndex);
  const { resourceId } = useContext(TeamContext);

  const getMemberRoleList = (operationList) => {
    const res = {};
    if (operationList?.length > 0) {
      operationList.forEach((operation) => {
        res[operation] = MemberRoleMap[operation];
      });
    }
    return res;
  };

  useEffect(() => {
    setChooseTitle(
      itemMessage
        ? MemberRoleMap[inheritValue]?.title
        : InheritConfig[inheritValue]?.title(),
    );
  }, [InheritConfig, inheritValue]);

  const doChange = throttle(
    async (configItem) => {
      changeInherit({ value: configItem.value, itemMessage });
      setPopoverVisible(!popoverVisible);
      // 成员采用刷新列表，继承方式采用前端设置
      if (!itemMessage) {
        setChooseTitle(configItem.title);
      }
    },
    2000,
    { trailing: false },
  );

  const onVisibleChange = (visible) => {
    if (visible && itemMessage) {
      const { principalType = null, principalMemberId = null } = itemMessage;

      if (itemMessage.sources === 'ALONE_PERM') {
        getAlonePerm({
          pageId: resourceId,
          principalType,
          principalMemberId,
          knowledgeMember,
        }).then(res => {
          setInheritConfig(getMemberRoleList(res) || {});
        })
      }
    }
    setPopoverVisible(visible);
  };

  const hasOperatePerm = () => {
    return Object.keys(InheritConfig)?.length > 0;
  };

  const _renderContent = () => {
    return (
      <div className={cx('inherit-set-wrap')}>
        <div className={cx('inherit-content')}>
          {
          (Object.values(InheritConfig) || []).map((item) => {
            return (
              <div
                className={cx('container', {
                  'change-container-inherit': inheritValue === 0 && item.value === 0,
                  'change-container-noninherit': inheritValue === 1 && item.value === 1,
                })}
                onClick={() => doChange(item)}
                key={!!item.value}
              >
                <img
                  className={cx('container-left')}
                  src={ item.value === inheritValue ? inheritCheck : inheritNoCheck}
                />
                <div className={cx('container-right')}>
                  <span
                    className={cx('right-title', {
                      'change-right-title': item.status === inheritMsg.isInherit,
                    })}
                  >
                    {item.title()}
                  </span>
                  <span className={cx('right-tip')}>
                    {item.desc()}
                  </span>
                </div>
              </div>
            );
          })
        }
        </div>
        <div
          className={cx('container-footer')}
          onClick={() => openNewWindow(bottomLink.link)}
        >
          {bottomLink.text}
          <i className={cx('dk-iconfont', 'dk-icon-youjiantou1', 'arrow-perm')} />
        </div>
      </div>
    );
  };

  return (
    <div
      className={cls('identity-select', { noHover: !hasOperatePerm() })}
      key={inheritValue}
    >
      <Popover
        trigger='click'
        overlayClassName={cls(
          'dk-changePower-wrap_reset',
          'dk-ant-popover__reset',
          'identity-select-popover',
          { hideChangePowerPopover: !hasOperatePerm() },
        )}
        content={_renderContent}
        onVisibleChange={onVisibleChange}
        visible={popoverVisible}
        placement={'bottom'}
      >
        {/* <div className={cls('identity-text')}>
          <p className={cls('identity-text-large')}>{chooseTitle}</p>
          {itemMessage && (
            <p className={cls('identity-text-little')}>{inheritValueSource}</p>
          )}
        </div>
        <img
          className={cls('openChangePower', { hide: !hasOperatePerm() })}
          src={SwitchLocaleIcon}
        /> */}
        <button
          className={cx('inherit-set-btn', {
            disabled: false,
          })}
        >
          {/* <i
            className={cx('dk-iconfont', 'set-icon', {
              'dk-icon-bujicheng-01': !inheritMsg.isInherit,
              'dk-icon-jicheng': inheritMsg.isInherit,
            })} 
          /> */}
          <span className={cx('set-text')}>{chooseTitle}</span>
          <i className={cx('dk-iconfont', 'dk-icon-zhankai', 'icon-zhankai1')} />
        </button>
      </Popover>
    </div>
  );
};
export default InheritSelect;
