import React, { useState, useEffect, useRef } from 'react';
import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import TeamContext from './TeamContext';
import { useSelector } from 'react-redux';
import { Button } from 'antd';
import MemberContainer from './MemberContainer';
import PermissionsPop from '@/components/PermissionsPop';
import { permConfigKnowledge } from './constants';
import ChangePageOwnerEntry from './ChangePageOwnerEntry';
import InheritSelect from './InheritSelect';
import { InheritConfigRoot } from '../PageMemberList/config';
import NotificationStatus from '@/constants/notification';
import {
  reqInheritType,
  changeInheritType,
  getPageDetailPerm,
} from '@/service/knowledge/page';
import usePermission from '@/hooks/usePermission';
import useNotification from '@/hooks/useNotification';
import { ROLE_TYPE_PAGE_DK } from './UserPermChangeNew/constants';
import { getApolloConfig } from '@/utils/ab';
import { sendFileTypeEvent } from '@/utils/type';

import styles from './style.module.less';

const cx = classBind.bind(styles);

const MemberListOfDK = ({
  isDkPage,
  closeFoldAuthModal,
  resourceId,
  teamId,
  goRootFolder,
  resourceType,
  teamIdAllTeam,
  onRemoved,
  onPermChange,
  changeCooperTotalCount,
  toggleChildVisible,
  title,
  showBack,
  handleBack,
  gotoNext,
  permission,
  needDKDetail,
  roleKey,
  needPerm,
  info,
  permissionsPopHover = false
}) => {
  let docInfo = {};
  const childMemberContainerRef = useRef(null);
  const [inheritValue, setInheritValue] = useState(null);
  const [ changeInheritPerm, setChangeInheritPerm ] = useState(false);
  const [ docInfoNew, setDocInfo ] = useState({});
  const [ roleKeyNew, setRoleKeyNew ] = useState(null);
  const [globalOutsideChain, setGlobalOutsideChain] = useState({});
  try {
    const pageDetail = useSelector((state) => state.pageDetail);
    docInfo = pageDetail.docInfo;
  } catch (error) {
    // console.log(error);
  }
  const { checkOperationPermission } = usePermission();
  const notification = useNotification();

  const managerMemberPermis = checkOperationPermission('MANAGE_PAGE_MEMBER', permission);
  const hasPerm = checkOperationPermission(
    'MANAGE_PAGE_SETTING_LIMIT',
    permission,
  );

  useEffect(() => {
    getApolloConfig('chain_of_outside').then((res) => {
      setGlobalOutsideChain(res);
    });
  }, []);

  useEffect(() => {
    if (isDkPage && (needDKDetail || needPerm)) {
      getPageDetailPerm({
        pageId: resourceId,
      }).then(res => {
        if (needDKDetail) {
          setDocInfo({
            ...res,
            ...docInfo,
            creatorMemberId: res.ownerMemberId,
            multiPath: res.parentId === 0 ? [1] : [1, 2] // 0：根节点，非0: 多级节点
          });
        }
        
        if (needPerm) {
          setRoleKeyNew(res.roleKey)
        }
      })
    }
  }, [isDkPage, resourceId]);

  useEffect(() => {
    setChangeInheritPerm(hasPerm && (docInfo.multiPath || docInfoNew.multiPath)?.length === 1);
  }, [hasPerm, docInfo.multiPath, docInfoNew.multiPath]);

  const formatePermText = () => {
    if ([ROLE_TYPE_PAGE_DK.Admin, ROLE_TYPE_PAGE_DK.Owner].includes(roleKey || roleKeyNew)) {
      return intl.t('管理员');
    }
    if ([ROLE_TYPE_PAGE_DK.Member].includes(roleKey || roleKeyNew)) {
      return intl.t('成员');
    }
    if ([ROLE_TYPE_PAGE_DK.ReadMember].includes(roleKey || roleKeyNew)) {
      return intl.t('只读成员');
    }
    if ([ROLE_TYPE_PAGE_DK.NoPerm].includes(roleKey || roleKeyNew)) {
      return intl.t('无权限成员');
    }
  }

  const getInheritType = () => {
    reqInheritType({ pageId: resourceId, knowledgeId: teamId }).then((res) => {
      setInheritValue(res.extensionType);
    });
  };

  useEffect(() => {
    getInheritType();
  }, []);

  const changeInherit = async ({ value }) => {
    window.__OmegaEvent('ep_dkpc_pagedetail_member_inheritmode_ck');

    try {
      let { extensionType } = await changeInheritType({
        pageId: resourceId,
        extensionType: value,
        knowledgeId: teamId,
      });
      setInheritValue(extensionType);
      childMemberContainerRef.current.loadMoreCurrList({ resourceId }, true);
      notification(NotificationStatus.SUCCESS, intl.t('已修改继承方式'));
    } catch (error) {
      notification(NotificationStatus.ERROR, error.errorMessage);
    }
  };

  return (
    <TeamContext.Provider value={{ resourceId, teamId, teamIdAllTeam, closeFoldAuthModal, goRootFolder, resourceType, toggleChildVisible }}>
      <div className={cx('member-list-of-resource')}>
        <div className={cx('list-header')}>
          <div className={cx('list-header-title')}>
            { showBack && <i className={cx('dk-iconfont dk-icon-fanhuiyemian')} onClick={handleBack} />}
            {title}
          </div>
          <div className={cx('title-action')}>
            {changeInheritPerm && (inheritValue !== null) && (
              <InheritSelect
                inheritValue={inheritValue}
                InheritConfig={InheritConfigRoot}
                changeInherit={changeInherit}
                bottomLink={{
                  text: intl.t('了解页面权限'),
                  link: globalOutsideChain?.is_inherit_perm_explain,
                }}
              />
            )}
            {
              managerMemberPermis && (
                <Button
                  className={cx('add-collaborator')}
                  onClick={() => {
                    gotoNext();
                    sendFileTypeEvent('ep_collaborator_add_ck', info?.fileType);
                  }}
                >
                  <i className='dk-iconfont dk-icon-tianjiachengyuan2' />
                  {intl.t('添加协作者')}
                </Button>
              ) 
            }
          </div>
        </div>

        <div className={cx('list-body-area')}>
          <MemberContainer
            isDkPage={isDkPage}
            info={info}
            toggleChildVisible={toggleChildVisible}
            managerMemberPermis={managerMemberPermis}
            updateTotalCount={(value) => {
              if (!value) return;
              changeCooperTotalCount && changeCooperTotalCount(value);
            }}
            ref={childMemberContainerRef}
            resourceType={'dk'}
            onRemoved={onRemoved}
            onPermChange={onPermChange}
            gotoNext={gotoNext}
          />
        </div>
        
        <div className={cx('list-footer')} >
          <div className={cx('user-perm-show')}>
            <span>{intl.t('我的权限：')}{formatePermText()}</span>
            <PermissionsPop
              permissionConfig={permConfigKnowledge()}
              title={intl.t('成员权限说明')}
              width={351}
              hiddenText={true}
              isHasHover={permissionsPopHover}
            />
          </div>

          <ChangePageOwnerEntry
            docInfo={{
              ...docInfo,
              ...docInfoNew,
            }}
            onVisibleChange={() => {}}
          />
        </div>
      </div>
    </TeamContext.Provider>
  );
};

export default MemberListOfDK;
