import React, { useState, useContext, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Input, Spin } from 'antd';
import { intl } from 'di18n-react';
import { debounce } from 'lodash-es';
import classBind from 'classnames/bind';
import InfiniteScroll from 'react-infinite-scroller';
import MemberList from '../MemberList';
import TeamContext from '../TeamContext';
import { getCooperatorsForDoc, getCooperators } from '../service';
import useLoadMore from '../useLoadMore';
import EmptyInfo from '@/components/CooperFoldAuth/emptyInfo';
import styles from './style.module.less';

const cx = classBind.bind(styles);

const MemberContainer = forwardRef(({
  updateTotalCount,
  userRole,
  spaceRole,
  onRemoved,
  onPermChange,
  toggleChildVisible,
  isDkPage,
}, ref) => {
  const { resourceId } = useContext(TeamContext);
  const [keyword, setKeyword] = useState('');

  const {
    loadMore: loadMoreCurrList,
    loading: loadingMoreCurr,
    list: currUserList,
    hasMore: hasMoreCurrList,
    totalCount,
  } = useLoadMore((args) => {
    return isDkPage 
      ? getCooperators({ keyword, resourceId, ...args })
      : getCooperatorsForDoc({ q: keyword, resourceId, ...args })
  }, true);


  useEffect(() => {
    if (keyword !== '') return;
    updateTotalCount(totalCount)
  }, [totalCount])

  useImperativeHandle(ref, () => ({
    loadMoreCurrList,
  }));

  const onInputChange = debounce((value) => {
    setKeyword(value);
    loadMoreCurrList({ q: value, keyword: value, resourceId }, true);
    window.__OmegaEvent('ep_teamspace_more_permisionset_searchmember_ck', '', {
      platform: 'new',
    });

  }, 500);


  return (
    <div className={cx('resource-member-wrap')}>
      <div className={cx('list-header-input')}>
        <Input
          className={cx('search-input')}
          onChange={(e) => onInputChange(e.target.value)}
          placeholder={intl.t('搜索当前文档协作者')}
        />
      </div>

      <div className={cx('list-wrap')}>
        <div className={cx('list-wrap-content')}>
          {
            currUserList && currUserList.length > 0 ? (
              <InfiniteScroll
                initialLoad={false}
                pageStart={0}
                loadMore={() => loadMoreCurrList({ keyword })}
                hasMore={!loadingMoreCurr && hasMoreCurrList}
                useWindow={false}
                getScrollParent={() => document.querySelector('.list-wrap-content')}
              >
                <MemberList
                  list={currUserList}
                  keyword={keyword}
                  toggleChildVisible={toggleChildVisible}
                  userRole={userRole}
                  spaceRole={spaceRole}
                  onRemoved={onRemoved}
                  onPermChange={onPermChange}
                  refresh={() => {
                    loadMoreCurrList({ resourceId }, true);
                  }}
                />
              </InfiniteScroll>
            ) : loadingMoreCurr ? (
              <Spin loading={loadingMoreCurr} className={cx('list-wrap-loading')} />
            ) : (
              <EmptyInfo desc={intl.t('暂无搜索结果')} />
            )
          }
        </div>
      </div>
    </div>
  );
})

export default MemberContainer;
