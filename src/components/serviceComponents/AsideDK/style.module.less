.changeViewBtnActive() {
  font-size: 26px;
  line-height: 26px;
  background: #ffffff;
  border: 1px solid #E8EAED;
  border-radius: 50%;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  &:hover{
    background: @primary-color;
    color: #ffffff;
  }
}

.modalContentView() {
  height: calc(100% - 92px);
  top: 52px;
  margin-bottom: 40px;
  z-index: 999;
  border-radius: 4px;
  box-shadow: 0px 6px 24px 0px rgba(18,19,21,0.16);
  border: 1px solid #EFF0F2;
}

.icon-shouqi {
  position: absolute;
  top: 19px;
  right: -14px;
  z-index: 1001;
  opacity: 0;
  line-height: 26px !important;
  .changeViewBtnActive();

  &:hover {
    opacity: 1;
  }
}

.icon-zhankai {
  position: relative;
  top: 16px;
  left: 6px;
  z-index: 1000;
  &:hover {
    &::before {
      content: '\e707';
    }
  }
  .changeViewBtnActive();
}

.aside-wrap {
  .aside-handle {
    width: 0;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1000; // 要在pagedetail的 loading 100上,在流程图弹窗下

    .aside-content {
      width: 280px;
      height: 100%;
      background: #f7f9fa;
      display: flex;
      position: absolute;
      top: 0;
      left: 0;
      // transition-property: left, top;
      // transition-duration:0.1s;
      // transition-timing-function:ease-in-out;

      // &:hover {
      //   .icon-shouqi {
      //     opacity: 1;
      //   }
      // }
    }
  }

   // hover在handle上的样式,由于需要透传拖拽后的width，部分样式用js添加
  .aside-handle-hover {
    z-index: 1002;
    //width: 40px;

    // &::before {
    //   content: '';
    //   position: absolute;
    //   width: 40px;
    //   height: 52px;
    // }
  }
}

// 弹窗模式下默认的样式
.aside-wrap-modal {
  .aside-handle {
    .aside-content {
      .modalContentView();
    }
  }
}
