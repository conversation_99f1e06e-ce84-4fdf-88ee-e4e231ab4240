import { intl } from 'di18n-react';
import { connect } from 'react-redux';
import classNames from 'classnames/bind';
import styles from './style.module.less';

const cls = classNames.bind(styles);

function Approver(props) {
  const { dkOwnerInfo } = props;
  const { ldap, ldapName } = dkOwnerInfo;
  return (
    <div className={cls('approver')}>
      {intl.t('请联系知识库所有者')}

      <a
        className={cls('link')}
        target='_blank'
        href={`dchat://im/start_conversation?name=${ldap}`}
      >
        {ldapName}({ldap}@didiglobal.com)
      </a>
      {intl.t('申请加入。')}
    </div>
  );
}

function mapStateToProps({ KnowledgeData }) {
  const { dkOwnerInfo } = KnowledgeData;
  return {
    dkOwnerInfo,
  };
}

export default connect(mapStateToProps)(Approver);
