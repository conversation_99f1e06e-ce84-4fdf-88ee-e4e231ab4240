import { intl } from 'di18n-react';
import { useState } from 'react';
import { Divider, Popover } from 'antd';
import classBind from 'classnames/bind';
import groupChoosenIcons from '@/constants/groupIconChoose';
import styles from './style.module.less';

const cx = classBind.bind(styles);

const GroupIconChoose = ({ getChooseIcon, children }) => {
  const [iconChooseVisible, setIconChooseVisible] = useState(false);
  const hideIconChoose = (img) => {
    setIconChooseVisible(false);
    getChooseIcon(img);
  };
  const handleVisibleChange = (visible) => {
    setIconChooseVisible(visible);
  };

  const IconChoose = (
    <div>
      <div className={cx('iconChooseTitle')}>{intl.t('默认')}</div>
      <Divider className={cx('iconChooseDivider')} />
      <div className={cx('iconArea')}>
        {groupChoosenIcons.map((item, index) => {
          return (
            <div
              className={cx('iconBlock')}
              key={index}>
              <img
                onClick={() => hideIconChoose(item.value)}
                className={cx('icon')}
                src={item.value}
              />
            </div>
          );
        })}
      </div>
    </div>
  );

  return (
    <Popover
      overlayClassName={`dk-ant-popover__reset ${cx(
        'iconChoose',
      )} knowLedge_groupIconChoose`}
      trigger="click"
      content={IconChoose}
      visible={iconChooseVisible}
      onVisibleChange={handleVisibleChange}
      placement="bottomLeft"
      zIndex={9999}
    >
      {children}
    </Popover>
  );
};
export default GroupIconChoose;
