.item-container {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 58px;
  box-sizing: border-box;
  cursor: pointer;
  overflow-y: auto;

  .title-container {
    display: flex;
    flex: 1;
    flex-direction: row;
    align-items: center;
    height: 58px;

    img {
      width: 26px;
    }

    .text-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 19px;
      width: 1px;
      flex: 1;

      .name {
        font-size: 14px;
        color: #2F343C;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .hint-container {
        display: flex;
        flex-direction: row;
        align-items: center;

        .size {
          font-size: 12px;
          color: #94A0B0;
        }

        .hint {
          font-size: 12px;
          color: #ff3f5a;
        }
      }
    }
  }

  .operation {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;

    .hint {
      font-size: 12px;
      color: #94A0B0;
    }

    .pause {
      background-size: 20px 20px;
      // background-image: url(../icon/pause.svg);
      cursor: pointer;

      &:hover {
        // background-image: url(../icon/pause-hover.svg);
      }
    }

    .continue-action {
      background-size: 20px 20px;
      // background-image: url(../icon/continue.svg);
      cursor: pointer;

      &:hover {
        // background-image: url(../icon/continue-hover.svg);
      }
    }

    .cancel {
      background-size: 20px 20px;
      // background-image: url(../icon/cancel.svg);
      cursor: pointer;

      &:hover {
        // background-image: url(../icon/cancel-hover.svg);
      }
    }

    .retry {
      background-size: 20px 20px;
      // background-image: url(../icon/retry.svg);
      cursor: pointer;

      &:hover {
        // background-image: url(../icon/retry-hover.svg);
      }
    }

    .jumpto {
      background-size: 20px 20px;
      // background-image: url(../icon/jumpto.svg);
      cursor: pointer;

      &:hover {
        // background-image: url(../icon/jumpto-hover.svg);
      }
    }

    .failed {
      background-size: 20px 20px;
      // background-image: url(../icon/failed.svg);
      cursor: pointer;

      &:hover {
        // background-image: url(../icon/retry.svg);
      }
    }

    .success {
      background-size: 20px 20px;
      // background-image: url(../icon/success.svg);
      cursor: pointer;
    }

    .icon {
      width: 20px;
      height: 20px;
    }

    .one {
      margin-right: 8px;
    }

    .two {
      margin-right: 20px;
    }

    .three {
      // C9CFD7 0066FF
    }
  }

  .progress {
    width: 100%;
    height: 60px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }
}