import { intl } from 'di18n-react';
import { Modal, Checkbox } from 'antd';
import classBind from 'classnames/bind';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

// cooper-confirm
export default function Confirm({
  className = '',
  type = 'warn',
  title,
  content,
  onOk = () => {},
  onCancel = () => {},
  okText,
  cancelText,
  onDestroy = () => {},
  isShowNoTip = false,
  onChangeToNoTip = () => {},
  isShowFooterCancel = true,
}) {
  let selfRef = null;
  selfRef = Modal.confirm({
    className: cx(
      `cooper-confirm cooper-confirm-${type} ${className} ${
        isShowFooterCancel ? '' : 'cooper-confirm-no-cancel'
      }`,
    ),
    title,

    // selfRef 保存了 Modal.confirm() 的引用，调用该实例的 destroy 方法销毁弹框
    content: (
      <div>
        <i
          className={cx('icon-close')}
          onClick={() => {
            onDestroy();
            selfRef.destroy();
          }}
        />

        {content}
        <br />
        {isShowNoTip ? (
          <Checkbox onChange={onChangeToNoTip}>{intl.t('不再提示')}</Checkbox>
        ) : null}
      </div>
    ),

    onOk,
    onCancel,
    width: 480,

    // antd button 在文字为2个时自动在文件间加空格，这与设计图不符，因此加入前置空格阻止 antd 默认行为
    okText: okText || intl.t('确定'),
    cancelText: cancelText || intl.t('取消'),
  });

  return selfRef;
}
