/* eslint-disable prefer-template */
import { intl } from 'di18n-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { MrParcel } from '@didi/mf-tenon';
import classnames from 'classnames/bind';
import {
  EditorConfig,
  EditorMicroUrl,
  EditorName,
} from '@/constants/editor';
import Loading from '@/components/Loading/index';

import styles from './style.module.less';

const cls = classnames.bind(styles);

window.msContainer = true;
function Editor({ editable, changeView, content }) {
  const editorConfig = {
    ...EditorConfig,
    schema: {
      nodes: [
        'doc',
        'paragraph',
        'text',
        'blockquote',
        'list_item',
        'bullet_list',
        'ordered_list',
        'heading',
        'image_block',
        'image',
        'hard_break',
        'checklist',
        'checklist_item',
        'code_block',
        'expand',
        'horizontal_rule',
        'file_block',
        'file',
        'table',
        'table_cell',
        'table_row',
        'table_header',
        'mathquill',
        'iframe',
        'flowchart',
        'block_column',
        'block_row',
        'link_card',
        'link_text',
        'contain_page',
      ],
      marks: [
        'link',
        'em',
        'strong',
        'textHighlight',
        'tabindent',
        'strikethrough',
        'underline',
        'textColor',
        'fontsize',
        'code',
        'fontfamily',
        'subscript',
        'superscript',
        'formatBrush',
        'collabUser',
        'formatPainter',
      ],
    },
    markdownGuide: true,
    menu: {
      floatingMenuContent: editable && {
        comment: false,
      },
      menuBarContent: editable && {
        undo: {
          width: 28,
          text: intl.t('撤销'),
          icon: 'icon-chexiao11',
          showTip: true,
          tooltip: intl.t('撤销') + ' <br /> control+z',
          macToolTip: intl.t('撤销') + ' <br /> ⌘+z',
        },
        redo: {
          width: 28,
          text: intl.t('重做'),
          icon: 'icon-zhongzuo11',
          showTip: true,
          tooltip: intl.t('重做') + ' <br /> shift+control+z',
          macToolTip: intl.t('重做') + ' <br /> ⇧+⌘+z',
        },
        formatPainter: {
          width: 28,
          text: '格式刷',
          icon: 'icon-geshishua1',
          showTip: true,
          tooltip: `${'格式刷'}`,
          macToolTip: `${'格式刷'}`,
          activeTooltip: `${'取消格式刷'}`,
          activeMacToolTip: `${'取消格式刷'}`,
        },
        formatBrush: {
          width: 28,
          text: intl.t('清除格式'),
          icon: 'icon-qingchugeshi3',
          showTip: true,
          tooltip: intl.t('清除格式'),
          macToolTip: intl.t('清除格式'),
        },
        split_line1: {
          width: 17,
        },
        heading: {
          width: 60,
          text: intl.t('标题'),
          showTip: true,
          tooltip: intl.t('标题 <br /> shift+control+number'),
          macToolTip: intl.t('标题 <br /> shift+control+number'),
        },
        fontsize: {
          width: 36,
          text: intl.t('字号'),
          showTip: true,
          tooltip: intl.t('字号'),
          macToolTip: intl.t('字号'),
        },
        split_line2: {
          width: 17,
        },

        strong: {
          width: 28,
          text: intl.t('加粗'),
          icon: 'icon-jiacu2',
          showTip: true,
          tooltip: intl.t('加粗') + ' <br /> control+b',
          macToolTip: intl.t('加粗') + ' <br /> ⌘+b',
        },
        em: {
          width: 28,
          text: intl.t('斜体'),
          icon: 'icon-xieti1',
          showTip: true,
          tooltip: intl.t('斜体') + ' <br /> control+i',
          macToolTip: intl.t('斜体') + ' <br /> ⌘+i',
        },
        underline: {
          width: 28,
          text: intl.t('下划线'),
          icon: 'icon-xiahuaxian11',
          showTip: true,
          tooltip: intl.t('下划线'),
          macToolTip: intl.t('下划线'),
        },
        strikethrough: {
          width: 28,
          text: intl.t('删除线'),
          icon: 'icon-henghuaxian1',
          showTip: true,
          tooltip: intl.t('删除线'),
          macToolTip: intl.t('删除线'),
        },
        split_line3: {
          width: 17,
        },
        textColor: {
          width: 28,
          text: intl.t('文本颜色'),
          icon: 'icon-wenziyanse3',
          showTip: true,
          tooltip: intl.t('文本颜色'),
          macToolTip: intl.t('文本颜色'),
        },
        textHighlight: {
          width: 28,
          text: intl.t('文本高亮'),
          icon: 'icon-wenzibeijingyanse',
          values: [
            {
              key: 'rgba(247,211,32,0.4)',
              value: 'rgba(247,211,32,0.4)',
            },
          ],

          showTip: true,
          tooltip: intl.t('文本高亮'),
          macToolTip: intl.t('文本高亮'),
        },
        split_line4: {
          width: 17,
        },

        ordered_list: {
          width: 28,
          text: intl.t('有序列表'),
          icon: 'icon-youxuliebiao11',
          // unsupportedNodes: ['text_align'],
          showTip: true,
          tooltip: intl.t('有序列表'),
          macToolTip: intl.t('有序列表'),
        },
        bullet_list: {
          width: 28,
          text: intl.t('无序列表'),
          icon: 'icon-wuxuliebiao11',
          // unsupportedNodes: ['text_align'],
          showTip: true,
          tooltip: intl.t('无序列表'),
          macToolTip: intl.t('无序列表'),
        },
        tabindent: {
          width: 28,
          text: intl.t('缩进'),
          icon: 'icon-suojin1',
          showTip: true,
          tooltip: intl.t('缩进 <br /> Tab'),
          macToolTip: intl.t('缩进 <br /> Tab'),
        },
        decreaseIndent: {
          width: 28,
          text: intl.t('取消缩进'),
          icon: 'icon-quxiaosuojin1',
          showTip: true,
          tooltip: intl.t('取消缩进 <br /> Shift + Tab'),
          macToolTip: intl.t('取消缩进 <br /> Shift + Tab'),
        },
        text_align: {
          width: 36,
          text: intl.t('对齐'),
          icon: 'icon-zuoduiqi11',
          showTip: true,
          tooltip: intl.t('对齐'),
          macToolTip: intl.t('对齐'),
        },
        split_line6: {
          width: 17,
        },
        checklist: {
          width: 28,
          text: intl.t('任务列表'),
          icon: 'icon-renwuliebiao2',
          // unsupportedNodes: ['text_align'],
          showTip: true,
          tooltip: intl.t('任务列表'),
          macToolTip: intl.t('任务列表'),
        },
        table: {
          width: 28,
          text: intl.t('表格'),
          icon: 'icon-biaoge11',
          showTip: true,
          tooltip: intl.t('表格'),
          macToolTip: intl.t('表格'),
        },
        image: {
          width: 28,
          text: intl.t('图片'),
          icon: 'icon-tupian11',
          showTip: true,
          tooltip: intl.t('图片'),
          macToolTip: intl.t('图片'),
        },
        link: {
          width: 28,
          text: intl.t('超链接'),
          icon: 'icon-lianjie1',
          showTip: true,
          tooltip: intl.t('超链接'),
          macToolTip: intl.t('超链接'),
        },
        split_line7: {
          width: 17,
        },
        selectGroup: {
          width: 36,
          children: {
            uploadFileNodes: {
              width: 28,
              text: intl.t('文件'),
              icon: 'icon-wenjian2',
              showTip: true,
              tooltip: intl.t('文件'),
              macToolTip: intl.t('文件'),
            },
            horizontal_rule: {
              width: 28,
              text: intl.t('分隔线'),
              icon: 'icon-fengexian1',
              showTip: true,
              tooltip: intl.t('分隔线'),
              macToolTip: intl.t('分隔线'),
            },
            code_block: {
              width: 28,
              text: intl.t('代码块'),
              icon: 'icon-daimakuai1',
              showTip: true,
              tooltip: intl.t('代码块 <br /> ```'),
              macToolTip: intl.t('代码块 <br /> ```'),
            },
            expand: {
              width: 28,
              text: intl.t('折叠块'),
              icon: 'icon-zhediekuai',
              showTip: true,
              tooltip: `${'折叠块'}`,
              macToolTip: `${'折叠块'}`,
            },
            blockquote: {
              width: 28,
              text: intl.t('引用'),
              icon: 'icon-yinyong1',
              showTip: true,
              tooltip: intl.t('引用 <br /> >+空格'),
              macToolTip: intl.t('引用 <br /> >+空格'),
            },
            mathquill: {
              width: 28,
              text: intl.t('公式'),
              icon: 'icon-gongshi2',
              showTip: true,
              tooltip: intl.t('公式'),
              macToolTip: intl.t('公式'),
            },
          },
        },
      },
    },
    eventHandler: {
      init: (state, view) => {
        changeView(view);
      },
    },
    title: {
      withTitle: false,
    },
    app: {
      sourceType: '6',
      content: content || '',
      dataLocation: 'other',
    },
    placeholder: {
      content: intl.t('添加首页内容进行展示'),
      switch: true,
    },
    comment: {
      show: false,
      editable: false,
    },
    dictionary: {
      show: false,
    },
    offline: true,
    user: {
      username: '',
      username_zh: '',
      avatar: '',
      nameCn: '',
    },
    backTopVisible: false,
    draggable: editable,
    slashCommand: editable,
    editable,
  };

  useEffect(() => {
    return () => {
      // 页面切换时卸载编辑器
      const TenonEditor = window[EditorName];
      if (TenonEditor && typeof TenonEditor.unmount === 'function') { // 监控到白屏报错做修复
        TenonEditor.unmount();
      }
    };
  }, []);

  return (
    <div className={cls('editor')}>
      <div
        className={cls('editor-container', { editable })}
        key={editable}>
        <MrParcel
          entry={EditorMicroUrl}
          name={EditorName}
          appProps={editorConfig}
        />
      </div>
    </div>
  );
}


export default Editor;
