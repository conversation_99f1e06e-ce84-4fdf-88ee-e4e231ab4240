import Loading from '@/components/Loading';
import { addLock, getSpaceNotice, removeLock, saveSpaceNotice } from '@/service/cooper/teamSpace';
import { TEAM_ADMIN, TEAM_OWNER } from '@/utils/cooperutils';
import { message, Popconfirm, Modal } from 'antd';
import classBind from 'classnames/bind';
import { debounce } from 'lodash-es';
import React, { useState, useRef, useEffect } from 'react';
import { intl } from 'di18n-react';
import { useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import Editor from './Editor';
import Empty from './Empty';

import styles from './style.module.less';

const cx = classBind.bind(styles);

function LockMessage({ lockUser }) {
  return (
    <span>
      用户<a
        className='announcement-lock-message-link'
        target='_blank'
        href={`dchat://im/start_conversation?name=${lockUser?.user}`}>{lockUser?.userCn}</a>正在编辑中，请稍后尝试
    </span>
  );
}

function SpaceAnnouncement() {
  const { teamId } = useParams();
  const { teamDetail } = useSelector((state) => state.TeamData);
  const { role } = teamDetail;
  const [announcement, setAnnouncement] = useState('');
  const [loading, setLoading] = useState(true);
  const [isEdit, saveIsEdit] = useState(false);
  const [isHover, saveIsHover] = useState(false);
  const viewRef = useRef(null);
  const timerRef = useRef(null);
  const [popupVisible, setPopupVisible] = useState(false);
  const navigate = useNavigate();

  const getAnnouncement = async () => {
    const data = await getSpaceNotice(teamId);
    setAnnouncement(data);
    if (!data) {
      setLoading(false);
    }
  }

  const saveAnnouncement = async (content) => {
    await saveSpaceNotice(teamId, content)
    setAnnouncement(content);
    saveIsEdit(false);
    unlock();
    message.success({
      content: intl.t('message-应用成功'),
      style: {
        marginTop: '80px',
      },
    });
  };

  const changeView = (view) => {
    viewRef.current = view;
    setLoading(false);
  }

  const getEditorContent = () => {
    const editorView = viewRef.current;
    let { content } = editorView.template.export();
    if (!editorView.dom.textContent.trim() && !editorView.dom.querySelector('div, img, table, li, i, span')) {
      content = '';
    }

    return content;
  }

  const handleSave = () => {
    window.__OmegaEvent('ep_team_edithomepage_confirm_ck', '团队空间-编辑首页-应用')
    const content = getEditorContent();
    saveAnnouncement(content);
  }

  const handelCancel = (isTrusted = true) => { // isTrusted: 判断是用户点击触发的，还是其他函数调用的
    if (isTrusted) {
      window.__OmegaEvent('ep_team_edithomepage_leave_ck', '团队空间-编辑首页-确认取消编辑')
    }
    saveIsEdit(false);
    setPopupVisible(false);
    unlock();
  }

  const cancelConfirm = () => {
    const content = getEditorContent();
    if (!content) {
      handelCancel(false);
    } else {
      setPopupVisible(true);
    }
  }

  const closePopConfirm = () => {
    window.__OmegaEvent('ep_team_edithomepage_unleave_ck', '团队空间-编辑首页-不取消编辑')
    setPopupVisible(false);
  }

  const handleEdit = () => {
    window.__OmegaEvent('ep_team_edithomepage_ck', '团队空间-编辑首页');
    addLock(teamId)
      .then(({ fetchLock, currentLockUser }) => {
        if (fetchLock) {
          saveIsEdit(true);
          timerRef.current = setTimeout(() => {
            handleEdit();
          }, 1000 * 60 * 10);
        } else {
          message.warning({
            className: 'announcement-lock-message',
            content: <LockMessage lockUser={currentLockUser} />,
            duration: 5,
          });
        }
      })
  }

  const unlock = () => {
    clearTimeout(timerRef.current);
    removeLock(teamId);
  }

  useEffect(() => {
    if (teamId) {
      setLoading(true);
      setAnnouncement('');
      saveIsEdit(false);
      setPopupVisible(false);
      viewRef.current = null;
      clearTimeout(timerRef.current);
      getAnnouncement();
    }
  }, [teamId]);

  useEffect(() => {
    return () => {
      handelCancel(false);
    }
  }, []);

  // 当前项目配置的react router不支持路由拦截，这里使用代理浏览器原生函数的方式进行拦截
  useEffect(() => {
    const originPushState = window.history.pushState;
    if (isEdit) {
      window.history.pushState = debounce((...args) => {
        Modal.confirm({
          title: intl.t('取消本次编辑将不会保存内容'),
          okText: intl.t('不保存'),
          cancelText: intl.t('再想想'),
          onOk: () => {
            window.history.pushState = originPushState;
            navigate(args[2]);
          },
          onCancel: () => { },
        });
      }, 300);
    }

    return () => {
      if (isEdit) {
        window.history.pushState = originPushState;
      }
    };
  }, [isEdit]);

  useEffect(() => {
    const beforeunload = (event) => {
      // Cancel the event as stated by the standard.
      event.preventDefault();
      // Chrome requires returnValue to be set.
      event.returnValue = intl.t('取消本次编辑将不会保存内容');
    }
    if (isEdit) {
      window.addEventListener('beforeunload', beforeunload);
    }

    return () => {
      if (isEdit) {
        window.removeEventListener('beforeunload', beforeunload);
      }
    }
  }, [isEdit]);

  return (
    <div
      className={cx('space-detail-card')}
      onMouseEnter={() => saveIsHover(true)}
      onMouseLeave={() => saveIsHover(false)}
    >
      {
        loading && (
          <div className={cx('loading')}>
            <Loading />
          </div>
        )
      }

      <div className={cx('card-title')}>
        {isEdit && (
          <>
            <Popconfirm
              title={intl.t('取消本次编辑将不会保存内容')}
              onConfirm={handelCancel}
              onCancel={closePopConfirm}
              okText={intl.t('不保存')}
              cancelText={intl.t('再想想')}
              placement="bottomRight"
              overlayClassName={cx('pop-confirm')}
              visible={popupVisible}
            >
              <span
                className={cx('card-title-cancel')}
                onClick={cancelConfirm}>{intl.t('取消')}</span>
            </Popconfirm>
            <span
              className={cx('card-title-confirm')}
              onClick={handleSave}
            >
              {intl.t('应用')}
            </span>
          </>
        )}

        {!isEdit && isHover && (role === TEAM_ADMIN || role === TEAM_OWNER) && !!announcement && (
          <span
            className={cx('card-title-edit')}
            onClick={handleEdit}
          >
            <span>{intl.t('编辑首页')}</span>
          </span>
        )}
      </div>

      <div className={cx('card-content')}>
        {
          !isEdit && !announcement ? (
            <div className={cx('card-no-data')}>
              <Empty
                hasPerm={(role === TEAM_ADMIN || role === TEAM_OWNER)}
                onClick={handleEdit} />
            </div>
          ) : (
            <Editor
              editable={isEdit}
              content={announcement}
              changeView={changeView}
            />
          )
        }
      </div>
    </div>
  );
}

export default SpaceAnnouncement;
