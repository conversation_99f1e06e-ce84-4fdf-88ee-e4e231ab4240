import { intl } from 'di18n-react';
import React from 'react'
import classBind from 'classnames/bind';
import styles from './style.module.less';
import EmptyIcon from '@/assets/icon/empty.png';

const cx = classBind.bind(styles);

function Empty({ hasPerm, onClick }) {
  return (
    <div className={cx('empty')}>
      <img
        className={cx('icon')}
        src={EmptyIcon}
        alt=""/>
      <div className={cx('text')}>{intl.t(hasPerm ? '管理员可添加内容，向空间内成员展示。' : '暂无内容')}</div>
      {
        hasPerm && (
          <div className={cx('options')}>
            <span
              className={cx('button')}
              onClick={onClick}>{intl.t('编辑首页')}</span>
          </div>
        )
      }
    </div>
  );
}

export default Empty;
