import { intl } from 'di18n-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import classnames from 'classnames/bind';
import * as styles from './style.module.less';

const cls = classnames.bind(styles);

function Collapse(props) {
  const { children } = props;
  const [isExpand, setIsExpand] = useState(false);
  const [visible, setVisible] = useState(false);
  const [style, setStyle] = useState({});
  const contentRef = useRef(null);
  const toggleCollapse = () => {
    setIsExpand(!isExpand);
  };

  useEffect(() => {
    const Container = contentRef.current.firstElementChild;
    const { clientHeight, scrollHeight } = Container;
    const { lineHeight, maxHeight } = window.getComputedStyle(Container);
    setStyle({
      WebkitLineClamp: Math.floor(
        parseFloat(maxHeight) / parseFloat(lineHeight),
      ),
    });
    setVisible(scrollHeight > clientHeight);
  }, []);

  return (
    <div className={cls('collapse')}>
      <div
        ref={contentRef}
        style={style}
        className={cls('collapse-container', { 'is-expand': isExpand })}
      >
        {children}
      </div>
      <div
        hidden={!visible}
        className={cls('collapse-footer')}>
        <span
          className={cls('toggle-switch')}
          onClick={toggleCollapse}>
          {isExpand ? intl.t('收起') : intl.t('展开')}
        </span>
      </div>
    </div>
  );
}

export default Collapse;
