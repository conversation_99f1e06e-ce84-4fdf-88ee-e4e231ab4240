import { intl } from 'di18n-react';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import classNames from 'classnames/bind';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { Modal, Button, Tooltip } from 'antd';
import CollapsePanel from '@/components/CollapsePanel';
import Editor from '@/pages/knowledge/SetUp/TemplateManagement/Editor';
import styles from './style.module.less';

const cls = classNames.bind(styles);

function TemplateModal(props) {
  const containerRef = useRef();
  const { knowledgeId } = useContext(LayoutContext);
  const {
    templateModalVisible,
    toggleTemplateModalVisible,
    changeTemplateVisible,
    selectTemplate,
    defaultTemplates,
    knowledgeTemplates,
    getKnowledgeTemplates,
    getDefaultTemplates,
  } = props;
  const [active, setActive] = useState({});

  const handleToggleTemplate = (template) => {
    const editorContainer = containerRef.current.querySelector('.didoc-editor-app');
    editorContainer && editorContainer.scrollTo(0, 0);
    setActive(template);
  };

  const handleSelectTemplate = (template) => {
    selectTemplate(template);
    toggleTemplateModalVisible();
    changeTemplateVisible(false);
    window.__OmegaEvent('ep_dkpc_pagedetail_template_all_use_ck');
  };

  const title = useMemo(() => {
    return (
      <span>
        {intl.t('模板库')}

        <a
          href={`/knowledge/${knowledgeId}/setUp?key=2`}
          target="_blank">
          <Tooltip title={intl.t('模板管理')}>
            <i
              className={cls(
                'dk-iconfont',
                'dk-icon-mobanguanli',
                'template-icon',
              )}
            />
          </Tooltip>
        </a>
      </span>
    );
  }, [knowledgeId]);

  useEffect(() => {
    getKnowledgeTemplates(knowledgeId);
    getDefaultTemplates(knowledgeId);
  }, [knowledgeId]);

  const data = useMemo(() => {
    return [
      {
        title: intl.t('当前知识库模版'),
        children: knowledgeTemplates,
      },
      {
        title: intl.t('系统模版'),
        children: defaultTemplates,
      },
    ];
  }, [defaultTemplates, knowledgeTemplates]);

  const isKnowledgeTemplate = useMemo(() => {
    return knowledgeTemplates.some(
      (item) => active.templateId === item.templateId,
    );
  }, [knowledgeTemplates, active]);

  useEffect(() => {
    const firstTemplate = [...knowledgeTemplates, ...defaultTemplates][0];
    firstTemplate && setActive(firstTemplate);
  }, [data]);

  return (
    <Modal
      width={1164}
      wrapClassName={cls('template-modal')}
      title={title}
      visible={templateModalVisible}
      onCancel={toggleTemplateModalVisible}
      footer={null}
      centered
      closeIcon={
        <i className={cls('dk-iconfont', 'dk-icon-guanbi', 'close-icon')} />
      }
    >
      <div
        ref={containerRef}
        className={cls('container')}>
        <div className={cls('sidebar')}>
          <CollapsePanel
            data={data}
            active={active}
            onChange={handleToggleTemplate}
          />
        </div>
        <div className={cls('main')}>
          <div className={cls('header')}>
            <div className={cls('title', { visible: active.creatorCnName })}>
              {isKnowledgeTemplate
                ? intl.t('{slot0} {slot1} 制作', {
                  slot0: active.creatorCnName,
                  slot1: active.creatorEnName,
                })
                : intl.t('系统模版')}
            </div>
            <Button
              className={cls('apply')}
              onClick={() => handleSelectTemplate(active)}
            >
              {intl.t('使用模板')}
            </Button>
          </div>
          <div className={cls('content')}>
            <Editor templateId={active.templateId} />
          </div>
        </div>
      </div>
    </Modal>
  );
}

function mapStateToProps({ template }) {
  const { templateModalVisible, defaultTemplates, knowledgeTemplates } = template;
  return {
    templateModalVisible,
    defaultTemplates,
    knowledgeTemplates,
  };
}

function mapDispatchToProps({ template }) {
  const {
    toggleTemplateModalVisible,
    changeTemplateVisible,
    getKnowledgeTemplates,
    getDefaultTemplates,
  } = template;

  return {
    toggleTemplateModalVisible,
    changeTemplateVisible,
    getKnowledgeTemplates,
    getDefaultTemplates,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(TemplateModal);
