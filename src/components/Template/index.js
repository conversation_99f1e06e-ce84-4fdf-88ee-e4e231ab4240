import { intl } from 'di18n-react';
import { useContext, useEffect, useMemo, useState } from 'react';
import { connect, useSelector } from 'react-redux';
import classNames from 'classnames/bind';
import { createPortal } from 'react-dom';
import { useParams } from 'react-router-dom';
import TemplateModal from './TemplateModal';
import usePermission from '@/hooks/usePermission';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import styles from './style.module.less';
import { Tooltip } from 'antd';

const cls = classNames.bind(styles);

function Template(props) {
  const {
    templateVisible,
    applyTemplate,
    toggleTemplateModalVisible,
    templateModalVisible,
    getRecentTemplates,
    recentTemplates,
    countRecentTemplate,
  } = props;
  const { knowledgeId } = useContext(LayoutContext);
  const { checkOperationPermission } = usePermission();
  const { permission } = useSelector((state) => state.KnowledgeData);

  const templateListPerm = useMemo(() => {
    return checkOperationPermission('MANAGE_DK_PAGE', permission?.perm);
  }, [permission]);

  const selectTemplate = (template) => {
    applyTemplate(template);
    countRecentTemplate({ templateId: template.templateId, knowledgeId });
    window.__OmegaEvent('ep_dkpc_pagedetail_template_use_ck');
  };

  const clickAll = () => {
    window.__OmegaEvent('ep_dkpc_pagedetail_template_all_ck');
    toggleTemplateModalVisible();
  };

  useEffect(() => {
    if (!templateListPerm) return;
    getRecentTemplates(knowledgeId);
  }, [knowledgeId]);

  const list = useMemo(() => {
    const data = [...recentTemplates];

    return data.slice(0, 4);
  }, [recentTemplates]);

  if (!templateVisible || !templateListPerm || !document.querySelector('#didoc-editor-slot-after')) {
    return null;
  }

  return createPortal(
    <div className={cls('template')}>

      <div className={cls('container-wrapper')}>
        <span className={cls('tooltip')}>
          <span className={cls('tooltip-content')}>
            {intl.t('选择模板1')}
          </span>
          <Tooltip title={intl.t('模版预览图中无法展示流程图，但应用后可正常使用')} placement="top">
            <span className={cls('tooptip-trigger')}>
              <i
                className={cls('dk-iconfont dk-icon-a-tishi2')}
              />
            </span>
          </Tooltip>
        </span>
        <div className={cls('container')} id="template-container">
          {list.map((item) => (
            <div
              key={item.templateId}
              className={cls('card')}
              onClick={() => selectTemplate(item)}
            >
              <div className={cls('cover')}>
                <img
                  className={cls('img')}
                  src={`/cooper_gateway/metis${item.thumbnailUrl}?width=200`}
                  alt=""
                />
              </div>
              <div className={cls('title')}>{item.name}</div>
            </div>
          ))}

          <div className={cls('card', 'all')} onClick={clickAll}>
            <div className={cls('cover')}>
              <div className={cls('img', 'all-template')} />
            </div>
            <div className={cls('title')}>
              {intl.t('全部模板')}

              <i
                className={cls(
                  'dk-iconfont',
                  'dk-icon-quanbumobanjiantou',
                  'arrow-icon'
                )}
              />
            </div>
          </div>
        </div>
      </div>
      {templateModalVisible && (
        <TemplateModal selectTemplate={selectTemplate} />
      )}
    </div>,
    document.querySelector('#didoc-editor-slot-after'),
  );
}

function mapStateToProps({ template }) {
  const { templateVisible, templateModalVisible, recentTemplates } = template;
  return {
    templateVisible,
    templateModalVisible,
    recentTemplates,
  };
}

function mapDispatchToProps({ template }) {
  const {
    toggleTemplateModalVisible,
    getRecentTemplates,
    countRecentTemplate,
  } = template;
  return {
    toggleTemplateModalVisible,
    getRecentTemplates,
    countRecentTemplate,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Template);
