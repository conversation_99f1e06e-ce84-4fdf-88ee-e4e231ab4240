import { intl } from 'di18n-react';
import React from 'react';
import { Modal, message, Tooltip } from 'antd';
import LinkInvite from '@/components/LinkInvite';
import mountAnywhere from '@/utils/mountAnywhere';
// import { sendEvent } from '../../../common/constant/MirrorEvent';
import { getDefaultPermis, doAddMemberBatch, searchAddTeamMember, batchAddMember } from '@/service/cooper/teamInvite';
import { EpClickAddMemberBatchAddFns, MEMBER_TYPE } from '@/constants/cooperConstants';
import './index.less';
import { CooperTabs, CooperTabsPane } from '@/components/common/CooperTabs';
import { isDiDiTenant } from '@/utils/entryEnhance';
// import DirectAdd from '@didi/add-member-search-react';

const EventMap = {
  [MEMBER_TYPE.USER]: 'ADD_MEMBER_CHOOSE_SINGLE_USER',
  [MEMBER_TYPE.OE]: 'ADD_MEMBER_CHOOSE_OE_TEAM',
  [MEMBER_TYPE.COOPER]: 'ADD_MEMBER_CHOOSE_COOPER_TEAM',
  [MEMBER_TYPE.MAIL]: 'ADD_MEMBER_CHOOSE_MAIL_TEAM',
  [MEMBER_TYPE.DC]: 'ADD_MEMBER_CHOOSE_DC_TEAM',
};

const EventMapOmega = {
  [MEMBER_TYPE.OE]: 'ep_team_member_addoe_ck',
  [MEMBER_TYPE.COOPER]: 'ep_team_member_addcooper_ck',
  [MEMBER_TYPE.MAIL]: 'ep_team_member_addemail_ck',
  [MEMBER_TYPE.DC]: 'ep_team_member_adddc_ck',
};

class AddMember extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: '0',
      showBatchAdd: false,
    };
  }

  setShowBatchAdd = (value) => {
    this.setState({
      showBatchAdd: value,
    });
  }

  onChange = (member) => {
    if (!member) {
      return;
    }

    const eventType = EventMap[member.type];
    // sendEvent(eventType);
    // window.__OmegaEvent(EventMapOmega[member.type],'',{
    //   platform:'new',
    // });
  }

  doInvite = async (memberData) => {
    window.__OmegaEvent(EventMapOmega[memberData[0].inviteeType], '', {
      platform: 'new',
    });
    const { teamInfo } = this.props;
    await doAddMemberBatch(teamInfo.id, memberData);
    this.afterAdd();
  }

  doSearch = async (params) => {
    const data = await searchAddTeamMember(params);
    return data;
  }

  getDefaultPermis = () => {
    const defaultPer = getDefaultPermis(this.props.teamInfo);
    return defaultPer;
  }

  epClickSearchTab = () => {
    // __mirrorSendEvent('ADD_MEMBER_CLICK_TAB_TEAM');
  }

  epClickSeeMore = () => {
    // __mirrorSendEvent('ADD_MEMBER_CLICK_MORE_TEAM');
  }

  backToTabModal = () => {
    this.setShowBatchAdd(false);
    EpClickAddMemberBatchAddFns.return();
  }

  batchAdd = async (invitees, permission, checkInvitees) => {
    const { teamInfo } = this.props;
    const data = await batchAddMember({ teamId: teamInfo.id, invitees, permission, limitCount: 1000, checkInvitees });
    if (data.addStatus === 1) {
      this.afterAdd();
    }
    return data;
  }

  afterAdd = () => {
    const { doneCallback, onClose } = this.props;
    doneCallback(true);
    message.success(intl.t('添加成功'));
    onClose();
  }

  handleTabChange = (val) => {
    this.setState({
      activeTab: val,
    })
  }

  render() {
    const { activeTab, showBatchAdd } = this.state; // onClose 是 mountAnywhere 自动注入的

    const { teamInfo, onClose } = this.props;

    const { protocol, host } = window.location;
    const { value, qr_code } = teamInfo.links[0]; // eslint-disable-line camelcase

    const link = `${protocol}//${host}/teams/invite/${value}`;
    const qrCode = `data:image/png;base64,${qr_code}`; // eslint-disable-line camelcase
    const Title = <div>
      <p className='title'>{intl.t('权限说明：')}</p>
      <p className='desc'>· {intl.t('已是团队成员，通过群组再次添加时不会更改该成员当前团队空间权限')}</p>
      <p className='desc'>· {intl.t('同时添加单个用户和包含该用户的群组时，该用户取添加单个用户时设置的权限')}</p>
      <p className='desc'>· {intl.t('同时添加包含某个用户的多个群组时，该用户取多个群组权限的并集')}</p>
    </div>;
    return (
      <Modal
        visible={true}
        maskClosable={false}
        wrapClassName={`${showBatchAdd ? 'show-batch-add' : ''} tab-modal`}
        maskStyle={{
          backgroundColor: 'rgba(0, 0, 0, 0.2)',
        }}
        footer={null}
        title={showBatchAdd
          ? <div className='batch-add-title'>
            <i
              onClick={this.backToTabModal}
              className='dk-iconfont dk-icon-fanhuiyemian back'
            />
            {intl.t('批量添加')}
          </div> : <div className='add-title'>{intl.t('添加成员')}
            <Tooltip
              style={{ width: '410px' }}
              title={Title}
              placement='top'
              overlayClassName='addMember-modalTitleTip'
            >
              <i className='add-title-tip' />
            </Tooltip></div>}
        onCancel={onClose}
      >
        {
          !showBatchAdd ? (
            <div className='addMember-tabs'>
              <CooperTabs
                defaultActiveKey={activeTab}
                activeKey={activeTab}
                destroyInactiveTabPane={true}
                onChange={this.handleTabChange}
                tabsize='small'
              >
                <CooperTabsPane
                  tab={intl.t('定向添加')}
                  key="0">
                  {/* <DirectAdd
                    currId={teamInfo.id}
                    onClose={onClose}
                    onChange={this.onChange}
                    onOk={this.doInvite}
                    defaultPermis={this.getDefaultPermis()}
                    searchFun={this.doSearch}
                    permissionType='Team' // 用于区分权限说明的文案
                    epClickSearchTab={this.epClickSearchTab}
                    epClickSeeMore={this.epClickSeeMore}
                    showBatchAdd={showBatchAdd}
                    setShowBatchAdd={this.setShowBatchAdd}
                    batchAdd={this.batchAdd}
                    EpClickBatchAddFns={EpClickAddMemberBatchAddFns}
                    isShowOutDes={false}
                    epClickChangePermis={() => { }} // 组件内没有默认定义，传一下
                  /> */}
                </CooperTabsPane>
                <CooperTabsPane
                  tab={intl.t('链接邀请')}
                  key="1">
                  <LinkInvite
                    link={link}
                    qrCode={qrCode} />
                </CooperTabsPane>
              </CooperTabs>
            </div>
          ) : (
            <DirectAdd
              currId={teamInfo.id}
              onClose={onClose}
              onChange={this.onChange}
              onOk={this.doInvite}
              defaultPermis={this.getDefaultPermis()}
              searchFun={this.doSearch}
              permissionType='Team' // 用于区分权限说明的文案
              epClickSearchTab={this.epClickSearchTab}
              epClickSeeMore={this.epClickSeeMore}
              showBatchAdd={showBatchAdd}
              setShowBatchAdd={this.setShowBatchAdd}
              batchAdd={this.batchAdd}
              EpClickBatchAddFns={EpClickAddMemberBatchAddFns}
              isDiDiTenant={isDiDiTenant()}
              epClickChangePermis={() => { }} // 组件内没有默认定义，传一下
            />
          )
        }
      </Modal>
    );
  }
}

function addMember({ teamInfo, doneCallback }) {
  const Comp = (
    <AddMember
      teamInfo={teamInfo}
      doneCallback={doneCallback}
    />
  );
  mountAnywhere(Comp);
}

export default addMember;
