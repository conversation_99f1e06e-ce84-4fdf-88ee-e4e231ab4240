import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import { useCallback, useState, useEffect, useMemo } from 'react';
import { KNOWLEDGE, TRASH, HOME, FILE, MEMBER, SETTING } from '@/constants/space';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Dropdown, Menu, Tooltip } from 'antd';
import CreateModal from '@/components/CreateKnowledge/CreateModal';
import { dkBook, spaceTeam } from '@/assets/icon/fileIcon';
import { TEAM_ADMIN, TEAM_OWNER } from '@/utils/cooperutils';
import styles from './style.module.less';

const cx = classBind.bind(styles);

const operations = [
  {
    function: 'MEMBER',
    title: intl.t('成员管理，开发中'),
    icon: (
      <i className={cx('dk-iconfont', 'dk-icon-duoren', 'space-operation')} />
    ),
  },
  {
    function: TRASH,
    title: intl.t('回收站'),
    icon: (
      <i
        className={cx(
          'dk-iconfont',
          'dk-icon-huishouzhan4px',
          'space-operation',
        )}
      />
    ),
  },
  {
    function: 'SETTING',
    title: intl.t('空间设置'),
    icon: (
      <i className={cx('dk-iconfont', 'dk-icon-shezhi4', 'space-operation')} />
    ),
  },
];

// 在这里获取当前选中的Tab信息，放在store中
function SpaceTab({ tabChangeCb, role }) {
  const { knowledgeId } = useParams();
  const dispatch = useDispatch();
  const [visible, setVisible] = useState(false);
  const { saveCurrentFuns } = dispatch.TeamData;
  const {
    currentSpaceType,
    features,
    personFeatures,
    currentFuns,
    currSpaceId,
  } = useSelector((state) => state.TeamData);

  const [displayFeatureView, setDisplayFeatureView] = useState([]);
  const [hideFeatureView, setHideFeatureView] = useState([]);

  const navigate = useNavigate();
  const { pathname } = useLocation();

  const isInTeamRoute = pathname.includes('/team-file/')
    || (pathname.includes('/knowledge/') && currentSpaceType === 'TEAM_SPACE');
  const isInPersonRoute = pathname.includes('/disk')
    || pathname.includes('/files/')
    || (pathname.includes('/knowledge/') && currentSpaceType === 'PERSONAL_SPACE');

  const activeKey = currentFuns?.tabKey;

  const featuresView = useMemo(() => {
    if (isInPersonRoute) return personFeatures;
    if (isInTeamRoute) return features;
    return [];
  }, [features, personFeatures]);

  const dropdownItems = useMemo(() => {
    return (
      <Menu className={cx('space-tab-dropdown')}>
        {hideFeatureView?.map((item) => {
          return (
            <Menu.Item
              key={item.tabKey}
              className={cx(
                item.tabKey === activeKey && 'space-tab-dropdown-item',
              )}
              onClick={() => {
                handleTabChange(item);
              }}
            >
              <span>{item.nameCn}</span>
            </Menu.Item>
          );
        })}
      </Menu>
    );
  }, [hideFeatureView, activeKey]);

  const handleResize = useCallback(() => {
    setDisplayFeatureView(featuresView);
    if (featuresView.length === 0) return;
    const totalWidth = document.getElementById('space-tab-title')?.clientWidth;
    const remainWidth = totalWidth - 80; // 80是后面的添加按钮的宽度
    const items = document.querySelectorAll('.space-tab-title-item');

    if (items.length === 0) return;

    let count = 0;
    let total = 0;

    for (let i = 0; i < items.length; i++) {
      total = total + items[i].offsetWidth + 2;
      if (total <= remainWidth) {
        count++;
      }
    }

    for (let i = 0; i < items.length; i++) {
      if (i < count) {
        items[i].style.visibility = 'visible';
        items[i].style.flex = `0 0 ${items[i].offsetWidth}px`;
      } else {
        items[i].style.visibility = 'hidden';
        items[i].style.flex = '0 0 0px';
      }
    }
    if (role !== TEAM_OWNER && role !== TEAM_ADMIN) {
      setHideFeatureView(
        featuresView
          ?.slice(count)
          .filter((item) => item.function !== 'KNOWLEDGE_NEW'),
      );
    } else {
      setHideFeatureView(featuresView?.slice(count));
    }
  }, [featuresView, role]);

  useEffect(() => {
    handleResize();
    setTimeout(() => {
      handleResize();
    }, 0);
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [featuresView]);

  useEffect(() => {
    // features列表更新会及时更新SpaceHeaderActFuns
    updateActFuns();
  }, [pathname, currentSpaceType, features, personFeatures]);

  const updateActFuns = () => {
    if (pathname.includes('/knowledge/')) {
      const currDk = featuresView?.find(
        (item) => item.subjectId == knowledgeId,
      );
      if (!currDk) return;
      saveCurrentFuns(currDk);
    } else if (pathname.includes('/disk') || pathname.includes('/files/')) {
      saveCurrentFuns(featuresView?.find((item) => item.function === FILE));
    } else if (pathname.includes('/home')) {
      saveCurrentFuns(featuresView?.find((item) => item.function === HOME));
    } else if (pathname.includes('/trash')) {
      saveCurrentFuns(operations.find((item) => item.function === TRASH));
    } else if (pathname.includes('/team-file/')) {
      saveCurrentFuns(featuresView?.find((item) => item.function === FILE));
    }
  };

  const handleTabChange = (item) => {
    if (item.function === 'MEMBER' || item.function === 'SETTING') return;
    saveCurrentFuns(item);
    if (!currSpaceId) return;
    if (item.function === HOME) {
      navigate(`/team-file/${currSpaceId}/home`);
    }
    if (item.function.includes(FILE)) {
      isInPersonRoute && navigate('/disk');
      isInTeamRoute && navigate(`/team-file/${currSpaceId}`);
    }
    if (item.function === KNOWLEDGE) {
      if (item.subjectId) {
        navigate(`/knowledge/${item.subjectId}/home`);
      }
    }
    if (item.function === TRASH) {
      navigate(`/team-file/${currSpaceId}/trash`);
    }

    tabChangeCb && tabChangeCb(item);
  };

  return (
    <>
      <div className={cx('space-tab')}>
        <div
          id="space-tab-title"
          className={cx('space-tab-title')}>
          <div className={cx('space-tab-title-left')}>
            {displayFeatureView?.map((feature) => {
              if (
                isInTeamRoute
                && feature.function === 'KNOWLEDGE_NEW'
                && role !== TEAM_OWNER
                && role !== TEAM_ADMIN
              ) {
                return null;
              }
              return (
                <div
                  key={feature.tabKey}
                  className={`${cx(
                    'space-tab-title-item',
                    activeKey === feature.tabKey
                      && 'space-tab-title-item-active',

                    feature.function !== 'HOME'
                      && feature.function !== 'FILE'
                      && 'space-tab-title-item-knowledge',
                  )} space-tab-title-item`}
                  onClick={() => {
                    handleTabChange(feature);
                  }}
                >
                  <div className={cx('space-tab-title-item-content')}>
                    {feature.nameCn}
                  </div>
                </div>
              );
            })}
          </div>

          <div className={cx('space-tab-title-right')}>
            {hideFeatureView?.length > 0 && (
              <Dropdown
                overlay={dropdownItems}
                placement="bottom"
                overlayClassName={cx('space-tab-dropdown')}
                trigger="click"
              >
                <div
                  className={cx(
                    'space-tab-title-more',
                    hideFeatureView?.findIndex(
                      (item) => item.tabKey === activeKey,
                    ) > -1 && 'space-tab-title-more-active',
                  )}
                >
                  <i
                    className={cx(
                      'dk-iconfont',
                      'dk-icon-xiayitiaopinglun',
                      'space-more',
                    )}
                  />
                </div>
              </Dropdown>
            )}

            {(isInPersonRoute
              || role === TEAM_OWNER
              || role === TEAM_ADMIN) && (
                <div
                  className={cx('space-tab-title-add')}
                  onClick={() => {
                    setVisible(true);
                  }}
              >
                  <i
                    className={cx(
                      'dk-iconfont',
                      'dk-icon-icon-test',
                      'space-add',
                    )}
                />
                </div>
            )}
          </div>
        </div>
        {isInTeamRoute && (
          <div className={cx('space-tab-operation')}>
            {operations.map((operation) => {
              return (
                <div
                  key={operation.function}
                  className={cx(
                    'space-tab-operation-item',
                    {
                      'space-tab-operation-item-active': activeKey !== 'KNOWLEDGE_NEW' && ((
                        pathname.includes('/trash') && operation.function === TRASH
                      ) || (
                        pathname.includes('/member') && operation.function === MEMBER
                      ) || (
                        pathname.includes('/setup') && operation.function === SETTING
                      )),
                    },
                  )}
                  onClick={() => {
                    handleTabChange(operation);
                  }}
                >
                  <Tooltip
                    title={operation.title}
                    placement="top"
                    align={{
                      offset: [0, -7],
                    }}
                  >
                    {operation.icon}
                  </Tooltip>
                </div>
              );
            })}
          </div>
        )}
      </div>
      <CreateModal
        visible={visible}
        isReplaceNewTab={false}
        onCloseModal={() => {
          setVisible(false);
        }}
      />
    </>
  );
}

export default SpaceTab;
