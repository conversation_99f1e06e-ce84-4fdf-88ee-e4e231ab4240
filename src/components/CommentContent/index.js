import { intl } from 'di18n-react';
import { Modal } from 'antd';
import { useEffect, useState } from 'react';
import cls from 'classnames';
// import Editor from './Editor';
import { CreateCommentIcon } from './iconConstant';
import useTreeData from './UseOperateNode';
import { inPhone } from '@/utils';
import CommentCard from './CommentCard';
import { useSelector } from 'react-redux';
import { parseUrlSearch } from '../../utils/index';


import {
  doDeleteComment,
  getCommentList,
  getShareCommentList,
  doDeleteShareComment,
} from './service';
import './style.less';

const { confirm } = Modal;

const PAGE_SIZE = 15;
const ACTIVE_TYPE = {
  NEW_COMMENT: '-1',
  REPLY_COMMENT: '-2',
};

const Editor = inPhone() ? null : React.lazy(() => import('./Editor'));


/**
 *
 * @param {object} reqUrlObj 由第三方传过来的requestUrl。
 * eg:{
 *  deleteUrl: '',
 *  submitUrl: '',
 *  commentListUrl: '',
 * }
 * @param {string} pageId pageId,必传
 * @param {object} user 当前用户信息，必传
 * eg:{
 *  ldap: '',
 *  avatar: '',
 *  name: '',
 * }
 */

export const formatNode = (nodeItem) => {
  const { commentParentId, commentId } = nodeItem;
  return {
    key: commentId,
    parentId: commentParentId,
    ...nodeItem,
  };
};

export default (props) => {
  let propsRes = props.appProps || props;

  const {
    pageId,
    isShare,
    shareType,
    shareId,
    user,
    reqUrlObj = {},
    eventHandler = {},
  } = propsRes;
  const [totalCount, setTotalCount] = useState(null);
  const treeFn = useTreeData(formatNode);
  const { openGuide } = useSelector((state) => state.guide);
  const {
    treeData: commentList,
    setData,
    addNode,
    deleteNode,
    updateNode,
    getNodeByKey,
    getChildKeysById,
  } = treeFn;
  const [isUpdate, setIsUpdate] = useState(false);
  const [operateCommentId, setOperateCommentId] = useState(null); // 用来获取发送成功的评论
  const [activeCommentId, setActiveCommentId] = useState(null); // 用于获取激活的是哪个评论相关的Editor
  const [editorObj, setEditorObj] = useState({
    containerSelector: '',
    commentId: '',
    replayId: '',
    replayUserId: '',
    content: '',
    submitUrl: reqUrlObj.submitUrl,
    height: '',
  });

  useEffect(() => {
    initData();
    console.log(eventHandler, 'eventHandler');
    if (eventHandler?.initTreeFn) {
      eventHandler.initTreeFn(treeFn);
    }
  }, []);

  const initData = async () => {
    let list = [];
    try {
      if (isShare) {
        list = await getShareCommentList(
          {
            pageId,
            shareType,
            shareId,
          },
          reqUrlObj.commentListUrl ?? null,
        );
      } else {
        list = await getCommentList(
          {
            pageId,
            // pageNum: 0,
            // pageSize: PAGE_SIZE,
          },
          reqUrlObj.commentListUrl ?? null,
        );
      }
      setData(list.items);
    } catch (error) {
    } finally {
      if (eventHandler?.handleMounted) {
        eventHandler?.handleMounted();
      }
      let msgId = parseUrlSearch('msgId');
      if (msgId) {
        let operateDiv = document.getElementById(msgId);
        operateDiv?.scrollIntoView(true);
      }
    }
  };

  useEffect(() => {
    let count = commentList.length;
    commentList.forEach((item) => {
      count += item?.children.length ?? 0;
    });
    setTotalCount(count);
  }, [commentList]);

  const onDeleteComment = (commentId) => {
    const url = reqUrlObj.deleteUrl ?? null;
    if (isShare) {
      doDeleteShareComment(
        {
          pageId,
          shareType,
          shareId,
          commentId,
        },
        url,
      );
    } else {
      doDeleteComment(commentId, url);
    }
    deleteNode(commentId);
  };

  const showAbandonConfirm = (editor, activeCommentIdTemp, doneCallback) => {
    confirm({
      title: intl.t('确认放弃未发送的评论/回复吗?'),
      // icon: <ExclamationCircleOutlined />,
      content: intl.t(
        '你有还未发送的评论，添加新的评论将默认放弃未发送的评论。',
      ),
      okText: intl.t('放弃'),
      cancelText: intl.t('取消'),
      wrapClassName: 'confirm-modal',
      icon: <i />,
      onOk() {
        setEditorObj(editor);
        setActiveCommentId(activeCommentIdTemp);
        doneCallback();
      },
      onCancel() {},
    });
  };

  const onAddNewComment = (typeName) => {
    window.__OmegaEvent
      && window.__OmegaEvent('ep_dkpc_essaycomment_addcomment_ck', '', {
        type: isShare
          ? intl.t('分享态{typeName}', { typeName })
          : intl.t('预览态{typeName}', { typeName }),
      });

    let editor = {
      containerSelector: '#add-comment-entry',
      content: '',
      submitUrl: reqUrlObj.submitUrl,
      foceUpdate: Math.random(),
    };

    let activeCommentIdCur = ACTIVE_TYPE.NEW_COMMENT;

    let doneCallback = () => {
      let bottomDiv = document.getElementById('bottom-div-add-comment');
      bottomDiv.scrollIntoView(false);
    };

    if (editorObj.containerSelector.length > 0) {
      showAbandonConfirm(editor, activeCommentIdCur, doneCallback);
      return;
    }

    setEditorObj(editor);
    setActiveCommentId(activeCommentIdCur);
    doneCallback();
  };

  const onReplyComment = (isSub, commentId, orgMemberId, commentParentId) => {
    const replayId = isSub ? commentParentId : commentId;
    let editor = {
      containerSelector: `#comment${commentId}reply`,
      content: '',
      // 一级评论的id
      replayId,
      replayUserId: isSub ? orgMemberId : null,
      replayReplayId: isSub ? commentId : null, // 添加完二级回复，需要知道回复的回复的位置
      submitUrl: reqUrlObj.submitUrl,
    };

    let activeCommentIdCur = ACTIVE_TYPE.REPLY_COMMENT;

    if (editorObj.containerSelector.length > 0) {
      showAbandonConfirm(editor, activeCommentIdCur, () => {});
      return;
    }

    setEditorObj(editor);
    setActiveCommentId(activeCommentIdCur);
  };

  const onUpdateComment = (id, content) => {
    let editorEle = document.querySelector(`#comment_${id} .item-comment`);
    let editorEleHeight = editorEle?.clientHeight;
    let editor = {
      containerSelector: `#comment${id}editor`,
      commentId: id,
      content,
      height: editorEleHeight,
      submitUrl: reqUrlObj.submitUrl,
    };

    let activeCommentIdCur = `comment_${id}`;

    let doneCallback = () => {
      setIsUpdate(true);
    };

    if (editorObj.containerSelector.length > 0) {
      showAbandonConfirm(editor, activeCommentIdCur, doneCallback);
      return;
    }

    setActiveCommentId(activeCommentIdCur);
    setEditorObj(editor);
    doneCallback();
  };

  const submitCommentCallback = (newComment) => {
    const { commentId, commentParentId } = newComment;

    setActiveCommentId(null);
    setEditorObj({
      containerSelector: '',
    });

    // 回复排序逻辑：最新添加的永远在最后
    if (editorObj?.replayId) {
      // 回复评论
      addNode(commentParentId, newComment);
      let operateDiv = document.getElementById(`comment_${commentId}`);
      operateDiv.scrollIntoView(false);
    } else if (isUpdate) {
      // 编辑评论
      updateNode(commentId, newComment);
      setIsUpdate(false);
    } else {
      // 添加评论
      addNode('0', newComment);
      let operateDiv = document.getElementById(`comment_${commentId}`);
      operateDiv.scrollIntoView(false);
    }

    setOperateCommentId(commentId);
  };

  const onCancel = () => {
    setActiveCommentId(null);
    setIsUpdate(false);
    setEditorObj({
      containerSelector: '',
      content: '',
    });
  };

  const _commentItemContent = (commentItem, isSub) => {
    return (
      <div
        className={cls('comment-item-wrap-style', {
          animated: operateCommentId === commentItem.commentId,
        })}
      >
        <div
          className={cls({
            hide: activeCommentId === `comment_${commentItem.commentId}`,
          })}
          id={`comment_${commentItem.commentId}`} // 用于跳转定位
        >
          <CommentCard
            commentItem={commentItem}
            user={user}
            isSub={isSub}
            isShare={isShare}
            onReplyComment={onReplyComment}
            onDeleteComment={onDeleteComment}
            onUpdateComment={onUpdateComment}
          />
        </div>

        {
          <div
            className={cls('editor-wrap', {
              sub: isSub,
            })}
          >
            <img
              src={user.avatar}
              alt=''
              className={cls('editor-avatar', {
                hide:
                  !isUpdate
                  || activeCommentId !== `comment_${commentItem.commentId}`,
              })}
            />

            <div
              id={`comment${commentItem.commentId}editor`}
              className='right-active-editor'
             />
          </div>
        }
      </div>
    );
  };

  const handleClick = () => {
    openGuide && openGuide('1-4-1029-comment');
  };

  if (!Editor) return null;

  return (

    <div
      className={'comment-content'}
      id={'comment-guide'}
      onClick={handleClick}>

      <div className='cooper-component-comment'>
        <Editor
          isShare={isShare}
          shareType={shareType}
          shareId={shareId}
          pageId={pageId}
          containerSelector={editorObj.containerSelector}
          commentId={editorObj.commentId ?? null}
          content={editorObj.content ?? null}
          replayId={editorObj.replayId ?? null}
          replayUserId={editorObj.replayUserId ?? null}
          height={editorObj.height ?? null}
          afterDefaultSubmit={submitCommentCallback}
          onCancel={onCancel}
          foceUpdate={editorObj.foceUpdate}
      />

        {commentList?.length > 0 && (
          <div className='header-comment'>
            <div className='header-left'>
              <span className='left-text'>{intl.t('全文评论')}</span>

              {totalCount && (
                <>
                  <span> · </span>

                  <span className='let-num'>{totalCount}</span>
                </>
              )}
            </div>

            <button
              className='add-btn'
              onClick={() => onAddNewComment(intl.t('添加评论'))}
          >
              <img
                src={CreateCommentIcon}
                className='add-btn-icon' />

              <span className='add-btn-text'>{intl.t('添加评论')}</span>
            </button>
          </div>
        )}

        {commentList?.map((item, index) => {
          return (
            <React.Fragment key={item.commentId}>
              {_commentItemContent(item, false)}

              <div
                id={`comment${item.commentId}reply`}
                className={cls('reply-comment', {
                  sub: true,
                })}
             />

              {item?.children?.map((sub, i) => {
                return (
                  <React.Fragment key={sub.commentId}>
                    {_commentItemContent(sub, true)}

                    <div
                      id={`comment${sub.commentId}reply`}
                      className={cls('reply-comment', {
                        'reply-comment-sub': true,
                      })}
                   />
                  </React.Fragment>
                );
              })}
            </React.Fragment>
          );
        })}

        <div className='none-comment'>
          <img
            src={user.avatar}
            alt={intl.t('用户本人头像')}
            className='user-avatar'
        />

          <div className='right-active-editor'>
            <p
              className={cls('none-tips', {
                hide: activeCommentId === ACTIVE_TYPE.NEW_COMMENT,
              })}
              onClick={() => onAddNewComment(intl.t('评论全文'))}
          >
              {intl.t('评论全文')}
            </p>

            <div id='add-comment-entry' />
          </div>
        </div>

        <div id='bottom-div-add-comment' />

      </div>
    </div>
  );
};
