.comment-item-content{
  display: flex;
  justify-content: flex-start;
  font-family: PingFangSC-Regular, PingFang SC;
  font-size: 14px;
  border-radius: 4px;

  .item-avatar{
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 12px;
    border: 1px solid #F4F4F4;
  }

  .item-main{
    width: 100%;

    .item-title{
      display: flex;
      align-items: center;
      font-weight: 400;

      .item-name{
        font-weight: 500;
        color: #333333;
        line-height: 20px;
        font-family: PingFangSC-Medium, PingFang SC;
      }
      .item-time{
        color: rgba(34, 42, 53, 0.4);
        margin-left: 10px;
      }
      .item-edited{

      }
    }
    .item-comment{
      margin-top: 8px;
      color: #333333;
      line-height: 22px;
      word-break: break-all;
      .disable-preview {
        display: none !important;
      }
      a {
        color: #3F81FF;
      }
      a.didoc-editor-pop-up-element-download {
        color: #333;
      }
      .image-view {
        display: block;
        position: relative;
        width: fit-content;
        cursor: pointer;
        i {
          display: none;
          font-size: 22px;
          position: absolute;
          // box-shadow: 0px 0px 4px 0px rgb(0 0 0 / 10%);
          top: 0;
          right: 0;
        }
        &:hover {
          i {
            display: block;
          }
        }
      }
      .didoc_editor_file_link_container {
        position: relative;
        .upload_file_container_operation {

          left: 30px;
        }
        .upload_file_container_operation_container {
          &>* {
            padding: 2px 6px;
            display: block;
            border-radius: 4px;
            color: #333;
            &:hover {
              cursor: pointer;
              background: #F0F1F2;
            }
          }
        }
      }
    }

    .item-operate{
      margin-top: 10px;
      display: flex;
      align-items: center;
      margin-left: -5px;
      .operate-icon-wrap{
        border-radius: 2px;
        padding: 5px;
        text-align: center;
        vertical-align: middle;
        margin-right: 6px;
        cursor: pointer;
        &:hover {
          background: #F2F3F4;
        }
        .operate-icon{
          color: #252525;
          width: 16px;
          height: 16px;
          display: inherit;
        }
      }

    }
  }


}
.confirm-wrap-del{
  width: 230px;
  .ant-popover-inner-content{
    padding: 8px 16px;
    .ant-popover-message-title{
      margin-left: 0;
      font-weight: 500;
      font-size: 16px;
      padding-left: 0;
    }
  }


}
.reply-pre{
  .reply-pre-at{
    color: #047FFE;
  }
}

.sub{
  margin-left: 44px;
}
.main[language="zh-CN"] {
  .comment-item-content {
    .didoc-editor-pop-up-element-preview:hover {
      &::after {
        content: '预览';
        position: absolute;
        color: #fff;
        background: rgba(0, 0, 0, 0.8);
        padding: 4px 8px;
        opacity: 0.9;
        font-size: 13px;
        line-height: 18px;
        border-radius: 3px;
        transform: translate(-30px, -38px);
        white-space: nowrap;
      }
    }
    .didoc-editor-pop-up-element-download:hover {
      &::after {
        content: '下载';
        position: absolute;
        color: #fff;
        background: rgba(0, 0, 0, 0.8);
        padding: 4px 8px;
        opacity: 0.9;
        font-size: 13px;
        line-height: 18px;
        border-radius: 3px;
        transform: translate(-30px, -38px);
        white-space: nowrap;
      }
    }
  }
}
.main[language="en-US"] {
  .comment-item-content {
    .didoc-editor-pop-up-element-preview:hover {
      &::after {
        content: 'Preview';
        position: absolute;
        color: #fff;
        background: rgba(0, 0, 0, 0.8);
        padding: 4px 8px;
        opacity: 0.9;
        font-size: 13px;
        line-height: 18px;
        border-radius: 3px;
        transform: translate(-30px, -38px);
        white-space: nowrap;
      }
    }
    .didoc-editor-pop-up-element-download:hover {
      &::after {
        content: 'Download';
        position: absolute;
        color: #fff;
        background: rgba(0, 0, 0, 0.8);
        padding: 4px 8px;
        opacity: 0.9;
        font-size: 13px;
        line-height: 18px;
        border-radius: 3px;
        transform: translate(-30px, -38px);
        white-space: nowrap;
      }
    }
  }
}
