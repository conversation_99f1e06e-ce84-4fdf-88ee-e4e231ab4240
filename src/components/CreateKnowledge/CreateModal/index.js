import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import { checkNameErrorMsg } from '@/utils';
import { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { Button, Form, Input, Modal, message } from 'antd';
import groupChoosenIcons from '@/constants/groupIconChoose';
import { createKnowledge } from '@/service/knowledge/createKnowledgeModal';
import * as styles from './style.module.less';

const { TextArea } = Input;
const cx = classBind.bind(styles);

const CreateModal = ({ visible, onCloseModal, isReplaceNewTab, onOkCb }) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [chooseIcon, setChooseIcon] = useState(
    'http://img-ys011.didistatic.com/static/cooper_cn/do1_cSnePNIn4llvyBMjFjxS',
  );
  const [spaceNameCheckMsg, setSpaceNameCheckMsg] = useState('');
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const spaceName = Form.useWatch('spaceName', form);
  const { knowledgeDetail } = useSelector((state) => state.KnowledgeData);
  const { teamDetail, currentSpaceType, currSpaceId } = useSelector(
    (state) => state.TeamData,
  );
  const { getFeatures, getPersonFeatures } = dispatch.TeamData;
  const [desc, saveDesc] = useState('');
  const { teamId } = useParams();
  const isInPersonRoute = currentSpaceType === 'PERSONAL_SPACE';

  useEffect(() => {
    if (isInPersonRoute) {
      form.setFieldsValue({
        spaceName: intl.t('个人空间知识库'),
      });
      return;
    }
    if (teamDetail.id == (teamId || knowledgeDetail.belongSpaceId)) {
      form.setFieldsValue({
        spaceName: intl.t('{slot0}知识库', {
          slot0: teamDetail.name?.slice(0, 97),
        }),
      });
    }
  }, [isInPersonRoute, teamDetail, teamId, knowledgeDetail, form, visible]);

  const saveSetup = async () => {
    setConfirmLoading(true);
    const metisSpaceName = form.getFieldValue('spaceName').trim();
    if (spaceNameCheckMsg.length > 0) {
      message.error(spaceNameCheckMsg);
      return;
    }
    try {
      const res = await createKnowledge({
        exDesc: form.getFieldValue('exDesc'),
        exPicture: chooseIcon,
        metisSpaceName,
        spaceId: currSpaceId,
        allowShare: false,
        function: isReplaceNewTab ? 'KNOWLEDGE_NEW' : 'KNOWLEDGE',
      });

      message.success(intl.t('新建成功'));
      onOkCb && onOkCb(res.knowledgeId);

      isInPersonRoute ? getPersonFeatures() : getFeatures(res.spaceId);
      navigate(`/knowledge/${res.knowledgeId}/home`);
      onCloseModal();
      reset();
    } catch (error) {
      if (error.errorCode === 1255) {
        message.error(error.errorMessage);
      } else {
        message.error(intl.t('新建失败，请稍后再试'));
      }
    } finally {
      setConfirmLoading(false);
    }
  };

  const handleValuesChange = () => {
    const name = form.getFieldValue('spaceName');
    const descValue = form.getFieldValue('exDesc');
    let s = checkNameErrorMsg(name);
    setSpaceNameCheckMsg(s);
    saveDesc(descValue);
  };

  const reset = () => {
    setChooseIcon(
      'http://img-ys011.didistatic.com/static/cooper_cn/do1_cSnePNIn4llvyBMjFjxS',
    );
    setSpaceNameCheckMsg('');
    setConfirmLoading(false);
    form.resetFields();
    saveDesc('');
  };

  const getChooseIcon = (img) => {
    setChooseIcon(img);
  };

  const handleCancel = () => {
    onCloseModal && onCloseModal();
    reset();
  };

  const okBtnDisabled = !spaceName || spaceName.length === 0 || spaceNameCheckMsg !== '';

  return (
    <Modal
      destroyOnClose
      footer={null}
      visible={visible}
      onCancel={handleCancel}
      wrapClassName={cx('createKnowledge')}
      maskClosable={false}
      width={620}
      afterClose={() => form.resetFields()}
      closeIcon={<></>}
      centered
    >
      <div className={cx('titleArea')}>
        <div className={cx('title')}>{intl.t('新建知识库')}</div>
        <div
          className={cx('backIconWrap')}
          onClick={handleCancel}>
          <i className={`${cx('ant-input-clear-icon', 'dk-iconfont')}`} />
        </div>
      </div>
      <div className={cx('basicSetup')}>
        <Form
          form={form}
          preserve={false}
          className={`${styles.form} dk-ant-form-item`}
          onValuesChange={handleValuesChange}
        >
          <div className={cx('mainTitle')}>
            {intl.t('知识库名称')}

            <span className={cx('required')}>*</span>
          </div>
          <div className={cx('nameItem')}>
            <Form.Item
              label=""
              name="spaceName"
              className={`${styles.nameInputWrap} knowLedge_InputInfo_nameInput`}
            >
              <Input
                allowClear
                maxLength={100}
                placeholder={intl.t('必填，上限100字')}
                className={cx({ nameInput: true })}
              />
            </Form.Item>
            <div className={cx('spaceNameCheckMsg', 'nameCheckMsg')}>
              {spaceNameCheckMsg !== '' && (
                <i
                  className={cx('dk-iconfont', 'dk-icon-cuowu', 'error-icon')}
                />
              )}
              {spaceNameCheckMsg}
            </div>
          </div>

          <div className={cx('mainTitle', 'avatarTitle')}>{intl.t('头像')}</div>
          <div className={cx('avatar-list')}>
            {groupChoosenIcons.map((item, index) => {
              return (
                <div
                  className={cx(
                    'iconBlock',
                    chooseIcon === item.value && 'iconBlockSeleted',
                  )}
                  key={index}
                >
                  <img
                    onClick={() => getChooseIcon(item.value)}
                    className={cx('icon')}
                    src={item.value}
                  />
                </div>
              );
            })}
          </div>
          <div className={cx('introTitle')}>
            {intl.t('简介')}

            <div className={cx('introTips')}>
              {desc?.length > 990 && `${desc?.length}/1000`}
            </div>
          </div>
          <Form.Item
            label=""
            name="exDesc">
            <TextArea
              className={cx({ introText: true })}
              maxLength={1000}
              placeholder={intl.t('选填，上限1000字')}
            />
          </Form.Item>

          <Form.Item>
            <div className={cx('buttonArea')}>
              <Button
                loading={confirmLoading}
                disabled={okBtnDisabled}
                onClick={saveSetup}
                type="primary"
                // className={cx({ ok: true, okDisabled: okBtnDisabled })}
              >
                {intl.t('新建')}
              </Button>
              <Button
                onClick={handleCancel}
                className={cx('cancel')}>
                {intl.t('取消')}
              </Button>
            </div>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default CreateModal;
