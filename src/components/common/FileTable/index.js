/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-05-16 14:12:50
 * @LastEditTime: 2023-05-29 14:23:24
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/components/common/FileTable/index.js
 *
 */
import { Table } from 'antd';
import * as styles from './style.module.less';
import classBind from 'classnames/bind';

const cx = classBind.bind(styles);


function FileTable(props) {
  return (
    <div className={cx('cooper-table-v2')}>
      <Table
        {...props}
        scroll={{ y: 1000 }}
        pagination={false}
        sticky={true}
        />
    </div>
  )
}

export default FileTable
