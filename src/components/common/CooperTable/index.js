/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-08-09 14:16:27
 * @LastEditTime: 2023-08-14 11:57:01
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/components/common/CooperTable/index.js
 *
 */
import { Table } from 'antd';
import classBind from 'classnames/bind';
import React from 'react';
import styles from './style.module.less';

const cx = classBind.bind(styles);
// TODO: 这个封装目前没啥用
const CooperTable = (props) => {
  return (
    <div
      className={cx('cooper-table-ordinary')}
      onScroll={props.onScroll}>
      <Table
        {...props}
        scroll={{ y: '270' }}
        pagination={false}
        showSorterTooltip={false}
      />
      {
        props.children
      }
    </div>
  );
};
export default CooperTable;
