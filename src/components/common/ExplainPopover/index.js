/**
 * 提示信息，通常文字比较多，带箭头、白色
 */
import classBind from 'classnames/bind';
import { Popover } from 'antd';
import * as styles from './style.module.less';


const cx = classBind.bind(styles);

const ExplainPopover = ({
  content,
  text,
  className = '',
  getPopupContainer,
  placement,
  align
}) => {
  return <Popover
    placement={placement ?? 'top'}
    align={align}
    arrow={true}
    content={content}
    overlayClassName={cx(`${className} cooper_popover_Msg_arrow`)}
    getPopupContainer={getPopupContainer || null}
    >
    <span className={cx('text-main')}>
      <i
        className={cx('dk-icon-shuoming3px', 'cursor', 'dk-iconfont', 'help-tip')}
      />
      <span>{text}</span>
    </span>
  </Popover>
}

export default ExplainPopover;
