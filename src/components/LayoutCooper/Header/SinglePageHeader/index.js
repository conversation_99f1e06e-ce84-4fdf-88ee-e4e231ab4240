
import Logo from '@/components/LayoutCooper/Aside/Logo';
import IconFamily from '@/components/LayoutCooper/IconFamily';
import CooperSearch from '@/components/CooperSearch';
import classBind from 'classnames/bind';
import * as styles from './style.module.less';
import { useEffect } from 'react';

const cx = classBind.bind(styles);

function SinglePageHeader() {
  return (
    <div className={cx('single-page-header')}>
      <div className={cx('left')}>
        <Logo />
      </div>
      <div className={cx('middle')}>
        <CooperSearch />
      </div>
      <div className={cx('right')}>
        <IconFamily />
      </div>
    </div>
  );
}

export default SinglePageHeader;
