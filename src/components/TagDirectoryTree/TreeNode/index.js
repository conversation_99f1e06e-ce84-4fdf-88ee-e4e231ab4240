/*
 * @Date: 2023-11-09 16:00:00
 * @LastEditors: guanzhong
 * @LastEditTime: 2023-11-10 17:22:24
 * @FilePath: /knowledgeforge/src/components/TagDirectoryTree/TreeNode/index.js
 * @Description: 描述文件功能
 */
import classBind from 'classnames/bind';
import { useParams } from 'react-router-dom';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import styles from '@/components/serviceComponents/CatalogTree/TreeNode/style.module.less';
import { useContext } from 'react';

const cx = classBind.bind(styles);

function TreeNode({
  nodeData,
}) {
  const { knowledgeId } = useContext(LayoutContext);
  const { teamId } = useParams();

  return (
    <span className={cx('tree-node-item-wrap')}>
      <a
        className={cx('item-name')}
        href={
          teamId
            ? `/team-file/${teamId}/knowledge/${knowledgeId}/${nodeData.pageId}`
            : `/knowledge/${knowledgeId}/${nodeData.pageId}`
        }
      >
        <span
          className={cx('item-name-link')}
          key={nodeData.key}
        >{nodeData.title}</span>
      </a>
    </span>
  );
}

export default TreeNode;
