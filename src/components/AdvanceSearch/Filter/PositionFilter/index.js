import { intl } from 'di18n-react';
import { useState, useRef, useEffect, useMemo } from 'react';
import { Checkbox } from 'antd';
import { FileType } from '@/constants/cooper';
import classBind from 'classnames/bind';
import * as styles from '../style.module.less';
import SelectAnyOption from '@/components/common/SelectAnyOption';


const cx = classBind.bind(styles);

const PositionFilter = (props) => {
  const {
    isHidden,
    scopeType, // 分为 space metis两种
    spaceType,
    currentId,
    onClickReset,
    onChangeInputValue,
    defaultValue,
    isContain,
    changeContainDK,
  } = props;


  const [reset, setReset] = useState(false);

  return (

    <div
      className={cx('filter-item', 'position-filter-item', {
        hidden: isHidden,
      })}
      >
      <div className={cx('title-has-reset')}>
        <p>{intl.t('位置')}</p>
        {scopeType && (
          <div
            className={cx('reset')}
            onClick={() => {
              onClickReset();
              setReset(reset + 1);
            }}
            >
            {intl.t('重置')}
          </div>
        )}
      </div>
      <SelectAnyOption
        placeholder={intl.t('输入空间/知识库名称')}
        reset={reset}
        onChangeInputValue={onChangeInputValue}
        defaultValue={defaultValue}
        currentId={currentId}
      />
      {scopeType === 'space' && ![FileType.PERSONAL_SPACE, 'PERSON_SPACE'].includes(spaceType) && <div
        className={cx('attach')}>
        <Checkbox
          checked={isContain}
          onChange={changeContainDK}>
          <span className={cx('text')}>{intl.t('包含关联知识库内容')}</span>
        </Checkbox>
      </div>}

    </div>
  );
};

export default PositionFilter;
