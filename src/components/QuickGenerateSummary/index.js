import React from 'react';
import { createPortal } from 'react-dom';
import * as styles from './style.module.less';
import classBind from 'classnames/bind';

const cx = classBind.bind(styles)

const IsInIframe = window.self !== window.top;

function QuickGenerateSummary({ onClick }) {
  const omegaTrack = () => {
    window.__OmegaEvent('ep_cooper_knowledge_aigc_summary_ck', '', {
      content_type: 'shortcut',
      platform: 'knowledge',
    });
  }

  const handleClick = () => {
    omegaTrack();
    onClick()
  }

  if (IsInIframe) {
    return null;
  }

  return createPortal(
    <div
      className={cx('livechat-quick-generate-summary')}
      onClick={handleClick}>
      <div className={cx('livechat-quick-generate-summary-content')}>
        <img
          style={{ width: 16, height: 16 }}
          src="https://s3-conveyor.didiglobal.com/didoc-upload-image-prod/1694681227714/xiaodi.png"
          alt=""/>
        <span className={cx('livechat-quick-generate-summary-content-text')}>智能摘要</span>
      </div>
    </div>,
    document.body,
  );
}

export default QuickGenerateSummary;
