import classBind from 'classnames/bind';
import { useCallback, useEffect, useState } from 'react';
import { Modal, Form, Input } from 'antd';
import { intl } from 'di18n-react';

import { TEAM_ADMIN, TEAM_OWNER } from '@/utils/cooperutils';
import styles from './style.module.less';

const layout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

const cx = classBind.bind(styles);

function isValidURL(str) {
  // const urlPattern = /^(?:(?:https?|ftp):\/\/)(?:[\w-]+\.)+[a-z]{2,6}(?:\/[^/#?]+)*\/?(?:#[\w-]+)?$/;
  const urlPattern = /https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z]{2,}\b(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)/gi;
  return urlPattern.test(str);
}

function FormTitle(props) {
  const { name, form, pair } = props;

  const handleChange = useCallback(
    (e) => {
      const pairValue = form.getFieldValue(pair);
      if (!pairValue && !e.target.value) {
        form.validateFields();
      }
    },
    [form],
  );

  return (
    <Form.Item
      name={name}
      label={intl.t('标题')}
      className={cx('form-item-title')}
      rules={[
        {
          pattern: /^[^$'?\\*:;"%\\/|\\]+$/,
          message: (
            <span>
              <i
                className={cx('dk-iconfont', 'dk-icon-cuowu', 'delete-icon')}
              />
              {intl.t('名称不能包含{slot0}等特殊字符', {
                slot0: '$\'?*:;"%/|\\',
              })}
            </span>
          ),
        },
        {
          validator: async (_, val) => {
            const pairValue = form.getFieldValue(pair);
            if (!!pairValue && !val) {
              return Promise.reject();
            }
            return Promise.resolve();
          },
          message: (
            <span>
              <i
                className={cx('dk-iconfont', 'dk-icon-cuowu', 'delete-icon')}
              />
              {intl.t('标题不能为空')}
            </span>
          ),
        },
      ]}
    >
      <Input
        allowClear
        placeholder={intl.t('请输入内容标题')}
        onChange={handleChange}
        maxLength={50}
      />
    </Form.Item>
  );
}

function FormLink(props) {
  const { name, form, pair } = props;

  const handleChange = useCallback(
    (e) => {
      const pairValue = form.getFieldValue(pair);
      if (!pairValue && !e.target.value) {
        form.validateFields();
      }
    },
    [form],
  );

  return (
    <Form.Item
      name={name}
      label={intl.t('链接')}
      className={cx('form-item-link')}
      rules={[
        {
          validator: async (_, value) => {
            const pairValue = form.getFieldValue(pair);
            if (!!pairValue && !value) {
              return Promise.reject();
            }
            return Promise.resolve();
          },
          message: (
            <span>
              <i
                className={cx('dk-iconfont', 'dk-icon-cuowu', 'delete-icon')}
              />
              {intl.t('链接不能为空')}
            </span>
          ),
        },
      ]}
    >
      <Input
        allowClear
        placeholder={intl.t('请输入链接地址')}
        onChange={handleChange}
      />
    </Form.Item>
  );
}

function SpaceRecommend(props) {
  const { recommendation, role, saveRecommendation } = props;
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isHover, saveIsHover] = useState(false);

  const handleOk = useCallback(() => {
    form.validateFields().then((values) => {
      const arr = [];
      if (values.firstLink && values.firstTitle) {
        arr[0] = {
          title: values.firstTitle,
          url: values.firstLink,
        };
      }

      if (values.secondLink && values.secondTitle) {
        arr[1] = {
          title: values.secondTitle,
          url: values.secondLink,
        };
      }

      if (values.thirdLink && values.thirdTitle) {
        arr[2] = {
          title: values.thirdTitle,
          url: values.thirdLink,
        };
      }

      saveRecommendation(arr);
      setIsModalOpen(false);
    });
  }, [saveRecommendation]);

  const handleCancel = useCallback(() => {
    setIsModalOpen(false);
    form.resetFields();
  }, []);

  useEffect(() => {
    if (recommendation?.value && isModalOpen) {
      const first = recommendation?.value[0];
      const second = recommendation?.value[1];
      const third = recommendation?.value[2];
      form.setFieldsValue({
        firstTitle: first?.title || '',
        firstLink: first?.url || '',
        secondTitle: second?.title || '',
        secondLink: second?.url || '',
        thirdTitle: third?.title || '',
        thirdLink: third?.url || '',
      });
    }
  }, [recommendation, isModalOpen]);

  return (
    <div
      className={cx('space-detail-card')}
      onMouseEnter={() => saveIsHover(true)}
      onMouseLeave={() => saveIsHover(false)}
    >
      <div className={cx('card-title')}>
        <span>{intl.t('内容推荐')}</span>
        {isHover && (role === TEAM_ADMIN || role === TEAM_OWNER) && (
          <span
            className={cx('card-title-edit')}
            onClick={() => setIsModalOpen(true)}
          >
            <i className={cx('dk-iconfont', 'dk-icon-shezhi4', 'edit-icon')} />
            <span>{intl.t('设置')}</span>
          </span>
        )}
      </div>
      {recommendation?.value && recommendation?.value.length > 0 ? (
        <div className={cx('card-content')}>
          {recommendation?.value.map((item, idx) => {
            if (item?.title) {
              return (
                <a
                  key={idx}
                  href={isValidURL(item.url) ? item.url : `https://${item.url}`}
                  className={cx('card-content-item')}
                  target="_blank"
                >
                  {item.title}
                </a>
              );
            }
            return null;
          })}
        </div>
      ) : (
        <div className={cx('card-no-data')}>{intl.t('暂无推荐内容')}</div>
      )}

      <Modal
        width={620}
        centered={true}
        destroyOnClose={true}
        className={cx('modal-setting')}
        title={intl.t('设置推荐内容')}
        visible={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        cancelText={intl.t('取消')}
        okText={intl.t('确认')}
        closeIcon={
          <i
            className={cx(
              'dk-iconfont',
              'dk-icon-guanbi',
              'modal-setting-close',
            )}
          />
        }
      >
        <Form
          form={form}
          layout={layout}
          autoComplete="off">
          <div className={cx('recommend-item')}>
            <div className={cx('recommend-order')}>1</div>
            <div className={cx('recommend-content')}>
              <FormTitle
                name="firstTitle"
                pair="firstLink"
                form={form} />

              <FormLink
                name="firstLink"
                pair="firstTitle"
                form={form} />
            </div>
          </div>
          <div className={cx('recommend-divider')} />
          <div className={cx('recommend-item')}>
            <div className={cx('recommend-order')}>2</div>
            <div className={cx('recommend-content')}>
              <FormTitle
                name="secondTitle"
                pair="secondLink"
                form={form} />

              <FormLink
                name="secondLink"
                pair="secondTitle"
                form={form} />
            </div>
          </div>
          <div className={cx('recommend-divider')} />
          <div className={cx('recommend-item')}>
            <div className={cx('recommend-order')}>3</div>
            <div className={cx('recommend-content')}>
              <FormTitle
                name="thirdTitle"
                pair="thirdLink"
                form={form} />

              <FormLink
                name="thirdLink"
                pair="thirdTitle"
                form={form} />
            </div>
          </div>
        </Form>
      </Modal>
    </div>
  );
}

export default SpaceRecommend;
