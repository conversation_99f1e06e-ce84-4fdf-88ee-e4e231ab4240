import { TEAM_ADMIN, TEAM_OWNER } from '@/utils/cooperutils';
import { Button, Input, Modal } from 'antd';
import classBind from 'classnames/bind';
import { useCallback, useState, useEffect } from 'react';
import { once } from 'lodash-es';
import { intl } from 'di18n-react';
// import { usePrompt } from '@/hooks/usePrompt';

import styles from './style.module.less';

const cx = classBind.bind(styles);

const { TextArea } = Input;
const { confirm } = Modal;

function SpaceAnnouncement(props) {
  const { announcement, role, saveAnnouncement } = props;
  const [isEdit, saveIsEdit] = useState(false);
  const [announcementValue, saveAnnouncementValue] = useState(
    announcement?.value || '',
  );
  const [isHover, saveIsHover] = useState(false);

  useEffect(() => {
    saveAnnouncementValue(announcement?.value || '');
    saveIsEdit(false);
  }, [announcement?.value]);

  const handleBlur = useCallback(() => {
    confirm({
      title: intl.t('退出编辑状态？'),
      icon: null,
      content: intl.t('还未保存公告内容，不保存将会丢失本次编辑内容'),
      okText: intl.t('保存本次修改'),
      cancelText: intl.t('不保存'),
      className: cx('confirm-modal'),
      centered: true,
      autoFocusButton: null,
      onOk() {
        saveAnnouncement(announcementValue);
        saveIsEdit(false);
      },
      onCancel() {
        saveIsEdit(false);
        saveAnnouncementValue(announcement?.value || '');
      },
    });
  }, [announcementValue, announcement?.value, saveAnnouncement]);

  const handelCancel = useCallback(() => {
    handleBlur();
  }, [handleBlur]);

  const handleSave = useCallback(() => {
    saveAnnouncement(announcementValue);
    saveIsEdit(false);
  }, [announcementValue]);

  const selectText = once((input) => {
    const endPos = input?.target?.value?.length;
    input.target.setSelectionRange(endPos, endPos);
  });

  return (
    <div
      className={cx('space-detail-card')}
      onMouseEnter={() => saveIsHover(true)}
      onMouseLeave={() => saveIsHover(false)}
    >
      <div className={cx('card-title')}>
        <span>{intl.t('公告')}</span>
        {isEdit && (
          <span>
            <Button
              size="small"
              className={cx('card-title-cancel')}
              onClick={() => {
                handelCancel();
              }}
            >
              {intl.t('取消')}
            </Button>
            <Button
              type="primary"
              className={cx('card-title-confirm')}
              size="small"
              onClick={handleSave}
            >
              {intl.t('保存')}
            </Button>
          </span>
        )}

        {!isEdit && isHover && (role === TEAM_ADMIN || role === TEAM_OWNER) && (
          <span
            className={cx('card-title-edit')}
            onClick={() => saveIsEdit(true)}
          >
            <i
              className={cx(
                'dk-iconfont',
                'dk-icon-zhongmingming1',
                'edit-icon',
              )}
            />
            <span>{intl.t('编辑')}</span>
          </span>
        )}
      </div>

      <div
        className={cx('card-content')}
        style={{ margin: isEdit ? '20px' : '12px 0 20px 20px' }}
      >
        {isEdit ? (
          <TextArea
            autoSize={true}
            autoFocus={true}
            value={announcementValue}
            className={cx('card-content-textarea')}
            onChange={(e) => saveAnnouncementValue(e.target.value)}
            placeholder={intl.t('请输入公告内容')}
            // onBlur={handleBlur}
            onFocus={selectText}
          />
        ) : (
          <>
            {announcement?.value ? (
              <TextArea
                disabled
                autoSize={true}
                value={announcementValue}
                className={cx('card-content-textarea-disabled')}
              />
            ) : (
              <div className={cx('card-no-data')}>
                {intl.t('管理员可以添加面向全员的公告信息')}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default SpaceAnnouncement;
