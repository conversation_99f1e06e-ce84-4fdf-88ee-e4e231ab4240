/* eslint-disable no-unreachable */
import { PageNotPublishIcon } from '@/assets/icon';
import ErrorTips from '@/components/ErrorTips';
import { inPhone } from '@/utils';
import { intl } from 'di18n-react';
import { useEffect, useState, useContext, useRef } from 'react';
import { message } from 'antd';
import classNames from 'classnames/bind';
import { connect } from 'react-redux';
import { fetchSignatureAndToken, initShimoExcel } from '@/utils/shimo-excel';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import * as styles from './style.module.less';

const cx = classNames.bind(styles);

const ShimoExcelBackup = (props) => {
  const { changeInitLoading, docInfo, isResizingAside } = props;
  const { knowledgeId, pageId } = useContext(LayoutContext);
  const containerRef = useRef(null);
  const [shimoSDK, setShimoSDK] = useState(null);
  const [versionMode, setVersionMode] = useState(false);

  useEffect(() => {
    changeInitLoading(false);
  }, [])

  const handleParamsChange = async () => {
    const Editor = shimoSDK.getEditor();
    const sheetId = await Editor.getActiveSheetId()
    this.props.history.push({
      pathname: this.props.history.location.pathname,
      query: { sheetId },
    })
  }

  const initEditor = async () => {
    const container = containerRef.current;
    const data = {
      fileId: docInfo.guid,
      signature: docInfo.accessToken,
      token: docInfo.uploadToken,
      id: pageId,
      container,
    };
    const shimoSDKCurr = await initShimoExcel(data);
    const Editor = shimoSDKCurr.getEditor();

    Editor.on('paramsChanged', handleParamsChange);
    container.addEventListener('keydown', Editor.print);
    if (inPhone()) {
      window.postMessage({
        type: 'initPreviewShimoExcelEnd',
      }, '*')
    } else {
      container.querySelector('iframe')?.contentWindow.postMessage({
        type: 'showVersion',
        version: Number(docInfo.latestVersion || 1),
      }, '*');
    }

    window.addEventListener('message', (e) => {
      const { data: dateCurr } = e;
      if (dateCurr?.type === 'initPreviewShimoExcelEnd') {
        changeInitLoading(false);
      }
    });
    setShimoSDK(shimoSDKCurr);
  };

  // 定时刷新token
  const refreshTimer = (wait = 60000) => {
    return setTimeout(async () => {
      console.log('*** refreshTimer');
      const { signature, token, ttl } = await fetchSignatureAndToken();
      shimoSDK.setCredentials({
        signature,
        // token
      });
      refreshTimer(Math.max(0, ttl - 1000 * 60 * 60));
    }, wait);
  };

  const showVersionList = () => {
    containerRef.current.querySelector('iframe')?.contentWindow.postMessage({
      type: 'showVersionList',
    }, '*');
    setVersionMode(true);
  };

  const closeVersionList = () => {
    containerRef.current.querySelector('iframe')?.contentWindow.postMessage({
      type: 'hideVersionList',
    }, '*');
    setVersionMode(false);
    shimoSDK.getEditor().hideHistory();
  }

  useEffect(() => {
    if (docInfo.pageName) {
      window.document.title = docInfo.pageName;
    }
  }, [docInfo.pageName]);

  useEffect(() => {
    let timer = shimoSDK ? refreshTimer() : null;

    return () => {
      console.log('*** refreshTimer -- clear');
      clearTimeout(timer);
    };
  }, [shimoSDK]);

  useEffect(() => {
    initEditor()
      .catch((e) => {
        message.error('文档初始化失败');
        console.error('*** initShimo', e);
      })
      .finally(() => {
        // changeInitLoading(false);
        containerRef.current.querySelector('iframe')?.contentWindow.postMessage({
          type: 'inKnowledge',
        }, '*');
      });
  }, []);

  return (
    <ErrorTips
      img={PageNotPublishIcon}
      title={
        <div className={cx('no-permission-tip')}>
          {intl.t('暂不支持通过访客链接访问')}
        </div>
      }
    />
  );

  return (
    <div className={cx('shimo-excel')}>
      {/* <div className={cx('shimo-excel-version')}>
        <button onClick={showVersionList}>版本记录</button>
      </div> */}
      {
        inPhone() && <h2 className={cx('shimo-excel-title')}>{docInfo.pageName}</h2>
      }
      <div className={cx('shimo-excel-content', { 'version-list-mode': versionMode })}>
        {
          versionMode && <button
            className={cx('hide-excel-version')}
            onClick={closeVersionList}>关闭版本记录</button>
        }
        <div ref={containerRef} id='shimo-excel-container' className={cx('shimo-excel-container', { 'stop-event': isResizingAside })}/>
      </div>
    </div>
  );
};

const ShimoExcel = (props) => {
  const { changeInitLoading } = props;

  useEffect(() => {
    changeInitLoading(false);
  }, [])

  return (
    <ErrorTips
      img={PageNotPublishIcon}
      title={
        <div className={cx('no-permission-tip')}>
          {intl.t('暂不支持通过访客链接访问')}
        </div>
      }
    />
  );
}

function mapStateToProps({ SharePage, asideDK }) {
  const { docInfo } = SharePage;
  const { isResizingAside } = asideDK;
  return {
    docInfo,
    isResizingAside,
  };
}

function mapDispatchToProps({ SharePage }) {
  const { changeInitLoading } = SharePage;
  return {
    changeInitLoading,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(ShimoExcel);
