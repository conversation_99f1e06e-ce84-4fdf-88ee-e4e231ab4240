/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-07-22 15:41:23
 * @LastEditTime: 2023-07-22 15:44:54
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/components/CooperFoldAuth/emptyInfo/index.js
 *
 */
import React from 'react';
import { intl } from 'di18n-react';
import classBind from 'classnames/bind';

import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const EmptyInfo = ({ desc = intl.t('暂无搜索结果') }) => {
  return (
    <div className={cx('emptyInfo')}>
      <img
        className={cx('pic')}
        src={require('../../../assets/icon/empty13.png')} />
      {intl.t(desc)}
    </div>
  );
};

export default EmptyInfo;
