import { message, Tooltip } from 'antd';
import { intl } from 'di18n-react';
import React from 'react';
import classBind from 'classnames/bind';
import PrimaryModal from '@/components/common/PrimaryModal';
import cooperConfirm from '@/components/common/CooperConfirm';
const TEAM_SPACE = 'TEAM_SPACE';
import { transferTo } from './action';
import UserSuggest from './user-suggest';
import * as styles from './index.module.less';
const cx = classBind.bind(styles);

class TransferCollaboration extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      transUser: [],
      styleVisible: true
    };
  }
  onSelect = transUser => {
    this.setState({transUser: [transUser]});
  }
  removeUser = (user) => {
    this.setState(preState => {
      return {transUser: preState.transUser.filter(item => (item.id !== user.id))};
    });
  }
  transfer = transUser => {
    if (transUser.length > 0) {
      this.setState({styleVisible: false});
      cooperConfirm({
        title: this.props.spaceType === TEAM_SPACE ? intl.t('成功转让文档所有者后，你将成为协作成员，继承团队空间的权限，{name} 将成为新的所有者，且拥有所有者全部权限，确认转让?', {
          name: transUser[0].title,
        }) : intl.t('成功转让文档所有者后，你将成为受邀成员，有查看、编辑和下载权限，{name} 将成为新的所有者，且拥有所有者全部权限，确认转让?', {
          name: transUser[0].title,
        }),
        onOk: () => {
          this.props.onClose();
          transferTo(this.props.docId, transUser[0]).then(() => {
            message.destroy();
            message.success(intl.t('转让成功'));
            this.props.success();
          });
        },
        onCancel: () => {
          this.setState({styleVisible: true});
        }
      });
    }
  }
  render() {
    const { transUser, styleVisible } = this.state;
    return (
      <PrimaryModal
        wrapClassName={'transfer-modal'}
        visible={styleVisible}
        width={580}
        title={
          <div className={cx('transfer-modal-title')}>
            <div className={cx('transfer-modal-title-text')}>{intl.t('转让文档所有权')}</div>
            <Tooltip
              placement='bottom'
              overlayClassName='apply-desc'
              title={
                this.props.spaceType === TEAM_SPACE ? (
                  <div>
                    <p>{intl.t('· 文档只能转让给团队成员，且有该文档的访问权限。')}</p>
                    <p>{intl.t('· 成功转让文档所有者后，你将成为协作成员，继承团队空间的权限，对方将成为新的所有者，且拥有所有者全部权限。')}</p>
                  </div>
                ) : (
                  <div>
                    <p>{intl.t('· 文档可以转让给任何人')}</p>
                    <p>{intl.t('· 成功转让文档所有者后，你将成为受邀成员，有查看、编辑和下载权限，对方将成为新的所有者，且拥有所有者全部权限。')}</p>
                  </div>
                )
              }>
              <img className={cx('transfer-modal-title-icon')} src={require('./icon/icon_wenhao_circle.png')} />
            </Tooltip>
          </div>
        }
        okText={intl.t('转让')}
        onCancel={this.props.onClose}
        onOk={() => this.transfer(transUser)}
      >
        <div className={cx('transfer-collaboration')}>
          <UserSuggest
            resourceId={this.props.docId}
            spaceType={this.props.spaceType}
            placeholder={intl.t('请输入用户或邮箱地址')}
            onSelect={this.onSelect}
          />
          {
            transUser.length > 0 && [
              transUser.map(user => (
                <div key={user.id} className={cx('transfer-user-lsit')}>
                  <div className={cx('transfer-user-item')}>
                    <div className={cx('avatar-container')}>
                      <img className={cx('avatar')} src={user.avatar} />
                    </div>
                    <div className={cx('transfer-user-info')}>
                      <div className={cx('transfer-user-title')}>{`${user.title} (${user.dep})`}</div>
                      <div className={cx('transfer-user-mail')}>{user.mail}</div>
                    </div>
                    <div className={cx('transfer-user-operation')} onClick={() => this.removeUser(user)}>
                      <div className={cx('transfer-user-delete')}>{intl.t('移除')}</div>
                    </div>
                  </div>
                </div>
              ))
            ]
          }
        </div>
      </PrimaryModal>
    );
  }
}

export default TransferCollaboration;
