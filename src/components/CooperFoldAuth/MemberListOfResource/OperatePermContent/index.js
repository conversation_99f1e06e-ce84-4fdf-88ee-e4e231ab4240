import React, { Fragment, useState, useMemo, useEffect } from 'react';
import classBind from 'classnames/bind';
import { intl } from 'di18n-react';
// import { getUserUidFromCookie, getUserNameFromCookie } from '../../../../common/utils';
import { getUserNameFromCookie, getUserUidFromCookie } from '@/utils';
import TransferCollaboration from '../TransferCollaboration';
import ApplyHigherPerm from '@/baseComponents/ApplyHigherPerm';
import BatchManagement from '../../BatchManagement';
import { ROLE_TYPE_DOC } from '../constants';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);
const currentUserUid = getUserUidFromCookie();
const currentUserName = getUserNameFromCookie();

const RenderEntryContent = ({
  onClick = () => { },
  transferable,
}) => (
  <div
    className={cx('change-page-owner')}
    onClick={onClick}
  >
    <img
      src={require('./applyPerm-icon.svg')}
      className={cx('owner-change-icon')}
    />
    <span className={cx('owner-change-text')}>
      {transferable ? intl.t('转让文档所有权') : intl.t('申请更多权限')}
    </span>
  </div>
);

const OperatePermContent = ({ spaceRole, userRole, info, resourceId, callback, toggleChildVisible, isDk ,resourceType,teamId,isInherit,showBatchMemberBtn,showInherit, showTransferable = true}) => {
  const [showTransferCollaboration, setShowTransferCollaboration] = useState();
  const [showApplyHighPerm, setShowApplyHighPerm] = useState();

  const transferable = useMemo(() => {
    return spaceRole === 'SPACE_OWNER' || userRole === ROLE_TYPE_DOC.Owner;
  }, [spaceRole])

  useEffect(() => {
    toggleChildVisible && toggleChildVisible(showTransferCollaboration || showApplyHighPerm)
  }, [showTransferCollaboration,showApplyHighPerm]);

  return <Fragment>
    <div className={cx('cooper-action')}>
      {
        showBatchMemberBtn && (
          <Fragment>
            <BatchManagement
              resourceId={resourceId}
              resourceType={resourceType}
              teamId={teamId}
              callback={callback}
              isInherit={isInherit}
            />
            {showInherit && <div className={cx('split-line')} />}
          </Fragment>
        )
      }
      {
        showTransferable && (transferable ? (
          <RenderEntryContent
            transferable={transferable}
            onClick={() => {
              setShowTransferCollaboration(true)
            }}
          />
        ) : (
          <RenderEntryContent
            transferable={transferable}
            onClick={() => {
              setShowApplyHighPerm(true)
            }}
          />
        ))
      }
    </div>
    {
      showTransferCollaboration && (
        <TransferCollaboration
          spaceType={info.spaceType}
          success={callback}
          docId={resourceId}
          onClose={() => {
            setShowTransferCollaboration(false)
          }}
        />
      )
    }
    {
      showApplyHighPerm && (
        <ApplyHigherPerm
          spaceType={info.spaceType}
          resourceId={resourceId}
          success={callback}
          isPersonalNormal={
            info.spaceType === 'PERSONAL_SPACE'
            && info.creatorOrgMemberId !== currentUserUid
          }
          onClose={() =>
           setShowApplyHighPerm(false)
          }
          isDk={isDk}
        />
      )
    }
  </Fragment>
};

export default OperatePermContent;
