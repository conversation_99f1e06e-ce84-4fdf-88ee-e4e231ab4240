import '@/iconfont/iconfont.css';
// import { masRecord } from '@didi/mas-web';
import React, { useMemo, useState, useEffect, Suspense } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Di18nProvider, intl } from 'di18n-react';
import { ConfigProvider, message } from 'antd';
import { Provider } from 'react-redux';
import { BrowserRouter, Route, Routes, Navigate } from 'react-router-dom';
import { getDcVersion, isBeforeVersion } from '@/utils/dc-helper';
import { inPhone, isSupportPreview, setCookie, isDC } from '@/utils';
import api from '@/utils/request/api/CooperApi';
import { getDkRoute } from '@/routes/route-dk';
import { getCombineTenantInfo } from '@/pages/cooper/TenantLogin/service';
import LayoutDK from '@/components/serviceComponents/Layout';
import <PERSON><PERSON>nnounceHalo from './components/CooperAnnounceHalo';
import enUS from '@/language/en-US.json';
import zhCN from '@/language/zh-CN.json';
import store, { history } from '@/model';
import { get } from '@/utils/request/cooper';
import './initLocal';
import { PhoneNoAccessIcon } from '@/assets/icon';
import asyncInitSdk from '@/service/knowledge/asyncInitSdk';
import { cleanLocalStorageIfNeeded } from '@/utils/localStorage';
import { EditorMicroUrl } from '@/constants/editor';
import { loadParcel } from '@/utils/tenon'
import { getCooperNoticeFromBackEnd, getCooperErrorFromBackEnd } from '@/service/cooper/index';
import '@/assets/style/antd-init-all.less';
import errorNotice from './components/LiteModal';
import ErrorBoundary from '@/components/ErrorBoundary';
import getBrowserInfo from '@/components/NeedUpdateBrowser/index'
import ErrorTips from '@/components/ErrorTipsDk';


const ExternalRedirect = React.lazy(() => import(/* webpackChunkName: "extRed" */ '@/components/ExternalRedirect'));

const locales = {
  'en-US': enUS,
  'zh-CN': zhCN,
};

function KnowledgeApp() {
  const [supportPreview, setSupportPreview] = useState(true);
  const [, setTenantId] = useState(null);
  const [dkRoute, setDkRoute] = useState([]);
  const [warningTip, setWarningTip] = useState({})
  const [haloClient, setHaloClient] = useState(null);
  // const [isPageVisible, setIsPageVisible] = useState(true);


  const handleWarnTips = (obj) => {
    setWarningTip(obj?.bytesObj?.data?.knowledge)
  }

  // const handleVisibilityChange = () => {
  //   setIsPageVisible(!document.hidden);
  // };

  useEffect(() => {
    import('@/service/knowledge/initHalo')
      .then((module) => {
        setHaloClient(module.default);
      })
      .catch((err) => {
        console.log('inithalo err', err);
      });
  }, []);


  // useEffect(() => {
  //   if (isPageVisible) {  // 页面可见
  //     // 当前未连接，或连接失败
  //     if (haloClient && haloClient?.haloState !== 2 && haloClient?.haloState !== 3) {
  //       haloClient.connect();
  //     }
  //   } else { // 页面不可见
  //     if (haloClient) {
  //       haloClient.close()
  //     }
  //   }
  // }, [isPageVisible])

  useEffect(() => {
    if (haloClient) {
      haloClient.addEventListener('publishMessage', handleWarnTips);
    }
    return () => {
      if (haloClient) {
        haloClient.removeEventListener('publishMessage', handleWarnTips);
      }
    };
  }, [haloClient]);


  useEffect(() => {
    setSupportPreview(
      isSupportPreview(window.location.pathname, isbeforevision),
    );
  }, [window.location.pathname]);

  const isbeforevision = useMemo(() => {
    if (inPhone()) {
      const dcVersion = getDcVersion();
      return isBeforeVersion(dcVersion, '3.29.0');
    }
    return true;
  }, [window.navigator.userAgent]);

  const mobileSupport = useMemo(() => {
    return inPhone() && !supportPreview;
  }, [supportPreview]);

  // 手动设置一次cookie-local_language，防止移动端访问报错
  // useEffect(() => {
  //   let locale = initLocal();
  //   try {
  //     const fn = inPhone() ? getMobileLang : (isDC() ? window.dcJSSDK?.getAppLanguage : null);
  //     console.log(fn, 'fnfnfnfnfnfnfn');
  //     if (fn) {
  //       fn().then((dcLocale) => {
  //         console.log(dcLocale, 'dcLocale 移动端locale');
  //         if (['zh-CN', 'en-US'].includes(dcLocale)) {
  //           locale = dcLocale;
  //         }
  //       });
  //     }
  //   } catch (error) {
  //     console.log(error, 'getMobileLang error');
  //   } finally {
  //     setCookie('local_language', locale);
  //   }
  // }, []);

  useEffect(async () => {
    await getAndSetTenant()

    // 监听页面关闭与打开
    // document.addEventListener('visibilitychange', handleVisibilityChange);

    // 获取是否出现黄条提示
    const res = await get(api.GET_WARNING);
    setWarningTip(res?.knowledge)
  }, []);

  const getAndSetTenant = async () => {
    // let res = await getTenantList();
    // console.log('res----', res);
    // const tenantConfig = await getTenantConfig();

    const res = await getCombineTenantInfo();
    const tenantListCooper = res?.tenants?.filter((v) => v.useCooper);
    const tenantId = tenantListCooper.filter((v) => v.selected)[0]?.tenantId;

    if (!tenantId && window.location.pathname !== '/tenant') {
      window.location.href = '/tenant';
      return;
    }

    // 如果不是滴滴租户的话就拦截用户回到首页去
    if (tenantId !== 1) {
      if (inPhone()) {
        window.dcH5Sdk.navigation.openWebview({
          url: window.location.origin,
          onSuccess() {},
          onFail() {
            window.location.href = '/';
          },
        });
      } else {
        window.location.href = '/';
      }
    }

    if (tenantId) {
      setTenantId(tenantId)
      store.dispatch({
        type: 'GlobalData/setTenantId',
        payload: tenantId,
      });
      store.dispatch({
        type: 'GlobalData/setIsExternalTenant',
        payload: tenantId !== 1,
      });
      store.dispatch({
        type: 'GlobalData/setTenantList',
        payload: tenantListCooper,
      });
      // store.dispatch({
      //   type: 'GlobalData/setTenantConfig',
      //   payload: tenantConfig,
      // });
      store.dispatch({
        type: 'CooperIndex/setProfile',
        payload: {
          avatar: res.avatar,
          email: res.email,
          orgMemberId: res.orgMemberId,
          tenantCode: res.tenantCode,
          tenantId: res.tenantId,
          tenantName: res.tenantName,
          uid: res.uid,
          username: res.username,
          username_zh: res.username_zh,
        },
      });

      setCookie('_cooper_username', res.username, 24 * 60 * 60);
      setCookie('_cooper_user_uid', res.uid, 24 * 60 * 60);
      setCookie('_cooper_user_orgMemberId', res.orgMemberId, 24 * 60 * 60);
      window.__OmegaEvent(
        'ep_cooper_login_ck',
        '多租户',
        {
          orgid: res.tenantId,
          orgname: res.tenantName,
        },
      );
      setDkRoute(getDkRoute().routesDK)
    }
  };

  useEffect(() => {
    if (process.env.APP_ENV === 'dev') {
      const { _username, _usertoken } = require('./cookie');
      setCookie('_user_token', _usertoken);
      setCookie('_cooper_username', _username);
      setCookie('_is_cooper_login', true);
      setCookie('_chain_user_token', _usertoken);
    }
    if (
      process.env.APP_ENV === 'qa'
      && process.env.NODE_ENV === 'development'
    ) {
      // eslint-disable-next-line import/extensions
      const { _username, _usertokenQa } = require('./cookie');
      setCookie('_user_token', _usertokenQa);
      setCookie('_cooper_username', _username);
      setCookie('_is_cooper_login', true);
      setCookie('_chain_user_token', _usertokenQa);
    }
    getBrowserInfo();
    asyncInitSdk();
    // setConnectedSiteId();
    cleanLocalStorageIfNeeded();
    getCooperNotice();
    store.dispatch.GlobalData.getCooperLinkConf();
    loadParcel(EditorMicroUrl, '_tenon_didoc_editor')
    message.config({
      maxCount: 1,
      duration: 2,
      rtl: false,
    })

    history.listen(() => {
      window.Omega?.sendPageView();
    });

    // if (window.Omega) {
    //   masRecord(window.Omega, {
    //     /** 是否屏蔽所有文本 */
    //     maskAllText: true,
    //     /** 是否给所有输入框内容脱敏 */
    //     maskAllInputs: true,
    //   })
    // }

    window.addEventListener('storage', (event) => {
      if (event.storageArea != localStorage) return;
      const tenantIdFromStore = store.getState().GlobalData.tenantId
      const tenantIdFromLocalStorage = window.localStorage.getItem('x-tenant-id')

      if (event.key === 'x-tenant-id') {
        // 测试使用 event.newValue
        if (tenantIdFromStore && tenantIdFromLocalStorage && tenantIdFromStore !== tenantIdFromLocalStorage) {
          if (window.location.pathname !== '/tenant') {
            window.location.reload()
          }
        }
      }
    });

    window.addEventListener('popstate', () => {
      getCooperNotice();
    });

    return () => {
      window.removeEventListener('popstate', () => {
        getCooperNotice();
      });
    }
  }, []);

  const routerViews = (routerItems) => {
    if (routerItems && routerItems.length) {
      return routerItems.map(({ path, element, exact, children, redirect }) => {
        return children && children.length ? (
          <Route
            path={path}
            exact={exact}
            key={path}
            element={element}>
            {routerViews(children)}
            {redirect ? (
              <Route
                path={path}
                key={path}
                element={<Navigate to={redirect} />}
              />
            ) : (
              <Route
                path={path}
                key={path}
                element={<Navigate to={children[0].path} />}
              />
            )}
          </Route>
        ) : (
          <Route
            path={path}
            exact={exact}
            key={path}
            element={element} />
        );
      });
    }
  };

  const getCooperNotice = async () => {
    const data = await getCooperNoticeFromBackEnd();
    const result = await getCooperErrorFromBackEnd();

    if (data && window.location.pathname !== '/send-file') {
      if (inPhone() && window.location.pathname.includes('/knowledge')) {
        errorNotice('switch_knowledge');
        return;
      }
      errorNotice('switch');
      return;
    }
    if (result && window.location.pathname !== '/send-file') {
      errorNotice('error');
    }
  }

  const isOpenDcWebView = () => {
    if (inPhone() && store.getState().GlobalData.IsExternalTenant) {
      window.dcH5Sdk.navigation.openWebview({
        url: window.location.origin,
        onSuccess() {},
        onFail() {},
      });
    } else {
      return (<Route
        path="*"
        key='default'
        element={<Suspense fallback={<div />}><ExternalRedirect /></Suspense>}
      />)
    }
  };

  return (
    <ErrorBoundary>
      <DndProvider backend={HTML5Backend}>
        <Di18nProvider
          locales={locales}
          cookieLocaleKey="local_language"
        >
          <Provider store={store}>
            <ConfigProvider
              autoInsertSpaceInButton={false}
              componentSize={'middle'}
              input={{ autoComplete: 'off' }}
            >
              { <CooperAnnounceHalo tips={warningTip} /> }
              {mobileSupport && (
                <ErrorTips
                  img={PhoneNoAccessIcon}
                  title={intl.t('暂不支持访问')}
                  desc={
                    isbeforevision
                      ? intl.t(
                        '移动端暂不支持访问知识库回收站、设置、数据看板，如需访问知识库目录及内容，请升级D-Chat至最新版本或前往PC端访问',
                      )
                      : intl.t(
                        '移动端暂不支持访问知识库回收站、设置、数据看板，请前往PC端访问',
                      )
                  }
                  overlayClassName={'error-tip-inPhone'}
              />
              )}
              {!mobileSupport && (
                <BrowserRouter history={history}>
                  <Routes>
                    <Route
                      path="/"
                      element={<LayoutDK warningTip={ warningTip} />}
                      key="dk">
                      {routerViews(dkRoute)}
                    </Route>
                    {
                      dkRoute.length !== 0 ? isOpenDcWebView() : null
                    }
                  </Routes>
                </BrowserRouter>
              )}
            </ConfigProvider>
          </Provider>
        </Di18nProvider>
      </DndProvider>
    </ErrorBoundary>
  );
}
export default KnowledgeApp;
