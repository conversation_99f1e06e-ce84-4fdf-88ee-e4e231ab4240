/*
 * @Date: 2023-11-13 16:09:16
 * @LastEditors: guanzhong
 * @LastEditTime: 2023-11-16 17:51:23
 * @FilePath: /knowledgeforge/src/pages/knowledge/DataBoard/index.js
 * @Description: 数据看板
 */
import { useContext, useState, useMemo } from 'react';
import { Helmet } from 'react-helmet';
import { CooperParcelUrl } from '@/constants/cooperComponents';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { MrParcel } from '@didi/mf-tenon';
import { Spin } from 'antd';
import { useSelector } from 'react-redux';
import classNames from 'classnames/bind';
import { getCookie, parseUrlSearch } from '@/utils';
import { useNavigate, useLocation } from 'react-router-dom';
import * as styles from './style.module.less';
import usePermission from '@/hooks/usePermission';
import { getLocale } from 'di18n-react';

const cls = classNames.bind(styles);

const debugDataBoard = getCookie('debugBoard');
// TODO:提前
// loadParcel(debugDataBoard || `${CooperParcelUrl}/js/cooper3partDataBoard.js`)
function DataBoard() {
  const { permission, knowledgeDetail } = useSelector((state) => state.KnowledgeData);
  const { knowledgeId } = useContext(LayoutContext);
  const { checkOperationPermission } = usePermission();
  const { perm } = permission;
  const [initLoading, setInitLoading] = useState(true);
  const navigate = useNavigate()
  const { pathname } = useLocation()

  const IsNotMember = useMemo(() => {
    return checkOperationPermission('MANAGE_DK_SETTING', perm);
  }, [perm]);

  return (
    <div className={cls('data-market-content')}>
      <Helmet>
        <title>{`数据看板-${knowledgeDetail.spaceName}`}</title>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png" />
      </Helmet>
      {
        initLoading && (
          <div className={'page-detail-loading'}>
            <Spin />
          </div>
        )
      }
      <MrParcel
        entry={debugDataBoard || `${CooperParcelUrl}/js/cooper3partDataBoard.js`}
        name='cooper3partDataBoard'
        appProps={{
          knowledgeId,
          knowledgeName: knowledgeDetail.spaceName,
          IsNotMember,
          local: getLocale(),
          pType: parseUrlSearch('type'),
          marginFromOuter: '80',
          eventHandler: {
            handleMounted: () => {
              setInitLoading(false);
            },
            changePType: (value) => {
              navigate(`${pathname}?type=${value}`)
            },
          },
        }}
      />
    </div>
  );
}

export default DataBoard;
