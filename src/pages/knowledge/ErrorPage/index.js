import { intl } from 'di18n-react';
import { Helmet } from 'react-helmet';
import { CooperParcelUrl } from '@/constants/cooperComponents';
import { MrParcel } from '@didi/mf-tenon';
import classNames from 'classnames/bind';
import { getCookie } from '@/utils';
import * as styles from './style.module.less';

const cls = classNames.bind(styles);

const debugError = getCookie('debugError');
function ErrorPage() {
  return (
    <div className={cls('error-page')}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png" />
      </Helmet>
      <MrParcel
        entry={debugError || `${CooperParcelUrl}/js/cooper3partErrorPage.js`}
        name="cooper3partErrorPage"
        appProps={{
          typeName: intl.t('知识库'),
          eventHandler: {
            handleGoBack: () => {
              window.location.href = '/knowledge';
            },
          },
        }}
      />
    </div>
  );
}

export default ErrorPage;
