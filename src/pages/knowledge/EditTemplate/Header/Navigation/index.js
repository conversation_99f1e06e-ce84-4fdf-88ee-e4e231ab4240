import { intl } from 'di18n-react';
import { connect } from 'react-redux';
import { useEffect, useRef, useState } from 'react';
import classNames from 'classnames/bind';
import * as styles from './style.module.less';

const cls = classNames.bind(styles);

function Navigation(props) {
  const titleRef = useRef();
  const { view, docInfo = {}} = props;
  const [title, setTitle] = useState(intl.t('无标题页面'));
  const handleBlur = () => {
    const { value } = titleRef.current;
    value && setTitle(value);
  };

  useEffect(() => {
    docInfo.name && setTitle(docInfo.name);
  }, [docInfo]);

  useEffect(() => {
    titleRef.current = document.querySelector(
      '.didoc-editor-input.editor-title',
    );
    if (titleRef.current) {
      titleRef.current.addEventListener('blur', handleBlur);
      handleBlur();
    }

    return () => {
      if (titleRef.current) {
        titleRef.current.removeEventListener('blur', handleBlur);
        titleRef.current = null;
      }
    };
  }, [view]);

  return (
    <div className={cls('navigation')}>
      <span className={cls('title')}>{title}</span>
      <span className={cls('badge')}>{intl.t('模版')}</span>
    </div>
  );
}

function mapStateToProps({ editTemplate }) {
  const { view, docInfo } = editTemplate;
  return {
    view,
    docInfo,
  };
}

export default connect(mapStateToProps)(Navigation);
