import { connect } from 'react-redux';
import classNames from 'classnames/bind';
import * as styles from './style.module.less';

const cls = classNames.bind(styles);

function Logo(props) {
  const { knowledgeDetail = {}} = props;
  const { exPicture, spaceName } = knowledgeDetail;

  return (
    <div className={cls('logo')}>
      <img
        className={cls('icon')}
        src={exPicture}
        alt=""
      />
      <span className={cls('title')}>{spaceName}</span>
      <span className={cls('divider')}/>
    </div>
  );
}

function mapStateToProps({ KnowledgeData }) {
  const { knowledgeDetail } = KnowledgeData;
  return {
    knowledgeDetail,
  };
}

export default connect(mapStateToProps)(Logo);
