import { useMemo, useContext, useState } from 'react';
import { intl } from 'di18n-react';
import { connect } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import classNames from 'classnames/bind';

import { Modal, Button } from 'antd';
import NotificationStatus from '@/constants/notification';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import useNotification from '@/hooks/useNotification';
import { editorScreenshot, checkNameErrorMsg, getUrlParams } from '@/utils';

import * as styles from './style.module.less';

const cls = classNames.bind(styles);

function Publish(props) {
  const { view, createTemplate, updateTemplate } = props;
  const [requesting, setRequesting] = useState(false);
  const navigate = useNavigate();
  const { knowledgeId, templateId } = useContext(LayoutContext);
  const notification = useNotification();
  const isNew = templateId === 'new';

  const { search } = useLocation();

  const spaceId = useMemo(() => {
    return getUrlParams('spaceId') ?? '';
  }, [search]);

  const handleConfirm = async () => {
    let request = createTemplate;
    let tipText = intl.t('模板保存成功');
    const templateExport = view.template.export();
    const name = templateExport.title || '';
    let msg = checkNameErrorMsg(name);
    if (msg.length > 0) {
      notification(NotificationStatus.ERROR, msg);
      return;
    }
    const isEmptyContent = !!document.querySelector('.empty-placeholder');
    if (isEmptyContent) {
      notification(NotificationStatus.WARN, intl.t('模板内容不能为空'));
      return;
    }
    setRequesting(true);
    const content = JSON.stringify(templateExport);
    const thumbnail = await editorScreenshot();
    const formData = new FormData();
    formData.append('name', name);
    formData.append('content', content);
    formData.append('thumbnail', thumbnail, 'thumbnail.png');

    if (!isNew) {
      request = updateTemplate;
      tipText = intl.t('模板更新成功');
      formData.append('templateId', templateId);
    }
    request({ knowledgeId, formData })
      .then((data) => {
        notification(NotificationStatus.SUCCESS, tipText, () => {
          if (spaceId) {
            navigate(
              `/team-file/${spaceId}/knowledge/${knowledgeId}/setUp?key=2&tpl=${isNew ? data : templateId
              }`,
            );
          } else {
            navigate(
              `/knowledge/${knowledgeId}/setUp?key=2&tpl=${isNew ? data : templateId
              }`,
            );
          }
        });
      })
      .finally(() => {
        setRequesting(false);
      });
  };

  const gotoManage = () => {
    let url;
    if (spaceId) {
      url = isNew
        ? `/team-file/${spaceId}/knowledge/${knowledgeId}/setUp?key=2`
        : `/team-file/${spaceId}/knowledge/${knowledgeId}/setUp?key=2&tpl=${templateId}`;
    } else {
      url = isNew
        ? `/knowledge/${knowledgeId}/setUp?key=2`
        : `/knowledge/${knowledgeId}/setUp?key=2&tpl=${templateId}`;
    }

    navigate(url);
  };

  const handleCancel = () => {
    const { title = '' } = view.template.export();
    const isEmptyContent = !!document.querySelector('.empty-placeholder');
    if (isNew && isEmptyContent && !title.trim()) {
      gotoManage();
      return;
    }
    Modal.confirm({
      className: cls('cancel-edit-modal'),
      width: 480,
      title: intl.t('放弃编辑'),
      icon: null,
      centered: true,
      content: intl.t('退出将不会保存对模版的修改，是否放弃编辑?'),
      okText: intl.t('放弃并退出'),
      cancelText: intl.t('取消'),
      onOk: gotoManage,
      onCancel() { },
    });
  };

  return (
    <div className={cls('publish')}>
      <Button
        className={cls('confirm')}
        onClick={handleConfirm}
        loading={requesting}
      >
        {isNew ? intl.t('保存模版') : intl.t('更新模版')}
      </Button>
      <Button
        className={cls('cancel')}
        onClick={handleCancel}>
        {intl.t('取消')}
      </Button>
    </div>
  );
}

function mapStateToProps({ editTemplate }) {
  const { view, docInfo } = editTemplate;
  return {
    view,
    docInfo,
  };
}

function mapDispatchToProps({ editTemplate }) {
  const { createTemplate, updateTemplate } = editTemplate;
  return {
    createTemplate,
    updateTemplate,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Publish);
