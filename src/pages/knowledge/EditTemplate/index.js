/*
 * @Author: guanzhong
 * @Date: 2023-11-09 16:00:00
 * @LastEditors: guanzhong
 * @LastEditTime: 2023-11-10 11:20:23
 * @FilePath: /knowledgeforge/src/pages/knowledge/EditTemplate/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useContext, useEffect } from 'react';
import { connect } from 'react-redux';
import { Helmet } from 'react-helmet';
import classNames from 'classnames/bind';
import { Spin } from 'antd';
import { useParams } from 'react-router-dom';
import Template from '@/components/Template';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import Authority from './Authority';
import Editor from './Editor';
import Header from './Header';
import * as styles from '@/pages/knowledge/PageDetail/style.module.less';


const cls = classNames.bind(styles);

function EditTemplate(props) {
  const { knowledgeId } = useContext(LayoutContext);
  const { view, initLoading, applyTemplate, resetState } = props;
  const handleApply = ({ templateId }) => {
    applyTemplate({
      view,
      templateId,
      knowledgeId,
    });
  };

  useEffect(() => {
    return resetState;
  }, []);

  return (
    <div
      className={cls('page-detail')}
    >
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png" />
      </Helmet>
      <Authority>
        <Header />
        {
          <div className={cls('page-content')}>
            {
              initLoading && (
                <div className={'page-detail-loading'}>
                  <Spin />
                </div>
              )
            }
            <>
              < Editor />
              <Template applyTemplate={handleApply} />
            </>
          </div>
        }
      </Authority>
    </div>
  );
}

function mapStateToProps({ editTemplate }) {
  const { view, initLoading } = editTemplate;
  return {
    view,
    initLoading,
  };
}

function mapDispatchToProps({ editTemplate, template }) {
  const { resetState } = editTemplate;
  const { applyTemplate } = template;
  return {
    resetState,
    applyTemplate,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(EditTemplate);
