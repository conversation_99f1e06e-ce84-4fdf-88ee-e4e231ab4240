import { intl } from 'di18n-react';
import { Helmet } from 'react-helmet';
/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-09-01 11:00:35
 * @LastEditTime: 2023-10-23 11:01:48
 * @Description: 收藏知识库
 * @FilePath: /di-knowledge/src/pages/MenHu/StarKonwledge/index.js
 *
 */
import * as commonStyles from '@/components/serviceComponents/Layout/style.module.less';
import useStarData from '@/hooks/useStarData';
import { isDesktopDC } from '@/utils';
import classBind from 'classnames/bind';
import { useEffect, useState } from 'react';
import StarList from './StarList/index';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);
const cm = classBind.bind(commonStyles);

function StarKonwledge(props) {
  const [ownerType, setOwnerType] = useState(); // 获取子组件的ownerType,用于dc切换时刷新
  const { loading, firstPaint, hasMore, loadMore, recentList } = useStarData(true);

  const refresh = () => {
    loadMore(true, ownerType);
  };
  const getOwnerType = (type) => {
    setOwnerType(type);
  };

  useEffect(() => {
    window.document.title = intl.t('收藏');
  }, []);

  useEffect(() => {
    if (isDesktopDC) {
      window.addEventListener('dc-event-app-active-knowledge', refresh);
    }

    return () => {
      window.removeEventListener('dc-event-app-active-knowledge', refresh);
    };
  }, []);
  return (
    <div
      className={`${cx('starKnowledge-wrap')} ${cm('body-main-wrap', {
        'dc-body-main-wrap': isDesktopDC,
      })}`}
    >
      <Helmet>
        <title>收藏-知识库-Cooper</title>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png" />
      </Helmet>
      <div className={`${cm('mainTitle')} ${cx('mainTitleCx')}`}>
        {/* {
           !isDesktopDC && <img
             src={StarTableIcon}
             className={cm('titleIcon')} />
          } */}
        <div
          className={cm('title', {
            'dk-menhu-title': isDesktopDC,
          })}
        >
          {intl.t('收藏')}
        </div>
      </div>
      <StarList
        getOwnerType={getOwnerType}
        loadMore={loadMore}
        loading={loading}
        firstPaint={firstPaint}
        hasMore={hasMore}
        recentList={recentList}
        callBack={refresh}
      />
    </div>
  );
}

export default StarKonwledge;
