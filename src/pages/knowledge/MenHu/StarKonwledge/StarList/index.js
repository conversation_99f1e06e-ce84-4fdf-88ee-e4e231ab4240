import { intl } from 'di18n-react';
/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-09-01 11:42:40
 * @LastEditTime: 2023-11-15 11:24:14
 * @Description: 收藏Tab
 * @FilePath: /knowledgeforge/src/pages/knowledge/MenHu/StarKonwledge/StarList/index.js
 *
 */
import { useState } from 'react';
import { Popover, Spin, Tooltip } from 'antd';
import { formatRecentTime } from '@/utils';
import classBind from 'classnames/bind';
import ScrollTableList from '@/components/serviceComponents/ScrollTableList';
import ErrorTips from '@/components/ErrorTipsDk';
import NoMore from '@/components/serviceComponents/NoMore';
import { PageNotPublishIcon } from '@/assets/icon';
import { OWNER_TYPE } from '@/constants/recent';
import { goStarPage } from '@/utils/goto';
import FileContent from '@/baseComponents/FileContent';
import * as styles from './style.module.less';
import CopyToClipboard from 'react-copy-to-clipboard';

const cx = classBind.bind(styles);

const StarList = ({
  getOwnerType,
  loadMore,
  loading,
  firstPaint,
  hasMore,
  recentList,
  refresh,
}) => {
  const [ownerType, setOwnerType] = useState(OWNER_TYPE.ALL);
  const chooseOwnerType = (type) => {
    window.__OmegaEvent('ep_dkpc_dkhome_chooseowner_all_ck', '', {
      type: type === OWNER_TYPE.ALL ? 'all' : 'me',
    });
    setOwnerType(type);
    getOwnerType(type);
    loadMore(true, type);
  };

  const ownerList = [
    {
      type: OWNER_TYPE.ALL,
      icon: 'dk-icon-suoyouzhe-buxian',
      text: intl.t('不限归属'),
    },
    {
      type: OWNER_TYPE.MINE,
      icon: 'dk-icon-suoyouzhe-geren1',
      text: intl.t('归我所有'),
    },
  ];

  const ownerPopoverContent = (
    <ul className={cx('ownerPopoverContent')}>
      {ownerList.map((item) => {
        return (
          <li
            key={item.type}
            className={cx('ownerPopoverContent-item', {
              checked: item.type === ownerType,
            })}
            onClick={() => chooseOwnerType(item.type)}
          >
            <i className={cx('dk-iconfont', item.icon, 'icon')} />
            {item.text}
          </li>
        );
      })}
    </ul>
  );

  const columns = [
    {
      title: intl.t('名称'),
      dataIndex: 'display_name',
      key: 'display_name',
      width: '55%',
      className: cx('pageColumn'),
      render: (text, record) => {
        return (
          <FileContent
            resourceName={text}
            isInTable={true}
            isLarge={true}
            hasStar={true}
            record={record}
            callBack={() => loadMore(true, ownerType)}
            metisId={record.resource_id}
            tags={
              record.star_type === 'DK_SHARE'
              || record.star_type === 'PAGE_SHARE'
                ? [intl.t('访客链接')]
                : null
            }
          />
        );
      },
    },
    {
      title: (
        <div>
          <Popover
            trigger="click"
            placement="bottom"
            overlayClassName={cx('dk-ant-popover__reset', 'option-popover')}
            content={ownerPopoverContent}
          >
            {intl.t('所有者')}
            <i
              className={`${cx(
                'dk-icon-shaixuan',
                'dk-iconfont',
                'owner-icon',
              )} `}
            />
          </Popover>
        </div>
      ),
      dataIndex: 'create_by_cn',
      key: 'create_by_cn',
      width: '18%',
      render: (text) => {
        return <div>{text}</div>;
      },
    },
    {
      title: intl.t('文件位置'),
      dataIndex: 'team_name',
      key: 'team_name',
      width: '30%',
      render: (text, record) => {
        return (
          <div className={cx('address-content')}>
            <Tooltip title={text}>{text}</Tooltip>
          </div>
        );
      },
    },

    {
      title: intl.t('收藏时间'),
      dataIndex: 'create_on',
      key: 'create_on',
      width: '18%',
      render: (text) => {
        return <div>{formatRecentTime(text)}</div>;
      },
    },
    // {
    //   title: '操作',
    //   key: 'action',
    //   width: '15%',
    //   render: (text, record) => {
    //     return <Popover
    //       key={record.id}
    //       trigger='click'
    //       content={renderActionPopverContent(record)}
    //       open={popoverVisible}
    //       onOpenChange={handleClickChange}
    //       placement='bottom'
    //     >
    //       <i className={`${cx('dk-icon-gengduo1', 'dk-iconfont', 'action-icon')} `} />
    //     </Popover>
    //   },
    // },
  ];

  const clickRow = (item) => {
    const { team_id, resource_id, star_type, share_id, star_info } = item;
    const params = {
      knowledgeId: team_id,
      pageId: resource_id,
      sourceType: star_type,
      shareId: share_id,
      starInfo: star_info,
    };
    goStarPage({ ...params });
  };

  return (
    <div className={cx('recent-list-box')}>
      {!recentList.length && loading && (
        <div className={'page-detail-loading'}>
          <Spin />
        </div>
      )}

      {!firstPaint
        && (recentList.length === 0 && !loading ? (
          <div className={cx('recent_empty')}>
            <ErrorTips
              img={PageNotPublishIcon}
              title={intl.t('你收藏的页面记录会展示在这里！')}
            />
          </div>
        ) : (
          <div
            id="menhu-recentList"
            className={cx('recentList', 'knowledge_recentList_wrap')}
          >
            <ScrollTableList
              loadMore={() => loadMore(false, ownerType)}
              loading={loading}
              hasMore={hasMore}
              columns={columns}
              dataList={recentList}
              clickRow={clickRow}
            />

            {!loading && !hasMore && <NoMore />}
          </div>
        ))}
    </div>
  );
};

export default StarList;
