import { intl } from 'di18n-react';
import { useEffect, useState } from 'react';
import { Popover } from 'antd';
import classBind from 'classnames/bind';
import { SORT_TYPE } from '@/constants/dkList';
import * as styles from './style.module.less';
import { RecentSortDkIcon } from '@/assets/icon/';

const cx = classBind.bind(styles);

export default ({ type, value, onChange, isMyKnowledge }) => {
  const conf = isMyKnowledge
    ? [intl.t('sortby-创建时间'), intl.t('sortby-访问时间'), intl.t('sortby-参与时间')]
    : [intl.t('sortby-访问时间'), intl.t('sortby-创建时间')];
  const [current, setCurrent] = useState(conf[value[type]]);
  const [visible, setVisible] = useState(false);

  const onSelect = (v, label) => {
    setCurrent(label);
    onChange(`${v}`);
    setVisible(false);
    if (isMyKnowledge && type === 'MY_OWN') {
      window.__OmegaEvent('ep_dkpc_dkhome_owned_sort_ck', '', {
        sort: SORT_TYPE[v],
      });
    } else if (isMyKnowledge && type === 'MY_JOIN') {
      window.__OmegaEvent('ep_dkpc_dkhome_joined_sort_ck', '', {
        sort: SORT_TYPE[v],
      });
    }
  };

  const itemLis = (v, i) => {
    return (
      <li
        key={i}
        onClick={() => onSelect(i, v)}>
        <span>{v}</span>
        {value[type] == i && (
          <i className={cx('dk-icon-xuanzhong', 'dk-iconfont')} />
        )}
      </li>
    );
  };

  useEffect(() => {
    setCurrent(conf[value[type]]);
  }, [type]);
  return (
    <Popover
      trigger="click"
      visible={visible}
      placement="bottom"
      getPopupContainer={() => document.querySelector('#dkMenhuWrap')}
      overlayInnerStyle={{ minWidth: 140 }}
      onVisibleChange={(v) => {
        !v && setVisible(false);
      }}
      content={
        <ul className={`${cx('recent-dk-sort-popover')}`}>
          {isMyKnowledge
            && conf.map((v, i) => ((i !== 0 && type === 'MY_JOIN') || (i !== 2 && type === 'MY_OWN')
              ? itemLis(v, i)
              : null))}
          {!isMyKnowledge
            && conf.map((v, i) => (i !== 2 ? itemLis(v, i) : null))}
        </ul>
      }
      overlayClassName={'dk-ant-popover__reset '}
    >
      <div
        className={`${cx('dk-sort-recent')} ${visible ? cx('visible') : ''}`}
        onClick={() => setVisible(!visible)}
      >
        <img src={RecentSortDkIcon} />
        <span>
          {intl.t('按')}
          {current}
          {intl.t('排序')}
        </span>
      </div>
    </Popover>
  );
};
