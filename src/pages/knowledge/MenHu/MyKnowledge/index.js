import { intl } from 'di18n-react';
import { useEffect, useState, useMemo } from 'react';
import { Helmet } from 'react-helmet';
import classBind from 'classnames/bind';
import { HomeIcon } from '@/assets/icon/';
import { getMyDKList, getMyOwnDKList } from '@/service/knowledge/menhuIndex';
import commonStyles from '@/components/serviceComponents/Layout/style.module.less';
import DKList from '@/components/serviceComponents/DKList';
import DKSort from './DKSort';
import * as styles from './style.module.less';
import { isDesktopDC } from '@/utils';
import { useDispatch, useSelector } from 'react-redux';
import { setLocalData, getLocalData } from '@/utils/localStorage';
import USER_VIEW from '@/constants/userView';
import { CooperTabs, CooperTabsPane } from '@/components/common/CooperTabs';
import SecondLevelTab from '@/components/common/SecondLevelTab';

// 门户

const cx = classBind.bind(styles);
const cm = classBind.bind(commonStyles);

const mapDKListKey = (data, key) => {
  for (let i = 0; i < 2; i++) {
    if (data[i].key === key) return i;
  }
};

const _MyTeamTab = () => [
  {
    key: '1',
    title: intl.t('我创建的')
  },
  {
    key: '2',
    title: intl.t('转交给我的')
  },
  {
    key: '0',
    title: intl.t('全部')
  }
];


const MyKnowledge = () => {
  const [joinData, setJoinData] = useState();

  const [ownerData, setOwnerData] = useState();
  const [initLoading, setInitLoading] = useState(true);
  const [errorData, setErrorData] = useState(false);
  const [isOwn, setIsOwn] = useState(true);
  const [totalOwner, setTotalOwner] = useState(null);
  const [activeOwnerType, setActiveOwnerType] = useState(
    getLocalData('always:USER_VIEW')?.DkChooseType|| '1',
  );

  const [sort, setSort] = useState({
    MY_JOIN:
      getLocalData('always:USER_VIEW')?.My_Knowledge?.Sort_Type?.MY_JOIN || '2',
    MY_OWN:
      getLocalData('always:USER_VIEW')?.My_Knowledge?.Sort_Type?.MY_OWN || '0',
  });

  const [tabActiveKey, setTabActiveKey] = useState('MY_OWN');
  

  useEffect(() => {
    window.document.title = intl.t('知识库');
  }, []);
  

  const tabInfo = useMemo(() => {
    return [
      {
        key: 'MY_OWN',
        title: intl.t('我拥有的'),
        data: ownerData,
      },
      {
        key: 'MY_JOIN',
        title: intl.t('我参与的'),
        data: joinData,
      },
    ];
  }, [joinData, ownerData]);

  useEffect(() => {
    // TODO: 门户dc中刷新
    if (isDesktopDC) {
      window.addEventListener('dc-event-app-active-knowledge', refreshDcData);
    }

    updateData(true);
    updateData(false);

    getMyOwnDKList({
      owner: true,
      sort: 1,
      ownerType: '0',
    }).then((res) => {
      setTotalOwner(res.length) // 总数
    })

    return () => {
      window.removeEventListener(
        'dc-event-app-active-knowledge',
        refreshDcData,
      );
    };
  }, []);

  const refreshDcData = () => {
    setIsOwn(true);
    updateData(isOwn);
  };
  const updateData = (isOwnParam, currentSort, DkChooseType) => {
    setInitLoading(true);
    let current = isOwnParam ? 'MY_OWN' : 'MY_JOIN';
    let type = DkChooseType || activeOwnerType;
    const getListFn = isOwnParam ? getMyOwnDKList : getMyDKList;
    const setDataOwner = setOwnerData;
    const setDataJoin = setJoinData;
    const params = {
      owner: isOwnParam,
      sort: currentSort || sort[current],
      ownerType: type,
    };

    getListFn(params)
      .then((res) => {
        if (isOwnParam) {
          setDataOwner(res);
          if ( type === '0') {
            setTotalOwner(res.length) // 总数
          }
        } else {
          setDataJoin(res);
        }
      })
      .catch(() => {
        setErrorData(true);
      })
      .finally(() => {
        setInitLoading(false);
      });
  };

  const handleSort = (activeSort) => {
    setInitLoading(true);
    let currView = JSON.stringify(getLocalData('always:USER_VIEW')) !== '{}'
      ? getLocalData('always:USER_VIEW')
      : USER_VIEW;
    let current = '';
    current = isOwn ? 'MY_OWN' : 'MY_JOIN';
    setSort({ ...sort, [current]: activeSort });
    if (currView?.My_Knowledge?.Sort_Type?.[current]) {
      currView.My_Knowledge.Sort_Type[current] = activeSort;
    }
    updateData(isOwn, activeSort);
    setLocalData('always:USER_VIEW', currView);
  };

  const handleTabChange = (activeKey) => {
    setTabActiveKey(activeKey);
    let isOwner = activeKey === 'MY_OWN';
    setInitLoading(true);
    setIsOwn(isOwner);
    updateData(isOwner);

    if (isOwner) {
      window.__OmegaEvent('ep_dkpc_dkhome_owned_ck');
    } else window.__OmegaEvent('ep_dkpc_dkhome_joined_ck');
  };

  const onClickOwnerTab = (key) => {
    updateData(true, null, key);
    setActiveOwnerType(key);


    let currView = JSON.stringify(getLocalData('always:USER_VIEW')) !== '{}'
      ? getLocalData('always:USER_VIEW')
      : USER_VIEW;
    if (currView?.DkChooseType) {
      currView.DkChooseType = key;
    }
    setLocalData('always:USER_VIEW', currView);
  }


  return (
    <div
      id="dkMenhuWrap"
      className={`${cx('my-dk-wrap')} ${cm('body-main-wrap', {
        'dc-body-main-wrap': isDesktopDC,
      })}`}
    >
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png" />
      </Helmet>
      <div
        className={`${cm('mainTitle')} ${cx('mainTitle-creact', {
          'mainTitle-creact-paddingRight': isDesktopDC,
        })}`}
      >
        <div
          className={`${cm('title', {
            'dk-menhu-title': isDesktopDC,
          })}`}
        >
          {/* {!isDesktopDC && <img
             src={HomeIcon}
             className={cx('dk-home-icon')} />} */}
          {intl.t('知识库-大写')}
        </div>
      </div>

      <CooperTabs
        activeKey={tabActiveKey}
        onChange={handleTabChange}
        className={cx("dk-tabs-has-fit")}
        tabBarExtraContent={{
          right: (
            <DKSort
              isMyKnowledge={true}
              type={isOwn ? 'MY_OWN' : 'MY_JOIN'}
              value={sort}
              onChange={handleSort}
            />
          ),
        }}
      >
        {tabInfo.map((item) => {
          return (
            <CooperTabsPane
              tab={
                <span>
                  <span>{item.title}</span>
                  <span className="ant-tabs-tab-btn-fit">
                    {item.key === 'MY_OWN' ? totalOwner : item?.data?.length }
                  </span>
                </span>
              }
              key={item.key}
            >
              {
                item.key === 'MY_OWN' && <SecondLevelTab
                  tabList={_MyTeamTab()}
                  onClick={onClickOwnerTab}
                  currKey={activeOwnerType}
                  className={cx('tab-knowledge')}
                />
              }
              <DKList
                isMyKnowledge={true}
                isOwn={isOwn}
                changeKey={item}
                errorData={errorData}
                initLoading={initLoading}
                activeOwnerType={activeOwnerType}
                dkData={tabInfo[mapDKListKey(tabInfo, item.key)].data}
              />
            </CooperTabsPane>
          );
        })}
      </CooperTabs>
    </div>
  );
};

export default MyKnowledge;
