import { PROCESS_ENV_MENHU_URL } from '@/constants';
import { getApolloConfig } from '@/service/knowledge/global';
import {
  checkMHName,
  checkMHSymbol,
  creactMH,
} from '@/service/knowledge/menhuIndex';
import { checkMHNameSymbolErrorMsg, openNewWindow } from '@/utils';
import { Button, Form, Input, Modal, Tooltip, message } from 'antd';
import classBind from 'classnames/bind';
import { intl } from 'di18n-react';
import { debounce } from 'lodash-es';
import { useMemo, useState } from 'react';
import { connect, useSelector } from 'react-redux';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const MODLE_TITLE = {
  LAYOUT: { TITLE: intl.t('选择门户布局'), FOOTER: intl.t('下一步') },
  FIELD: { TITLE: intl.t('申请域名'), FOOTER: intl.t('创建') },
};
const SymolHttpUrl = {
  dev: 'localhost:3005/portal/',
  test: 'dk-test.didichuxing.com/portal/',
  prod: 'k.didi.cn/portal/',
  qa: 'dk-qa.didichuxing.com/portal/',
};

const CreateMenhu = ({ upDataFn }) => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false); // 弹框显示
  const [templateList, setTemplateList] = useState([]); // 布局列表
  const [tabKey, setTabKey] = useState('LAYOUT'); // LAYOUT：门户布局 / FIELD：申请域名 类型区分
  const [confirmLoading, setConfirmLoading] = useState(false); // 确认按钮loading
  const [layoutType, setLayoutType] = useState(null); // 门户布局选中布局类型
  const [nameCheckMsg, setNameCheckMsg] = useState(''); // 门户名称 校验提示文案
  const [symbolCheckMsg, setSymbolCheckMsg] = useState(''); // 门户域名 校验提示文案
  const [nameSymbolObj, setNameSymbolObj] = useState({ name: '', symbol: '' });
  const [SymolFocus, setSymolFocus] = useState(false); // 门户域名 校验提示样式

  const { portalABSwitch } = useSelector((state) => state.KnowledgePortal);

  const createMHBtn = async () => {
    const name = process.env.APP_ENV === 'prod' ? 'dk-template' : 'clone_dk-template_test';
    const res = await getApolloConfig({ ns: 'cooper_announce', name });
    const newList = JSON.parse(res?.templateList || '[]');
    setTemplateList([...newList]);
    setLayoutType(newList[0]?.type);
    setVisible(true);
  };
  const formDefault = () => {
    form.resetFields();
    setNameCheckMsg('');
    setSymbolCheckMsg('');
  };
  const defaultValueModal = () => {
    // 恢复默认值
    setTabKey('LAYOUT');
    setLayoutType(null);
    formDefault();
  };

  const handleCancel = () => {
    // 取消
    defaultValueModal();
    setVisible(false);
    setTemplateList([]);
    setConfirmLoading(false);
  };

  const handleOk = () => {
    // 确定创建
    if (tabKey === 'LAYOUT') {
      setTabKey('FIELD');
      return;
    }
    setConfirmLoading(true);
    const { name, symbol } = form.getFieldsValue();
    const schema = layoutRightContent[0]?.schema;
    /**
     * 创建门户请求
     * 跳转 设置管理
     * 测试： https://dk-test.didichuxing.com/portal/davinci/setup
     * 线上： k.didi.cn/protal/xxxx/designer
     */
    creactMH({ name, symbol, layoutType, schema })
      .then(() => {
        const url = PROCESS_ENV_MENHU_URL[process.env.APP_ENV];
        setConfirmLoading(false);
        handleCancel();
        upDataFn();
        openNewWindow(`${url}${symbol}/designer`);
      })
      .catch((err) => {
        setConfirmLoading(false);
        message.error(err.message);
      });
  };

  const loadName = (name, flag = false) => {
    // 门户名称 校验
    const str = checkMHNameSymbolErrorMsg(name, 'name');
    setNameCheckMsg(str);
    if (!str && !flag) {
      checkMHName({ name })
        .then((res) => {
          setNameCheckMsg(str);
          if (res === 1) setNameCheckMsg(intl.t('门户名称重复'));
        })
        .catch((err) => message.error(err.message));
    }
  };

  const loadSymbol = (symbol, flag = false) => {
    // 门户域名 校验
    const str = checkMHNameSymbolErrorMsg(symbol, 'symbol');
    setSymbolCheckMsg(str);
    if (!str && !flag) {
      checkMHSymbol({ symbol })
        .then((res) => {
          setSymbolCheckMsg(str);
          if (res === 1) setSymbolCheckMsg(intl.t('门户域名重复'));
        })
        .catch((err) => message.error(err.message));
    }
  };

  const onValuesChange = debounce((value = {}, allValue) => {
    // form 表单更改值进行校验
    const { name, symbol } = allValue;
    setNameSymbolObj({ name, symbol });
    if (Object.keys(value)[0] === 'name') loadName(value.name);
    if (Object.keys(value)[0] === 'symbol') loadSymbol(value.symbol);
  }, 500);

  const nameOnBlur = (e) => {
    // 门户名称失焦
    const value = e.target.value || '';
    loadName(value);
  };

  const symbolOnBlur = (e) => {
    // 门户域名失焦
    setSymolFocus(false);
    const value = e.target.value || '';
    loadSymbol(value);
  };

  const layoutRightContent = useMemo(() => {
    // 布局右侧key值展示对应信息
    return templateList.filter((item) => item.type === layoutType);
  }, [layoutType, templateList]);

  const creactMenHuBtnDisabled = useMemo(() => {
    // 创建按钮 禁用状态
    const { name, symbol } = nameSymbolObj;
    return name && symbol && !nameCheckMsg && !symbolCheckMsg;
  }, [nameSymbolObj, nameCheckMsg, symbolCheckMsg]);

  const modalTitleContentFooter = useMemo(() => {
    const titleKey = {
      LAYOUT: (
        <p className={cx('mh-create-modal-title')}>
          {MODLE_TITLE.LAYOUT.TITLE}
          <i
            className={cx('dk-iconfont', 'dk-icon-guanbi', 'close-icon')}
            onClick={handleCancel}
          />
        </p>
      ),
      FIELD: (
        <p className={cx('mh-create-modal-title')}>
          <i
            className={cx(
              'dk-iconfont',
              'dk-icon-fanhuiyemian',
              'fanhuiyemian-icon',
            )}
            style={{ marginRight: '2px', cursor: 'pointer' }}
            onClick={() => {
              setTabKey('LAYOUT');
              formDefault();
            }}
          />
          <span>{MODLE_TITLE.FIELD.TITLE}</span>
          <i
            className={cx('dk-iconfont', 'dk-icon-guanbi', 'close-icon')}
            onClick={handleCancel}
          />
        </p>
      ),
    };
    const footerKey = {
      LAYOUT: MODLE_TITLE.LAYOUT.FOOTER,
      FIELD: MODLE_TITLE.FIELD.FOOTER,
    };
    const contentKey = {
      LAYOUT: (
        <div className={cx('mh-create-modal-layout')}>
          <div className={cx('mh-create-modal-layout-left')}>
            {templateList.length > 0
              && templateList.map((item) => {
                return (
                  <div
                    className={cx('mh-layout-left-dl')}
                    key={item.type}
                    onClick={() => setLayoutType(item.type)}
                  >
                    <div
                      className={cx('mh-layout-left-dl-son', {
                        'mh-layout-left-dl-active': layoutType === item.type,
                      })}
                    >
                      <img
                        src={item.tinyImg}
                        alt="" />
                    </div>
                    <p>{item.title}</p>
                  </div>
                );
              })}
          </div>
          <div className={cx('mh-create-modal-layout-right')}>
            <p>{intl.t('布局预览')}</p>
            <div>
              {templateList.length > 0 && (
                <img src={layoutRightContent[0]?.img} />
              )}
            </div>
          </div>
        </div>
      ),
      FIELD: (
        <div className={cx('mh-create-modal-field')}>
          <Form
            form={form}
            className={cx('mh-create-modal-field-form')}
            layout="vertical"
            onValuesChange={onValuesChange}
          >
            <div className={cx('namelabel')}>{intl.t('门户名称')}</div>
            <div className={cx('nameItem')}>
              <Form.Item name="name">
                <Input
                  className={cx('nameInput', {
                    'nameInput-err-active': nameCheckMsg,
                  })}
                  placeholder={intl.t('请填写门户名称')}
                  onBlur={nameOnBlur}
                />
              </Form.Item>
              <div className={cx('nameCheckMsg')}>{nameCheckMsg}</div>
            </div>
            <div className={cx('namelabel', 'nameItemTop')}>
              {intl.t('门户域名')}
              <Tooltip title={intl.t('域名是用于成员直接访问门户的网址')}>
                <div className={cx('mh-iconfont-tip')}>
                  <i className={cx('dk-iconfont', 'dk-icon-a-bangzhuzhongxin4px')} />
                </div>
              </Tooltip>
            </div>
            <div className={cx('nameItem')}>
              <Form.Item
                name="symbol"
                className={cx({
                  'symbolInput-err-active': symbolCheckMsg,
                  'symbolInput-active': SymolFocus,
                })}
              >
                <Input
                  className={cx('nameInput')}
                  addonBefore={SymolHttpUrl[process.env.APP_ENV]}
                  placeholder={intl.t('请填写自定义域名后缀')}
                  onFocus={() => setSymolFocus(true)}
                  onBlur={symbolOnBlur}
                />
              </Form.Item>
              <div className={cx('nameCheckMsg')}>{symbolCheckMsg}</div>
            </div>
          </Form>
        </div>
      ),
    };
    return {
      title: titleKey[tabKey],
      footer: footerKey[tabKey],
      content: contentKey[tabKey],
    };
  }, [
    tabKey,
    nameCheckMsg,
    symbolCheckMsg,
    SymolFocus,
    templateList,
    layoutType,
    layoutRightContent,
  ]);

  if (portalABSwitch === false) {
    return false;
  }

  return (
    <>
      <button
        id="mh-create-btn" // 用于引导气泡位置
        className={cx('createMH')}
        onClick={createMHBtn}
      >
        <i
          className={cx('dk-iconfont', 'dk-icon-ic_chuangjian', 'create-icon')}
        />
        <span className={cx('create-text')}>{intl.t('创建知识门户')}</span>
      </button>

      <Modal
        className={cx('mh-create-modal')}
        title={modalTitleContentFooter.title}
        visible={visible}
        closable={false}
        onCancel={handleCancel}
        bodyStyle={{ padding: '0 0 0 0' }}
        width={851}
        centered={true}
        footer={[
          <Button
            key="cancel"
            onClick={handleCancel}>
            {intl.t('取消')}
          </Button>,
          <Button
            key="submit"
            className={cx({
              'mh-btn-disabled': tabKey === 'FIELD' && !creactMenHuBtnDisabled,
            })}
            type="primary"
            loading={confirmLoading}
            disabled={tabKey === 'FIELD' && !creactMenHuBtnDisabled}
            onClick={handleOk}
          >
            {modalTitleContentFooter.footer}
          </Button>,
        ]}
      >
        {modalTitleContentFooter.content}
      </Modal>
    </>
  );
};

const mapState = ({ CreateKnowledge: { choosenTemplate }}) => ({
  choosenTemplate,
});

export default connect(mapState)(CreateMenhu);
