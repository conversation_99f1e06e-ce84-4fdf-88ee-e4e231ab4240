import { intl } from 'di18n-react';
import { useEffect, useState, useMemo } from 'react';
import { Helmet } from 'react-helmet';
import { Tabs } from 'antd';
import classBind from 'classnames/bind';
import { MenhuIcon } from '@/assets/icon/';
import { getMyDKList, getMyMHList } from '@/service/knowledge/menhuIndex';
import * as commonStyles from '@/components/serviceComponents/Layout/style.module.less';
import DKList from '@/components/serviceComponents/DKList';
import DKSort from '@/pages/knowledge/MenHu/MyKnowledge/DKSort';
import CreateMenhu from '@/pages/knowledge/MenHu/MenhuList/CreateMenhu';
import * as styles from '@/pages/knowledge/MenHu/MyKnowledge/style.module.less';
import { isDesktopDC } from '@/utils';
import { useLocation, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { setLocalData, getLocalData } from '@/utils/localStorage';
import USER_VIEW from '@/constants/userView';
import { CooperTabs, CooperTabsPane } from '@/components/common/CooperTabs';


// 门户
// const { TabPane } = Tabs;

const cx = classBind.bind(styles);
const cm = classBind.bind(commonStyles);

const mapDKListKey = (data, key) => {
  for (let i = 0; i < 2; i++) {
    if (data[i].key === key) return i;
  }
};

const MyKnowledge = () => {
  const location = useLocation();
  const [joinData, setJoinData] = useState();

  const [ownerData, setOwnerData] = useState();
  const [initLoading, setInitLoading] = useState(true);
  const [errorData, setErrorData] = useState(false);
  const [isOwn, setIsOwn] = useState(true);

  const [sort, setSort] = useState({
    MY_JOIN:
      getLocalData('always:USER_VIEW')?.MH_Knowledge?.Sort_Type?.MY_JOIN || '2',
    MY_OWN:
      getLocalData('always:USER_VIEW')?.MH_Knowledge?.Sort_Type?.MY_OWN || '0',
  });
  const [MHsort, setMHSort] = useState({
    MH_MY_JOIN:
      getLocalData('always:USER_VIEW')?.MH_Knowledge?.Sort_Type?.MH_MY_JOIN
      || '0',
    MH_MY_OWN:
      getLocalData('always:USER_VIEW')?.MH_Knowledge?.Sort_Type?.MH_MY_OWN
      || '0',
  });
  const [menhuOwnerData, setMenhuOwnerData] = useState();
  const [menhuJoinData, setMenhuJoinData] = useState();

  const [tabActiveKey, setTabActiveKey] = useState();

  const dispatch = useDispatch();
  const { getPortalABSwitch } = dispatch.KnowledgePortal;
  const { portalABSwitch } = useSelector((state) => state.KnowledgePortal);

  useEffect(() => {
    setTabActiveKey('MH_MY_OWN');
    if (portalABSwitch === null) {
      getPortalABSwitch();
    }
  }, []);

  const tabInfo = useMemo(() => {
    return [
      {
        key: 'MH_MY_OWN',
        title: intl.t('我管理的'),
        data: menhuOwnerData,
      },
      {
        key: 'MH_MY_JOIN',
        title: intl.t('我参与的'),
        data: menhuJoinData,
      },
    ];
  }, [menhuOwnerData, menhuJoinData]);

  useEffect(() => {
    updateData(true);
    updateData(false);
  }, []);

  const updateData = (isOwnParam, currentSort) => {
    setInitLoading(true);
    let current = isOwnParam ? 'MY_OWN' : 'MY_JOIN';
    current = isOwnParam ? 'MH_MY_OWN' : 'MH_MY_JOIN';
    const getListFn = getMyMHList;
    const setDataOwner = setMenhuOwnerData;
    const setDataJoin = setMenhuJoinData;
    const params = {
      type: isOwnParam ? 1 : 2,
      orderType: currentSort || MHsort[current],
    };

    getListFn(params)
      .then((res) => {
        if (isOwnParam) {
          setDataOwner(res);
        } else {
          setDataJoin(res);
        }
      })
      .catch(() => {
        setErrorData(true);
      })
      .finally(() => {
        setInitLoading(false);
      });
  };

  const handleSort = (activeSort) => {
    setInitLoading(true);
    let currView = JSON.stringify(getLocalData('always:USER_VIEW')) !== '{}'
      ? getLocalData('always:USER_VIEW')
      : USER_VIEW;
    let current = '';
    current = isOwn ? 'MH_MY_OWN' : 'MH_MY_JOIN';
    setMHSort({ ...MHsort, [current]: activeSort });
    if (currView?.MH_Knowledge?.Sort_Type?.[current]) {
      currView.MH_Knowledge.Sort_Type[current] = activeSort;
    }
    updateData(isOwn, activeSort);
    setLocalData('always:USER_VIEW', currView);
  };

  const handleTabChange = (activeKey) => {
    setTabActiveKey(activeKey);
    let isOwner = activeKey === 'MY_OWN';
    isOwner = activeKey === 'MH_MY_OWN';
    setInitLoading(true);
    setIsOwn(isOwner);
    updateData(isOwner);
  };

  return (
    <div
      id="dkMenhuWrap"
      className={`${cx('my-dk-wrap')} ${cm('body-main-wrap', {
        'dc-body-main-wrap': isDesktopDC,
      })}`}
    >

      <Helmet>
        <title>{'知识门户-知识库-Cooper'}</title>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png" />
      </Helmet>
      <div
        className={`${cm('mainTitle')} ${cx('mainTitle-creact', {
          'mainTitle-creact-paddingRight': isDesktopDC,
        })}`}
      >
        <div
          className={`${cm('title', {
            'dk-menhu-title': isDesktopDC,
          })}`}
        >
          {/* {!isDesktopDC && (
            <img
              src={MenhuIcon}
              className={cx('dk-home-icon')} />
          )} */}
          {intl.t('知识门户')}
        </div>
        <CreateMenhu
          upDataFn={() => {
            setInitLoading(true);
            updateData(isOwn);
          }}
        />
      </div>

      <CooperTabs
        activeKey={tabActiveKey}
        onChange={handleTabChange}
        className="dk-tabs-has-fit"
        tabBarExtraContent={{
          right: (
            <DKSort
              knowledgeBoolean={false}
              type={isOwn ? 'MH_MY_OWN' : 'MH_MY_JOIN'}
              value={MHsort}
              onChange={handleSort}
            />
          ),
        }}
      >
        {tabInfo.map((item) => {
          return (
            <CooperTabsPane
              tab={
                <span>
                  <span>{item.title}</span>
                  <span className="ant-tabs-tab-btn-fit">
                    {item?.data?.length}
                  </span>
                </span>
              }
              key={item.key}
            >
              <DKList
                portalABSwitch={portalABSwitch}
                isOwn={isOwn}
                changeKey={item}
                errorData={errorData}
                initLoading={initLoading}
                dkData={tabInfo[mapDKListKey(tabInfo, item.key)].data}
                knowledgeBoolean={false}
              />
            </CooperTabsPane>
          );
        })}
      </CooperTabs>
    </div>
  );
};

export default MyKnowledge;
