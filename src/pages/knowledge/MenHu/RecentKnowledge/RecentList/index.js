import { intl } from 'di18n-react';
import { useState } from 'react';
import { Popover, Spin } from 'antd';
import { formatRecentTime } from '@/utils';
import classBind from 'classnames/bind';
import TextOnlyModal from '@/components/TextOnlyModal';
import RestrictLength from '@/components/RestrictLength';
import ScrollTableList from '@/components/serviceComponents/ScrollTableList';
import ErrorTips from '@/components/ErrorTipsDk';
import NoMore from '@/components/serviceComponents/NoMore';
import { PageNotPublishIcon, RecentListDkIcon } from '@/assets/icon';
import { RECENT_TABS, OWNER_TYPE } from '@/constants/recent';
import { gotoDk, gotoRecentPage } from '@/utils/goto';
import FileContent from '@/baseComponents/FileContent';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const RecentList = ({
  getOwnerType,
  loadMore,
  loading,
  firstPaint,
  hasMore,
  recentList,
  activeTab,
}) => {
  const [removeModalShow, setRemoveModalShow] = useState(false);
  const [ownerType, setOwnerType] = useState(OWNER_TYPE.ALL);

  const chooseOwnerType = (type) => {
    window.__OmegaEvent('ep_dkpc_dkhome_chooseowner_all_ck', '', {
      type: type === OWNER_TYPE.ALL ? 'all' : 'me',
    });
    setOwnerType(type);
    getOwnerType(type);
    loadMore(true, type);
  };

  const ownerList = [
    {
      type: OWNER_TYPE.ALL,
      icon: 'dk-icon-suoyouzhe-buxian',
      text: intl.t('不限归属'),
    },
    {
      type: OWNER_TYPE.MINE,
      icon: 'dk-icon-suoyouzhe-geren1',
      text: intl.t('归我所有'),
    },
  ];

  const ownerPopoverContent = (
    <ul className={cx('ownerPopoverContent')}>
      {ownerList.map((item) => {
        return (
          <li
            key={item.type}
            className={cx('ownerPopoverContent-item', {
              checked: item.type === ownerType,
            })}
            onClick={() => chooseOwnerType(item.type)}
          >
            <i className={cx('dk-iconfont', item.icon, 'icon')} />
            {item.text}
          </li>
        );
      })}
    </ul>
  );

  const columns = [
    {
      title: intl.t('页面'),
      dataIndex: 'pageName',
      key: 'pageName',
      width: '55%',
      className: cx('pageColumn'),
      render: (text, record) => {
        return (
          <FileContent
            resourceName={text}
            metisName={record.knowledgeName}
            onClickDkCallBack={clickDkName}
            isInTable={true}
            isLarge={true}
            metisId={record.knowledgeId}
            tags={record.shareId ? [intl.t('访客链接')] : null}
          />
        );
      },
    },
    {
      title: (
        <div>
          <Popover
            trigger="click"
            placement="bottom"
            overlayClassName={cx('dk-ant-popover__reset', 'option-popover')}
            content={ownerPopoverContent}
          >
            {intl.t('所有者')}
            <i
              className={`${cx(
                'dk-icon-shaixuan',
                'dk-iconfont',
                'owner-icon',
              )} `}
            />
          </Popover>
        </div>
      ),
      dataIndex: 'owner',
      key: 'owner',
      width: '18%',
      render: (text) => {
        return <div>{text}</div>;
      },
    },
    {
      title: intl.t('{slot0}时间', {
        slot0: activeTab === RECENT_TABS.EDIT ? intl.t('编辑') : intl.t('浏览'),
      }),
      dataIndex: `${activeTab === RECENT_TABS.EDIT ? 'editTime' : 'viewTime'}`,
      key: 'time',
      width: '18%',
      render: (text) => {
        return <div>{formatRecentTime(text)}</div>;
      },
    },
    {
      title: intl.t('最新发布'),
      dataIndex: 'publishTime',
      key: 'publishTime',
      width: '18%',
      render: (text) => {
        return <div>{formatRecentTime(text)}</div>;
      },
    },
    // 下面的后期要用，不要删
    // {
    //   title: '操作',
    //   key: 'action',
    //   width: '15%',
    //   render: (text, record) => (
    //     <div>
    //       <Popover
    //         // visible={true}
    //         trigger={['click']}
    //         zIndex={2002}
    //         getPopupContainer={(trigger) => trigger.parentNode}
    //         overlayClassName={cx('dk-ant-popover__reset', 'option-popover')}
    //         content={ <div className={cx('option')}>
    //           <div
    //             className={cx('option-item')}
    //             onClick={openRemoveModal}>
    //             <i className={`${cx('dk-icon-shanchucharulie', 'remove-icon', 'dk-iconfont')} `} />移除
    //           </div>
    //         </div>}
    //         placement="bottom">
    //         <Tooltip
    //           title="更多"
    //           placement="top"
    //           overlayClassName={'dk-ant-tooltip__reset'}>
    //           <div className={cx('option-icon-wrap')}>
    //             <i className={`${cx('dk-icon-gengduo', 'option-icon', 'dk-iconfont')} `} />
    //           </div>
    //         </Tooltip>
    //       </Popover>
    //     </div>
    //   ),
    // },
  ];
  // const openRemoveModal = () => {
  //   setRemoveModalShow(true);
  // };
  const closeRemoveModal = () => {
    setRemoveModalShow(false);
  };
  const remove = () => {};
  const clickRow = (item) => {
    if (activeTab === RECENT_TABS.EDIT) {
      window.__OmegaEvent('ep_dkpc_dkhome_recentedit_visitpage_ck');
    } else {
      window.__OmegaEvent('ep_dkpc_dkhome_recentview_visitpage_ck');
    }
    gotoRecentPage({ ...item, activeTab });
  };
  const clickDkName = () => {
    if (activeTab === RECENT_TABS.EDIT) {
      window.__OmegaEvent('ep_dkpc_dkhome_recentedit_visitdk_ck');
    } else {
      window.__OmegaEvent('ep_dkpc_dkhome_recentview_visitdk_ck');
    }
  };

  return (
    <div className={cx('recent-list-box')}>
      {!recentList.length && loading && (
        <div className={'page-detail-loading'}>
          <Spin />
        </div>
      )}

      {!firstPaint
        && (recentList.length === 0 && !loading ? (
          <div className={cx('recent_empty')}>
            <ErrorTips
              img={PageNotPublishIcon}
              title={intl.t('你最近{slot0}的页面记录会展示在这里！', {
                slot0: activeTab === RECENT_TABS.EDIT ? intl.t('编辑') : intl.t('浏览'),
              })}
            />
          </div>
        ) : (
          <div
            id="menhu-recentList"
            className={cx('recentList', 'knowledge_recentList_wrap')}
          >
            <ScrollTableList
              loadMore={() => loadMore(false, ownerType)}
              loading={loading}
              hasMore={hasMore}
              columns={columns}
              dataList={recentList}
              clickRow={clickRow}
            />

            {!loading && !hasMore && <NoMore />}

            <TextOnlyModal
              handleOk={remove}
              handleCancel={closeRemoveModal}
              modalVisible={removeModalShow}
              title={intl.t('是否从最近列表中移除?')}
              tip={intl.t('移除后，该内容在最近列表中将不可见。')}
              okText={intl.t('移除')}
            />
          </div>
        ))}
    </div>
  );
};

export default RecentList;
