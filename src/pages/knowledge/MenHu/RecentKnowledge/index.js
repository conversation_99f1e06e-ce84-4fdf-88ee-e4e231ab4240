import { intl } from 'di18n-react';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet';
import RecentList from './RecentList';
import classBind from 'classnames/bind';
import * as styles from './style.module.less';
import { RecentDkIcon } from '@/assets/icon';
import { getUrlParams, isDesktopDC } from '@/utils';
import { RECENT_TABS } from '@/constants/recent';
import useRecentData from '@/hooks/useRecentData';
import commonStyles from '@/components/serviceComponents/Layout/style.module.less';
import { CooperTabs, CooperTabsPane } from '@/components/common/CooperTabs';

const cx = classBind.bind(styles);
const cm = classBind.bind(commonStyles);

const RecentKnowledge = () => {
  const key = getUrlParams('key');
  const [ownerType, setOwnerType] = useState(); // 获取子组件的ownerType,用于dc切换时刷新
  const {
    tabInfo,
    loading,
    firstPaint,
    hasMore,
    loadMore,
    handleTabChange,
    recentList,
    activeTab,
  } = useRecentData(true);

  useEffect(() => {
    window.document.title = intl.t('最近');
  }, []);

  const refresh = () => {
    loadMore(true, ownerType);
  };
  const getOwnerType = (type) => {
    setOwnerType(type);
  };
  const onTabChange = (activeKey) => {
    if (activeKey === RECENT_TABS.VISIT) {
      window.__OmegaEvent('ep_dkpc_dkhome_recentview_ck');
    } else {
      window.__OmegaEvent('ep_dkpc_dkhome_recentedit_ck');
    }
    handleTabChange(activeKey);
  };

  useEffect(() => {
    if (isDesktopDC) {
      window.addEventListener('dc-event-app-active-knowledge', refresh);
    }

    return () => {
      window.removeEventListener('dc-event-app-active-knowledge', refresh);
    };
  }, []);

  return (
    <div
      className={`${cx('recentKnowledge-wrap')} ${cm('body-main-wrap', {
        'dc-body-main-wrap': isDesktopDC,
      })}`}
    >
      <Helmet>
        <title>最近-知识库-Cooper</title>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png" />
      </Helmet>
      <div className={cm('mainTitle')}>
        {/* {!isDesktopDC && <img
         src={RecentDkIcon}
         className={cm('titleIcon')} />} */}
        <div
          className={cm('title', {
            'dk-menhu-title': isDesktopDC,
          })}
        >
          {intl.t('最近')}
        </div>
      </div>
      <CooperTabs
        className={cx('recentKnowledge')}
        defaultActiveKey={key}
        destroyInactiveTabPane={true}
        onChange={onTabChange}
      >
        {tabInfo.map((item) => {
          return (
            <CooperTabsPane
              tab={item.name}
              key={item.key}>
              <RecentList
                getOwnerType={getOwnerType}
                loadMore={loadMore}
                loading={loading}
                firstPaint={firstPaint}
                hasMore={hasMore}
                recentList={recentList}
                activeTab={activeTab}
              />
            </CooperTabsPane>
          );
        })}
      </CooperTabs>
    </div>
  );
};

export default RecentKnowledge;
