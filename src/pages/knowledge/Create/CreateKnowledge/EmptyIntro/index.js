import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import React from 'react';
import { connect } from 'react-redux';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const EmptyIntro = ({ choosenTemplate }) => {
  return (
    <div className={cx('emptyIntro')}>
      <p className={cx('title')}>{intl.t('结构预览')}</p>
      <img
        src={choosenTemplate.thumbnailUrl}
        className={cx('pic')} />
    </div>
  );
};

const mapState = ({ CreateKnowledge: { choosenTemplate }}) => ({
  choosenTemplate,
});
export default connect(mapState)(EmptyIntro);
