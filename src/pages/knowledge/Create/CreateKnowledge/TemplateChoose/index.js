import { intl } from 'di18n-react';
import { useState } from 'react';
import { connect } from 'react-redux';
import { Divider } from 'antd';
import classBind from 'classnames/bind';
import { CreateKnowledgeIcon, NotificationWarnIcon } from '@/assets/icon';
import { STEP, TEMPLATE_TYPE } from '@/constants/createKnowledge';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import EmptyIntro from '../EmptyIntro/index';
import { openNewWindow } from '@/utils';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const TemplateChoose = ({
  templateList,
  step,
  setStep,
  setChoosenTemplate,
  setChoosenIcon,
}) => {
  const [choosenId, setChoosenId] = useState();
  const notification = useNotification();

  const gotoTeam = () => {
    openNewWindow('https://team.didichuxing.com');
  };
  const teamNotice = (
    <div className={cx('teamNotice')}>
      <p>
        <img
          className={cx('icon')}
          src={NotificationWarnIcon} />
        {intl.t('部门知识库的创建能力正在路上！')}
      </p>
      <p>
        {intl.t('部门负责人可前往')}
        <span
          onClick={gotoTeam}
          className={cx('link')}>
          {intl.t('团队中心')}
        </span>
        {intl.t('升级现有知识为部门知识库')}
      </p>
    </div>
  );
  const onChoose = (templateId) => {
    const choosenTemplate = templateList.find(
      (item) => item.templateId === templateId,
    );
    let type = '';
    switch (choosenTemplate.businessType) {
      case TEMPLATE_TYPE.DEPART:
        type = 'team';
        notification(
          NotificationStatus.WARN,
          teamNotice,
          null,
          2.4,
          'team',
          true,
        );
        return;
      case TEMPLATE_TYPE.PROJECT:
        type = 'item';
        notification(NotificationStatus.WARN, intl.t('正在路上，敬请期待'));
        return;
      default:
        type = 'blank';
    }
    window.__OmegaEvent('ep_dkpc_createdk_dktemplate_ck', '', { type });
    setStep(STEP.TEMPLATE_CHOOSED);
    setChoosenId(templateId);
    setChoosenTemplate(choosenTemplate);
    setChoosenIcon(choosenTemplate.iconUrl);
  };

  return (
    <div className={cx('templateChoose')}>
      <div className={cx('content')}>
        <div className={cx('templateWrap')}>
          {(templateList || []).map((template) => {
            return (
              <div
                key={template.templateId}
                className={cx({
                  template: true,
                  choosen: choosenId === template.templateId,
                })}
                onClick={() => onChoose(template.templateId)}
              >
                <img
                  src={template.iconUrl}
                  className={cx('avatar')} />
                <div>
                  <p className={cx('title')}>
                    {intl.t(template.name)}
                    {(template.businessType === TEMPLATE_TYPE.PROJECT
                      || template.businessType === TEMPLATE_TYPE.DEPART) && (
                        <span className={cx('tip')}>
                          <span className={cx('tipWord')}>
                            {intl.t('敬请期待')}
                          </span>
                        </span>
                    )}
                  </p>
                  <p className={cx('description')}>{intl.t(template.description)}</p>
                </div>
              </div>
            );
          })}
        </div>
        <Divider
          type="vertical"
          className={cx('divider')} />
        {step === STEP.TEMPLATE_CHOOSING ? (
          <div className={cx('defaultArea')}>
            <img
              className={cx('defaultPic')}
              src={CreateKnowledgeIcon} />
            <p className={cx('defaultTitle')}>{intl.t('欢迎使用知识库')}</p>
          </div>
        ) : (
          <EmptyIntro />
        )}
      </div>
    </div>
  );
};

const mapDispatch = ({
  CreateKnowledge: { setChoosenTemplate, setChoosenIcon },
}) => ({
  setChoosenTemplate,
  setChoosenIcon,
});
export default connect(null, mapDispatch)(TemplateChoose);
