import { useEffect, useState } from 'react';
import { intl } from 'di18n-react';
import { connect, useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { But<PERSON>, Modal } from 'antd';
import classBind from 'classnames/bind';
import { CreateKnowledgeBackIcon, ModalCloseIcon } from '@/assets/icon';
import { STEP } from '@/constants/createKnowledge';
import { createKnowledge, getTeamDetail, getTemplate } from '@/service/knowledge/createKnowledgeModal';
import { loadImage, isDesktopDC, openNewWindow, checkNameErrorMsg } from '@/utils';
import TextOnlyModal from '@/components/TextOnlyModal';
import InfoInput from './InfoInput/index';
import TemplateChoose from './TemplateChoose/index';
import { cloneDeep } from 'lodash-es';
import * as styles from './style.module.less';


const cx = classBind.bind(styles);

const CreateKnowledge = ({ choosenTemplate, mapperSpaceId }) => {
  const navigate = useNavigate();
  const [visible] = useState(true);
  const [cooperTeamDetail, setCooperTeamDetail] = useState({});
  // const [mySpaceCreation, setMySpaceCreation] = useState([]);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [step, setStep] = useState(STEP.TEMPLATE_CHOOSING);
  const [form, setForm] = useState();
  const [templateList, setTemplateList] = useState([]);
  const [selectCooperGroup] = useState([]);
  const [departmentId, setDepartmentId] = useState('');
  const [fields, setFields] = useState([{ name: 'dkName', value: '' }]);
  const [departMentModalVisible, setDepartMentModalVisible] = useState(false);
  const { chooseIcon } = useSelector((state) => state.CreateKnowledge);
  const dispatch = useDispatch();

  const { getFeatures } = dispatch.TeamData;

  useEffect(() => {
    getTemplate().then((list) => {
      setTemplateList(list);
      list.forEach((item) => {
        loadImage(item.thumbnailUrl);
      });
    });
    mapperSpaceId && getTeamDetail(mapperSpaceId).then((res) => {
      res && setCooperTeamDetail(res);
    });

    mapperSpaceId && getFeatures(mapperSpaceId);
    // getMySpaceCreation().then((res) => {
    //   setMySpaceCreation(res);
    // });
  }, []);

  const setChooseDepartmentId = (id) => {
    setDepartmentId(id);
  };

  const handleOk = () => {
    if (step === STEP.TEMPLATE_CHOOSING) {
      return;
    }
    setStep(STEP.INPUT_INFO);
    if (step === STEP.INPUT_INFO) {
      setConfirmLoading(true);

      createKnowledge({
        exPicture: chooseIcon,
        exDesc: form.getFieldValue('exDesc') || '',
        exTemplateId: choosenTemplate.templateId,
        metisSpaceName: form.getFieldValue('dkName'),
        departmentId,
        allMapperSpaceId: mapperSpaceId ? selectCooperGroup.concat([mapperSpaceId]) : selectCooperGroup,
      }).then((res) => {
        const { metisSpaceId, createTeamReq: { name }} = res;
        if (departmentId) {
          setDepartMentModalVisible(true);
        } else if (isDesktopDC) {
          // 原页面不能维持在创建页面
          navigate('/knowledge');
          openNewWindow(`/knowledge/${metisSpaceId}/home`);
        } else {
          window.location.href = `/knowledge/${metisSpaceId}/home`;
        }
      }).catch(() => {
        // getMySpaceCreation().then((res) => {
        //   setMySpaceCreation(res);
        // });
      }).finally(() => {
        setConfirmLoading(false);
      });
    }
  };

  const goBack = () => {
    setFields([{ name: 'dkName', value: '' }]);
    setStep(STEP.TEMPLATE_CHOOSING);
  };

  const handleCancel = () => {
    if (mapperSpaceId) {
      window.close();
    } else {
      navigate('/knowledge', { replace: true });
    }
  };

  const getForm = (formData) => {
    setForm(formData);
  };
  const isInfoComplete = () => {
    if (!form) return false;
    const dkName = fields[0].value;
    // if (choosenTemplate.businessType === TEMPLATE_TYPE.DEPART) {
    //   return dkName && departmentId;
    // }
    return checkNameErrorMsg(dkName) === '';
  };
  const okBtnDisabled = step === STEP.TEMPLATE_CHOOSING ? true
    : step === STEP.TEMPLATE_CHOOSED ? false
      : !isInfoComplete();

  const onInputInfoChange = (newFields) => {
    setFields(newFields);
  };

  const closeDepartMentModal = () => {
    setDepartMentModalVisible(false);
    handleCancel();
  };
  // const setSelectCooperGroupList = (cooperGroupList) => {
  //   setSelectCooperGroup(cooperGroupList);
  // };

  return (
    <div>
      <Modal
        footer={null}
        visible={visible}
        onCancel={handleCancel}
        wrapClassName={cx('createKnowledge')}
        maskClosable={false}
        width={851}
        bodyStyle={{ padding: '26px 0 24px 30px' }}
        closable={false}
      >
        <div className={cx('titleArea')}>
          {step === STEP.INPUT_INFO && <div
            className={cx('backIconWrap')}
            onClick={goBack}>
            <img
              src={CreateKnowledgeBackIcon}
              className={cx('backIcon')} />
          </div>}
          <div className={cx('title')}>{step === STEP.INPUT_INFO ? intl.t('完善知识库信息') : intl.t('选择所需知识库类型')}</div>
        </div>
        <div
          onClick={handleCancel}
          className={cx('closeIconWrap')}><img
            className={cx('closeIcon')}
            src={ModalCloseIcon} />
        </div>
        {step === STEP.INPUT_INFO ? <InfoInput
          mapperSpaceId={mapperSpaceId}
          cooperTeamDetail={cooperTeamDetail}
          // mySpaceCreation={mySpaceCreation}
          handleCancel={handleCancel}
          getForm={getForm}
          selectCooperGroupList={selectCooperGroup}
          setChooseDepartmentId={setChooseDepartmentId}
          // setSelectCooperGroupList={setSelectCooperGroupList}
          onChange={onInputInfoChange} />
          : <TemplateChoose
              templateList={templateList}
              setStep={setStep}
              step={step} />
        }
        <div className={cx('buttonArea')}>
          <Button
            loading={confirmLoading}
            disabled={okBtnDisabled}
            onClick={handleOk}
            className={cx({ ok: true, okDisabled: okBtnDisabled })}>
            {step === STEP.INPUT_INFO ? intl.t('新建知识库-创建按钮') : intl.t('新建知识库-下一步按钮')}
          </Button>
          <Button
            onClick={handleCancel}
            className={cx('cancel')}>{intl.t('新建知识库-取消按钮')}</Button>
        </div>
      </Modal>
      <TextOnlyModal
        okText='知道了'
        customClass={cx('departMentModal')}
        handleOk={closeDepartMentModal}
        handleCancel={closeDepartMentModal}
        modalVisible={departMentModalVisible}
        title='部门官方知识库申请已提交，请等待！'
        tip={<span>审核阶段不支持使用该知识库，审核通过后知识库会出现在知识库门户「我拥有的」模块下，打上“部门”标签，部门成员实时同步成为知识库成员；审核未通过亦无法使用该部门知识库。
          如需查看审核进度，请前往：<a
            style={{ color: '#047FFE' }}
            href='https://bpm.didichuxing.com/process/self/list'>https://bpm.didichuxing.com/process/self/list</a></span>}
      />
    </div>
  );
};

const mapState = ({ CreateKnowledge: { choosenTemplate }}) => ({
  choosenTemplate,
});

export default connect(mapState)(CreateKnowledge);
