import { intl } from 'di18n-react';
import { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Divider, Form, Input, Tooltip, Select } from 'antd';
import classBind from 'classnames/bind';
import { debounce } from 'lodash-es';
import GroupIconChoose from '@/components/serviceComponents/GroupIconChoose';
import groupChoosenIcons from '@/constants/groupIconChoose';
import { GroupIcon, hoverTipIcon } from '@/assets/icon';
import {
  getDepartment,
  canDepartmentUse,
} from '@/service/knowledge/createKnowledgeModal';
import {
  beautyAllCapitalSub,
  highLightKeyword,
  checkNameErrorMsg,
} from '@/utils';
import { TEMPLATE_TYPE } from '@/constants/createKnowledge';
import EmptyIntro from '../EmptyIntro/index';
import * as styles from './style.module.less';

const { TextArea } = Input;
const { Option } = Select;

const cx = classBind.bind(styles);
const InfoInput = ({
  handleCancel,
  getForm,
  onChange,
  chooseIcon,
  cooperTeamDetail,
  setChoosenIcon,
  choosenTemplate,
  mapperSpaceId,
  setChooseDepartmentId,
}) => {
  const [form] = Form.useForm();
  const [departmentList, setDepartmentList] = useState([]);
  const [department, setDepartment] = useState({});
  const [searchValue, setSearchValue] = useState('');
  const [nameCheckMsg, setNameCheckMsg] = useState('');
  const [departmentCannotUseTip, setDepartmentCannotUseTip] = useState('');
  const isDepartmentDk = choosenTemplate.businessType === TEMPLATE_TYPE.DEPART;

  useEffect(() => {
    getForm(form);
    form.setFieldsValue({ dkName: choosenTemplate.name });
    onChange([{ name: 'dkName', value: choosenTemplate.name }]);
    setChoosenIcon(
      groupChoosenIcons[Math.floor(Math.random() * groupChoosenIcons.length)]
        .value,
    );
  }, []);

  const searchDepartment = async (value) => {
    const data = await getDepartment({ keywords: value });
    data && setDepartmentList(data.items);
  };

  const handleSearch = debounce((value) => {
    setSearchValue(value);
    if (value) {
      searchDepartment(value);
    } else {
      setDepartmentList([]);
    }
  }, 500);

  const clearChooseDepartMent = () => {
    setDepartment({});
    setChooseDepartmentId('');
    setDepartmentList([]);
  };

  const handleChange = async (value) => {
    const departmentInfo = departmentList.find(
      (item) => item.departmentId === value,
    );
    if (!departmentInfo) {
      clearChooseDepartMent();
      return;
    }
    const res = await canDepartmentUse({
      departmentId: departmentInfo.departmentId,
    });
    if (res === 'EMPTY') {
      departmentInfo.path = optionText(departmentInfo.departmentPath, 16);
      setDepartment(departmentInfo);
      setChooseDepartmentId(value);
      setDepartmentCannotUseTip('');
    } else if (res === 'APPROVE') {
      clearChooseDepartMent();
      setDepartmentCannotUseTip(
        intl.t('该部门已存在一个审批中的部门知识库申请，不可重复创建'),
      );
    } else {
      clearChooseDepartMent();
      setDepartmentCannotUseTip(
        intl.t('该部门已存在官方部门知识库，不可重复创建'),
      );
    }
  };

  // const selectCooperGroupchange = (cooperGroupList) => {
  //   setSelectCooperGroupList(cooperGroupList);
  // };
  // const checkTip = (cannotCheck) => {
  //   cannotCheck && notification(NotificationStatus.WARN, '该团队关联知识库已达上限5个，不支持选择');
  // };
  // const optionDisabled = (bindWith) => {
  //   return bindWith.length > 4;
  // };
  const optionText = (departmentPath, maxLength) => {
    const { length } = departmentPath;
    const lastword = departmentPath[length - 1];
    return `${beautyAllCapitalSub(
      departmentPath.slice(0, length - 1).join('>'),
      maxLength - lastword.length,
    )}>${lastword}`;
  };
  const hideDepartmentPath = (departmentPath) => {
    const { length } = departmentPath.join('>');
    return length < 22;
  };
  const options = departmentList.map((d) => (
    <Option key={d.departmentId}>
      <Tooltip
        placement="top"
        overlayClassName={cx('dk-ant-popover__reset', {
          hideDepartmentPath: hideDepartmentPath(d.departmentPath),
        })}
        title={d.departmentPath.join('>')}
      >
        <span
        >{
            highLightKeyword(
              searchValue,
              optionText(d.departmentPath, 22),
            )
        }</span>
      </Tooltip>
    </Option>
  ));

  const handleValuesChange = () => {
    const name = form.getFieldValue('dkName');
    setNameCheckMsg(checkNameErrorMsg(name));
  };

  return (
    <div className={cx('InfoInput')}>
      <div className={cx('content')}>
        <div>
          <Form
            className={cx('form', 'dk-ant-form_reset')}
            name="basic"
            form={form}
            onFieldsChange={(_, allFields) => {
              onChange(allFields);
            }}
            onValuesChange={handleValuesChange}
          >
            <div className={cx('namelabel', 'title')}>
              <span className={cx('redIcon')}>* </span>
              {intl.t('名称')}
            </div>
            <div className={cx('nameItem')}>
              <GroupIconChoose getChooseIcon={setChoosenIcon}>
                <div className={cx('iconWrap')}>
                  <img
                    className={cx('icon')}
                    src={chooseIcon} />
                </div>
              </GroupIconChoose>
              <Form.Item
                label=""
                name="dkName"
                className={cx('nameInput-wrap')}
              >
                <Input
                  maxLength={100}
                  className={cx('nameInput')}
                  placeholder={intl.t('必填，上限100字')}
                />
              </Form.Item>
              <div className={cx('createDkNameCheckMsg', 'nameCheckMsg')}>
                {nameCheckMsg}
              </div>
            </div>
            {isDepartmentDk && (
              <div className={cx('chooseDepartment')}>
                <div className={cx('title')}>
                  <span className={cx('redIcon')}>* </span>
                  {intl.t('选择部门')}
                  <Tooltip
                    title={intl.t(
                      '部门知识库创建成功后会自动提交审核，审核阶段不能使用该知识库，审核通过后该知识库会打上“部门”标签，部门成员实时同步成为知识库成员；审核未通过亦无法使用该部门知识库',
                    )}
                    placement="bottom"
                    overlayClassName={`${cx(
                      'dk-ant-tooltip__reset ',
                    )}createDk-chooseDepartmentTip`}
                  >
                    <img
                      className={cx('departmentTip')}
                      src={hoverTipIcon} />
                  </Tooltip>
                </div>
                <Form.Item
                  label=""
                  name="department">
                  <Select
                    allowClear
                    showSearch
                    placeholder={intl.t('请选择部门')}
                    bordered={false}
                    defaultActiveFirstOption={false}
                    showArrow={false}
                    filterOption={false}
                    onSearch={handleSearch}
                    onChange={handleChange}
                    notFoundContent={null}
                    listHeight={334}
                    dropdownClassName={cx('chooseDepartment-dropdown')}
                  >
                    {options}
                  </Select>
                </Form.Item>
                {departmentCannotUseTip && (
                  <div className={cx('departmentCannotUseTip')}>
                    {departmentCannotUseTip}
                  </div>
                )}
              </div>
            )}

            <div className={cx('title')}>{intl.t('简介')}</div>
            <Form.Item
              label=""
              name="exDesc">
              <TextArea
                className={cx('introText')}
                maxLength={1000}
                placeholder={intl.t('选填，上限1000字')}
              />
            </Form.Item>

            {/* 以下注释内容产品说之后会用到，先不删 */}
            {/* <Form.Item
             label=""
             name="selectCooperGroup"
            >
             <div className={cx('selectCooperTitile')}>关联Cooper团队空间</div>
             <Select
               listItemHeight={40}
               listHeight={200}
               onChange={selectCooperGroupchange}
               removeIcon={<img
                 className={cx('removeIcon')}
                 src={ModalCloseIcon} />}
               className={cx('selectCooperGroup')}
               mode="multiple"
               notFoundContent={'您暂未创建团队空间\n可前往Cooper进行创建'}
             // tagRender={tagRender}
               // defaultOpen={true}
               placeholder="请选择你创建的Cooper团队"
               menuItemSelectedIcon={<img
                 className={cx('checkIcon')}
                 src={powerManagermentCheckIcon} />}
               dropdownClassName={cx('selectCooperGroup_dropdown')}
               dropdownRender={(menu) => {
                 return <div>
                   <div className={ cx('title') }>
                     我创建的团队空间
                   </div>
                   {menu}
                 </div>;
               }}
            >
               {mySpaceCreation.map((item) => {
                 return <Option
                   disabled={optionDisabled(item.bindWith)}
                   key={item.metisSpaceId}
                   value={item.metisSpaceId}
                 >
                   <div
                     onClick={() => checkTip(optionDisabled(item.bindWith))}>
                     <span className={cx('spaceName')}>{item.spaceName}</span>
                     <span className={cx('teamManNumber')}>({item.teamManNumber}人)</span>
                   </div>
                 </Option>;
               })}
             </Select>
             <div className={cx('selectCooperTip')}>
               关联后，该知识库将会出现在团队空间中，空间成员能访问知识库。
             </div>
            </Form.Item> */}
          </Form>
          {(mapperSpaceId || department?.departmentId) && (
            <div className={cx('groupTitle')}>{intl.t('成员')}</div>
          )}
          {mapperSpaceId && (
            <div className={cx('group')}>
              <div className={cx('groupInfo')}>
                <img
                  className={cx('groupIcon')}
                  src={GroupIcon} />
                <div className={cx('groupNameAndNum')}>
                  <span className={cx('groupName')}>
                    {cooperTeamDetail.name}
                  </span>
                  ({cooperTeamDetail.member_count}
                  {intl.t('人)')}
                </div>
              </div>
              <p className={cx('groupFunction')}>
                {intl.t('来源：Cooper团队空间')}
              </p>
            </div>
          )}
          {department.departmentId && (
            <div className={cx('group')}>
              <div className={cx('groupInfo')}>
                <img
                  className={cx('groupIcon')}
                  src={GroupIcon} />
                <div className={cx('groupNameAndNum')}>
                  <span className={cx('groupName')}>{department.path}</span>
                </div>
              </div>
              <p className={cx('groupFunction')}>{intl.t('来源：组织结构')}</p>
            </div>
          )}
        </div>
        <Divider
          type="vertical"
          className={cx('divider')} />
        <EmptyIntro handleCancel={handleCancel} />
      </div>
    </div>
  );
};

const mapState = ({ CreateKnowledge: { choosenTemplate, chooseIcon }}) => ({
  choosenTemplate,
  chooseIcon,
});
const mapDispatch = ({ CreateKnowledge: { setChoosenIcon }}) => ({
  setChoosenIcon,
});
export default connect(mapState, mapDispatch)(InfoInput);
