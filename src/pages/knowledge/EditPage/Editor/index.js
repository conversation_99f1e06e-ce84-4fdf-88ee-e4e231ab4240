import { intl } from 'di18n-react';
import { useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { debounce } from 'lodash-es';
import ReactDOM from 'react-dom';
import { connect } from 'react-redux';
import { MrParcel } from '@didi/mf-tenon';
import classnames from 'classnames/bind';
import { useLocation } from 'react-router-dom';
import { Modal, Tooltip } from 'antd';
import produce from 'immer';
import {
  EditorConfig,
  EditorMicroUrl,
  EditorName,
  getIframeConfig,
  MenuConfig,
} from '@/constants/editor';
import { checkEmpty, formatDateTime, inPhone } from '@/utils';
import { getName, reportMeasure } from '@/utils/performance';
import styles from './style.module.less';

const cls = classnames.bind(styles);

const isInPhone = inPhone();

window.msContainer = true;
function Editor(props) {
  const containerRef = useRef();
  const viewRef = useRef();
  const {
    changeSaveStatus,
    changeView,
    changeCollaborators,
    docInfo,
    changeInitLoading,
    profile,
    changeTemplateVisible,
    writerVisible,
    changeEditReady,
    toggleWriterVisible,
    editorKey,
    changeBlock,
    isBlock,
    parentId,
    alertTip,
    setAlertTip,
  } = props;
  const { guid, accessToken, latestVersion, pageId } = docInfo;
  const userInfo = profile;
  const { pathname } = useLocation();
  const [view, setView] = useState();
  const alertTipRef = useRef(null);
  const boxRef = useRef(null);

  useEffect(() => {
    const updateWidth = () => {
      if (boxRef.current) {
        const newWidth = document.querySelector('#knowedge-body-content')?.clientWidth;
        boxRef.current.style.width = `${newWidth}px`;
      }
    };

    window.addEventListener('resize', updateWidth);
    updateWidth();

    return () => {
      window.removeEventListener('resize', updateWidth);
    };
  }, []);

  useEffect(async () => {
    const element = document.querySelector('#knowedge-body-content');

    if (window.ResizeObserver === undefined) {
      const module = await import('resize-observer-polyfill');
      window.ResizeObserver = module.default;
    }

    const resizeObserver = new window.ResizeObserver((entries) => {
      for (const entry of entries) {
        if (boxRef.current) {
          boxRef.current.style.width = `${entry.contentRect.width}px`;
        }
      }
    });

    if (element) {
      resizeObserver.observe(element);
    }

    return () => {
      if (element) {
        resizeObserver.unobserve(element);
      }
    };
  }, []);

  const updateCollaboratorsByView = (view) => {
    if (view) {
      // 更新协作者
      const { cursorList = [] } = view?.getOnlineCollabs?.() || {};
      changeCollaborators(cursorList);
    }
  };

  const reloadWindow = () => {
    window.location.reload();
  };

  const detectTemplate = useCallback(debounce(() => {
    const ele = containerRef.current?.querySelector('.ProseMirror.didoc-editor-container');
    if (!ele) return;
    
    const isEmpty = checkEmpty(ele);
    changeTemplateVisible(isEmpty);
  }, 300), []);

  const handleRevertHistory = (data = {}) => {
    const { users = {}, versionTime } = data;
    if (users.name === profile.username) {
      return;
    }
    const content = (
      <div>
        {`${decodeURI(decodeURI(users.nameCn))} ${users.name}`}
        {intl.t('已将⻚面恢复至')}
        <span className={cls('time')}>{formatDateTime(versionTime)}</span>
        {intl.t(
          '的历史记录，⻚面刷新后将展示最新内容。你仍可以在历史记录中找回修改内容。',
        )}
      </div>
    );

    Modal.info({
      className: cls('revert-history-modal'),
      width: 480,
      title: intl.t('当前页面已被还原'),
      icon: null,
      centered: true,
      content,
      okText: intl.t('刷新页面'),
      onOk() {
        window.location.reload();
      },
    });
  };

  useEffect(() => {
    const dom = view?.dom || view?.dom?.dom;
    if (!dom) return;
    writerVisible
      ? dom.classList.add('collab-show')
      : dom.classList.remove('collab-show');
  }, [writerVisible]);

  const handleBeforeunload = (event) => {
    let res = isBlock ? 'unload' : undefined;
    if (res) {
      event.preventDefault();
      event.returnValue = res;
      return res;
    }
  };

  useEffect(() => {
    changeSaveStatus('');
    window.addEventListener('beforeunload', handleBeforeunload);
  }, []);

  const getMenuConfig = () => {
    const newMenu = produce(MenuConfig, (draft) => {
      const iframeConfig = getIframeConfig();
      const { children } = draft.menuBarContent.selectGroup;
      draft.menuBarContent.selectGroup.children = {
        ...children,
        ...iframeConfig,
      };
    });
    return newMenu;
  };

  const initAI = (editorView) => {
    const showAi = window.editorIframeABSwitch;

    if (showAi && !isInPhone) {
      try {
        window.CooperClientService?.create({
          resourceId: pageId,
          getVersion: editorView.getVersion,
        })
      } catch (error) {}
    }
  }

  const editorConfig = {
    ...EditorConfig,

    eventHandler: {
      init: (state, view) => {
        changeInitLoading(false);
        changeView(view);
        updateCollaboratorsByView(view);
        detectTemplate();
        changeEditReady(true);
        setView(view);
        viewRef.current = view;

        initAI(view);

        // document.getElementById('didoc-editor-slot-sub-title').innerHTML = '';

        if (!window.pageEditFirstShow) {
          window.pageEditFirstShow = true;
          window.pageDetailFirstShow = true;
          reportMeasure({
            startName: `editPage-first-start${pageId}`,
            endName: `editPage-first-end${pageId}`,
            measureName: 'load-editPage-first',
          });
        } else {
          reportMeasure({
            startName: `editPage-start${pageId}`,
            endName: `editPage-end${pageId}`,
            measureName: 'load-editPage',
          });
        }

        /**
         * 切换页面等会多次执行，设置flag，全局只上报最开始进来那次
         */
        if (window.isInitEditMark) {
          window.endTime = window.performance.now();
          window.__OmegaEvent(
            'tech_performance_custom',
            intl.t('性能自定义埋点'),
            {
              timingCategory: 'load-editPage-global',
              timingName: getName(pathname),
              timingValue: window?.endTime - window?.startTime ?? 0,
            },
          );
          window.isInitEditMark = false;
          console.log(
            '*load-editPage-global*',
            window?.endTime - window?.startTime,
          );
        }

        reportMeasure({
          startName: `catalog-create-start${parentId}`,
          endName: `catalog-create-open-end${parentId}`,
          measureName: 'catalog-create-open-time',
        });
      },
      onChange: (state, view, action) => {
        if (action && action.type === 'PluginCursor') {
          updateCollaboratorsByView(view);
        }

        if (action && action.type === 'historyRevert') {
          handleRevertHistory(action.value);
        }
        if (action && action.type === 'fileChange') {
          changeBlock(!!action.value?.length);
        }

        detectTemplate()
      },
      onSendStatusChange: debounce((statusConfig) => {
        const { saveStatus } = statusConfig;
        changeSaveStatus(saveStatus);
      }, 400),
    },
    title: {
      value: docInfo.pageName || intl.t('无标题页面'),
      withTitle: true,
      placeholder: intl.t('无标题页面'),
      maxLength: 200,
      priority: 0,
    },
    markdownGuide: true,
    app: {
      sourceId: guid,
      latestVersion,
      accessToken,
      sourceType: '6',
      content: '',
      dataLocation: 'self',
    },
    placeholder: {
      content: isInPhone ? '' : intl.t('直接输入内容，或选取模板进行新建'),
      switch: true,
    },
    editable: !isInPhone,
    user: {
      ...userInfo,
      nameCn: userInfo.username_zh,
    },
    comment: {
      show: !isInPhone,
      editable: true,
    },
    dictionary: {
      show: !isInPhone,
    },
    collab: {
      collabUser: true,
      collabUnderLine: true,
      collabCursor: true,
    },
    draggable: !isInPhone && 'vertical',
    slashCommand: !isInPhone,
    menu: {
      ...getMenuConfig(),
    },
  };

  const phoneConfig = {
    ...editorConfig,
    menu: {},
  };

  const editorShow = useMemo(() => {
    return guid && userInfo.username;
  }, [guid, userInfo]);

  useEffect(() => {
    if (docInfo.pageName) {
      window.document.title = docInfo.pageName;
    }
  }, [docInfo.pageName]);

  // 编辑器埋点
  const handleOmega = (event) => {
    const insertCommentEle = document.querySelector(
      '#float-menu-inner-box .button-menu-icon:last-child',
    );
    const outlineEle = document.querySelector(
      '.tab-intergation-float-container .didoc-button-two-chinese-chars:first-child',
    );
    const commentListEle = document.querySelector(
      '.tab-intergation-float-container .didoc-button-two-chinese-chars:last-child',
    );
    const markdownEle = document.querySelector('.markdown-tip');
    const map = {
      ep_dkpc_pagedetail_edit_comment_ck: insertCommentEle,
      ep_dkpc_pagedetail_commentlist_ck: commentListEle,
      ep_dkpc_pagedetail_markdown_ck: markdownEle,
      ep_dkpc_pagedetail_outlinelist_ck: outlineEle,
    };

    Object.entries(map).forEach(([key, dom]) => {
      if (dom && dom.contains(event.target)) {
        window.__OmegaEvent(key);
      }
    });
  };

  useEffect(() => {
    if (document.body) { // fix:报白屏错误removeEventListener不存在
      document.body.addEventListener('click', handleOmega, { capture: true });
      return () => {
        document.body?.removeEventListener('click', handleOmega, {
          capture: true,
        });
      };
    }
  }, []);

  useEffect(() => {
    return () => {
      // 页面切换时卸载编辑器
      const TenonEditor = window[EditorName];
      if (TenonEditor && typeof TenonEditor.unmount === 'function') { // 监控到白屏报错做修复
        TenonEditor.unmount();
      }
      viewRef.current?.unmount && viewRef.current.unmount();
    };
  }, []);

  useEffect(() => {
    return () => {
      try {
        window.CooperClientService?.destroy();
      } catch (error) {}
    }
  }, [])

  useEffect(() => {
    const ele = alertTipRef.current;
    if (view && alertTip && ele) {
      ele.classList.add('animate');
      ele.onanimationend = () => {
        ele.classList.remove('animate');
        setAlertTip(null);
      };
    }
  }, [view, alertTip]);

  return (
    <div
      ref={boxRef}
      id='knowledge_editor_box'
      className={cls('editor')}
    >
      {writerVisible && !isInPhone && (
        <div className={cls('closure-tip-box')}>
          <Tooltip title={intl.t('隐藏编写者')}>
            <i
              className={cls('dk-iconfont', 'dk-icon-shanchu1', 'closure-tip')}
              onClick={toggleWriterVisible}
            />
          </Tooltip>
        </div>
      )}

      <div
        ref={containerRef}
        className={cls('editor-container', { 'writer-hidden': !writerVisible })}
      >
        {view
          && alertTip
          && ReactDOM.createPortal(
            <div
              ref={alertTipRef}
              id="editor-alert-tip"
              className={cls('editor-alert-tip')}
            >
              <span className={cls('editor-alert-tip-icon')} />
              <span className={cls('editor-alert-tip-text')}>
                {alertTip?.text}
              </span>
              {alertTip?.refresh && (
                <span
                  className={cls('editor-alert-link')}
                  onClick={reloadWindow}
                >
                  {intl.t('点击刷新')}
                </span>
              )}
            </div>,
            containerRef.current.querySelector('#didoc-editor-slot-menu'),
          )}

        {useMemo(() => {
          if (!editorShow) return <></>;
          return (
            <MrParcel
              key={editorKey}
              entry={EditorMicroUrl}
              name={EditorName}
              appProps={isInPhone ? phoneConfig : editorConfig}
            />
          );
        }, [editorShow, editorKey])}
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail, CooperIndex }) {
  const { docInfo, writerVisible, editorKey, isBlock, alertTip } = pageDetail;
  const { profile } = CooperIndex;
  return {
    docInfo,
    profile,
    writerVisible,
    editorKey,
    isBlock,
    alertTip,
  };
}

function mapDispatchToProps({ pageDetail, template }) {
  const {
    changeView,
    changeSaveStatus,
    changeCollaborators,
    changeInitLoading,
    changeEditReady,
    toggleWriterVisible,
    changeBlock,
    setAlertTip,
  } = pageDetail;

  const { changeTemplateVisible } = template;
  return {
    changeView,
    changeSaveStatus,
    changeCollaborators,
    changeInitLoading,
    changeTemplateVisible,
    changeEditReady,
    toggleWriterVisible,
    changeBlock,
    setAlertTip,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Editor);
