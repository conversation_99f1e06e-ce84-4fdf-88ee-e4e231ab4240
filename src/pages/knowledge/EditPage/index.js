import { useContext, useEffect, useState, useMemo } from 'react';
import { connect } from 'react-redux';
import classNames from 'classnames/bind';
import { Spin } from 'antd';
import { Helmet } from 'react-helmet';
import Template from '@/components/Template';
import { ASIDE_SHOW_TYPE } from '@/constants';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { addRecentEdit } from '@/service/knowledge/recent';
import Authority from './Authority';
import Editor from './Editor';
import EditorNew from './EditorNew';
import Header from './Header';
import { inPhone, isDkSheet, isPreviewFile } from '@/utils';
import useGetPreviewFileInfo from '@/hooks/useGetPreviewFileInfo';
import Preview from '@/components/serviceComponents/Preview';
import styles from '@/pages/knowledge/PageDetail/style.module.less';
import ShimoExcel from '@/components/ShimoExcel';
import ErrorBoundary from '@/components/ErrorBoundary';
import FullScreenObserver from '@/utils/fullScreenObserver';
// import EditorTitleContent from '@/pages/knowledge/EditorTitleContent';


// 知识库编辑页面
const cls = classNames.bind(styles);

const isInPhone = inPhone();
function EditPage(props) {
  const {
    view,
    initLoading,
    applyTemplate,
    asideType,
    setWriterVisible,
    fileId,
    docType,
    permission,
    multiPath,
    docInfo,
    checkToEditor,
    newEditorMsg,
    pageStyle = { // 设置一个默认值，兜底数据错误
      editFontSize: 16,
      editScreen: 0,
    },
  } = props;
  const { knowledgeId, pageId } = useContext(LayoutContext);
  const isPreview = isPreviewFile(docType);
  const isSheet = isDkSheet(docType);
  const { loading } = useGetPreviewFileInfo(isPreview, fileId, 1);
  const [isReady, setIsReady] = useState(false);

  const parentId = useMemo(() => {
    if (!multiPath) return;
    return multiPath[multiPath?.length - 2 ?? 0]?.id;
  }, [multiPath, pageId]);

  useEffect(() => {
    return () => {
      setIsReady(false);
    };
  }, []);

  // 在灰度时，调用check接口转换为新文档数据
  useEffect(() => {
    if (!docInfo.guid) {
      return;
    }

    checkToEditor({
      guid: docInfo.guid,
      latestVersion: docInfo.latestVersion,
    }).finally(() => {
      setIsReady(true);
    });

  }, [docInfo])

  const handleApply = ({ templateId }) => {
    applyTemplate({
      view,
      knowledgeId,
      templateId,
    });
  };

  useEffect(() => {
    setWriterVisible(false);
    return () => {
      setWriterVisible(false);
    };
  }, []);

  useEffect(() => {
    const memoryStatus = asideType === ASIDE_SHOW_TYPE.TILE;
    // 进入编辑态若目录树为展开状态，自隐藏左侧目录树，退出编辑态则还原
    if (memoryStatus) {
      const foldEle = document.querySelector('.dk-icon-shouqijiantou.icon-unfold');
      foldEle && foldEle.click();
    }

    setTimeout(() => {
      addRecentEdit({
        resourceId: pageId,
      });
    }, 4000);
    return () => {
      if (memoryStatus) {
        const unfoldEle = document.querySelector('.dk-icon-liebiaomulushu.icon-unfold');
        unfoldEle && unfoldEle.click();
      }
    };
  }, []);

  const getDocView = () => {
    if (isPreview) {
      return (<Preview
        permission={permission}
        parentId={parentId}
        isEdit={true}
      />)
    }
    if (isSheet) {
      return <ShimoExcel/>
    }

    if (isReady && newEditorMsg.useEditorNew === true) {
      return <EditorNew />;
    }
    if (isReady && newEditorMsg.useEditorNew === false || newEditorMsg.useEditorNew === null) {
      return <Editor parentId={parentId} />;
    }
    return <></>
  }


  return (
    <div
      className={
        cls(
          'page-detail',
          'page-style-root',
          `font-size-${pageStyle?.editFontSize}`,
          `screen-${pageStyle?.editScreen}`,
          {
            'page-detail-inphone': isInPhone,
          },
        )
      }
    >
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png"
        />
      </Helmet>
      {/* 编辑器编写全屏下，禁用黄条 */}
      <FullScreenObserver />
      <Authority>
        {!isInPhone && <Header
          isPreview={isPreview}
          isSheet={isSheet}/>}
        {
          <div
            className={cls('page-content', {
              'no-header': isInPhone,
              'dk-editor-in-phone_reset': isInPhone,
            })}>
            {
              (isPreview ? loading : initLoading) && (
                <div className={'page-detail-loading'}>
                  <Spin />
                </div>
              )
            }
            {/* <EditorTitleContent
              canEdit={true}
              docInfo={docInfo} /> */}
            <ErrorBoundary>
              {getDocView()}
              {!isInPhone && !isSheet && <Template applyTemplate={handleApply} />}
            </ErrorBoundary>
          </div>
        }
      </Authority>
    </div>
  );
}

function mapStateToProps({ pageDetail, KnowledgeData }) {
  const { view, initLoading, docInfo, previewFileId, newEditorMsg } = pageDetail;
  const { asideType } = KnowledgeData;
  const { docType, name: fileName, permission, multiPath, pageStyle } = docInfo ?? {};

  return {
    view,
    initLoading,
    asideType,
    fileId: previewFileId.inEdit,
    docType,
    fileName,
    permission,
    multiPath,
    pageStyle,
    docInfo,
    newEditorMsg,
  };
}

function mapDispatchToProps({ template, pageDetail }) {
  const { applyTemplate } = template;
  const { setWriterVisible, checkToEditor } = pageDetail;
  return {
    applyTemplate,
    setWriterVisible,
    checkToEditor,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(EditPage);
