.save-as-template-modal {
  :global {
    .ant-modal {
      padding-bottom: 0;
      border-radius: 6px;
      overflow: hidden;

      .ant-modal-close {
        top: 8px;
        right: 4px;
      }

      .ant-modal-header {
        border-bottom: none;
        padding: 24px 24px 20px;

        .ant-modal-title {
          font-size: 20px;
          font-weight: 500;
          color: @text-color;
          line-height: 28px;
        }
      }

      .ant-modal-body {
        height: 76px;
        padding: 0 24px 12px;

        .ant-form-item-explain-error {
          font-size: 12px;
          color: #FE0B19;
          line-height: 17px;
          margin-top: 4px;
        }
      }

      .ant-modal-footer {
        padding: 0 24px 24px;
        border-top: none;

        .ant-btn {
          width: 96px;
          height: 44px;
          color: #444B4F;
          background: #F6F6F6;
          border-radius: 4px;
          font-size: 16px;
          border: none;

          &.ant-btn-primary {
            color: #FFFFFF;
            background: @primary-color;
            margin-left: 16px;
            font-weight: 500;

            &:disabled {
              opacity: 0.5;
            }
          }
        }
      }
    }
  }

  .close-icon {
    font-size: 20px;
    color: #505050;
  }

  .container {
    position: relative;

    .form {
      .form-item {
        .input {
          height: 40px;
          font-size: 14px;
          color: #222A35;
          line-height: 40px;
          box-shadow: none;
          border-radius: 2px;
          caret-color: @primary-color;
          border: 1px solid #DADCE3;

          &:hover, &:focus {
            border-color: @primary-color;
            box-shadow: none;
          }
        }
      }
    }
    .max-tip {
      position: absolute;
      top: 100%;
      font-size: 12px;
      color: #FE0B19;
      line-height: 17px;
      margin-top: 4px;
    }
  }
}

.success-tip {
  .link {
    color: @primary-color;
    cursor: pointer;
  }
}
