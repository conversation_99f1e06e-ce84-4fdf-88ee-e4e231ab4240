import { intl } from 'di18n-react';
import { useMemo, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import classNames from 'classnames/bind';
import { blurTime } from '@/utils';
import { getDidocHistory } from '@/service/knowledge/didoc';
import * as styles from './style.module.less';

const cls = classNames.bind(styles);

function DocMetaInfo(props) {
  const { view, docInfo } = props;
  const { creator, createOn, guid } = docInfo;
  const [latestHistory, setLatestHistory] = useState({});

  const writer = useMemo(() => {
    return latestHistory.user ? decodeURI(latestHistory.user.nameCn) : creator;
  }, [creator, latestHistory]);
  const updateTime = useMemo(() => {
    return latestHistory.updateTime || createOn;
  }, [createOn, latestHistory]);

  const getHistory = async () => {
    window.__OmegaEvent('ep_dkpc_pagedetail_pageinformation_ck');
    const { histories } = await getDidocHistory(guid);
    if (histories && histories[0]) {
      setLatestHistory(histories[0]);
    }
  };

  const count = view.state ? view.state.doc.textContent.length : 0;
  const CharacterCount = useMemo(() => {
    const MAX = 10000;
    if (count < MAX) {
      return <span className={cls('number')}>{count}</span>;
    }

    return (
      <span className={cls('number')}>
        {(count / MAX).toFixed(1)}
        <span className={cls('base')}>{intl.t('万')}</span>
      </span>
    );
  }, [count]);

  useEffect(() => {
    if (guid) {
      getHistory();
    }
  }, [guid]);

  return (
    <div className={cls('meta-info')}>
      <div className={cls('character-count')}>
        {CharacterCount}
        <span className={cls('unit')}>{intl.t('字')}</span>
      </div>
      <div className={cls('info')}>
        <span className={cls('username')}>{creator}</span>
        <span className={cls('time')}>
          {intl.t('创建于')}
          {blurTime(createOn)}
        </span>
      </div>
      <div className={cls('info')}>
        <span className={cls('username')}>{decodeURI(writer)}</span>
        <span className={cls('time')}>
          {intl.t('最后修改于')}
          {blurTime(updateTime)}
        </span>
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail }) {
  const { docInfo, view } = pageDetail;

  return {
    docInfo,
    view,
  };
}

export default connect(mapStateToProps)(DocMetaInfo);
