import { intl } from 'di18n-react';
import { getPagePermission } from '@/service/knowledge/page';
import { useContext, useEffect, useState, useMemo } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Dropdown, Menu, Tooltip, message } from 'antd';
import classnames from 'classnames/bind';
import * as service from '@/service/knowledge/page';
import { copyPage } from '@/service/knowledge/pageTree';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import HistoryVersionModal from '@/components/serviceComponents/HistoryVersionModal';
import HistoryRecordModal from '@/components/serviceComponents/HistoryRecordModal';
import SaveAsTemplateModal from './SaveAsTemplateModal';
import { isDkPage, isPreviewFile } from '@/utils';
import { openNewWindow } from '@/utils/index';
import NotificationStatus from '@/constants/notification';
import DocMetaInfo from './DocMetaInfo';
import FileMetaInfo from '@/components/serviceComponents/FileMetaInfo';
import usePermission from '@/hooks/usePermission';
import useNotification from '@/hooks/useNotification';
import { gainSetting } from '@/service/knowledge/setup';
import PageStyle from '@/components/PageStyle';
import localforage from 'localforage';
import SendToIportalModal from '@/components/SendToIportalModal';
import { getIsWikiHTML } from '@/constants/wikihtmlrenderHelper';
import { starAction } from '@/service/knowledge/star';
import VisitHistoryModal from '@/pages/knowledge/PageDetail/Header/DropdownMenu/VisitHistory';
import * as styles from './style.module.less';

const cls = classnames.bind(styles);

function DropdownMenu(props) {
  const {
    writerVisible,
    docInfo = {},
    isSign,
    toggleHistoryVersionVisible,
    toggleHistoryRecordVisible,
    toggleWriterVisible,
    updatePageStyle,
    setAlertTip,
    view
  } = props;
  const {
    multiPath,
    docType,
    initLoading,
    latestVersion,
    pageStyle = {},
    markedStar,
    permission
  } = docInfo;
  const { knowledgeId, pageId } = useContext(LayoutContext);
  const [signOffConfig, setSignOffConfig] = useState();
  const [pagePermission, setPagePermission] = useState(null);
  const [visitShow, setVisitShow] = useState(false);
  const [viewMeta, setViewMeta] = useState({});
  const [iportalModalVisible, setIportalModalVisible] = useState(false);
  const { checkOperationPermission } = usePermission();
  const isPreview = isPreviewFile(docType);
  const isWikiHTML = getIsWikiHTML(docInfo);
  const isDk = isDkPage(docType);
  const dispatch = useDispatch();

  const editPageStyle = useMemo(() => {
    return {
      fontSize: pageStyle?.editFontSize,
      screen: pageStyle?.editScreen,
    };
  }, [pageStyle]);
  const { changeStar } = dispatch.pageDetail;
  const notification = useNotification();
  useEffect(() => {
    gainSetting({ knowledgeId }).then((res) => {
      setSignOffConfig(res?.switchSetting?.signOffConfig);
    });
    getPagePermission({ pageId }).then((permission) => {
      setPagePermission(permission);
    });
  }, []);

  useEffect(() => {
    if (
      pagePermission?.roleKey
      && pageStyle?.editFontSize
    ) {
      initPageStyleTip();
    }
  }, [pagePermission, pageStyle]);

  const managerMemberPermis = useMemo(() => {
    return checkOperationPermission('MANAGE_PAGE_MEMBER', permission);
  }, [permission]);

  const initPageStyleTip = () => {
    const noPerm = !['PAGE_OWNER', 'PAGE_ADMIN'].includes(
      pagePermission?.roleKey,
    );
    const noDefault = pageStyle?.editFontSize !== 16 || pageStyle?.editScreen !== 0;
    if (noPerm && noDefault) {
      localforage
        .getItem(pageId)
        .then((val) => {
          if (!val) {
            setAlertTip({
              type: 'warn',
              text: intl.t('页面所有者/管理员已调整页面设置'),
            });
            localforage.setItem(pageId, pageStyle);
          }
        })
        .catch((err) => {
          console.log('*** initPageStyleTip err', err);
        });
    }
  };

  const saveAsTemplate = () => {
    window.__OmegaEvent('ep_dkpc_pagedetail_more_savetemplate_ck');
    const { toggleSaveAsTemplateModalVisible } = props;
    toggleSaveAsTemplateModalVisible();
  };

  const goToHistoryVersion = () => {
    window.__OmegaEvent('ep_dkpc_pagedetail_more_historyversion_ck');
    toggleHistoryVersionVisible();
  };

  const goToHistoryRecord = () => {
    window.__OmegaEvent('ep_dkpc_pagedetail_more_history_ck');
    toggleHistoryRecordVisible();
  };

  const gotoVisitHistory = () => {
    setVisitShow(true);
    window.__OmegaEvent('ep_detail_visit_ck');
  }

  const onClickMenu = ({ key }) => {
    const menuMap = {
      goToHistoryVersion,
      saveAsTemplate,
      goToHistoryRecord,
      goToapprovalProgress,
      copy,
      sendToIportal,
      restore,
      star: async () => {
        const type = markedStar ? 0 : 1;
        const listInfo = {
          resourceId: pageId,
          starType: 'RESOURCE', // 收藏类型  RESOURCE普通页面及资源; PAGE_SHARE页面分享; DK_SHARE知识库分享【必填】
          starInfo: '',
          appId: 4,
        };
        let data = await starAction(type, [listInfo]);
        if (data) {
          message.success(type === 1 ? intl.t('收藏成功') : intl.t('已取消收藏'));
          changeStar(!markedStar);
        }
      },
      gotoVisitHistory
    };
    menuMap[key]();
  };

  const goToapprovalProgress = () => {
    service
      .getpageApprovalProgressUrl({
        resourceId: pageId,
      })
      .then((res) => {
        let { code, message } = res;
        if (code == '2000') {
          openNewWindow(message);
        } else {
          notification(NotificationStatus.WARN, message);
        }
      });
  };

  const restore = () => {
    view?.showCacheModal?.();
  }

  const copy = () => {
    let toParentPageId = multiPath.length > 1 ? multiPath[multiPath.length - 2]?.id || '0' : '0';
    message.loading({
      content: intl.t('创建中...'),
      key: pageId,
      duration: 10,
    });

    copyPage({
      pageIds: [pageId],
      toParentPageId,
      knowledgeId,
      pageStatus: 0,
    }).catch((error) => {
      message.error({
        content: error.message || intl.t('副本创建失败，您没有权限'),
        key: pageId,
        duration: 2,
      });
    });
  };

  const checkEmpty = () => {
    const ele = view.dom;
    if (!ele) return;
    return [...ele.children].every((child) => {
      // 判断是否空段落
      if ((child.tagName === 'P')) {
        return (child.innerHTML === '<br>' || !child.innerHTML.trim())
      }
      // 判断是否空标题
      if (child.hasAttribute('heading-key')) {
        const heading = child.querySelector('h1, h2, h3, h4');
        return (heading.innerHTML === '<br>' || !child.innerHTML.trim())
      }
      // 包含其它内容一律判定非空
      return false;
    })
  };

  const sendToIportal = () => {
    const textLength = view.state.doc.textContent.length;
    const { title, content } = view.template.exportToIportal();
    if (!title) {
      return message.error(intl.t('标题不能为空'));
    }
    const isEmpty = checkEmpty();
    if (isEmpty) {
      return message.error(intl.t('内容不能为空'));
    }
    if (textLength > 70000) {
      return message.error(intl.t('单篇文章字数限制7万'));
    }
    setViewMeta({title, richText: content, textLength});
    setIportalModalVisible(true);
  }

  const changePageStyle = ({ fontSize, screen }) => {
    const params = {
      id: pageId,
      spaceId: knowledgeId,
      operateBy: docInfo?.operatorName,
    };
    if (fontSize !== undefined) {
      params.editFontSize = fontSize;
    }
    if (screen !== undefined) {
      params.editScreen = screen;
    }
    updatePageStyle(params);
  };

  const NormalMenu = (
    <Menu onClick={onClickMenu}>
      {['PAGE_OWNER', 'PAGE_ADMIN'].includes(pagePermission?.roleKey) && (
        <PageStyle
          title={intl.t('页面设置')}
          summary={intl.t('决定页面的正文字号和页面宽度')}
          tips={intl.t(
            '切换后同步影响编辑态协同方，协同方刷新后展示。发布后影响页面预览态和分享态。',
          )}
          pageStyle={editPageStyle}
          changePageStyle={changePageStyle}
        />
      )}
      <Menu.Item key="goToHistoryVersion">
        <span className={cls('align-menu')}>
          <i className={cls('dk-iconfont', 'dk-icon-lishifabubanben')} />
          <span>{intl.t('查看已发布版本')}</span>
        </span>
      </Menu.Item>
      {
        managerMemberPermis && (
          <Menu.Item key="gotoVisitHistory">
            <span className={cls('align-menu')}>
              <i className={cls('dk-iconfont', 'dk-icon-fangwenjilu')} />
              <span>{intl.t('查看访问记录')}</span>
            </span>
          </Menu.Item>
        )
      }
      {signOffConfig == 1 && (
        <Menu.Item key="goToapprovalProgress">
          <span className={cls('align-menu')}>
            <i className={cls('dk-iconfont', 'dk-icon-lingcunweimoban')} />
            <span>{intl.t('查看签批进度')}</span>
          </span>
        </Menu.Item>
      )}

      {!isPreview && (
        <>
          <Menu.Item key="saveAsTemplate">
            <span className={cls('align-menu')}>
              <i className={cls('dk-iconfont', 'dk-icon-lingcunweimoban')} />
              <span>{intl.t('另存为模板')}</span>
            </span>
          </Menu.Item>
          <Menu.Item key="goToHistoryRecord">
            <span className={cls('align-menu')}>
              <i className={cls('dk-iconfont', 'dk-icon-lishijilu')} />
              <span>{intl.t('历史记录')}</span>
            </span>
          </Menu.Item>
        </>
      )}

      <Menu.Item key="copy">
        <span className={cls('align-menu')}>
          <i
            className={`${cls('dk-icon-fuzhi', 'dk-iconfont', 'icon')}`}
            style={{ marginLeft: '1px', fontSize: '14px' }}
          />
          {intl.t('创建副本')}
        </span>
      </Menu.Item>
      {
        ['PAGE_OWNER', 'PAGE_ADMIN', 'PAGE_MEMBER'].includes(pagePermission?.roleKey) && (isDk && !isWikiHTML) && (
          <Menu.Item key="sendToIportal">
            <div className={cls('align-menu')}>
              <i className={`${cls('dk-icon-juzidui-01', 'dk-iconfont', 'icon')}`}/>
              {intl.t('发布至桔子堆')}
            </div>
            <div className={cls('dk-dropdown-menu-tip')}>
              {intl.t('将当前内容发布至桔子堆')}
            </div>
          </Menu.Item>
        )
      }
      <Menu.Item key="restore">
        <span className={cls('align-menu')}>
          <i className={`${cls('dk-icon-restore', 'dk-iconfont', 'icon')}`} />
          {intl.t('找回本地数据')}
        </span>
      </Menu.Item>
      <Menu.Item key='star'>
        <span className={cls('align-menu')}>
          <i
            className={cls({
              'dk-iconfont': true,
              'dk-icon-quxiaoshoucang-01': markedStar,
              'star-button': true,
              'dk-icon-shoucang4px': !markedStar,
              hasStar: markedStar,
            })}
          />
          { markedStar ? intl.t('取消收藏') : intl.t('收藏') }
        </span>
      </Menu.Item>
      <Menu.Item
        key="metaInfo"
        className={cls('readonly-menu-item', 'no-pointer-events')}
        disabled
      >
        {isPreview ? <>{!initLoading && <FileMetaInfo />}</> : <DocMetaInfo />}
      </Menu.Item>
    </Menu>
  );

  return (
    <div className={cls('dropdown-menu')}>
      <Dropdown
        trigger={['click']}
        overlay={NormalMenu}
        overlayClassName={cls('menu-container')}
        destroyPopupOnHide={true}
      >
        <Tooltip
          title={intl.t('更多')}
          placement="bottom">
          <span className={cls('more-trigger')}>
            <i
              className={cls('dk-iconfont', 'dk-icon-gengduo2', 'more-button')}
            />
          </span>
        </Tooltip>
      </Dropdown>
      <SaveAsTemplateModal />
      <HistoryVersionModal isEdit={true} />
      <HistoryRecordModal />
      { iportalModalVisible && <SendToIportalModal {...viewMeta} closeModal={setIportalModalVisible}/> }
      {
        visitShow && (
          <VisitHistoryModal 
            show={visitShow}
            onClose={() => setVisitShow(false)}
          />
        )
      }
    </div>
  );
}

function mapStateToProps({ pageDetail, KnowledgeData }) {
  const { writerVisible, docInfo, view } = pageDetail;
  const { isSign } = KnowledgeData?.knowledgeDetail ?? {};
  return { writerVisible, docInfo, isSign, view };
}

function mapDispatchToProps({ pageDetail, historyVersion, historyRecord }) {
  const {
    toggleSaveAsTemplateModalVisible,
    toggleWriterVisible,
    updatePageStyle,
    setAlertTip,
  } = pageDetail;

  const { toggleHistoryVersionVisible } = historyVersion;
  const { toggleHistoryRecordVisible } = historyRecord;
  return {
    toggleSaveAsTemplateModalVisible,
    toggleHistoryVersionVisible,
    toggleHistoryRecordVisible,
    toggleWriterVisible,
    updatePageStyle,
    setAlertTip,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(DropdownMenu);
