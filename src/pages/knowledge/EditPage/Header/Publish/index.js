import { intl } from 'di18n-react';
import { useContext, useEffect, useState, useMemo } from 'react';
import { connect, useSelector } from 'react-redux';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import classNames from 'classnames/bind';
import { Button, Tooltip, Modal } from 'antd';
import { checkNameErrorMsg, openNewWindow, getUrlParams } from '@/utils/index';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import customModal from '@/components/CustomModal';
import Notice from '../Notice';
import { getNoticeTemp, noticeMember } from '@/service/knowledge/page';
import useUpload from '@/hooks/uploadWiki';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import styles from './style.module.less';

const cls = classNames.bind(styles);

function Publish(props) {
  const {
    view,
    releasePage,
    saveStatus,
    getPageDetail,
    resetState,
    changeBlock,
    updateTitle,
    isBlock,
    isPreview,
    isSheet,
    reissueApproval,
    updatePageStyle,
  } = props;
  const [loading, setLoading] = useState(false);
  const [showNotice, setShowNotice] = useState(false);
  const [publishTitle, setPublishTitle] = useState('');
  const [noticeInfo, setNoticeInfo] = useState({});
  const navigate = useNavigate();
  const { tree } = useSelector((state) => state.PageTree);
  const { knowledgeId, pageId, setPageInfo } = useContext(LayoutContext);
  const notification = useNotification();
  const { previewFileInfo, previewFileId, docInfo, initLoading } = useSelector(
    (state) => state.pageDetail,
  );

  const { knowledgeDetail, treeViewType } = useSelector(
    (state) => state.KnowledgeData,
  );
  const { previewFileName } = previewFileInfo;
  const [isFromShare, setIsFromShare] = useState();
  const [refer, setRefer] = useState();
  const { isUploading } = useUpload();
  const { teamId } = useParams();

  useEffect(() => {
    setIsFromShare(getUrlParams('from') === 'share');
    setRefer(document.referrer);
  }, []);

  const disablePublish = () => {
    if (!isPreview) return saveStatus === 'sending';
    return isUploading(pageId);
  };

  const backToPage = () => {
    resetState();
    setPageInfo({});
    navigate(
      teamId
        ? `/team-file/${teamId}/knowledge/${knowledgeId}/${pageId}`
        : `/knowledge/${knowledgeId}/${pageId}`,
    );
    // getPageDetail({ pageId }).then((res) => {
    //   setPageInfo(res);
    // });

    // getPageDetail({ pageId }).then(() => {
    //   navigate(
    //     teamId
    //       ? `/team-file/${teamId}/knowledge/${knowledgeId}/${pageId}`
    //       : `/knowledge/${knowledgeId}/${pageId}`,
    //   );
    // });
  };

  const tipContent = () => {
    return {
      title: intl.t('是否重新提交发布申请'),
      content: intl.t(
        '该页面存在审批中的签批流程，是否重新发起申请。一旦重新发起，原申请会被自动作废。',
      ),
      okText: intl.t('重新发布'),
      cancelText: intl.t('关闭'),
    };
  };

  const tipContentApplicantName = (applicantName, applicantEmail) => {
    return {
      title: intl.t('无法重复提交发布申请'),
      content: intl.t(
        '该页面存在审批中的签批流程，不能重复提交发布申请。如果要重新提交需先联系原发布申请者（{applicantName}-{applicantEmail}）撤回申请',
        { applicantName, applicantEmail },
      ),
      okText: intl.t('联系原发布者'),
      cancelText: intl.t('关闭'),
    };
  };

  const tipContenApplicantNameQuit = (
    applicantName,
    approverName,
    approverEmail,
  ) => {
    return {
      title: intl.t('无法重复提交发布申请'),
      content: intl.t(
        '该页面存在审批中的签批流程，不能重复提交发布申请。检测到原发布申请者{applicantName}已离职，可联系流程审批者{approverName}（{approverEmail}）尽快驳回或通过，流程结束后方可重新发起签批流程。',
        {
          applicantName,
          approverName,
          approverEmail,
        },
      ),
      okText: intl.t('联系当前审批者'),
      cancelText: intl.t('关闭'),
    };
  };

  const tipContenapplicantAllQuit = (applicantName, approverName) => {
    return {
      title: intl.t('无法重复提交发布申请'),
      content: intl.t(
        '该页面存在审批中的签批流程，不能重复提交发布申请。检测到原发布申请者{applicantName}和流程审批者{approverName}已离职，请联系审批中心终止流程，流程终止后方可重新发起签批流程',
        { applicantName, approverName },
      ),
      okText: intl.t('联系审批中心'),
      cancelText: intl.t('关闭'),
    };
  };

  // 重新发起
  // pageId,
  // title: pageName,
  // isPreview,
  // previewFileName,
  // version: previewFileId.inEdit,
  // pageType: DocType.DK_FILE,
  const reissueClick = () => {
    setLoading(true);
    reissueApproval({ isPreview, previewFileName, view })
      .then((data) => {
        backToPage();
        setLoading(false);
        if (data) {
          openNewWindow(data);
        }
      })
      .catch((error) => {
        setLoading(false);
      });
  };

  const goToShare = () => {
    window.__OmegaEvent('ep_dkpc_edit_preview_ck');
    openNewWindow(refer);
  };

  const publishContent = useMemo(() => {
    if (isFromShare === false) return intl.t('已发布新版本');
    return (
      <div>
        {intl.t('已发布新版本')}

        <span
          style={{ color: '#127FF0', cursor: 'pointer' }}
          className={cls('toast-jump')}
          onClick={goToShare}
        >
          {intl.t('前往分享页面预览')}
        </span>
      </div>
    );
  }, [isFromShare]);

  const publichPage = (title) => {
    setLoading(true);
    window.__OmegaEvent('ep_dkpc_pagedetail_publish_ck');

    if (knowledgeDetail?.isSign) {
      noticeMember({
        resourceId: pageId,
        atUserInfos: [],
        userIds: [],
        groupIds: [],
        leaveWord: '',
        selectAllFlag: false,
      });
    }
    // 若修改了页面样式
    const pageStyle = docInfo?.pageStyle || {};
    if (pageStyle?.id) {
      updatePageStyle({
        id: pageId,
        spaceId: knowledgeId,
        preFontSize: pageStyle?.editFontSize,
        preScreen: pageStyle?.editScreen,
        operateBy: docInfo?.operatorName,
      })
    }
    return releasePage({ isPreview, isSheet, previewFileName })
      .then((data) => {
        if (!data) {
          return;
        }
        const {
          redirectBpmUrl,
          applying,
          applicantName,
          applicantEmail,
          applicantLadp,
          applicantHrState,
          approverHrState,
          approverName,
          approverEmail,
          approverLadp,
          approveUrl,
        } = data?.signInfo;
        setLoading(false);

        redirectBpmUrl && applying
          ? customModal({
            reissue: true,
            redirectBpmUrl,
            ...tipContent(),
            onOk() {
              reissueClick();
            },
          })
          : redirectBpmUrl && !applying
            ? (openNewWindow(redirectBpmUrl), backToPage())
            : // 别人发起了，在审批中，重复发起审批 && 申请人在职 && 审批人在职
            !redirectBpmUrl
              && applying
              && applicantHrState == 'A'
              && approverHrState == 'A'
              ? customModal({
                ...tipContentApplicantName(applicantName, applicantEmail),
                onOk() {
                  openNewWindow(
                    `dchat://im/start_conversation?name=${applicantLadp}`,
                  );
                },
              })
              : // 审批流程未结束 && 申请人离职 && 审批人未离职
              !redirectBpmUrl
                && applying
                && applicantHrState != 'A'
                && approverHrState == 'A'
                ? customModal({
                  ...tipContenApplicantNameQuit(
                    applicantName,
                    approverName,
                    approverEmail,
                  ),
                  onOk() {
                    // 跳转到审批人dc
                    openNewWindow(
                      `dchat://im/start_conversation?name=${approverLadp}`,
                    );
                  },
                })
                : // 审批流程未结束 && 申请人离职 && 审批人离职
                !redirectBpmUrl
                  && applying
                  && approverHrState != 'A'
                  && applicantHrState != 'A'
                  ? customModal({
                    ...tipContenapplicantAllQuit(applicantName, approverName),
                    onOk() {
                      // 联系审批中心
                      openNewWindow(approveUrl);
                    },
                  })
                  : !showNotice
                  && notification(NotificationStatus.SUCCESS, publishContent);
        if (treeViewType === 0) {
          tree?.updateNode && tree.updateNode(pageId, { title: title.trim() });
        } else {
          updateTitle({ name: title.trim(), pageId });
        }
        changeBlock(false);
        backToPage();
      })
      .catch((error) => {
        setLoading(false);
        if (error.errorCode !== 600003) {
          notification(NotificationStatus.ERROR, error.errorMessage);
        }
        throw error;
      });
  };

  const getNoticeConf = (title, hideNoticeModal) => {
    return getNoticeTemp(pageId)
      .then((res) => {
        const { openNotice, noticeList, leavedMessage, selectAllFlag } = res;
        if (openNotice && (noticeList.length > 0 || selectAllFlag)) {
          setNoticeInfo({ noticeList, leavedMessage, selectAllFlag });
          setNoticeOpen(title);
        } else if (hideNoticeModal) {
          publichPage(title);
        }
      })
      .catch(() => {
        if (hideNoticeModal) {
          publichPage(title);
        }
      });
  };

  const handleConfirm = () => {
    if (isPreview) {
      getNoticeConf(previewFileName, true);
      return;
    }

    window.performance.mark(`page-publish-start${pageId}`);
    if (!view) {
      notification(NotificationStatus.ERROR, intl.t('页面加载中，请等待~'));
      return;
    }
    let title = '';
    if (isSheet) {
      title = docInfo.pageName;
    } else if (view.template) {
      ({ title } = view.template.export());
    }
    let s = checkNameErrorMsg(title);
    if (s.length > 0) {
      notification(NotificationStatus.ERROR, s);
      return;
    }
    if (isBlock) {
      Modal.confirm({
        content: intl.t(
          '正在上传附件，现在发布会导致上传失败，确认要继续发布页面吗？',
        ),
        okText: intl.t('确认'),
        cancelText: intl.t('再想想'),
        onOk() {
          publichPage(title);
        },
      });
      return;
    }

    let showNoticeModal = false;
    if (view.getMentionList && view.getMentionList().length > 0) {
      setNoticeOpen(title);
      showNoticeModal = true;
    }
    getNoticeConf(title, !showNoticeModal);
  };

  const setNoticeOpen = (title) => {
    setShowNotice(true);
    setPublishTitle(title);
  };

  const handleCancel = () => {
    if (isBlock) {
      Modal.confirm({
        title: intl.t(
          '正在上传附件，现在关闭会导致上传失败，确认要继续关闭页面吗？',
        ),
        content: '',
        okText: intl.t('确认'),
        cancelText: intl.t('再想想'),
        onOk: () => {
          setPageInfo({});
          window.__OmegaEvent('ep_dkpc_pagedetail_close_ck');
          navigate(
            teamId
              ? `/team-file/${teamId}/knowledge/${knowledgeId}/${pageId}`
              : `/knowledge/${knowledgeId}/${pageId}`,
          );
          // getPageDetail({ pageId }).then(res => {
          //   setPageInfo(res);
          // })
        },
        onCancel: () => { },
      });
    } else {
      setPageInfo({});
      window.__OmegaEvent('ep_dkpc_pagedetail_close_ck');
      navigate(
        teamId
          ? `/team-file/${teamId}/knowledge/${knowledgeId}/${pageId}`
          : `/knowledge/${knowledgeId}/${pageId}`,
      );
      // getPageDetail({ pageId }).then(res => {
      //   setPageInfo(res);
      // })
    }
  };
  useEffect(() => {
    return () => {
      changeBlock(false);
    };
  }, []);

  const disableSheetPublish = useMemo(() => {
    if (isSheet) {
      return initLoading;
    }

    return false;
  }, [initLoading, isSheet])

  return (
    <div className={cls('publish')}>
      <Button
        id='dk-edit-publish-btn'
        className={cls('confirm')}
        onClick={handleConfirm}
        loading={loading}
        disabled={disablePublish() || disableSheetPublish}
      >
        <i className={cls('dk-icon-fabu-01','dk-iconfont')} />
        {intl.t('发布')}
      </Button>
      <Tooltip
        title={intl.t('关闭页面但不发布')}
        placement='bottom'
      >
        <Button
          className={cls('cancel')}
          onClick={handleCancel}
        >
          {intl.t('关闭')}
        </Button>
      </Tooltip>
      {showNotice && (
        <Notice
          close={() => {
            setShowNotice(false);
            setNoticeInfo({});
          }}
          publishTitle={publishTitle}
          noticeInfo={noticeInfo}
          publish={publichPage}
        />
      )}
    </div>
  );
}

function mapStateToProps({ pageDetail }) {
  const { saveStatus, view, isBlock } = pageDetail;
  return {
    saveStatus,
    view,
    isBlock,
  };
}

function mapDispatchToProps({ pageDetail, TagTree }) {
  const { releasePage, getPageDetail, resetState, changeBlock, reissueApproval, updatePageStyle } = pageDetail;
  return {
    releasePage,
    reissueApproval,
    getPageDetail,
    resetState,
    changeBlock,
    updateTitle: TagTree.updateNameOnTag,
    updatePageStyle,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Publish);
