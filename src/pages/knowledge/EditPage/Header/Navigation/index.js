/*
 * @Author: guanzhong
 * @Date: 2023-11-09 16:00:00
 * @LastEditors: guanzhong
 * @LastEditTime: 2023-11-10 11:21:25
 * @FilePath: /knowledgeforge/src/pages/knowledge/EditPage/Header/Navigation/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useMemo, useEffect, useRef, useContext } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import classNames from 'classnames/bind';
import { checkNameErrorMsg } from '@/utils/index';
import BreadCrumb from '@/components/BreadCrumb';
import NotificationStatus from '@/constants/notification';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import useNotification from '@/hooks/useNotification';
import * as styles from './style.module.less';

const cls = classNames.bind(styles);

function Navigation(props) {
  const titleRef = useRef();
  const notification = useNotification();
  const { docInfo, view, changeTitle } = props;
  const { multiPath = [], knowledgeId } = docInfo;
  const routes = useMemo(() => {
    return multiPath.map((item) => ({
      path: `/knowledge/${knowledgeId}/${item.id}`,
      breadcrumbName: item.name,
    }));
  }, [multiPath]);
  const { pageId } = useContext(LayoutContext);

  const handleBlur = () => {
    const { value } = titleRef.current;
    let s = checkNameErrorMsg(value);
    if (s.length > 0) {
      notification(NotificationStatus.ERROR, s);
      return;
    }
    changeTitle({
      name: value.trim(),
      pageId,
    });
  };

  useEffect(() => {
    titleRef.current = document.querySelector('.didoc-editor-input.editor-title');
    if (titleRef.current) {
      titleRef.current.addEventListener('blur', handleBlur);
      handleBlur();
    }

    return () => {
      if (titleRef.current) {
        titleRef.current.removeEventListener('blur', handleBlur);
        titleRef.current = null;
      }
    };
  }, [view]);

  return (
    <div className={cls('navigation')}>
      <BreadCrumb routes={routes} />
    </div>
  );
}

function mapStateToProps({ pageDetail }) {
  const { view, docInfo } = pageDetail;
  return {
    view,
    docInfo,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const { changeTitle } = pageDetail;
  return {
    changeTitle,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Navigation);
