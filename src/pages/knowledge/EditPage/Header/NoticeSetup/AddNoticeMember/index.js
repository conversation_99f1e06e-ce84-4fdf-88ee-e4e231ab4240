/*
 * @Author: guanzhong
 * @Date: 2023-11-09 16:00:00
 * @LastEditors: guanzhong
 * @LastEditTime: 2023-11-10 11:20:49
 * @FilePath: /knowledgeforge/src/pages/knowledge/EditPage/Header/NoticeSetup/AddNoticeMember/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { intl } from 'di18n-react';
import { useState, useContext, useMemo } from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import classNames from 'classnames/bind';
import { debounce } from 'lodash-es';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import MemberInput from '@/components/MemberComponent/MemberInput';
import { searchNoticeMember } from '@/service/knowledge/page';
import MemberItem from '@/components/MemberComponent/MemberItem';
import useLoadMore from '@/hooks/useLoadMore';
import { highlight } from '@/utils';
import SpinRender from '@/components/SpinRender';
import * as styles from './style.module.less';

const cx = classNames.bind(styles);

const AddNoticeMember = ({ noticeList, changeNoticeMembers }) => {
  const { pageId } = useContext(LayoutContext);
  const [keyword, setKeyword] = useState('');
  const { loadMore, loading, list, hasMore } = useLoadMore(
    (args) => searchNoticeMember({ resourceId: pageId, ...args }),
    { keyword: '' },
  );
  const selectedList = useMemo(() => noticeList.map((v) => v.id), [noticeList]);

  const onInputChange = debounce((e) => {
    const key = e.target.value;
    setKeyword(key);
    loadMore({ keyword: key }, true);
  }, 500);

  const onChange = (member, exist) => {
    if (member && !exist) {
      window.__OmegaEvent('ep_knowledge_notification_choosedirectedmember');
      changeNoticeMembers([member, ...noticeList]);
    }
  };

  return (
    <div className={cx('select-member-popover')}>
      <div className={cx('header')}>
        <MemberInput
          customStyle={{ width: 260 }}
          onInputChange={onInputChange}
          placeholder={intl.t('请搜索通知对象')}
        />
      </div>
      <div className={cx('notice-memberList-wrap')}>
        <div className={cx('notice-memberList')}>
          <InfiniteScroll
            initialLoad={false}
            pageStart={0}
            loadMore={() => loadMore({ keyword })}
            hasMore={!loading && hasMore}
            useWindow={false}
          >
            {list.map((member) => {
              const exist = selectedList.includes(member.id);
              return (
                <div
                  className={cx('notice-memberItem')}
                  key={member.id}
                  onClick={() => onChange(member, exist)}
                >
                  <MemberItem
                    customClass={exist ? cx('exit') : ''}
                    nameFlex={true}
                    name={highlight(member.cooperate_with_cn)}
                    department={member.dep ? `(${member.dep})` : undefined}
                    key={member.id}
                    email={highlight(member.email)}
                    avatar={member.avatar}
                    exist={exist}
                  />
                </div>
              );
            })}

            <SpinRender
              loading={loading}
              style={{ height: 48, paddingTop: 10 }}
            />
            {!loading && list.length === 0 && (
              <div className={cx('empty-text')}>{intl.t('搜索-无结果')}</div>
            )}
            {!loading && !!list.length && !hasMore && (
              <div className={cx('empty-text')}>{intl.t('已显示全部结果')}</div>
            )}
          </InfiniteScroll>
        </div>
      </div>
    </div>
  );
};
export default AddNoticeMember;
