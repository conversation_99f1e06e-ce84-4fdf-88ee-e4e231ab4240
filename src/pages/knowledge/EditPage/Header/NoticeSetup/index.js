import { intl } from 'di18n-react';
import { useState, useContext } from 'react';
import { Popover, Tooltip, Switch, Input, Radio } from 'antd';
import classNames from 'classnames/bind';
import AddNoticeMember from './AddNoticeMember';
import useMemberNumber from '@/hooks/useMemberNumber';
import MemberCircleList from '@/components/MemberComponent/MemberCircleList';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { setNoticeTemp, getNoticeTemp } from '@/service/knowledge/page';
import * as styles from './style.module.less';

const cx = classNames.bind(styles);
const { TextArea } = Input;
function NoticeSetup() {
  const { pageId } = useContext(LayoutContext);
  const [noticeOpen, setNoticeOpen] = useState(false);
  const [leavedMessage, setLeavedMessage] = useState('');
  const [visible, setVisible] = useState(false);
  const [showAddNoticeMember, setShowAddNoticeMember] = useState(false);
  const [noticeList, setNoticeList] = useState([]);
  const [changedMsg, setChangedMsg] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const { userNumber, groupNumber } = useMemberNumber(noticeList);

  const changeNoticeOpen = () => {
    setNoticeOpen(!noticeOpen);
    if (!noticeOpen) {
      setNoticeList([]);
      setLeavedMessage('');
      window.__OmegaEvent('ep_dkpc_notification_closenotificationset_ck');
    } else {
      window.__OmegaEvent('ep_dkpc_notification_opennotificationset_ck');
    }
  };
  const inputMessage = (e) => {
    setLeavedMessage(e.target.value);
    setChangedMsg(true);
  };
  const addMember = () => {
    window.__OmegaEvent(
      'ep_dkpc_notification_publicnotificationset_addnember_ck',
    );
    setShowAddNoticeMember(true);
  };
  const changeCheckAll = (e) => {
    const { value } = e.target;
    setCheckAll(value);
    if (value) {
      window.__OmegaEvent(
        'ep_dkpc_notification_publicnotificationset_addnember_allelection_ck',
      );
    } else {
      window.__OmegaEvent('ep_knowledge_notification_directedmember');
    }
  };
  const changeNoticeMembers = (list) => {
    setNoticeList(list);
    setShowAddNoticeMember(false);
  };
  const onVisibleChange = async (v) => {
    if (showAddNoticeMember) return;
    setVisible(v);
    if (v) {
      const { openNotice, noticeList, leavedMessage, selectAllFlag } = await getNoticeTemp(pageId);
      setNoticeList(noticeList);
      setLeavedMessage(leavedMessage);
      setNoticeOpen(openNotice);
      setChangedMsg(false);
      setCheckAll(selectAllFlag);
      window.__OmegaEvent('ep_dkpc_notification_publicnotificationset_ck');
    } else {
      if (changedMsg) {
        window.__OmegaEvent(
          'ep_dkpc_notification_publicnotificationset_addnotion_ck',
        );
      }
      setNoticeTemp({
        pageId,
        openNotice: noticeOpen,
        noticeList,
        leavedMessage,
        selectAllFlag: checkAll,
      });
    }
  };
  const remove = (id) => {
    const tempList = noticeList.filter((item) => item.id !== id);
    setNoticeList(tempList);
    window.__OmegaEvent('ep_knowledge_notification_deletedirectedmember');
  };
  const Content = (
    <div>
      <h3 className={cx('main-title')}>{intl.t('发布通知设置')}</h3>
      {
        <div className={cx('notice-setup-content')}>
          <div className={cx('notice-switch-wrap')}>
            <Switch
              checked={noticeOpen}
              className={cx('notice-switch', 'dk-ant-switch_reset')}
              onChange={changeNoticeOpen}
            />

            <div className={cx('desc')}>
              {intl.t(
                '发布页面时，同步向所选对象发送D-Chat消息通知。此设置仅对你个人的下一次发布生效。',
              )}
            </div>
          </div>
          {noticeOpen && (
            <div className={cx('member-and-message')}>
              <div className={cx('members-title')}>
                <p className={cx('title')}>{intl.t('将向以下用户发送通知')}</p>
                {!checkAll && (
                  <p className={cx('count')}>
                    {intl.t('总计：')}
                    {userNumber}
                    {intl.t('人，')}
                    {groupNumber}
                    {intl.t('个成员组')}
                  </p>
                )}
              </div>
              <Radio.Group
                onChange={changeCheckAll}
                value={checkAll}
                className={cx('add-radio-group')}
              >
                <Radio
                  className={cx('add-radio')}
                  value={false}>
                  {intl.t('定向成员')}
                </Radio>
                <Radio
                  className={cx('add-radio')}
                  value={true}>
                  {intl.t('全部页面成员')}
                </Radio>
              </Radio.Group>
              {!checkAll && (
                <div className={cx('members')}>
                  <Popover
                    trigger="click"
                    placement="bottom"
                    visible={showAddNoticeMember}
                    onVisibleChange={(v) => setShowAddNoticeMember(v)}
                    overlayClassName="dk-ant-popover__reset"
                    destroyTooltipOnHide={true}
                    content={
                      <AddNoticeMember
                        noticeList={noticeList}
                        changeNoticeMembers={changeNoticeMembers}
                      />
                    }
                  >
                    <div
                      className={cx('add')}
                      onClick={addMember}>
                      <i
                        className={cx(
                          'dk-iconfont',
                          'dk-icon-ic_chuangjian',
                          'add-icon',
                        )}
                      />
                      {intl.t('添加')}
                    </div>
                  </Popover>
                  {visible && (
                    <MemberCircleList
                      customClass={cx('member-list')}
                      remove={remove}
                      list={noticeList}
                    >
                      <i
                        className={cx(
                          'dk-iconfont',
                          'dk-icon-a-guanbibeifen3',
                          'remove-icon',
                        )}
                      />
                    </MemberCircleList>
                  )}
                </div>
              )}
              <div className={cx('message')}>
                <div className={cx('title')}>{intl.t('留言')}</div>
                <TextArea
                  className={cx('message-input')}
                  onChange={inputMessage}
                  value={leavedMessage}
                  placeholder={intl.t('选填，最多支持200字')}
                  maxLength={200}
                />
              </div>
            </div>
          )}
        </div>
      }
    </div>
  );

  return (
    <>
      <Popover
        align={{
          offset: [76, 8],
        }}
        trigger="click"
        content={Content}
        placement="bottomRight"
        visible={visible}
        onVisibleChange={onVisibleChange}
        overlayClassName={`dk-ant-popover__reset ${cx('notice-setup')}`}
      >
        <Tooltip
          title={intl.t('发布通知设置')}
          placement="top">
          <span className={cx('notice-trigger')}>
            <i
              className={cx('dk-iconfont', 'dk-icon-xiaoxi4', 'notice-icon')}
            />
          </span>
        </Tooltip>
      </Popover>
    </>
  );
}

export default NoticeSetup;
