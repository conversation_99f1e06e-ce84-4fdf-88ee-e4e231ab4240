import { intl } from 'di18n-react';
import { useState, useEffect, useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Modal } from 'antd';
import classNames from 'classnames/bind';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import useMemberNumber from '@/hooks/useMemberNumber';
import MemberCircleList from '@/components/MemberComponent/MemberCircleList';
import MemberFooter from '@/components/MemberComponent/MemberFooter';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { noticeMember } from '@/service/knowledge/page';
import * as styles from './style.module.less';

const cx = classNames.bind(styles);

function Notice({ close, publish, publishTitle, noticeInfo }) {
  const { view } = useSelector((state) => state.pageDetail);
  const dispatch = useDispatch();
  const { getMentionList } = dispatch.pageDetail;
  const [mentionList, setMentionList] = useState([]);
  const [mentionObj, setMentionObj] = useState({});
  const { leavedMessage, noticeList = [], selectAllFlag } = noticeInfo;
  const { userNumber, groupNumber } = useMemberNumber(noticeList);
  const { pageId } = useContext(LayoutContext);
  const notification = useNotification();

  useEffect(() => {
    getMentionList().then((res) => {
      setMentionList(res);
      const obj = {};
      res.forEach((item) => {
        const name = item.creater?.name || intl.t('用户');
        if (obj[name]) {
          if (!obj[name].list.find((element) => element.id === item.meta?.id)) {
            obj[name].list.push(item.meta);
          }
        } else {
          obj[name] = {
            nameCn: item.creater?.cooperate_with_cn || intl.t('用户'),
            list: [item.meta],
          };
        }
      });
      setMentionObj(obj);
    });
  }, []);

  const notice = async () => {
    window.__OmegaEvent('ep_dkpc_notification_publicandnotify_ck');
    await publish(publishTitle);
    noticeMember({
      resourceId: pageId,
      atUserInfos: mentionList.map((item) => {
        const { id, mentionId } = item.element?.dataset;
        return {
          atId: mentionId,
          atLdap: item.creater?.uid,
          atTargetLdap: item.meta?.ldap || id,
        };
      }),
      userIds: noticeList
        .filter((item) => item.memberType === 'User')
        .map((item) => item.id),
      groupIds: noticeList
        .filter((item) => item.memberType === 'Group')
        .map((item) => item.id),
      leaveWord: leavedMessage,
      selectAllFlag,
    }).then(() => {
      view.onPublish && view.onPublish();
      notification(
        NotificationStatus.SUCCESS,
        intl.t('已发布新版本，并发送通知'),
      );
    });
  };

  return (
    <Modal
      title={intl.t('发布后，将向以下用户发送D-Chat通知')}
      visible={true}
      closeIcon={<i className={cx('dk-iconfont', 'dk-icon-guanbi', 'close')} />}
      onCancel={close}
      footer={null}
      width={472}
      wrapClassName={cx('notice')}
    >
      <div className={cx('post-and-at')}>
        {(noticeList.length > 0 || selectAllFlag) && (
          <div className={cx('post-notice')}>
            <p className={cx('post-notice-header')}>
              <div className={cx('common-title')}>
                {intl.t('发布时发送通知')}
              </div>
              {!selectAllFlag && (
                <div className={cx('count')}>
                  {intl.t('总计：')}
                  {userNumber}
                  {intl.t('人，')}
                  {groupNumber}
                  {intl.t('个成员组')}
                </div>
              )}
            </p>
            {selectAllFlag ? (
              <MemberCircleList
                list={[
                  {
                    cooperate_with_cn: intl.t('全部页面成员'),
                    icon: (
                      <i
                        className={`${cx(
                          'dk-icon-suoyouzhe-buxian',
                          'dk-iconfont',
                          'all-icon',
                        )} `}
                      />
                    ),
                  },
                ]}
              />
            ) : (
              <MemberCircleList list={noticeList} />
            )}
            {leavedMessage && (
              <div className={cx('message')}>
                <div className={cx('message-title', 'common-title')}>
                  {intl.t('留言')}
                </div>
                <div className={cx('message-content')}>{leavedMessage}</div>
              </div>
            )}
          </div>
        )}
        {Object.keys(mentionObj).length > 0 && (
          <div className={cx('at-notice')}>
            <p className={cx('at-notice-title', 'common-title')}>
              {intl.t('@信息通知')}
            </p>
            {Object.keys(mentionObj).map((key) => {
              return (
                <div
                  className={cx('at-notice-block')}
                  key={key}>
                  <div className={cx('name-and-desc')}>
                    <span className={cx('name')}>{mentionObj[key].nameCn}</span>
                    <span className={cx('desc')}>
                      {intl.t('@了以下用户：')}
                    </span>
                  </div>
                  <MemberCircleList list={mentionObj[key].list} />
                </div>
              );
            })}
          </div>
        )}
      </div>
      <MemberFooter
        customClass={cx('btn-area')}
        okTxt={intl.t('发布并通知')}
        cancel={close}
        ok={notice}
      />
    </Modal>
  );
}

export default Notice;
