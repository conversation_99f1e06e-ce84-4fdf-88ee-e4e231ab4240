import { useMemo } from 'react';
import { connect } from 'react-redux';
import { Tooltip, Popover } from 'antd';
import classNames from 'classnames/bind';
import { AvatarIcon } from '@/assets/icon';
import * as styles from './style.module.less';

const cls = classNames.bind(styles);

function CollaboratorList(props) {
  const MaxSize = 4;
  // const { collaborators, members } = props;
  const { collaborators } = props;

  const frontList = useMemo(() => {
    return collaborators?.slice(0, MaxSize);
  }, [collaborators]);
  const otherList = useMemo(() => {
    return collaborators?.slice(MaxSize);
  }, [collaborators]);

  const renderContent = useMemo(() => {
    return (
      <div className={cls('collaborator-others-wrapper')}>
        {
          otherList.map((item) => {
            return (
              <div
                className={cls('collaborator-item')}
                key={item.id}>
                <Tooltip
                  placement='bottom'
                  title={item.name}

                >
                  <img
                    className={cls('collaborator-avatar')}
                    src={item.avatar || AvatarIcon}
                    alt={item.name}/>
                </Tooltip>
              </div>
            );
          })
        }
      </div>
    );
  }, [otherList]);

  return (
    <div className={cls('collaborator-list')}>
      {
        frontList.map((item) => {
          return (
            <div
              className={cls('collaborator-item')}
              key={item.id}>
              <Tooltip
                placement="bottom"
                title={item.name}

              >
                <img
                  className={cls('collaborator-avatar')}
                  src={item.avatar || AvatarIcon}
                  alt={item.name}/>
              </Tooltip>
            </div>
          );
        })
      }
      {
        (otherList.length > 0) && (
          <Popover
            trigger={['click']}
            content={renderContent}
            overlayClassName={cls('collaborator-others', 'dk-ant-popover__reset')}
          >
            <div className={cls('collaborator-item', 'collaborator-more')}>
              {otherList.length}+
            </div>
          </Popover>
        )
      }
    </div>
  );
}

function mapStateToProps({ pageDetail }) {
  const { collaborators, members } = pageDetail;
  return {
    collaborators,
    members,
  };
}

export default connect(mapStateToProps)(CollaboratorList);
