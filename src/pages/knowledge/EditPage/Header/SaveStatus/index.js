import { useMemo } from 'react';
import { connect } from 'react-redux';
import classnames from 'classnames/bind';
import { EXIT_FULLSCREEN, FULLSCREEN, SaveStatusText } from '@/constants/editor';
import { DOCUMENT_SAVE_FAILED, DOCUMENT_SAVE_SUCCESS, HISTORY_REVERT_FAILED, HISTORY_REVERT_SUCCESS } from '@/constants/editor.js'
import styles from './style.module.less';

const cls = classnames.bind(styles);

function SaveStatus(props) {
  const { saveStatus } = props;
  const iconStatus = useMemo(() => {
    if (saveStatus === 'success' || saveStatus === HISTORY_REVERT_SUCCESS || saveStatus === DOCUMENT_SAVE_SUCCESS) {
      return 'dk-icon-chenggong';
    }
    if (saveStatus === 'sending') {
      return 'loading-icon';
    }
    if (saveStatus === DOCUMENT_SAVE_FAILED || saveStatus === HISTORY_REVERT_FAILED) {
      return 'dk-icon-shibai';
    }

    if (saveStatus === FULLSCREEN || saveStatus === EXIT_FULLSCREEN) {
      return '';
    }
    return 'dk-icon-shibai';
  }, [saveStatus]);

  return (
    <div
      className={cls('save-status')}
      hidden={!saveStatus}
    >
      <div className={cls('tip-switch')}>
        <i className={cls('dk-iconfont', iconStatus)}/>
        <span className={cls('text')}>{ SaveStatusText[saveStatus] }</span>
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail }) {
  const { saveStatus } = pageDetail;

  return {
    saveStatus,
  };
}

export default connect(mapStateToProps)(SaveStatus);
