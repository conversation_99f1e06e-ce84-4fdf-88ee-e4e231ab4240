import { useMemo } from 'react';
import { connect } from 'react-redux';
import classnames from 'classnames/bind';
import { SaveStatusText } from '@/constants/editor';
import * as styles from './style.module.less';

const cls = classnames.bind(styles);

function SaveStatus(props) {
  const { saveStatus } = props;
  const iconStatus = useMemo(() => {
    if (saveStatus === 'success') {
      return 'dk-icon-chenggong';
    }
    if (saveStatus === 'sending') {
      return 'loading-icon';
    }
    return 'dk-icon-shibai';
  }, [saveStatus]);

  return (
    <div
      className={cls('save-status')}
      hidden={!saveStatus}
    >
      <div className={cls('tip-switch')}>
        <i className={cls('dk-iconfont', iconStatus)}/>
        <span className={cls('text')}>{ SaveStatusText[saveStatus] }</span>
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail }) {
  const { saveStatus } = pageDetail;

  return {
    saveStatus,
  };
}

export default connect(mapStateToProps)(SaveStatus);
