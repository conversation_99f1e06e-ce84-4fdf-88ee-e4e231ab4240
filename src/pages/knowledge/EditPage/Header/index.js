import { intl } from 'di18n-react';
import { connect } from 'react-redux';
import PageTag from '@/components/serviceComponents/PageTag';
import classNames from 'classnames/bind';
import { ASIDE_SHOW_TYPE } from '@/constants';
import CollaboratorList from './CollaboratorList';
import DropdownMenu from './DropdownMenu';
import Publish from './Publish';
import NoticeSetup from './NoticeSetup';
import SaveStatus from './SaveStatus';
import { Button, Popover } from 'antd';
import MemberListOfDK from '@/components/serviceComponents/MemberListOfDK';
import AddNewCollaborator from '@/components/serviceComponents/AddNewCollaborator';
import { useContext, useState } from 'react';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { getType } from '@/utils/type'
import * as styles from './style.module.less';

const cls = classNames.bind(styles);

function Header(props) {
  const { isCollapse, isPreview, isSheet, docInfo } = props;
  const [ step, setStep ] = useState(0);
  const { pageId, knowledgeId, visitPerms } = useContext(LayoutContext);

  const getContent = () => {
    if (step === 0) {
      return (
        <MemberListOfDK
          isDkPage={true}
          needPerm={true}
          permission={docInfo.permission}
          roleKey={docInfo.roleKey}
          teamId={knowledgeId}
          teamIdAllTeam={knowledgeId}
          resourceId={pageId}
          info={{ fileType: docInfo.type }}
          title={intl.t('协作者')}
          gotoNext={() => setStep(1)}
          handleBack={() => setStep(0)}
        />
      );
    }

    if (step === 1) {
      return (
        <AddNewCollaborator
          showBack={true}
          isDkPage={true}
          fileType={docInfo.type}
          teamId={knowledgeId}
          resourceId={pageId}
          handleBack={() => setStep(0)}
          backToFirstStep={() => {}}  // 不处理
          closeShare={() => {
            setStep(0);
          }}
        />
      );
    }
  }

  const handleVisibleChange = (visible) => {
    if (!visible) {
      setStep(0);
    } else {
      const isType = [ 'docs', 'sheets', 'slides', 'flowchart', 'xmind', 'pages' ].includes(getType(docInfo.type));
      window.__OmegaEvent('ep_detail_collaborator_ck', '', {
        source: isType ? getType(docInfo.type) : '',
      });
    }
  }

  return (
    <div className={cls('header-wrap')}>
      <div className={cls('header')}>
        <div className={cls('left-section', { collapse: isCollapse })}>
          <div className={cls('bottom-section')}>
            <PageTag overlayClassName={cls('page-tag')} />
            <SaveStatus />
          </div>
        </div>
        <div className={cls('right-section')}>
          <CollaboratorList />
          <Publish
            isPreview={isPreview}
            isSheet={isSheet}
          />
          {
            visitPerms?.length === 0 && (
              <div className={cls('right-collaborator')}>
                <Popover
                  trigger='click'
                  placement='bottomRight'
                  overlayClassName={cls('right-collaborator-popover')}
                  content={
                    <div className={cls('right-collaborator-content')}>
                      {getContent()}
                    </div>
                  }
                  onVisibleChange={handleVisibleChange}
                >
                  <Button className={cls('collab-btn')}>
                    <i className={cls('dk-icon-duoren','dk-iconfont')} />
                    <span>{intl.t('协作者')}</span>
                  </Button>
                </Popover>
              </div>
            )
          }
          <NoticeSetup />
          { isSheet || <DropdownMenu /> }
        </div>
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail, KnowledgeData }) {
  const { asideType } = KnowledgeData;
  const { docInfo } = pageDetail;
  const isCollapse = asideType === ASIDE_SHOW_TYPE.MODAL;

  return {
    isCollapse,
    docInfo,
  };
}

export default connect(mapStateToProps)(Header);
