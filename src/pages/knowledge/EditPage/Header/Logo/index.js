import { connect } from 'react-redux';
import classNames from 'classnames/bind';
import { DKDefaultIcon } from '@/assets/icon';
import * as styles from './style.module.less';

const cls = classNames.bind(styles);

function Logo(props) {
  const { knowledgeDetail = {}} = props;
  const { exPicture } = knowledgeDetail;

  return (
    <div className={cls('logo')}>
      <img
        className={cls('icon')}
        src={exPicture || DKDefaultIcon}
        alt=""
      />
    </div>
  );
}

function mapStateToProps({ KnowledgeData }) {
  const { knowledgeDetail } = KnowledgeData;
  return {
    knowledgeDetail,
  };
}

export default connect(mapStateToProps)(Logo);
