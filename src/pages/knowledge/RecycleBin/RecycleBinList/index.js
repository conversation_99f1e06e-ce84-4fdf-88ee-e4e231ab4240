import { getLocale, intl } from 'di18n-react';
import { useEffect, useRef, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { throttle } from 'lodash-es';
import { Tooltip } from 'antd';
import classBind from 'classnames/bind';
import {
  CompleteDeleteIcon,
  RecoverIcon,
  RecycleBinEmptyIcon,
} from '@/assets/icon';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import usePermission from '@/hooks/usePermission';
import {
  completedDelete,
  getRecycleBinList,
  restore,
} from '@/service/knowledge/recycleBin';
import { formatTime } from '@/utils/index';
import TextOnlyModal from '@/components/TextOnlyModal';
import NoMore from '@/components/serviceComponents/NoMore';
import ScrollTableList from '@/components/serviceComponents/ScrollTableList';
import FileContent from '@/baseComponents/FileContent';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const RecycleBinList = (props) => {
  const { knowledgeId } = useParams();
  const navigate = useNavigate();
  const [delModalVisible, setDelModalVisible] = useState(false);
  const [recycleBinList, setRecycleBinList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [pageNum, setPageNum] = useState(0);
  const [atBottom, setAtBottom] = useState(false);
  const [firstPaint, setFirstPaint] = useState(false);
  const [delId, setDelId] = useState(0);
  const restoreIdRef = useRef(0);
  const { checkOperationPermission } = usePermission();
  const dispatch = useDispatch();
  const notification = useNotification();

  const { permission } = useSelector((state) => state.KnowledgeData);
  const { tree } = useSelector((state) => state.PageTree);
  const { perm } = permission;
  const { teamId } = useParams();

  const loadMore = () => {
    setLoading(true);
    return getRecycleBinList(knowledgeId, {
      pageSize: Math.ceil((window.screen.height - 207) / 50),
      pageNum,
    })
      .then((data) => {
        if (data) {
          const { currentPage, pageSize, totalCount } = data;
          const more = (currentPage + 1) * pageSize < totalCount;
          setHasMore(more);
          setRecycleBinList(
            recycleBinList.concat(data.items || []).map((item) => {
              item.key = item.id;
              return item;
            }),
          );
          setPageNum(pageNum + 1);
          return totalCount;
        }
        return 0;
      })
      .finally((totalCount) => {
        setLoading(false);
        return totalCount;
      });
  };

  const handleScroll = (e) => {
    let _this = e.target;
    const viewH = _this.clientHeight;
    const contentH = _this.scrollHeight;
    const { scrollTop } = _this;
    const toBottomPercent = scrollTop / (contentH - viewH);
    if (toBottomPercent === 1) {
      setAtBottom(true);
    } else if (toBottomPercent < 0.99 && toBottomPercent > 0.98) {
      setAtBottom(false);
    }
  };

  const restorePermission = useMemo(() => {
    return checkOperationPermission('MANAGE_DK_PAGE', perm);
  }, [perm]);

  const deletePermission = useMemo(() => {
    return checkOperationPermission('DELETE_DK_RECYCLER_PAGE', perm);
  }, [perm]);

  useEffect(() => {
    setFirstPaint(true);
    loadMore().then(() => {
      setFirstPaint(false);
    });
  }, []);

  const delRecycleBinListOneItem = (id) => {
    setRecycleBinList(recycleBinList.filter((item) => item.id !== id));
  };

  const recoverNotice = () => {
    const dkId = knowledgeId;
    const pageId = restoreIdRef.current;
    return (
      <span>
        {intl.t('已恢复至目录中，')}
        <span
          style={{ color: '#047FFE', cursor: 'pointer' }}
          onClick={() => {
            navigate({
              pathname: teamId ? `/team-file/${teamId}/knowledge/${dkId}/${pageId}` : `/knowledge/${dkId}/${pageId}`,
            });
          }}
        >
          {intl.t('点击查看')}
        </span>
      </span>
    );
  };

  const recover = throttle(
    (id) => {
      window.__OmegaEvent('ep_dkpc_pagetrash_recover_ck');
      restoreIdRef.current = id;
      restore(knowledgeId, id).then((res) => {
        // 恢复请求已经发出，在pending时又发了一次请求导致后来的请求返回结果是null
        if (res.pageId) {
          tree.addNode('0', res, 0);

          notification(NotificationStatus.SUCCESS, recoverNotice());
          delRecycleBinListOneItem(id);
        }
      });
    },
    3000,
    { trailing: false },
  );
  const completeDelete = (id) => {
    window.__OmegaEvent('ep_dkpc_pagetrash_delete_ck');
    setDelId(id);
    setDelModalVisible(true);
  };
  const handleCancel = () => {
    setDelModalVisible(false);
  };
  const handleOk = () => {
    setDelModalVisible(false);
    completedDelete({ knowledgeId, resourceId: delId }).then(() => {
      delRecycleBinListOneItem(delId);
    });
  };

  const columns = [
    {
      title: intl.t('名称'),
      dataIndex: 'resourceName',
      key: 'resourceName',
      width: '50%',
      className: cx('pageColumn'),
      render: (text, record) => {
        return (
          <div className={cx('pageTd')}>
            <FileContent
              resourceName={record.resourceName}
              description={
                record.childrenCount > 0
                  ? intl.t('包含{slot0}个子页面', {
                    slot0: record.childrenCount,
                  })
                  : null
              }
              metisId={record.knowledgeId}
              isInTable={true}
              isLarge={true}
            />
          </div>
        );
      },
    },
    {
      title: intl.t('操作者'),
      dataIndex: 'deletedByName',
      key: 'deletedByName',
      width: '20%',
      render: (name) => {
        return (
          <div>
            { getLocale() === 'zh-CN' ? name.chineseName : name.englishName }
          </div>
        );
      },
    },
    {
      title: intl.t('剩余时间'),
      dataIndex: 'leftTime',
      key: 'address',
      width: '20%',
      render: (text) => {
        return <div>{formatTime(text)}</div>;
      },
    },
    {
      title: intl.t('操作'),
      key: 'action',
      width: '70px',
      render: (text, record) => (
        <div className={cx('action-wrap')}>
          {restorePermission && (
            <Tooltip
              title={intl.t('恢复')}
              placement="top"
              overlayClassName={
                'dk-ant-tooltip__reset knowledge_recycleBin_tooltip'
              }
            >
              <div
                className={cx('recoverWrap')}
                onClick={() => recover(record.id)}
              >
                <img
                  className={cx('recover')}
                  src={RecoverIcon} />
              </div>
            </Tooltip>
          )}
          {deletePermission && (
            <Tooltip
              title={intl.t('彻底删除')}
              placement="top"
              overlayClassName={
                'dk-ant-tooltip__reset knowledge_recycleBin_tooltip'
              }
            >
              <div
                onClick={() => completeDelete(record.id)}
                className={cx('completeDeleteWrap')}
              >
                <img
                  className={cx('completeDelete')}
                  src={CompleteDeleteIcon}
                />
              </div>
            </Tooltip>
          )}
        </div>
      ),
    },
  ];

  return (
    <div>
      {!firstPaint
        && (recycleBinList.length === 0 && !loading ? (
          <div className={cx('empty_wrap')}>
            <img
              className={cx('emptyIcon')}
              src={RecycleBinEmptyIcon} />
            <div className={cx('emptyTip')}>{intl.t('空空如也')}</div>
          </div>
        ) : (
          <div
            onScroll={handleScroll}
            id="recycleBinList"
            className={`${cx('recycleBinList')} knowledge_recycleBin_wrap`}
          >
            <ScrollTableList
              loadMore={loadMore}
              loading={loading}
              hasMore={hasMore}
              columns={columns}
              dataList={recycleBinList}
            />

            {!loading && !hasMore && atBottom && <NoMore />}
            <TextOnlyModal
              handleOk={handleOk}
              handleCancel={handleCancel}
              modalVisible={delModalVisible}
              title={intl.t('确定彻底删除？')}
            />
          </div>
        ))}
    </div>
  );
};

export default RecycleBinList;
