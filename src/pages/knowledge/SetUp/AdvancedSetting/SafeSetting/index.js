import { intl } from 'di18n-react';
import { useEffect, useState, useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Radio, Tooltip } from 'antd';
import classNames from 'classnames/bind';
import TextOnlyModal from '@/components/TextOnlyModal';
import * as service from '@/service/knowledge/setup';
import * as styles from './style.module.less';
import useNotification from '@/hooks/useNotification';
import NotificationStatus from '@/constants/notification';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { SAFE_SETTING_VALUE } from '@/constants/setup';
import { openNewWindow } from '@/utils';

const cx = classNames.bind(styles);
const SafeSetting = () => {
  const [downloadModalVisible, setDownloadModalVisible] = useState(false);
  const [commentModalVisible, setCommentModalVisible] = useState(false);
  const notification = useNotification();
  const { knowledgeId } = useContext(LayoutContext);
  const [isSharing, setIsSharing] = useState(false);
  const { globalOutsideChain } = useSelector((state) => state.CooperIndex);
  const { operatePerm } = useSelector((state) => state.Setting);
  const dispatch = useDispatch();
  const { changeOperatePerm } = dispatch.Setting;

  useEffect(() => {
    service.gainSetting({ knowledgeId }).then((res) => {
      changeOperatePerm({
        downloadValue: res?.switchSetting?.shareLinkDownloadConfig,
        commentValue: res?.switchSetting?.shareLinkCommentConfig,
      });
    });
  }, [knowledgeId]);

  const goToWiki = () => {
    if (!globalOutsideChain?.safe_setting_share_link) {
      notification(NotificationStatus.ERROR, intl.t('请求中请稍后'));
      return;
    }
    openNewWindow(globalOutsideChain?.safe_setting_share_link);
  };

  const change = async (changedValue, afterChange) => {
    try {
      await service.alterSetting({
        knowledgeId,
        switchSetting: changedValue,
      });
      afterChange();
      notification(NotificationStatus.SUCCESS, intl.t('变更成功'));
    } catch (error) {
      notification(NotificationStatus.ERROR, intl.t('变更失败'));
    }
  };

  const getDesc = (value, diffDesc) => {
    let newValue = 3 - value;
    let isClose = newValue === SAFE_SETTING_VALUE.CLOSE;
    return intl.t(
      '{slot0}{slot1}后，获得知识库访客链接、页面访客链接的人将{slot2}{diffDesc}',
      {
        slot0: isSharing ? intl.t('当前知识库存在分享中的页面，') : '',
        slot1: isClose ? intl.t('知识库-权限-关闭') : intl.t('知识库-权限-开启'),
        slot2: isClose ? intl.t('知识库-权限-无法') : intl.t('知识库-权限-可以'),
        diffDesc,
      },
    );
  };

  const safeSettingArray = [
    {
      title: intl.t(
        '是否允许获得知识库访客链接、页面访客链接的人下载页面中的附件。',
      ),
      modalDesc: getDesc(operatePerm.downloadValue, intl.t('下载页面内的附件')),
      modalVisible: downloadModalVisible,
      changeVisible: async (e) => {
        const res = await service.shareSetting(knowledgeId);
        setIsSharing(res);
        setDownloadModalVisible(true);
      },
      handleOk: () => {
        let value = 3 - operatePerm.downloadValue;
        change({ shareLinkDownloadConfig: value }, () => {
          changeOperatePerm({
            ...operatePerm,
            downloadValue: value,
          });
          setDownloadModalVisible(false);
          if (value === SAFE_SETTING_VALUE.OPEN) {
            window.__OmegaEvent('ep_dkpc_advancedsetting_opendownload_ck');
            return;
          }
          window.__OmegaEvent('ep_dkpc_advancedsetting_closedownload_ck');
        });
      },
      handleCancel: () => {
        setDownloadModalVisible(false);
      },
      radioValue: operatePerm.downloadValue,
    },
    {
      title: intl.t('是否允许获得知识库访客链接、页面访客链接的人进行评论。'),
      modalDesc: getDesc(operatePerm.commentValue, intl.t('查看和添加评论')),
      modalVisible: commentModalVisible,
      commentModalVisible,
      changeVisible: async (e) => {
        const res = await service.shareSetting(knowledgeId);
        setIsSharing(res);
        setCommentModalVisible(true);
      },
      handleOk: () => {
        let value = 3 - operatePerm.commentValue;
        change({ shareLinkCommentConfig: value }, () => {
          changeOperatePerm({
            ...operatePerm,
            commentValue: value,
          });
          setCommentModalVisible(false);
        });
        if (value === 1) {
          window.__OmegaEvent('ep_dkpc_setup_advancedsetup_allowcomment_ck');
        } else {
          window.__OmegaEvent('ep_dkpc_setup_advancedsetup_forbidcomment_ck');
        }
      },
      handleCancel: () => {
        setCommentModalVisible(false);
      },
      radioValue: operatePerm.commentValue,
    },
  ];

  return (
    <div className={cx('safe')}>
      <div className={cx('safe-setting')}>{intl.t('安全设置')}</div>
      {safeSettingArray.map(
        ({
          title,
          changeVisible,
          radioValue,
          modalVisible,
          modalDesc,
          handleOk,
          handleCancel,
        }) => (
          <div className={cx('safe-item')}>
            <div className={cx('safe-title')}>
              {title}
              <Tooltip
                title={intl.t('什么是访客链接？')}
                overlayClassName="dk-ant-tooltip__reset"
              >
                <span className={cx('safe-icon-wrapper')}>
                  <i
                    id="dk-create-page-btn"
                    className={`${cx(
                      'dk-icon-bangzhuzhongxin',
                      'dk-iconfont',
                      'safe-icon',
                    )} `}
                    onClick={goToWiki}
                  />
                </span>
              </Tooltip>
            </div>
            <Radio.Group
              onChange={changeVisible}
              value={radioValue}
              className={cx('safe-radio')}
            >
              <Radio
                className={cx('safe-need')}
                value={SAFE_SETTING_VALUE.OPEN}
              >
                {intl.t('允许')}
              </Radio>
              <Radio
                className={cx('safe-need')}
                value={SAFE_SETTING_VALUE.CLOSE}
              >
                {intl.t('不允许')}
              </Radio>
            </Radio.Group>
            <TextOnlyModal
              okText={intl.t('确认变更')}
              handleOk={handleOk}
              handleCancel={handleCancel}
              modalVisible={modalVisible}
              title={intl.t('权限变更提示')}
              tip={modalDesc}
            />
          </div>
        ),
      )}
    </div>
  );
};

export default SafeSetting;
