import classBind from 'classnames/bind';
import { Button } from 'antd';
import { useMemo, useState, useContext } from 'react';
import { intl } from 'di18n-react';
import TransferOwner from '@/components/serviceComponents/OperateFn/TransferOwner';
import ApplyOwner from '@/components/serviceComponents/OperateFn/ApplyOwner';
import * as styles from './style.module.less';
import * as service from '@/service/knowledge/setup';
import TextOnlyModal from '@/components/TextOnlyModal';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { openNewWindow } from '@/utils/index';

const cx = classBind.bind(styles);

const ChangeDkOwnerEntry = ({ isDkOwner, changeEntryState }) => {
  const [visible, setVisible] = useState(false);
  const [verifyModalVisible, setVerifyModalVisible] = useState(false);
  const [data, setData] = useState({})
  const { knowledgeId } = useContext(LayoutContext);
  const textConf = useMemo(() => {
    return (
      {
        header: isDkOwner ? intl.t('移交知识库所有权') : intl.t('申请知识库所有权'),
        desc: isDkOwner ? intl.t('将知识库权限移交给其他司内用户后，你将自动变为知识库管理员权限') : intl.t('申请成功后即可成为知识库所有者，将拥有知识库内的所有权限'),
        btn: isDkOwner ? intl.t('移交知识库所有权') : intl.t('申请知识库所有权'),
      }
    );
  }, [isDkOwner]);
  const modalData = useMemo(() => {
    if (!Object.keys(data).length) return {};
    return {
      10001: {
        title: intl.t('无法重复提交申请'),
        cancelText: intl.t('知道了'),
        okText: intl.t('查看详情'),
        handleOk: () => {
          openNewWindow(data?.bpmUrl)
        },
        tip: <span>
          {intl.t('你已提交过申请，由')}  <a
            style={{ color: '#127FF0' }}
            target='_blank'
            href={`dchat://im/start_conversation?name=${data?.approver?.ldap}`}
      >
            {data?.approver?.cnName}
          </a> {intl.t('进行审批。请等待审批结果')}
        </span>,
      },
      10002: {
        title: intl.t('无法提交申请'),
        cancelText: intl.t('关闭'),
        okText: intl.t('联系申请者'),
        handleOk: () => {
          openNewWindow(`dchat://im/start_conversation?name=${data?.applicant?.ldap}`)
        },
        tip: <span>
          <a
            style={{ color: '#127FF0' }}
            target='_blank'
            href={`dchat://im/start_conversation?name=${data?.applicant?.ldap}`}
          >
            {data?.applicant?.cnName}
          </a>
          {intl.t('已提交所有权申请，你暂时无法提交申请')}
        </span>,
      },
    }[data?.code]
  }, [data])

  // 点击按钮后执行的方法
  const handleClick = async () => {
    if (isDkOwner) {
      setVisible(true);
      window.__OmegaEvent('ep_dkpc_advancedsetting_transferownership_ck');
      return;
    }
    window.__OmegaEvent('ep_dkpc_advancedsetting_applyownership_ck');
    try {
      const res = await service.verifySetting(knowledgeId);
      const { code } = res;
      if (code === 10000) {
        setVisible(true);
        return;
      }
      setVerifyModalVisible(true);
      setData(res)
    } catch (error) {
      if (error.errorCode === 10010) { // 已经是所有者
        changeEntryState(true)
      }
    }
  };

  const onClose = () => {
    setVisible(false);
  };

  const onTransferCallback = () => {
    setVisible(false);
    changeEntryState(false);
    window.__OmegaEvent('ep_dkpc_advancedsetting_transferownership_confirm_ck');
  };

  const handleVerify = () => {
    setVerifyModalVisible(false);
  };

  return (
    <div className={cx('owner-setting-wrap')}>
      <h1 className={cx('setting-header')} >{textConf.header}</h1>
      <div className={cx('setting-desc')}>{textConf.desc}</div>
      <Button
        type={isDkOwner ? 'danger' : 'primary'}
        ghost={isDkOwner}
        className={cx('setting-btn')}
        onClick={handleClick}
      >{textConf.btn}</Button>
      {isDkOwner
        ? <TransferOwner
            visible={visible}
            onClose={onClose}
            onTransferCallback={onTransferCallback}
            isDK={true}
        />
        : <ApplyOwner
            visible={visible}
            onClose={onClose}
            isDK={true} />

      }
      <TextOnlyModal
        customClass={cx('departMentModal')}
        handleCancel={handleVerify}
        modalVisible={verifyModalVisible}
        {...modalData}
      />
    </div>
  );
};
export default ChangeDkOwnerEntry;