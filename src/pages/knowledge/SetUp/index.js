import { intl } from 'di18n-react';
import { useMemo, useEffect } from 'react';
import { Tabs } from 'antd';
import { Helmet } from 'react-helmet';
import classBind from 'classnames/bind';
import { getUrlParams } from '@/utils';
import { SetupIcon } from '@/assets/icon';
import { SETUP_TABS } from '@/constants/setup';
import * as commonStyles from '@/components/serviceComponents/Layout/style.module.less';
import { useSelector } from 'react-redux';
import usePermission from '@/hooks/usePermission';
import BasicSetup from './BasicSetup';
import PowerManagementWrapper from './PowerManagementWrapper';
import TemplateManagement from './TemplateManagement';
import AdvancedSetting from './AdvancedSetting';
import { CooperTabs, CooperTabsPane } from '@/components/common/CooperTabs';

import * as styles from './style.module.less';

const { TabPane } = Tabs;

const cx = classBind.bind(styles);
const cm = classBind.bind(commonStyles);

const SetUp = () => {
  const { knowledgeDetail, permission } = useSelector(
    (state) => state.KnowledgeData,
  );
  const { perm } = permission;
  const { checkOperationPermission } = usePermission();

  useEffect(() => {
    if (knowledgeDetail.spaceName) {
      window.document.title = intl.t('设置-{slot0}', {
        slot0: knowledgeDetail.spaceName,
      });
    }
  }, [knowledgeDetail]);

  const handleTabChange = (activeKey) => {
    switch (activeKey) {
      case SETUP_TABS.BASIC_SETUP:
        window.__OmegaEvent('ep_dkpc_pagesetting_basicsetting_ck');
        break;
      case SETUP_TABS.POWER_MANAGERMENT:
        window.__OmegaEvent('ep_dkpc_pagesetting_membermanage_ck');
        break;
      default:
    }
  };

  const defaultKey = getUrlParams('key');

  const hasTemplatePerm = useMemo(() => {
    return checkOperationPermission('MANAGE_DK_PAGE', perm);
  }, [perm]);
  return (
    <div className={`${cx('setUpWrap')} ${cm('body-main-wrap')}`}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png" />
      </Helmet>
      <div className={cm('mainTitle')}>
        {/* <img
           src={ SetupIcon }
           className={ cm('titleIcon') } /> */}
        <div className={cm('title')}>{intl.t('设置')}</div>
      </div>
      <CooperTabs
        onChange={handleTabChange}
        defaultActiveKey={defaultKey}>
        <CooperTabsPane
          tab={intl.t('基本设置')}
          key={SETUP_TABS.BASIC_SETUP}>
          <BasicSetup />
        </CooperTabsPane>
        <CooperTabsPane
          tab={intl.t('权限管理')}
          key={SETUP_TABS.POWER_MANAGERMENT}>
          <PowerManagementWrapper />
        </CooperTabsPane>
        {hasTemplatePerm && (
          <CooperTabsPane
            tab={intl.t('模板管理')}
            key={SETUP_TABS.TEMPLATE_MANAGERMENT}
          >
            <TemplateManagement />
          </CooperTabsPane>
        )}

        <CooperTabsPane
          tab={intl.t('高级设置')}
          key={SETUP_TABS.ADVANCED_SETTING}>
          <AdvancedSetting />
        </CooperTabsPane>
      </CooperTabs>
    </div>
  );
};

export default SetUp;
