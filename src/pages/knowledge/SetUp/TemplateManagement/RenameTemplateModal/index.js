import { intl } from 'di18n-react';
import { useEffect, useState } from 'react';
import { Modal, Form, Input } from 'antd';
import classnames from 'classnames/bind';
import { checkNameErrorMsg } from '@/utils';
import * as styles from './style.module.less';

const cls = classnames.bind(styles);

function RenameTemplateModal(props) {
  const [form] = Form.useForm();
  const { title = '', visible, onOk, onCancel } = props;
  const [nameCheckMsg, setNameCheckMsg] = useState('');

  const handleOk = () => {
    form
      .validateFields()
      .then((values) => {
        onOk(values);
      })
      .catch(() => {});
  };
  const onChange = (e) => {
    const name = e.target.value;
    setNameCheckMsg(checkNameErrorMsg(name));
    form.setFieldsValue('title', name);
  };
  const closeModal = () => {
    setNameCheckMsg('');
    onCancel();
  };

  useEffect(() => {
    form.resetFields();
  }, [visible]);

  return (
    <Modal
      wrapClassName={cls('rename-template-modal')}
      visible={visible}
      onOk={handleOk}
      onCancel={closeModal}
      title={intl.t('重命名模版')}
      okText={intl.t('确定')}
      cancelText={intl.t('取消')}
      closeIcon={
        <i className={cls('dk-iconfont', 'dk-icon-guanbi', 'close-icon')} />
      }
      okButtonProps={{ disabled: nameCheckMsg !== '' }}
      width={480}
      centered={true}
      forceRender={true}
    >
      <div className={cls('container')}>
        <Form
          form={form}
          initialValues={{ title }}>
          <Form.Item name="title">
            <Input
              className={cls('input')}
              placeholder={intl.t('请输入模板名称')}
              maxLength={100}
              onChange={onChange}
              autoFocus
            />
          </Form.Item>
        </Form>
        <div className={cls('templateNameCheckMsg', 'nameCheckMsg')}>
          {nameCheckMsg}
        </div>
      </div>
    </Modal>
  );
}

export default RenameTemplateModal;
