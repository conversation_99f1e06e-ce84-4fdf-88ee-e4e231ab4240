import { intl } from 'di18n-react';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { Modal, Button } from 'antd';
import classNames from 'classnames/bind';
import CollapsePanel from '@/components/CollapsePanel';
import useNotification from '@/hooks/useNotification';
import NotificationStatus from '@/constants/notification';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { getUrlParams } from '@/utils';
import usePermission from '@/hooks/usePermission';
import RenameTemplateModal from './RenameTemplateModal';
import Editor from './Editor';
import * as styles from './style.module.less';

const cls = classNames.bind(styles);

function TemplateManage(props) {
  const containerRef = useRef();
  const {
    profile,
    permission,
    defaultTemplates,
    knowledgeTemplates,
    getKnowledgeTemplates,
    getDefaultTemplates,
    deleteTemplate,
    renameTemplate,
    updateTemplateName,
  } = props;
  const { perm } = permission;
  const [active, setActive] = useState({});
  const [renameVisible, setRenameVisible] = useState(false);
  const navigate = useNavigate();
  const { knowledgeId } = useContext(LayoutContext);
  const notification = useNotification();
  const tpl = getUrlParams('tpl');
  const { checkOperationPermission } = usePermission();
  const { teamId } = useParams();

  const editTemplate = () => {
    navigate(
      teamId
        ? `/knowledge/${knowledgeId}/template/${active.templateId}/edit?spaceId=${teamId}`
        : `/knowledge/${knowledgeId}/template/${active.templateId}/edit`,
    );
  };

  const openRenameModal = () => {
    setRenameVisible(true);
  };

  const handleRename = ({ title: name }) => {
    const { templateId } = active;
    const formData = new FormData();
    formData.append('name', name);
    formData.append('templateId', templateId);
    setRenameVisible(false);
    renameTemplate({ knowledgeId, formData }).then(() => {
      updateTemplateName({
        templateId,
        name,
      });
      notification(NotificationStatus.SUCCESS, intl.t('模板已更新'));
      setActive({
        ...active,
        name,
      });
    });
  };

  const cancelRename = () => {
    setRenameVisible(false);
  };

  const activeFirstTemplate = (templateId) => {
    const data = [...knowledgeTemplates, ...defaultTemplates].filter(
      (item) => item.templateId !== templateId,
    );
    const firstTemplate = data[0];
    firstTemplate && setActive(firstTemplate);
  };

  const handleDelete = () => {
    deleteTemplate({
      knowledgeId,
      templateId: active.templateId,
    }).then(() => {
      activeFirstTemplate(active.templateId);
      notification(NotificationStatus.SUCCESS, intl.t('已删除模板'));
    });
  };

  const confirmDelete = () => {
    Modal.confirm({
      width: 480,
      icon: null,
      centered: true,
      title: intl.t('确定删除吗？'),
      content: intl.t('模版删除后将无法恢复。已使用模版创建的⻚面不受影响。'),
      okText: intl.t('确定'),
      cancelText: intl.t('取消'),
      className: cls('delete-template-modal'),
      onOk: handleDelete,
    });
  };

  const goToCreateTemplate = () => {
    navigate(
      teamId
        ? `/knowledge/${knowledgeId}/template/new/edit?spaceId=${teamId}`
        : `/knowledge/${knowledgeId}/template/new/edit`,
    );
    window.__OmegaEvent('ep_dkpc_templatesetting_createtemplate_ck');
  };

  const toggleTemplate = (template) => {
    const editorContainer = containerRef.current.querySelector('.didoc-editor-app');
    editorContainer && editorContainer.scrollTo(0, 0);
    setActive(template);
  };

  const isReadOnly = useMemo(() => {
    return !checkOperationPermission('MANAGE_DK_PAGE', perm);
  }, [perm]);

  const hasOperationPermission = useMemo(() => {
    return (
      checkOperationPermission('MANAGE_DK_SETTING', perm)
      || profile.username === active.creatorEnName
    );
  }, [perm, profile, active]);

  const isKnowledgeTemplate = useMemo(() => {
    return knowledgeTemplates.some(
      (item) => active.templateId === item.templateId,
    );
  }, [knowledgeTemplates, active]);

  const data = useMemo(() => {
    return [
      {
        title: intl.t('当前知识库模版'),
        children: knowledgeTemplates,
      },
      {
        title: intl.t('系统模版'),
        children: defaultTemplates,
      },
    ];
  }, [defaultTemplates, knowledgeTemplates]);

  useEffect(async () => {
    Promise.all([
      getKnowledgeTemplates(knowledgeId),
      getDefaultTemplates(knowledgeId),
    ]).then((all) => {
      const allTemplate = all.flat();
      const activeTemplate = allTemplate.find((item) => item.templateId === tpl) || allTemplate[0];
      activeTemplate && setActive(activeTemplate);
    });
  }, [knowledgeId]);

  return (
    <div
      ref={containerRef}
      className={cls('template-management')}>
      <div className={cls('container')}>
        <div className={cls('sidebar')}>
          <Button
            className={cls('new-template')}
            icon={
              <i
                className={cls('dk-iconfont', 'dk-icon-tianjia', 'new-icon')}
              />
            }
            onClick={goToCreateTemplate}
            disabled={isReadOnly}
          >
            {intl.t('新建模板')}
          </Button>
          <div className={cls('menu')}>
            <CollapsePanel
              data={data}
              active={active}
              onChange={toggleTemplate}
            />
          </div>
        </div>
        <div className={cls('main')}>
          {isKnowledgeTemplate ? (
            <div className={cls('header')}>
              <div className={cls('title')}>
                {intl.t('{slot0} {slot1} 制作', {
                  slot0: active.creatorCnName,
                  slot1: active.creatorEnName,
                })}
                {/* {active.creatorCnName} {active.creatorEnName}
                {intl.t('制作')} */}
              </div>
              {hasOperationPermission && (
                <div className={cls('options')}>
                  <span
                    className={cls('edit')}
                    onClick={editTemplate}>
                    <i
                      className={cls(
                        'dk-iconfont',
                        'dk-icon-bianjimoban',
                        'edit-icon',
                      )}
                    />
                    {intl.t('编辑')}
                  </span>
                  <span
                    className={cls('rename')}
                    onClick={openRenameModal}>
                    <i
                      className={cls(
                        'dk-iconfont',
                        'dk-icon-zhongmingming1',
                        'rename-icon',
                      )}
                    />
                    {intl.t('重命名')}
                  </span>
                  <span
                    className={cls('delete-template')}
                    onClick={confirmDelete}>
                    <i
                      className={cls(
                        'dk-iconfont',
                        'dk-icon-shanchu2',
                        'delete-icon',
                      )}
                    />
                    {intl.t('删除')}
                  </span>
                </div>
              )}
            </div>
          ) : (
            <div className={cls('header')}>
              <div className={cls('title')}>{intl.t('系统模版')}</div>
            </div>
          )}

          <div className={cls('content')}>
            <Editor templateId={active.templateId} />
          </div>
        </div>
      </div>
      <RenameTemplateModal
        title={active.name}
        visible={renameVisible}
        onCancel={cancelRename}
        onOk={handleRename}
      />
    </div>
  );
}

function mapStateToProps({ template, KnowledgeData, CooperIndex }) {
  const { permission } = KnowledgeData;
  const { profile } = CooperIndex;
  const { defaultTemplates, knowledgeTemplates } = template;
  return {
    permission,
    profile,
    defaultTemplates,
    knowledgeTemplates,
  };
}

function mapDispatchToProps({ template }) {
  const {
    getKnowledgeTemplates,
    getDefaultTemplates,
    renameTemplate,
    deleteTemplate,
    updateTemplateName,
  } = template;
  return {
    getKnowledgeTemplates,
    getDefaultTemplates,
    renameTemplate,
    deleteTemplate,
    updateTemplateName,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(TemplateManage);
