import { Tooltip } from 'antd';
import MemberItem from '@/components/serviceComponents/MemberComponent/MemberItem';
import { departmentMemberIcon } from '@/assets/icon'
import { getDepartmentStr } from '@/utils';
import classBind from 'classnames/bind';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const DepartmentMemberItem = ({ memberInfo, children, name }) => {
  const { departmentPath, departmentId } = memberInfo
  const department = departmentPath.join('/');

  return <MemberItem
    exist={memberInfo.exist}
    id={departmentId}
    number={''}
    name={ name || <Tooltip
      title={department}
      placement="top"
      overlayClassName={cx('dk-ant-tooltip__reset', { hide: department.length <= 26 })} >
      {getDepartmentStr(department, 13)}
    </Tooltip>
    }
    avatar={departmentMemberIcon}>
    {children}
  </MemberItem>;
}
export default DepartmentMemberItem;
