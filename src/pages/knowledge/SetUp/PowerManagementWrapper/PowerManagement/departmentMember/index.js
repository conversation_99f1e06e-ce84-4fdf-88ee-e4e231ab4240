import { intl } from 'di18n-react';
import { useState } from 'react';
import { Modal } from 'antd';
import classBind from 'classnames/bind';
import InfiniteScroll from 'react-infinite-scroller';
import { debounce } from 'lodash-es';
import { deepClone } from '@/components/FileTreeModal/utils';
import { isEmoji } from '@/utils';
import MemberInput from '@/components/serviceComponents/MemberComponent/MemberInput';
import { DK_MEMBER_TYPE, ADD_MEMBER_TYPE } from '@/constants/setup';
import { getDepartment } from '@/service/knowledge/createKnowledgeModal';
import { addDepartmentGroup } from '@/service/knowledge/setup';
import PermPopover from '@/components/serviceComponents/PermPopover';
import MemberFooter from '@/components/serviceComponents/MemberComponent/MemberFooter';
import EmptySearch from '@/components/serviceComponents/MemberComponent/EmptySearch';
import SpinRender from '@/components/SpinRender';
import useNotification from '@/hooks/useNotification';
import useLoadMore from '@/hooks/useLoadMore';
import NotificationStatus from '@/constants/notification';
import DepartmentMemberItem from './departmentMemberItem';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const DepartmentMember = ({
  closeDepartmentMember,
  refreshPowerList,
  knowledgeId,
  getDkMember,
}) => {
  const notification = useNotification();
  const [resultList, setResultList] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [showConfrimBtn, setShowConfrimBtn] = useState(true);
  const { loadMore, loading, hasMore, list, setList } = useLoadMore(
    (args) => getDepartment({ ...args, markExist: true, knowledgeId }),
    null,
  );

  const onInputChange = debounce((e) => {
    const { value } = e.target;
    setInputValue(value);
    if (isEmoji(value)) {
      setList([]);
      return;
    }
    if (value) {
      setShowConfrimBtn(false);
      loadMore({ keywords: value }, true);
    } else {
      setShowConfrimBtn(true);
      setList([]);
    }
    window.__OmegaEvent('ep_dkpc_member_invitegroup_search_ck');
  }, 500);

  const findMemberIndex = (id) => {
    return resultList.findIndex((item) => item.departmentId === id);
  };
  const setPerm = (id, perm) => {
    const temp = deepClone(resultList);
    temp[findMemberIndex(id)].perm = perm;
    setResultList(temp);
  };
  const add = async () => {
    window.__OmegaEvent('ep_dkpc_member_invitegroup_organization_ck');
    await addDepartmentGroup(knowledgeId, {
      groupType: ADD_MEMBER_TYPE.DEPARTMENT,
      body: resultList.map(({ departmentId, perm, departmentName }) => {
        return {
          id: departmentId,
          role: perm,
          name: departmentName,
        };
      }),
    });
    closeDepartmentMember();
    refreshPowerList();
    notification(NotificationStatus.SUCCESS, intl.t('添加成功'));
  };
  const del = (id) => {
    const temp = deepClone(resultList);
    temp.splice(findMemberIndex(id), 1);
    setResultList(temp);
    if (temp.length === 0) {
      setShowConfrimBtn(false);
    }
  };
  const choose = (item) => {
    setResultList(
      resultList.concat([{ ...item, perm: DK_MEMBER_TYPE.DK_MEMBER }]),
    );
    setShowConfrimBtn(true);
  };
  const permList = getDkMember
    ? [
      { value: DK_MEMBER_TYPE.DK_MEMBER, name: intl.t('成员') },
      { value: DK_MEMBER_TYPE.DK_READ_MEMBER, name: intl.t('只读成员') },
    ]
    : [{ value: DK_MEMBER_TYPE.DK_MEMBER, name: intl.t('成员') }];

  return (
    <Modal
      title={intl.t('添加成员组')}
      visible={true}
      closeIcon={<i className={cx('dk-iconfont', 'dk-icon-guanbi', 'close')} />}
      onCancel={closeDepartmentMember}
      footer={null}
      width={640}
      wrapClassName={cx('knowledge_component_addMember', 'dynamicMemberModal')}
    >
      <div className={cx('dynamicMember')}>
        <div className={cx('header')}>
          <p className={cx('tip')}>
            {intl.t(
              '组织架构节点以组的形式被添加到知识{slot0}内，组内成员范围会随员工入转调离情况实时同步',
              { slot0: getDkMember ? intl.t('添加到-知识-库') : intl.t('添加到-知识-门户') },
            )}
          </p>
          <MemberInput
            placeholder={intl.t('请输入组织架构节点名称')}
            onInputChange={onInputChange}
          />
        </div>
        <div className={cx('memberList')}>
          {showConfrimBtn ? (
            resultList.map((memberInfo) => {
              return (
                <DepartmentMemberItem
                  key={memberInfo.departmentId}
                  name={memberInfo.departmentName}
                  memberInfo={memberInfo}
                >
                  <PermPopover
                    id={memberInfo.departmentId}
                    setPerm={setPerm}
                    permList={permList}
                    perm={memberInfo.perm}
                    del={del}
                  />
                </DepartmentMemberItem>
              );
            })
          ) : (
            <InfiniteScroll
              initialLoad={false}
              pageStart={0}
              loadMore={() => loadMore({ keywords: inputValue })}
              hasMore={!loading && hasMore}
              useWindow={false}
            >
              {list.length > 0 ? (
                list.map((memberInfo) => {
                  return (
                    <DepartmentMemberItem
                      key={memberInfo.departmentId}
                      memberInfo={memberInfo}
                    >
                      {!memberInfo.exist
                        && findMemberIndex(memberInfo.departmentId) === -1 && (
                          <div
                            className={cx('add')}
                            onClick={() => choose(memberInfo)}
                          >
                            {intl.t('添加')}
                          </div>
                      )}
                    </DepartmentMemberItem>
                  );
                })
              ) : loading ? (
                <SpinRender loading={loading} />
              ) : inputValue ? (
                <EmptySearch />
              ) : (
                <></>
              )}
            </InfiniteScroll>
          )}
        </div>
        {showConfrimBtn && (
          <MemberFooter
            okBtnDisabled={resultList.length === 0}
            cancel={closeDepartmentMember}
            ok={add}
            okTxt={intl.t('添加并通知')}
          />
        )}
      </div>
    </Modal>
  );
};
export default DepartmentMember;
