import classBind from 'classnames/bind';
import InfiniteScroll from 'react-infinite-scroller';
import useLoadMore from '@/hooks/useLoadMore';
import MemberList from '../memberList';
import { DK_MEMBER_TYPE, POWER_MANAGERMENT_TABS_TYPE } from '@/constants/setup';
import * as styles from '../style.module.less';
import { useEffect } from 'react';


const cx = classBind.bind(styles);

const MemberTab = ({
  fn, managerMemberPermis, manageAdminPermis, hasAdvancedPerm, getDkMember,
  openCustomMember, TabType, knowledgeId, getRefreshGroup, getRefreshSingleMember, getRefreshAll,
}) => {
  const { loadMore, loading, hasMore, list } = useLoadMore((args) => fn(knowledgeId, args));

  useEffect(() => {
    if (TabType === POWER_MANAGERMENT_TABS_TYPE.ALL) {
      getRefreshAll(loadMore);
    } else if (TabType === POWER_MANAGERMENT_TABS_TYPE.GROUP) {
      getRefreshGroup(refreshList);
    } else {
      getRefreshSingleMember(refreshList);
    }
  }, []);

  const delMemberListOneItem = () => {
    getDkMember && getDkMember();
    refreshList();
  };

  const refreshList = () => {
    return loadMore({}, true);
  };

  const afterChangeRole = (role) => {
    loadMore({}, true, false);
    if (role === DK_MEMBER_TYPE.DK_ADMIN) {
      document.getElementById('dk-memberList-wrap').scrollTop = 0;
    }
  };

  return <><div
    className={cx('memberList-wrap')}
    id='dk-memberList-wrap'
    >
    <InfiniteScroll
      initialLoad={false}
      pageStart={0}
      loadMore={() => loadMore({})}
      hasMore={!loading && hasMore}
      useWindow={false}>
      <MemberList
        loading={loading}
        delMemberListOneItem={delMemberListOneItem}
        afterChangeRole={afterChangeRole}
        showNoMoreTip={!loading && !hasMore}
        memberList={list}
        keyword={''}
        knowledgeId={knowledgeId}
        managerMemberPermis={managerMemberPermis}
        manageAdminPermis={manageAdminPermis}
        hasAdvancedPerm={hasAdvancedPerm}
        openCustomMember={openCustomMember}
        getDkMember={getDkMember}
      />
    </InfiniteScroll>
  </div>
  </>;
};

export default MemberTab;
