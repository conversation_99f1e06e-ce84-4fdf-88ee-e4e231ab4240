import { getLocale, intl } from 'di18n-react';
import { useState } from 'react';
import { Popover, Tooltip } from 'antd';
import classBind from 'classnames/bind';
import {
  DK_MEMBER_TYPE,
  DK_MEMBER_OMEGA_TYPE,
  ADD_GROUP_TYPE,
} from '@/constants/setup';
import { SwitchLocaleIcon, powerManagermentCheckIcon } from '@/assets/icon';
import { changeMemberRole } from '@/service/knowledge/setup';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const PowerPopover = ({
  openDelModal,
  member,
  manageAdminPermis,
  hidePopover,
  managerMemberPermis,
  hasAdvancedPerm,
  afterChangeRole,
  knowledgeId,
  getDkMember,
}) => {
  const {
    id,
    externalGroupId,
    roleKey,
    memberType,
    externalGroupType,
    roleNameZh,
    roleName<PERSON>,
  } = member;
  const notification = useNotification();
  const [visible, setVisible] = useState(false);

  const changeRole = async (e) => {
    setVisible(false);
    const { role } = e.target.dataset;
    window.__OmegaEvent('ep_dkpc_member_changetrole_ck', '', {
      role: DK_MEMBER_OMEGA_TYPE[role],
    });

    await changeMemberRole(knowledgeId, { id, role, memberType });
    afterChangeRole(role);
    notification(NotificationStatus.SUCCESS, intl.t('修改成功'));
  };

  const changePower = () => (
    <div>
      <div
        data-role={DK_MEMBER_TYPE.DK_MEMBER}
        className={cx('changePower-item')}
      >
        {intl.t('成员')}
        {roleKey === DK_MEMBER_TYPE.DK_MEMBER && (
          <img
            className={cx('checkIcon')}
            src={powerManagermentCheckIcon} />
        )}
      </div>
      {getDkMember && (
        <div
          data-role={DK_MEMBER_TYPE.DK_READ_MEMBER}
          className={cx('changePower-item')}
        >
          {intl.t('只读成员')}
          {roleKey === DK_MEMBER_TYPE.DK_READ_MEMBER && (
            <img
              className={cx('checkIcon')}
              src={powerManagermentCheckIcon} />
          )}
        </div>
      )}
    </div>
  );

  const changeUserPower = () => (
    <div
      className={cx('changePower')}
      onClick={(e) => changeRole(e, id, memberType)}
    >
      <div
        data-role={DK_MEMBER_TYPE.DK_ADMIN}
        className={cx('changePower-item', { disabled: !manageAdminPermis })}
      >
        {intl.t('管理员')}

        {roleKey === DK_MEMBER_TYPE.DK_ADMIN && (
          <img
            className={cx('checkIcon')}
            src={powerManagermentCheckIcon} />
        )}
      </div>
      {changePower(roleKey)}
      <div
        className={cx('changePower-item', 'cancelConnect')}
        onClick={(e) => openDelModal(e, id, ADD_GROUP_TYPE.PERSON)}
      >
        {intl.t('移除')}
      </div>
    </div>
  );

  const changeGroupPower = () => (
    <div
      className={cx('changePower')}
      onClick={(e) => changeRole(e, id, memberType)}
    >
      {changePower(roleKey)}
      {(externalGroupType === ADD_GROUP_TYPE.COOPER_GROUP
        ? hasAdvancedPerm
        : managerMemberPermis) && (
          <div
            className={cx('changePower-item', 'cancelConnect')}
            onClick={(e) => openDelModal(e, id, externalGroupType, externalGroupId)
          }
        >
            {externalGroupType === ADD_GROUP_TYPE.COOPER_GROUP
              ? intl.t('取消关联')
              : externalGroupType === ADD_GROUP_TYPE.CUSTOM_GROUP
                ? intl.t('删除')
                : intl.t('移除')}
          </div>
      )}
    </div>
  );

  const getPowerTip = () => {
    if (getDkMember) {
      switch (roleKey) {
        case 'DK_OWNER':
          return intl.t('拥有知识库的所有权限');
        case 'DK_ADMIN':
          return intl.t('拥有管理和编辑页面、添加和管理成员的权限');
        case 'DK_READ_MEMBER':
          return intl.t('仅拥有知识库页面的阅读权限');
        case 'DK_MEMBER':
          return intl.t('拥有管理和编辑知识库页面的权限');
        default:
          return '';
      }
    } else {
      switch (roleKey) {
        case 'DK_OWNER':
          return intl.t('拥有门户管理权限');
        case 'DK_ADMIN':
          return intl.t('拥有配置门户和管理成员权限');
        case 'DK_MEMBER':
          return intl.t('拥有查看知识门户权限');
        default:
          return '';
      }
    }
  };

  return (
    <Tooltip
      title={getPowerTip(roleKey)}
      placement="top"
      overlayClassName="dk-ant-tooltip__reset"
    >
      <Popover
        visible={visible}
        onVisibleChange={(v) => {
          !v && setVisible(false);
        }}
        trigger="click"
        getPopupContainer={(trigger) => trigger.parentNode}
        overlayClassName={cx(
          'dk-changePower-wrap_reset',
          'dk-ant-popover__reset',
          { hide: hidePopover(roleKey) },
        )}
        content={() => (memberType === 'User' ? changeUserPower() : changeGroupPower())
        }
        placement="bottomLeft"
      >
        <div onClick={() => setVisible(!visible)}>
          {getLocale() === 'en-US' ? roleNameEN : roleNameZh}
          <img
            className={cx('openChangePower', {
              hideChangePower: hidePopover(roleKey),
            })}
            src={SwitchLocaleIcon}
          />
        </div>
      </Popover>
    </Tooltip>
  );
};

export default PowerPopover;
