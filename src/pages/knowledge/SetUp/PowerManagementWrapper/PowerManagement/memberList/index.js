import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Avatar, List, Tooltip } from 'antd';
import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import { cloneDeep } from 'lodash-es';
import { ADD_GROUP_TYPE } from '@/constants/setup';
import { unboundSpace, delDkMember, deleteGroup } from '@/service/knowledge/setup';
import { znStrlength, highLightKeyword } from '@/utils';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import TextOnlyModal from '@/components/TextOnlyModal';
import SpinRender from '@/components/SpinRender';
import MemberEmail from '@/components/serviceComponents/MemberComponent/MemberEmail';
import useDebounceFn from '@/hooks/useDebounceFn';
import PowerPopover from './powerPopover';
import * as styles from './style.module.less';
import { useDispatch, useSelector } from 'react-redux';

const cx = classBind.bind(styles);

const MemberList = ({
  memberList, keyword, delMemberListOneItem, afterChangeRole, showNoMoreTip, knowledgeId,
  managerMemberPermis, manageAdminPermis, hasAdvancedPerm, openCustomMember, loading, getDkMember,
}) => {
  const [delModalVisible, setDelModalVisible] = useState(false);
  const [delId, setDelId] = useState();
  const [delCooperGroupId, serDelCooperGroupId] = useState();
  const [delType, setDelType] = useState();
  const notification = useNotification();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const isGroup = delType === ADD_GROUP_TYPE.CUSTOM_GROUP || delType === ADD_GROUP_TYPE.DEPARTMENT_GROUP;
  const { teamId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  // const { saveFuncs } = dispatch.TeamData;
  const { getFeatures } = dispatch.TeamData;

  const {
    features,
  } = useSelector(
    (state) => state.TeamData,
    );
  

  const handleCloseTab = (knowledgeId) => {
    const cloneFeature = cloneDeep(features);
    // const result = cloneFeature.filter((item) => {
    //   return item.subjectId !== knowledgeId;
    // });

   

    setTimeout(() => {
      navigate(`/team-file/${teamId}/home`);
      getFeatures(teamId);
    }, 1000);
  }

  const delMember = async () => {
    setConfirmLoading(true);
    try {
      if (delType === ADD_GROUP_TYPE.COOPER_GROUP) {
        await unboundSpace({ metisSpaceId: knowledgeId, spaceId: delCooperGroupId });
      } else if (isGroup) {
        await deleteGroup(knowledgeId, delId);
      } else {
        await delDkMember(knowledgeId, { id: delId, memberType: 'User' });
      }
      setDelModalVisible(false);
      if (delType === ADD_GROUP_TYPE.CUSTOM_GROUP) {
        notification(NotificationStatus.SUCCESS, intl.t('删除成功'));
      } else {
        notification(NotificationStatus.SUCCESS, intl.t('移除成功'));
      }
      delMemberListOneItem();
      setConfirmLoading(false);
      if (delType === ADD_GROUP_TYPE.COOPER_GROUP && teamId && teamId === delCooperGroupId) {
        handleCloseTab(knowledgeId);
      }
    } catch {
      setConfirmLoading(false);
    }
  };

  const { run: debounceDel } = useDebounceFn(delMember);

  const openDelModal = (e, id, type, groupId) => {
    e.stopPropagation();
    setDelId(id);
    serDelCooperGroupId(groupId);
    setDelType(type);
    setDelModalVisible(true);
    if (type === ADD_GROUP_TYPE.COOPER_GROUP) {
      window.__OmegaEvent('ep_dkpc_member_unbound_ck');
    } else {
      window.__OmegaEvent('ep_dkpc_member_remove_ck');
    }
  };

  const closeDelModal = () => {
    setDelModalVisible(false);
  };

  const getMemberName = () => {
    const member = memberList.find((item) => item.id === delId);
    const name = member ? member.memberName : '';
    return name;
  };

  const delModalTitle = () => {
    if (delType === ADD_GROUP_TYPE.COOPER_GROUP) {
      return intl.t('解除关联关系');
    }
    if (delType === ADD_GROUP_TYPE.CUSTOM_GROUP) {
      return intl.t('删除成员组');
    }
    if (isGroup) {
      return intl.t('移除成员组');
    }

    const user = <span style={{ color: '#ff563b' }}>{getMemberName()}</span>
    return <span>{intl.t('是否移除成员')}:{user}?</span>;
  };

  const delModalTip = () => {
    if (delType === ADD_GROUP_TYPE.PERSON) {
      return '';
    }
    if (isGroup) {
      return intl.t('确认要移除该成员组吗？一旦移除，该成员组内的成员就无法访问该{name}', {
        name: getDkMember ? intl.t('知识库及知识库内的所有页面内容') : intl.t('知识门户内容'),
      })
    }
    return <span>
      {`${intl.t('是否解除与团队')}`}
      <span style={{ color: '#127FF0' }}>{getMemberName()}</span>{intl.t('的关联关系？一旦解除成功，该团队将无法访问知识库。你可前往')}<span style={{ color: '#127FF0' }}>{getMemberName()}</span>{intl.t('重新进行关联。')}
    </span>;
  };


  const hidePopover = (roleKey) => {
    return roleKey === 'DK_OWNER' || !managerMemberPermis;
  };


  return <div className={cx('memberList')}>
    <List
      className="knowledge_MemberManagement_list"
      itemLayout="horizontal"
      dataSource={memberList}
      renderItem={({
        id, memberName, avatar, memberEmail, memberCount, memberType,
        roleKey, roleNameZh, externalGroupId, groupUsers, externalGroupType, roleNameEN, hrStatus
      }) => (
        <List.Item>
          <List.Item.Meta
            avatar={<Avatar src={avatar} />}
            title={<div className={cx('memberName', { oneline: keyword && !groupUsers && !memberEmail })}>
              {<Tooltip
                title={memberName}
                placement="top"
                overlayClassName={cx('dk-ant-tooltip__reset', { hide: znStrlength(memberName) < 20 })} >
                <span className={cx('text')}>
                  <span>{
                    highLightKeyword(keyword, memberName)
                    }
                  </span>
                </span>
                {memberCount ? <span>
                  {
                    (() => {
                      switch (externalGroupType) {
                        case ADD_GROUP_TYPE.CUSTOM_GROUP: return ` · ${memberCount}`;
                        case ADD_GROUP_TYPE.COOPER_GROUP: return ` · ${memberCount}`;
                        case ADD_GROUP_TYPE.DEPARTMENT_GROUP: return '';
                        default: return ''
                      }
                    })()
                  }</span> : ''}
                  {/* A: 在职，I: 离职，其他：未知 */}
                    {hrStatus === 'I' && (
                  <span className={cx('name-label-dimissed')}>{intl.t('已离职')}</span>
                  )}
              </Tooltip>}
              {managerMemberPermis && externalGroupType === ADD_GROUP_TYPE.CUSTOM_GROUP && <Tooltip
                title={intl.t('编辑')}
                placement="top"
                overlayClassName="dk-ant-tooltip__reset">
                <i
                  className={cx('dk-iconfont', 'dk-icon-zhongmingming1', 'openCustomMember-icon')}
                  onClick={() => openCustomMember(id, memberName, roleKey)} />
              </Tooltip>}
            </div>}
            description={
              keyword === '' ? <div className={cx('memberEmail')}>{
                (() => {
                  switch (externalGroupType) {
                    case ADD_GROUP_TYPE.CUSTOM_GROUP: return intl.t('来自：自定义成员组');
                    case ADD_GROUP_TYPE.COOPER_GROUP: return intl.t('来自：Cooper团队');
                    case ADD_GROUP_TYPE.DEPARTMENT_GROUP: return intl.t('来自：组织架构');
                    default: return <MemberEmail
                      memberEmail={memberEmail}
                      maxlength={45}
                      customClass={cx('memberEmail')} />;
                  }
                })()
              }</div>
                : <MemberEmail
                    groupUsers={groupUsers}
                    keyword={keyword}
                    memberEmail={memberEmail}
                    maxlength={45}
                    customClass={cx('memberEmail')} />
            }
          />

          <div className={cx('identity', { space: hidePopover(roleKey) })}>
            <PowerPopover
              managerMemberPermis={managerMemberPermis}
              getDkMember={getDkMember}
              hidePopover={hidePopover}
              knowledgeId={knowledgeId}
              afterChangeRole={afterChangeRole}
              hasAdvancedPerm={hasAdvancedPerm}
              manageAdminPermis={manageAdminPermis}
              member={{ id, externalGroupId, roleKey, memberType, externalGroupType, roleNameZh, roleNameEN }}
              openDelModal={openDelModal} />
          </div>
        </List.Item>
      )}
    />
    <SpinRender loading={loading} />
    {showNoMoreTip && <div className={cx('noMore')}>{intl.t('没有更多了')}</div>}

    <TextOnlyModal
      handleOk={debounceDel}
      handleCancel={closeDelModal}
      modalVisible={delModalVisible}
      title={delModalTitle()}
      confirmLoading={confirmLoading}
      tip={delModalTip()}
    />
  </div>;
};

export default MemberList;

