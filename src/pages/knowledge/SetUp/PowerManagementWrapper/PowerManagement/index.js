import React, { useState, useMemo } from 'react';
import { intl } from 'di18n-react';

import { Tabs } from 'antd';
import classBind from 'classnames/bind';
import ReactDOM from 'react-dom';
import { singleSpaReact } from '@didi/mf-tenon';
import { debounce } from 'lodash-es';
import InfiniteScroll from 'react-infinite-scroller';
import {
  addDkMember,
  batchAddDkMember,
  getExternalGroupMember,
} from '@/service/knowledge/setup';
import AddMember from '@/components/serviceComponents/AddMember';
import {
  getKnowledgeMemberList,
  getKnowledgeGroupMemberList,
  getKnowledgeSingleMemberList,
} from '@/service/knowledge/knowledgeData';
import {
  DK_MEMBER_TYPE,
  ADD_MEMBER_TYPE,
  ADD_MEMBER_FROM_TYPE,
  POWER_MANAGERMENT_TABS,
  EpClickAddDkMemberBatchAddFns,
  POWER_MANAGERMENT_TABS_TYPE,
  GROUP_OMEGA_TYPE,
} from '@/constants/setup';
import usePermission from '@/hooks/usePermission';
import useNotification from '@/hooks/useNotification';
import useLoadMore from '@/hooks/useLoadMore';
import NotificationStatus from '@/constants/notification';
import AddMemberBtn from './addMemberBtn';
import CustomMember from './customMember';
import { getDkRole } from '@/utils';
import MemberTab from './memberTab';
import MemberList from './memberList';
import DepartmentMember from './departmentMember';
import * as styles from './style.module.less';
import { NoSearchResultIcon } from '@/assets/icon';
import * as addMemberBtnStyles from './addMemberBtn/style.module.less';

const cx = classBind.bind(styles);
const addMemberBtnCx = classBind.bind(addMemberBtnStyles);
const { TabPane } = Tabs;
let loadAll;
let refreshGroup;
let refreshSingleMember;

const PowerManagement = (wrapProps) => {
  const props = wrapProps.appProps ?? wrapProps;
  const { knowledgeId, perm, getDkMember, appId, originService } = props;
  window.appId = appId;
  window.originService = originService;
  const [keyword, setKeyword] = useState('');
  const [showCustomMember, setShowCustomMember] = useState(false);
  const [showDepartmentMember, setShowDepartmentMember] = useState(false);
  const { checkOperationPermission } = usePermission();
  const [addList, setAddList] = useState([]);
  const [customGroupInfo, setCustomGroupInfo] = useState({});
  const [tabActiveKey, setTabActiveKey] = useState(
    POWER_MANAGERMENT_TABS_TYPE.ALL,
  );
  const [openAddMemberModal, setOpenAddMemberModal] = useState();
  const notification = useNotification();
  const { loadMore, loading, hasMore, list } = useLoadMore(
    (args) => getKnowledgeMemberList(knowledgeId, args),
    null,
  );

  const managerMemberPermis = useMemo(() => {
    return checkOperationPermission('MANAGE_DK_MEMBER', perm);
  }, [perm]);
  const manageAdminPermis = useMemo(() => {
    return checkOperationPermission('MANAGE_DK_ADMIN', perm);
  }, [perm]);
  const hasAdvancedPerm = useMemo(() => {
    return checkOperationPermission('MANAGE_DEPAERMENT', perm);
  }, [perm]);

  const onInputChange = debounce((e) => {
    const key = e.target.value;
    setKeyword(key);
    // 清空搜索框时清除三个tab搜索函数，tabClick时不请求，依靠内部tab的useLoadMore来请求
    if (key === '') {
      loadAll = null;
      refreshGroup = null;
      refreshSingleMember = null;
    } else {
      loadMore({ keyword: key.trim() }, true);
    }
    window.__OmegaEvent('ep_dkpc_member_search_ck');
  }, 500);

  const afterAddMember = () => {
    if (keyword === '') {
      loadAll({}, true);
    } else {
      loadMore({ keyword }, true);
    }
    getDkMember && getDkMember();
  };

  const delMemberListOneItem = () => {
    getDkMember && getDkMember();
    refreshList();
  };

  const refreshList = () => {
    return loadMore({ keyword }, true);
  };

  const afterChangeRole = async (role) => {
    try {
      await loadMore({ keyword }, true, false);
      if (role === DK_MEMBER_TYPE.DK_ADMIN) {
        document.getElementById('dk-memberList-wrap').scrollTop = 0;
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const invite = async (currId, memberData) => {
    const invitees = memberData.map((item) => {
      const role = getDkRole(item.permis[0], DK_MEMBER_TYPE);
      return {
        id: item.id,
        inviteeType: item.inviteeType,
        role,
        name: item.title,
      };
    });
    return addDkMember(currId, { invitees, limitCount: 1000 }).then(
      afterAddMember,
    );
  };

  const batchAdd = (invitees, permission, checkInvitees) => {
    return batchAddDkMember({
      knowledgeId,
      invitees,
      role: getDkRole(permission, DK_MEMBER_TYPE),
      limitCount: 1000,
      checkInvitees,
    }).then((res) => {
      if (res.addStatus === 1) {
        afterAddMember();
        notification(NotificationStatus.SUCCESS, intl.t('添加成功'));
      }
      return res;
    });
  };
  const closeCustomMember = () => {
    setShowCustomMember(false);
    setAddList([]);
  };
  const openCustomMember = (id, name, roleKey) => {
    setShowCustomMember(true);
    if (id && name) {
      window.__OmegaEvent('ep_dkpc_member_editgroup_ck');
      setCustomGroupInfo({ id, name, roleKey });
    } else {
      window.__OmegaEvent('ep_dkpc_member_creategroup_ck');
      setCustomGroupInfo({});
    }
  };

  const getGroupMember = (currId, memberData) => {
    let tempList = [];
    const promiseList = [];
    memberData.forEach(({ title, mail, inviteeType, avatar, id }) => {
      if (inviteeType === 0) {
        tempList.push({
          memberName: title,
          email: mail,
          avatar,
          id,
          inviteeType,
        });
      } else {
        let params = {};
        if (inviteeType === ADD_MEMBER_TYPE.DEPARTMENT) {
          params = { groupName: title };
        }
        const p = getExternalGroupMember(id, inviteeType, params).then(
          (newList) => {
            const temp = newList.filter((item) => {
              return !tempList.find((member) => member.id === item.id);
            });
            tempList = tempList.concat(temp);
          },
        );
        promiseList.push(p);
      }
    });
    return Promise.all(promiseList)
      .then(() => {
        setAddList(tempList);
      })
      .catch(() => {
        notification(NotificationStatus.ERROR, intl.t('请少选择一些组'));
      });
  };
  const onTabClick = (key) => {
    setTabActiveKey(key);
    if (key === POWER_MANAGERMENT_TABS_TYPE.ALL) {
      loadAll && loadAll({}, true);
    } else if (key === POWER_MANAGERMENT_TABS_TYPE.MEMBER) {
      refreshSingleMember && refreshSingleMember();
    } else {
      refreshGroup && refreshGroup();
    }
  };
  const getRefreshGroup = (fn) => {
    refreshGroup = fn;
  };
  const getRefreshAll = (fn) => {
    loadAll = fn;
  };
  const getRefreshSingleMember = (fn) => {
    refreshSingleMember = fn;
  };
  const openDepartmentMember = () => {
    window.__OmegaEvent('ep_dkpc_member_invitegroup_ck');
    setShowDepartmentMember(true);
  };
  const closeDepartmentMember = () => {
    setShowDepartmentMember(false);
  };
  const getRefreshPowerList = () => {
    return keyword
      ? refreshList
      : tabActiveKey === POWER_MANAGERMENT_TABS_TYPE.ALL
        ? () => loadAll()
        : refreshGroup;
  };

  return (
    <div className={cx('powerManagement')}>
      <AddMember
        hasTip={false}
        addMemberPermission={managerMemberPermis}
        addManagerPermission={manageAdminPermis}
        currId={knowledgeId}
        permissionType={
          showCustomMember
            ? ADD_MEMBER_FROM_TYPE.Dk_Group
            : ADD_MEMBER_FROM_TYPE.DK
        }
        onInputChange={onInputChange}
        existMessage={
          showCustomMember
            ? intl.t('该用户已选择')
            : intl.t('该用户已是{slot0}成员', {
              slot0: getDkMember ? intl.t('该用户已是-知识库') : intl.t('该用户已是-知识门户'),
            })
        }
        placeholder={
          getDkMember ? intl.t('搜索当前知识库成员') : intl.t('搜索成员')
        }
        invite={showCustomMember ? getGroupMember : invite}
        batchAdd={showCustomMember ? undefined : batchAdd}
        getOpenAddMemberModalFn={setOpenAddMemberModal}
        EpClickBatchAddFns={EpClickAddDkMemberBatchAddFns}
        groupOmegaType={
          customGroupInfo.id ? GROUP_OMEGA_TYPE.EDIT : GROUP_OMEGA_TYPE.CREATE
        }
        inDk={!!getDkMember}
      >
        <div className={cx('addMemberBtns', { inGateway: !getDkMember })}>
          <AddMemberBtn
            openAddMemberModal={() => openAddMemberModal(ADD_MEMBER_FROM_TYPE.DK)
            }
          />
          <div
            className={addMemberBtnCx('addMemberBtn-inSetup')}
            onClick={openDepartmentMember}
          >
            <i
              className={addMemberBtnCx(
                'dk-iconfont',
                'dk-icon-tianjiachengyuan1',
                'addMemberBtn-inSetup-icon',
              )}
            />
            <span className={addMemberBtnCx('addMemberBtn-inSetup-text')}>
              {intl.t('添加成员组')}
            </span>
          </div>
          {getDkMember && (
            <div
              className={addMemberBtnCx('addMemberBtn-inSetup')}
              onClick={() => openCustomMember()}
            >
              <i
                className={addMemberBtnCx(
                  'dk-iconfont',
                  'dk-icon-zidingyizu',
                  'addMemberBtn-inSetup-icon',
                )}
              />
              <span className={addMemberBtnCx('addMemberBtn-inSetup-text')}>
                {intl.t('创建自定义成员组')}
              </span>
            </div>
          )}
        </div>
      </AddMember>
      {keyword ? (
        <div
          className={cx('memberList-wrap')}
          id="dk-memberList-wrap">
          <InfiniteScroll
            initialLoad={false}
            pageStart={0}
            loadMore={() => loadMore({ keyword })}
            hasMore={!loading && hasMore}
            useWindow={false}
          >
            <MemberList
              knowledgeId={knowledgeId}
              loading={loading}
              delMemberListOneItem={delMemberListOneItem}
              afterChangeRole={afterChangeRole}
              showNoMoreTip={!loading && !hasMore}
              memberList={list}
              keyword={keyword}
              managerMemberPermis={managerMemberPermis}
              manageAdminPermis={manageAdminPermis}
              hasAdvancedPerm={hasAdvancedPerm}
              openCustomMember={openCustomMember}
              getDkMember={getDkMember}
            />
          </InfiniteScroll>
        </div>
      ) : (
        <Tabs
          className={cx('powerManagement-tab')}
          activeKey={tabActiveKey}
          onTabClick={onTabClick}
        >
          {POWER_MANAGERMENT_TABS.map(({ key, value }) => {
            return (
              <TabPane
                className="powerManagement-tab"
                tab={ typeof value === 'function' ? value() : value }
                key={key}>
                <MemberTab
                  fn={
                    key === POWER_MANAGERMENT_TABS_TYPE.MEMBER
                      ? getKnowledgeSingleMemberList
                      : key === POWER_MANAGERMENT_TABS_TYPE.ALL
                        ? getKnowledgeMemberList
                        : getKnowledgeGroupMemberList
                  }
                  getDkMember={getDkMember}
                  TabType={key}
                  knowledgeId={knowledgeId}
                  managerMemberPermis={managerMemberPermis}
                  manageAdminPermis={manageAdminPermis}
                  hasAdvancedPerm={hasAdvancedPerm}
                  getRefreshAll={getRefreshAll}
                  getRefreshGroup={getRefreshGroup}
                  getRefreshSingleMember={getRefreshSingleMember}
                  openCustomMember={openCustomMember}
                />
              </TabPane>
            );
          })}
        </Tabs>
      )}
      {keyword && list.length === 0 && !loading && (
        <div className={cx('searchResult')}>
          <div className={cx('emptySearch')}>
            <img
              className={cx('emptySearch-icon')}
              src={NoSearchResultIcon} />
            <p className={cx('emptySearch-tip')}>
              {intl.t('未搜索到相关成员和团队，请更换关键词')}
            </p>
          </div>
        </div>
      )}
      {showCustomMember && (
        <CustomMember
          knowledgeId={knowledgeId}
          groupId={customGroupInfo.id}
          groupInitialName={customGroupInfo.name}
          roleKey={customGroupInfo.roleKey}
          refreshPowerList={getRefreshPowerList()}
          addList={addList}
          openAddMemberModal={openAddMemberModal}
          closeCustomMember={closeCustomMember}
        />
      )}

      {showDepartmentMember && (
        <DepartmentMember
          getDkMember={getDkMember}
          knowledgeId={knowledgeId}
          refreshPowerList={getRefreshPowerList()}
          closeDepartmentMember={closeDepartmentMember}
        />
      )}
    </div>
  );
};

const reactLifecycles = singleSpaReact({
  React,
  ReactDOM,
  rootComponent: PowerManagement,
});

export const { bootstrap } = reactLifecycles;
export const { mount } = reactLifecycles;
export const { unmount } = reactLifecycles;
export default PowerManagement;
