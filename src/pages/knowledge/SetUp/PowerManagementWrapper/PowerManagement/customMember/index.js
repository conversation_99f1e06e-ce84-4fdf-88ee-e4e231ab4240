import { intl } from 'di18n-react';
import { useEffect, useState } from 'react';
import { Tooltip, Modal } from 'antd';
import SpinRender from '@/components/SpinRender';
import classBind from 'classnames/bind';
import { cloneDeep } from 'lodash-es';
import {
  getGroupMember,
  addGroup,
  updateGroup,
  changeMemberRole,
} from '@/service/knowledge/setup';
import AddMemberBtn from '../addMemberBtn';
import {
  DK_MEMBER_TYPE,
  GROUP_OMEGA_TYPE,
  ADD_MEMBER_FROM_TYPE,
} from '@/constants/setup';
import MemberItem from '@/components/serviceComponents/MemberComponent/MemberItem';
import PermPopover from '@/components/serviceComponents/PermPopover';
import MemberFooter from '@/components/serviceComponents/MemberComponent/MemberFooter';
import { checkNameErrorMsg } from '@/utils';
import MemberInput from '@/components/serviceComponents/MemberComponent/MemberInput';
import TextOnlyModal from '@/components/TextOnlyModal';
import useNotification from '@/hooks/useNotification';
import NotificationStatus from '@/constants/notification';
import ModalTitleTip from '@/components/ModalTitleTip';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);
const CustomMember = ({
  groupId,
  groupInitialName = '',
  roleKey,
  knowledgeId,
  addList,
  openAddMemberModal,
  closeCustomMember,
  refreshPowerList,
}) => {
  const [memberList, setMemberList] = useState([]);
  const [choosenPerm, setChoosenPerm] = useState(
    roleKey || DK_MEMBER_TYPE.DK_MEMBER,
  );
  const [nameCheckMsg, setNameCheckMsg] = useState('');
  const [groupName, setGroupName] = useState(groupInitialName);
  const [showConfrimModal, setShowConfrimModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [removed, setRemoved] = useState(false);
  const [list, setList] = useState([]);
  const notification = useNotification();

  useEffect(async () => {
    if (groupId) {
      setLoading(true);
      getGroupMember(knowledgeId, { groupId })
        .then((temp) => {
          setMemberList(temp);
          setList(temp);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, []);

  useEffect(async () => {
    const temp = addList.filter((item) => {
      return !list.find((member) => member.id === item.id);
    });
    setList(list.concat(temp));
  }, [addList]);

  const onInputChange = (e) => {
    const name = e.target.value;
    setGroupName(name);
    setNameCheckMsg(checkNameErrorMsg(name));
  };
  const closeConfrimModal = () => {
    setShowConfrimModal(false);
  };
  const remove = (index) => {
    const tempList = cloneDeep(list);
    tempList.splice(index, 1);
    setRemoved(true);
    setList(tempList);
    window.__OmegaEvent('ep_dkpc_member_creategroup_delete_ck', '', {
      source: groupId ? GROUP_OMEGA_TYPE.EDIT : GROUP_OMEGA_TYPE.CREATE,
    });
  };

  const params = {
    role: choosenPerm,
    name: groupName,
  };

  const update = async () => {
    if (removed || addList.length > 0 || groupName !== groupInitialName) {
      await updateGroup(knowledgeId, groupId, {
        ...params,
        invitees: list
          .filter((item) => !memberList.find((member) => member.id === item.id))
          .map(({ id, inviteeType }) => {
            return { id, inviteeType };
          }),
        deletes: memberList
          .filter((item) => !list.find((member) => member.id === item.id))
          .map(({ id }) => {
            return { id, inviteeType: 0 };
          }),
      });
    }

    roleKey !== choosenPerm
      && (await changeMemberRole(knowledgeId, {
        id: groupId,
        role: choosenPerm,
        memberType: 'Group',
      }));
    refreshPowerList();
    closeCustomMember();
    notification(NotificationStatus.SUCCESS, intl.t('更新成功'));
    window.__OmegaEvent('ep_dkpc_member_editgroup_update_ck');
    window.__OmegaEvent(
      'ep_dkpc_member_creategroup_invite_grouporganization_ck',
      '',
      { source: GROUP_OMEGA_TYPE.EDIT },
    );
  };

  const add = async () => {
    try {
      await addGroup(knowledgeId, {
        ...params,
        invitees: list.map(({ id, inviteeType }) => {
          return { id, inviteeType };
        }),
      });
      closeCustomMember();
      refreshPowerList();
      notification(NotificationStatus.SUCCESS, intl.t('创建成功'));
      window.__OmegaEvent('ep_dkpc_member_creategroup_create_ck');
    } catch (err) {
      if (err.errorCode === 501200) {
        setNameCheckMsg(intl.t('知识库内已存在同名的成员组，请重新命名'));
      }
    }
    window.__OmegaEvent(
      'ep_dkpc_member_creategroup_invite_grouporganization_ck',
      '',
      { source: GROUP_OMEGA_TYPE.CREATE },
    );
  };
  const changePerm = (_, val) => {
    setChoosenPerm(val);
    window.__OmegaEvent('ep_dkpc_member_creategroup_changerole_ck', '', {
      source: groupId ? GROUP_OMEGA_TYPE.EDIT : GROUP_OMEGA_TYPE.CREATE,
    });
  };

  return (
    <Modal
      title={
        <>
          {intl.t('自定义成员组')}

          <ModalTitleTip
            title={intl.t('权限说明：')}
            desc={intl.t(
              '同时添加包含某个用户的多个成员组时，该用户取较高权限（成员>只读成员）',
            )}
          />
        </>
      }
      visible={true}
      closeIcon={<i className={cx('dk-iconfont', 'dk-icon-guanbi', 'close')} />}
      onCancel={() => setShowConfrimModal(true)}
      footer={null}
      width={640}
      wrapClassName={cx('knowledge_component_addMember', 'customMemberModal')}
    >
      <div className={cx('customMember')}>
        <div className={cx('header')}>
          <p className={cx('tip')}>
            {intl.t('成员组创建后将成员以组的形式添加到知识库，授予一致的角色')}
          </p>
          <div className={cx('input-title')}>{intl.t('名称')}</div>
          <MemberInput
            defaultValue={groupInitialName}
            onInputChange={onInputChange}
            placeholder={intl.t('请输入成员组名称')}
            customStyle={{ background: 'none', paddingLeft: '16px' }}
            error={nameCheckMsg !== ''}
          />

          <div className={cx('nameErrorMsg', 'nameCheckMsg')}>
            {nameCheckMsg}
          </div>
        </div>
        <div className={cx('memberList-wrap')}>
          <div className={cx('title')}>
            {intl.t('成员列表')}
            {!loading && <>（{list.length}）</>}
            <AddMemberBtn
              openAddMemberModal={() => openAddMemberModal(
                ADD_MEMBER_FROM_TYPE.Dk_Group,
                groupId ? GROUP_OMEGA_TYPE.EDIT : GROUP_OMEGA_TYPE.CREATE,
              )
              }
            />
          </div>
          <div className={cx('memberList')}>
            {list.map(({ memberName, email, avatar }, index) => {
              return (
                <MemberItem
                  name={memberName}
                  avatar={avatar}
                  email={email}>
                  <div className={cx('remove')}>
                    <Tooltip
                      title={intl.t('移除')}
                      placement="top"
                      overlayClassName={cx('dk-ant-tooltip__reset')}
                    >
                      <i
                        className={cx('dk-iconfont', 'dk-icon-guanbi', 'icon')}
                        onClick={() => remove(index)}
                      />
                    </Tooltip>
                  </div>
                </MemberItem>
              );
            })}

            <SpinRender loading={loading} />
          </div>
        </div>
        <MemberFooter
          okBtnDisabled={nameCheckMsg || groupName === ''}
          cancel={() => setShowConfrimModal(true)}
          ok={groupId ? update : add}
          okTxt={groupId ? intl.t('更新并通知') : intl.t('添加并通知')}
        >
          <div className={cx('choosePerm')}>
            <span className={cx('label')}>{intl.t('权限：')}</span>
            <PermPopover
              setPerm={changePerm}
              perm={choosenPerm} />
          </div>
        </MemberFooter>
        <TextOnlyModal
          handleOk={closeCustomMember}
          handleCancel={closeConfrimModal}
          modalVisible={showConfrimModal}
          title={intl.t('取消{slot0}自定义成员组', {
            slot0: groupId ? intl.t('取消-自定义成员组-编辑') : intl.t('取消-自定义成员组-创建'),
          })}
          tip={intl.t('确认退出当前界面吗？一旦退出，不会保存设置。')}
        />
      </div>
    </Modal>
  );
};
export default CustomMember;
