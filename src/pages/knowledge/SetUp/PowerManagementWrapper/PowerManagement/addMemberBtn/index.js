import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const AddMemberBtn = ({ openAddMemberModal }) => {
  return (
    <div
      className={cx('addMemberBtn-inSetup')}
      onClick={openAddMemberModal}>
      <i
        className={cx(
          'dk-iconfont',
          'dk-icon-tianjiachengyuan1',
          'addMemberBtn-inSetup-icon',
        )}
      />
      <span className={cx('addMemberBtn-inSetup-text')}>
        {intl.t('添加成员')}
      </span>
    </div>
  );
};

export default AddMemberBtn;
