import { intl } from 'di18n-react';
import { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { Avatar, Button, Form, Input } from 'antd';
import classBind from 'classnames/bind';
import GroupIconChoose from '@/components/serviceComponents/GroupIconChoose';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import usePermission from '@/hooks/usePermission';
import { updateknowledge } from '@/service/knowledge/setup';
import { checkNameErrorMsg } from '@/utils';
import * as styles from './style.module.less';

const { TextArea } = Input;
const cx = classBind.bind(styles);

const BasicSetup = (props) => {
  const { knowledgeId } = useParams();
  const { knowledgeDetail, permission } = useSelector(
    (state) => state.KnowledgeData,
  );
  const dispatch = useDispatch();
  const { checkOperationPermission } = usePermission();
  const [valueChange, setValueChange] = useState(false);
  const [chooseIcon, setChooseIcon] = useState('');
  const [spaceNameCheckMsg, setSpaceNameCheckMsg] = useState('');
  const [form] = Form.useForm();
  const notification = useNotification();
  const { perm } = permission;
  const { spaceName, exDesc, exPicture } = knowledgeDetail;
  const { teamId } = useParams();

  const editPermission = useMemo(() => {
    return checkOperationPermission('MANAGE_DK_SETTING', perm);
  }, [perm]);

  const {
    getFeatures,
  } = dispatch.TeamData;

  useEffect(() => {
    form.setFieldsValue({
      spaceName,
      exPicture,
      exDesc,
    });
    setChooseIcon(exPicture);
  }, [spaceName]);

  const updateBtnDisabled = !editPermission || !valueChange || spaceNameCheckMsg;

  const saveSetup = async () => {
    const metisSpaceName = form.getFieldValue('spaceName').trim();
    if (spaceNameCheckMsg.length > 0) {
      notification(NotificationStatus.ERROR, spaceNameCheckMsg);
      return;
    }
    window.__OmegaEvent('ep_dkpc_pagesetting_basicsetting_name_ck');
    await updateknowledge({
      metisSpaceId: knowledgeId,
      exDesc: form.getFieldValue('exDesc'),
      exPicture: chooseIcon,
      metisSpaceName,
    });
    notification(NotificationStatus.SUCCESS, intl.t('更新成功'));
    const { getKnowledgeDetail } = dispatch.KnowledgeData;
    getKnowledgeDetail(knowledgeId);
    if (teamId) {
      getFeatures(teamId);
    }
  };
  const handleValuesChange = () => {
    const name = form.getFieldValue('spaceName');
    let s = checkNameErrorMsg(name);
    setValueChange(true);
    setSpaceNameCheckMsg(s);
  };

  const getChooseIcon = (img) => {
    setValueChange(true);
    setChooseIcon(img);
  };
  const changeAvatar = () => {
    window.__OmegaEvent('ep_dkpc_pagesetting_basicsetting_portrait_ck');
  };

  return (
    <div className={cx('basicSetup')}>
      <div className={cx('mainTitle')}>
        {intl.t('名称')}
        <span className={cx('knowledgeId')}>(ID：{knowledgeId})</span>
      </div>
      <Form
        form={form}
        className={`${styles.form} dk-ant-form-item`}
        onValuesChange={handleValuesChange}
      >
        <div className={cx('nameItem')}>
          {editPermission ? (
            <GroupIconChoose getChooseIcon={getChooseIcon}>
              <Avatar
                onClick={changeAvatar}
                className={cx('coverIcon', 'canEdit')}
                src={chooseIcon}
              />
            </GroupIconChoose>
          ) : (
            <Avatar
              className={cx('coverIcon')}
              src={chooseIcon} />
          )}
          <Form.Item
            label=""
            name="spaceName"
            className={`${styles.nameInputWrap} knowLedge_InputInfo_nameInput`}
          >
            <Input
              disabled={!editPermission}
              maxLength={100}
              placeholder={intl.t('必填，上限100字')}
              className={cx({ nameInput: true, disabled: !editPermission })}
            />
          </Form.Item>
          <div className={cx('spaceNameCheckMsg', 'nameCheckMsg')}>
            {spaceNameCheckMsg}
          </div>
        </div>

        <div className={cx('introTitle')}>{intl.t('简介')}</div>
        <Form.Item
          label=""
          name="exDesc">
          <TextArea
            className={cx({ introText: true, disabled: !editPermission })}
            maxLength={1000}
            disabled={!editPermission}
            placeholder={intl.t('选填，上限1000字')}
          />
        </Form.Item>

        <Form.Item>
          <Button
            disabled={updateBtnDisabled}
            className={cx({ saveBtn: true, disabled: updateBtnDisabled })}
            onClick={saveSetup}
          >
            {intl.t('更新')}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default BasicSetup;
