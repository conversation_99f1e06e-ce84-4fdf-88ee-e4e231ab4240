// 认为所有有编辑器的页面都是由预览页衍生而来，均使用同一页面结构，共用样式文件
.page-detail {
  position: relative;
  height: 100%;
  background: #FFFFFF;
  line-height: 1;
  overflow-x: auto;
  min-width: 600px;

  .page-content {
    position: relative;
    height: calc(100% - 52px);
    padding-top: 0;
    transition: all 0.3s;

    // 兼容内容不满一屏的情况
    :global {
      .didoc-editor-app {
        .editor {
          min-height: calc(100vh - 328px);
        }
      }

      .full-screen {
        padding-bottom: 0;
        width: 100% !important;
      }
    }
  }

  .page-content-book {
    height: calc(100% - 60px);
  }

  .no-header {
    height: 100%;
    :global {
      .didoc-editor-app {
        .editor {
          min-height: calc(100vh - 197px);
        }
      }
    }
  }

  &-inphone {
    min-width: unset;
  }
}

.authority {
  height: 100%;
}


.no-permission-tip{
  font-size: 14px;
  font-weight: 500;
  line-height: 33px;
}
.fullComment-tip{
  border-radius: 35px;
  box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.1);
  background: rgb(255, 255, 255);
  border: 1px solid rgb(238, 238, 238);
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  z-index: 19;
  bottom: 35px;
  right: 20px;
  padding: 11px;
  cursor: pointer;
  .tip-img{
    width: 18px;
    flex: 0 0 18px;
    opacity: 0.8;
  }
}
.didoc-editor-app .editor .didoc-editor-container {
  padding: 0 56px 15px;
}
// 仅根据权限预览文档
.view-document-detail, .view-share-document-detail {
  :global {
    .view-document-detail-header {
      display: none;
    }
    .utils-drawer {
      display: none;
    }
    .wiki-editor-container-update-info {
      display: none;
    }
    // 隐藏前后插槽
    #didoc-editor-slot-sub-title {
      display: none;
    }
    #didoc-editor-slot-after {
      display: none;
    }
    .didoc-editor-container {
      padding: 0 8px 80px 8px !important;
    }
    .didoc-editor-content-container {
      padding-right: 0 !important;

      .code_block:first-child {
        margin-top: 15px;
      }

      .expand-container:first-child {
        margin-top: 15px;
      }

      .didoc-editor-file-containter:first-child {
        margin-top: 48px!important;
      }
    }
  }
}
