import { intl } from 'di18n-react';
import { useState, useContext, useMemo, useEffect } from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';
import { Dropdown, Menu, Tooltip, Switch, message } from 'antd';
import classnames from 'classnames/bind';
import { copyPage } from '@/service/knowledge/pageTree';
import * as service from '@/service/knowledge/page';
import HistoryVersionModal from '@/components/serviceComponents/HistoryVersionModal';
import SaveAsTemplateModal from './SaveAsTemplateModal';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import DocMetaInfo from './DocMetaInfo';
import FileMetaInfo from '@/components/serviceComponents/FileMetaInfo';
import WikiMetaInfo from '@/components/serviceComponents/WikiMetaInfo';
import { isDkPage, isPreviewFile, openNewWindow } from '@/utils';
import { getQuickStatus } from '@/service/cooper/quick';
import { starAction } from '@/service/knowledge/star';

import {
  downloadFileWordPDF,
  getPagePermission,
} from '@/service/knowledge/page';
import usePermission from '@/hooks/usePermission';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import { debounce } from 'lodash-es';
import { gainSetting } from '@/service/knowledge/setup';
import AddQuick from '@/components/CooperOperation/AddQuick';
import RemoveQuick from '@/components/CooperOperation/RemoveQuick';
import PageStyle from '@/components/PageStyle';
import { getIsWikiHTML } from '@/constants/wikihtmlrenderHelper';

import * as styles from './style.module.less';
import SendToIportalModal from '@/components/SendToIportalModal';
import VisitHistoryModal from './VisitHistory';

const cls = classnames.bind(styles);

let reqEd = false;
function DropdownMenu(props) {
  const {
    toggleSaveAsTemplateModalVisible,
    docInfo,
    toggleHistoryVersionVisible,
    writerVisible,
    toggleWriterVisible,
    setPageStyle,
    view,
  } = props;

  const {
    multiPath,
    docType,
    permission,
    latestVersion,
    type,
    pageStyle = {},
    markedStar
  } = docInfo;
  const isPreview = isPreviewFile(docType);
  const isWikiHTML = getIsWikiHTML(docInfo);
  const isDk = isDkPage(docType);
  const dispatch = useDispatch();

  const [signOffConfig, setSignOffConfig] = useState();
  const [quickObj, setQuickObj] = useState({});
  const [viewMeta, setViewMeta] = useState({});
  const [visitShow, setVisitShow] = useState(false);
  const [iportalModalVisible, setIportalModalVisible] = useState(false);
  const { knowledgeId, pageId, pageInfo, visitPerms } = useContext(LayoutContext);
  const { checkOperationPermission } = usePermission();
  const notification = useNotification();
  const saveAsTemplate = () => {
    window.__OmegaEvent('ep_dkpc_pagedetail_more_savetemplate_ck');
    toggleSaveAsTemplateModalVisible();
  };
  const { changeStar } = dispatch.pageDetail;
  const { changeSharStar } = dispatch.SharePage;
  const { tree } = useSelector((state) => state.PageTree);

  const prePageStyle = useMemo(() => {
    return {
      fontSize: pageStyle?.preFontSize,
      screen: pageStyle?.preScreen,
    };
  }, [pageStyle]);

  const managerMemberPermis = useMemo(() => {
    return checkOperationPermission('MANAGE_PAGE_MEMBER', permission);
  }, [permission]);

  const onClickMenu = ({ key }) => {
    const menuMap = {
      goToHistoryVersion,
      saveAsTemplate,
      goToapprovalProgress,
      copy,
      sendToIportal,
      word: down,
      pdf: down,
      quickVisit: () => {
        if (!quickObj.quickId) {
          AddQuick({
            objectType: type,
            objectId: pageId,
            sourceAppId: 4,
            sourceId: knowledgeId,
            doneCallback: () => {},
          });
        } else {
          RemoveQuick({
            id: quickObj.quickId,
            doneCallback: () => {},
          });
        }
      },
      star: async () => {
        const markType = markedStar ? 0 : 1;
        const listInfo = {
          resourceId: pageId,
          starType: 'RESOURCE', // 收藏类型  RESOURCE普通页面及资源; PAGE_SHARE页面分享; DK_SHARE知识库分享【必填】
          starInfo: '',
          appId: 4,
        };
        let data = await starAction(markType, [listInfo]);
        if (data) {
          message.success(markType === 1 ? intl.t('收藏成功') : intl.t('已取消收藏'));
          changeStar(!markedStar);
          try {
            tree.updateNode(pageId, { markedStar: !markedStar });
          } catch (error) {}
        }
      },
      gotoVisitHistory
    };
    menuMap[key](key);
  };

  const goToapprovalProgress = () => {
    service
      .getpageApprovalProgressUrl({
        resourceId: pageId,
      })
      .then((res) => {
        let { code, message } = res;
        if (code == '2000') {
          openNewWindow(message);
        } else {
          notification(NotificationStatus.WARN, message);
        }
      });
  };

  const gotoVisitHistory = () => {
    setVisitShow(true);
    window.__OmegaEvent('ep_detail_visit_ck');
  }

  const copy = () => {
    let toParentPageId = multiPath.length > 1 ? multiPath[multiPath.length - 2]?.id || '0' : '0';
    message.loading({
      content: intl.t('创建中...'),
      key: pageId,
      duration: 10,
    });
    copyPage({
      pageIds: [pageId],
      toParentPageId,
      knowledgeId,
      pageStatus: 1,
    }).catch((error) => {
      message.error({
        content: error.message || intl.t('副本创建失败，您没有权限'),
        key: pageId,
        duration: 2,
      });
    });
  };

  const checkEmpty = () => {
    const ele = view.dom;
    if (!ele) return;
    return [...ele.children].every((child) => {
      // 判断是否空段落
      if ((child.tagName === 'P')) {
        return (child.innerHTML === '<br>' || !child.innerHTML.trim())
      }
      // 判断是否空标题
      if (child.hasAttribute('heading-key')) {
        const heading = child.querySelector('h1, h2, h3, h4');
        return (heading.innerHTML === '<br>' || !child.innerHTML.trim())
      }
      // 包含其它内容一律判定非空
      return false;
    })
  };

  const sendToIportal = () => {
    const textLength = view.state.doc.textContent.length;
    const { title, content } = view.template.exportToIportal();
    if (!title) {
      return message.error(intl.t('标题不能为空'));
    }
    const isEmpty = checkEmpty();
    if (isEmpty) {
      return message.error(intl.t('内容不能为空'));
    }
    if (textLength > 70000) {
      return message.error(intl.t('单篇文章字数限制7万'));
    }
    setViewMeta({title, richText: content, textLength});
    setIportalModalVisible(true);
  }

  const getQuickStatusReq = async () => {
    let res = await getQuickStatus({
      objectType: type || pageInfo.type,
      objectId: pageId,
    });
    setQuickObj(res);
  };

  const down = debounce(async (type = 'word') => {
    window.__OmegaEvent('ep_dkpc_page_download_ck', '', { type });
    const { downloadTask = {}} = window;
    // if (!window.navigator.onLine) {
    //   notification(
    //     NotificationStatus.WARN,
    //     intl.t('当前网络已断开，请联网后重试')
    //   );
    //   return;
    // }
    if (downloadTask.pageId) {
      notification(
        NotificationStatus.WARN,
        intl.t('正在导出中，请等待导出完成后再操作'),
      );
      return;
    }
    notification(
      NotificationStatus.LOADING,
      intl.t('导出中...'),
      () => {},
      0,
      String(pageId),
    );
    try {
      const res = await downloadFileWordPDF(pageId, type);
      if (!res.taskId) {
        notification(
          NotificationStatus.ERROR,
          intl.t('导出失败'),
          () => {},
          2,
          String(pageId),
        );
        return;
      }
      window.downloadTask = { taskId: res.taskId, pageId };
      let timer = setTimeout(() => {
        const downTaskObj = window.downloadTask || {};
        if (downTaskObj.taskId === res?.taskId) {
          notification(
            NotificationStatus.ERROR,
            intl.t('导出任务超时'),
            () => {},
            2,
            String(downTaskObj.pageId),
          );
          window.downloadTask = {};
        }
        clearTimeout(timer);
      }, 1000 * 60);
    } catch (error) {
      const { errorCode = 0, errorMessage = '' } = error;
      window.downloadTask = {};
      const msg = errorCode === 100001 ? errorMessage : intl.t('导出失败');
      notification(NotificationStatus.ERROR, msg, () => {}, 2, String(pageId));
    }
  }, 500);

  const goToHistoryVersion = () => {
    window.__OmegaEvent('ep_dkpc_pagedetail_more_historyversion_ck');
    toggleHistoryVersionVisible();
  };

  const downloadPerm = useMemo(() => {
    return checkOperationPermission('MANAGE_PAGE_CONTEXT', permission);
  }, [permission]);

  const onVisibleChange = async (dropMenuVisible) => {
    if (dropMenuVisible && !reqEd) {
      reqEd = true;
    }
    if (dropMenuVisible) {
      getQuickStatusReq();
    }
  };

  const changePageStyle = ({ fontSize, screen }) => {
    const params = {};
    if (fontSize !== undefined) {
      params.preFontSize = fontSize;
    }
    if (screen !== undefined) {
      params.preScreen = screen;
    }
    setPageStyle(params);
  };

  useEffect(() => {
    gainSetting({ knowledgeId }).then((res) => {
      setSignOffConfig(res?.switchSetting?.signOffConfig);
    });
    getQuickStatusReq();
  }, [knowledgeId]);

  const canSetPageStyle = useMemo(() => {
    return !getIsWikiHTML(docInfo);
  }, [permission]);

  const canSaveAsTemplate = useMemo(() => {
    return !getIsWikiHTML(docInfo) && visitPerms && visitPerms.length === 0;
  }, [permission, visitPerms]);

  const canDownload = useMemo(() => {
    return !getIsWikiHTML(docInfo);
  }, [permission]);

  const canCopy = useMemo(() => {
    return visitPerms && visitPerms.length === 0;
  }, [permission, visitPerms]);

  const getMetaInfoView = () => {
    if (isPreview) return <FileMetaInfo />
    if (getIsWikiHTML(docInfo)) return <WikiMetaInfo />
    return <DocMetaInfo />
  }

  const NormalMenu = (
    <Menu onClick={onClickMenu}>
      {
        canSetPageStyle && <PageStyle
          title={intl.t('阅读样式')}
          summary={intl.t('正文字号调整、全宽模式')}
          tips={intl.t('该样式仅供阅读，不影响页面设置，刷新后还原。')}
          pageStyle={prePageStyle}
          changePageStyle={changePageStyle}
        />
      }
      <Menu.Item key="goToHistoryVersion">
        <span className={cls('align-menu')}>
          <i className={cls('dk-iconfont', 'dk-icon-lishifabubanben')} />
          <span>{intl.t('查看已发布版本')}</span>
        </span>
      </Menu.Item>
      {
        managerMemberPermis && (
          <Menu.Item key="gotoVisitHistory">
            <span className={cls('align-menu')}>
              <i className={cls('dk-iconfont', 'dk-icon-fangwenjilu')} />
              <span>{intl.t('查看访问记录')}</span>
            </span>
          </Menu.Item>
        )
      }
      {signOffConfig == 1 && (
        <Menu.Item key="goToapprovalProgress">
          <span className={cls('align-menu')}>
            <i className={cls('dk-iconfont', 'dk-icon-lingcunweimoban')} />
            <span>{intl.t('查看签批进度')}</span>
          </span>
        </Menu.Item>
      )}

      {!isPreview && canSaveAsTemplate && (
        <Menu.Item key="saveAsTemplate">
          <span className={cls('align-menu')}>
            <i className={cls('dk-iconfont', 'dk-icon-lingcunweimoban')} />
            <span>{intl.t('另存为模板')}</span>
          </span>
        </Menu.Item>
      )}
      {
        canCopy && <Menu.Item key="copy">
          <span className={cls('align-menu')}>
            <i
              className={`${cls('dk-icon-fuzhi', 'dk-iconfont', 'icon')}`}
              style={{ marginLeft: '1px', fontSize: '14px', textIndent: 0 }}
            />
            {intl.t('创建副本')}
          </span>
        </Menu.Item>
      }
      {
        downloadPerm && (isDk && !isWikiHTML) && (
          <Menu.Item key="sendToIportal">
            <div className={cls('align-menu')}>
              <i className={`${cls('dk-icon-juzidui-01', 'dk-iconfont', 'icon')}`}/>
              {intl.t('发布至桔子堆')}
            </div>
            <div className={cls('dk-dropdown-menu-tip')}>
              {intl.t('将当前发布版本内容发布至桔子堆')}
            </div>
          </Menu.Item>
        )
      }
      {/* <Menu.Item key="quickVisit">
       <span>
         <i
           className={cls(
             `${quickObj.quickId
               ? 'dk-icon-quxiaokuaisufangwen'
               : 'dk-icon-kuaisu4'
             }`,
             'icon',
             'dk-iconfont',
           )}
           style={{ marginLeft: '0px', fontSize: '16px' }}
         />
          {quickObj.quickId
           ? intl.t('从"快速访问"移出')
           : intl.t('添加至"快速访问"')}
       </span>
      </Menu.Item> */}

      {((downloadPerm && !isPreview && canDownload) || visitPerms?.includes('DOWNLOAD')) && (
        <Menu.Item key="word">
          <span className={cls('align-menu')}>
            <i
              className={`${cls('dk-icon-xiazai1', 'dk-iconfont', 'icon')}`}
              style={{ marginLeft: '0px', fontSize: '16px' }}
            />
            {intl.t('下载为Word')}
          </span>
        </Menu.Item>
      )}

      <Menu.Item key='star'>
        <span className={cls('align-menu')}>
          <i
            className={cls({
              'dk-iconfont': true,
              'dk-icon-quxiaoshoucang-01': markedStar,
              'star-button': true,
              'dk-icon-shoucang4px': !markedStar,
              hasStar: markedStar,
            })}
          />
          { markedStar ? intl.t('取消收藏') : intl.t('收藏') }
        </span>
      </Menu.Item>

      <Menu.Item
        key="metaInfo"
        className={cls('readonly-menu-item', 'no-pointer-events')}
        disabled
      >
        { getMetaInfoView() }
      </Menu.Item>
    </Menu>
  );

  return (
    <div className={cls('dropdown-menu')}>
      <Dropdown
        trigger={['click']}
        overlay={NormalMenu}
        overlayClassName={cls('menu-container')}
        destroyPopupOnHide={true}
        onVisibleChange={onVisibleChange}
      >
        <Tooltip
          title={intl.t('更多')}
          placement="bottom">
          <span className={cls('more-trigger')}>
            <i
              className={cls('dk-iconfont', 'dk-icon-gengduo2', 'more-button')}
            />
          </span>
        </Tooltip>
      </Dropdown>
      <SaveAsTemplateModal />
      <HistoryVersionModal isEdit={true} />
      { iportalModalVisible && <SendToIportalModal {...viewMeta} closeModal={setIportalModalVisible}/> }
      {
        visitShow && (
          <VisitHistoryModal 
            show={visitShow}
            onClose={() => setVisitShow(false)}
          />
        )
      }
    </div>
  );
}

function mapStateToProps({ pageDetail, KnowledgeData }) {
  const { docInfo, writerVisible, view } = pageDetail;
  const { perm } = KnowledgeData?.permission ?? {};
  return {
    docInfo,
    writerVisible,
    dkPermission: perm,
    view,
  };
}

function mapDispatchToProps({ pageDetail, historyVersion }) {
  const {
    toggleSaveAsTemplateModalVisible,
    toggleWriterVisible,
    setPageStyle,
  } = pageDetail;
  const { toggleHistoryVersionVisible } = historyVersion;
  return {
    toggleSaveAsTemplateModalVisible,
    toggleHistoryVersionVisible,
    toggleWriterVisible,
    setPageStyle,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(DropdownMenu);
