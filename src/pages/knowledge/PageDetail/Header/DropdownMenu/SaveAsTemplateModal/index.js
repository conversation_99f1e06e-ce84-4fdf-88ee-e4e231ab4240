import { intl } from 'di18n-react';
import { useRef, useState, useEffect, useContext } from 'react';
import { connect } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { Form, Modal, Input } from 'antd';
import classnames from 'classnames/bind';
import useNotification from '@/hooks/useNotification';
import NotificationStatus from '@/constants/notification';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { editorScreenshot, checkNameErrorMsg } from '@/utils';
import styles from './style.module.less';

const cls = classnames.bind(styles);

const MaxLength = 200;
function SaveTemplateModal(props) {
  const formRef = useRef();
  const {
    view,
    saveAsTemplateModalVisible,
    toggleSaveAsTemplateModalVisible,
    createTemplate,
  } = props;
  const [requesting, setRequesting] = useState(false);
  const [title, setTitle] = useState('');
  const navigate = useNavigate();
  const { knowledgeId } = useContext(LayoutContext);
  const [nameCheckMsg, setNameCheckMsg] = useState('');
  const { teamId } = useParams();

  const viewDetail = (templateId) => {
    navigate(
      teamId
        ? `/team-file/${teamId}/knowledge/${knowledgeId}/setUp?key=2&tpl=${templateId}`
        : `/knowledge/${knowledgeId}/setUp?key=2&tpl=${templateId}`,
    );
  };
  const notification = useNotification();
  const saveAsTemplate = async (name) => {
    const content = JSON.stringify(view.template.export());
    const thumbnail = await editorScreenshot();
    const formData = new FormData();
    formData.append('name', name);
    formData.append('content', content);
    formData.append('thumbnail', thumbnail, 'thumbnail.png');
    return createTemplate({ knowledgeId, formData });
  };

  const handleOk = () => {
    setRequesting(true);
    formRef.current
      .validateFields()
      .then((res) => {
        return saveAsTemplate(res.title)
          .then((data) => {
            notification(
              NotificationStatus.SUCCESS,
              <span className={cls('success-tip')}>
                {intl.t('模板保存成功，')}

                <span
                  className={cls('link')}
                  onClick={() => viewDetail(data)}>
                  {intl.t('点击查看')}
                </span>
              </span>,
            );
            toggleSaveAsTemplateModalVisible();
          })
          .finally(() => {
            setRequesting(false);
          });
      })
      .catch();
  };
  const handleCancel = () => {
    setNameCheckMsg('');
    toggleSaveAsTemplateModalVisible();
  };

  const onValuesChange = (changedValues) => {
    setNameCheckMsg(checkNameErrorMsg(changedValues.title));
    setTitle(changedValues.title);
  };

  useEffect(() => {
    if (view?.template && formRef.current) {
      const templateExport = view.template.export();
      setTitle(templateExport.title);
      formRef.current.setFieldsValue({
        title: templateExport.title,
      });
    }
  }, [saveAsTemplateModalVisible]);

  return (
    <Modal
      width={480}
      wrapClassName={cls('save-as-template-modal')}
      visible={saveAsTemplateModalVisible}
      okText={intl.t('确定')}
      cancelText={intl.t('取消')}
      onOk={handleOk}
      okButtonProps={{ loading: requesting, disabled: nameCheckMsg !== '' }}
      onCancel={handleCancel}
      title={intl.t('另存为模板')}
      closeIcon={
        <i className={cls('dk-iconfont', 'dk-icon-guanbi', 'close-icon')} />
      }
      centered={true}
    >
      <div className={cls('container')}>
        <Form
          ref={formRef}
          className={cls('form')}
          initialValues={{ title }}
          onValuesChange={onValuesChange}
        >
          <Form.Item
            className={cls('form-item')}
            name="title"
            rules={[
              {
                required: true,
                message: intl.t('名称不能为空'),
              },
            ]}
          >
            <Input
              placeholder={intl.t('请输入模板名称')}
              className={cls('input')}
              maxLength={MaxLength}
              autoFocus
            />
          </Form.Item>
        </Form>
        <div className={cls('max-tip')}>{nameCheckMsg}</div>
      </div>
    </Modal>
  );
}

function mapStateToProps({ pageDetail }) {
  const { view, saveAsTemplateModalVisible } = pageDetail;
  return {
    view,
    saveAsTemplateModalVisible,
  };
}

function mapDispatchToProps({ pageDetail, editTemplate }) {
  const { toggleSaveAsTemplateModalVisible } = pageDetail;
  const { createTemplate } = editTemplate;
  return {
    toggleSaveAsTemplateModalVisible,
    createTemplate,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(SaveTemplateModal);
