.dropdown-menu {
  margin-left: 16px;

  .more-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background: #ECEDF0;
    }

    .more-button {
      font-size: 18px;
      color: @blueGray-15;
    }
  }
}

.menu-container {
  width: 252px;

  .toggle-switch {
    float: right;
    box-shadow: none;
    transform: scale(0.8);

    :global {
      .ant-click-animating-node {
        display: none;
      }
    }
  }

  :global {
    .ant-dropdown-menu {
      border-radius: 4px;
      padding: 7px;
    }

    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
      color: @text-color;
      font-size: 14px;
      line-height: 20px;
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid transparent;

      &:hover {
        background: #F4F4F4;
        border: 1px solid #F1f2f3;
      }

      .align-menu {
        display: flex;
        align-items: center;
      }

      &.readonly-menu-item {
        cursor: pointer;

        &.no-pointer-events {
          pointer-events: none;
        }

        .show-writer-text {
          color: @text-color;
        }

        .show-writer-container {
          &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
          }
        }
      }

      .dk-iconfont {
        display: inline-block;
        width: 16px;
        height: 16px;
        font-size: 16px;
        color: #505050;
        margin-right: 10px;
      }
    }
  }

  .dk-dropdown-menu-icon {
    margin: -4px 12px 0 2px;
  }

  .dk-dropdown-menu-tip {
    color: #94979B;
    font-size: 12px;
    margin: 2px 0 0 26px;
  }
}