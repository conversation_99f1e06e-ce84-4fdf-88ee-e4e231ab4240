import React from 'react';
import { getLocale, intl } from 'di18n-react';
import { highlight, formatRecentTime } from '@/utils';
import classBind from 'classnames/bind';

import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const Tag = ({ content, className = '' }) => {
  return (
    <span className={cx('tag', className)}>
      <span className={cx('word')}>{intl.t(content)}</span>
    </span>
  );
};
        
const MemberItem = ({
  memberInfo,
}) => {
  const {
    id,
    userInfo : {
      avatar,
      userCn,
      user_cn,
      userEn,
      user_en,
      email,
    },
    visitTime
  } = memberInfo;

  return (
    <div key={id} className={cx('member-list')}>
      <img className={cx('pic')} src={avatar} />
      <div className={cx('info')}>
        <div className={cx('nameArea')}>
          <div className={cx('name')}>
            {highlight(getLocale() === 'zh-CN' ? (userCn || user_cn) : (userEn || user_en))}
          </div>
          {/* <Tag content={intl.t('外部')} className={cx('out-yellow')} /> */}
        </div>
        <p className={cx('mail')} >{ highlight(email) }</p>
      </div>
      <div className={cx('time')}>
        { formatRecentTime(visitTime) }
      </div>
    </div>
  );
};

export default MemberItem;
