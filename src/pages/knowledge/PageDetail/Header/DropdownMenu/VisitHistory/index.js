import { useContext } from 'react';
import { Modal, Spin } from 'antd';
import { intl } from 'di18n-react';
import InfiniteScroll from 'react-infinite-scroller';
import classNames from 'classnames/bind';
import EmptyInfo from '@/components/CooperFoldAuth/emptyInfo';
import useLoadMore from '@/components/serviceComponents/MemberListOfDK/useLoadMore';
import MemberList from './MemberList';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { getVisitHistory } from '@/service/knowledge/page';

import * as styles from './style.module.less';

const cx = classNames.bind(styles);

const VisitHistoryModal = (props) => {
  const { show, onClose } = props;
  const { pageId } = useContext(LayoutContext);

  const {
    loadMore: loadMoreCurrList,
    loading: loadingMoreCurr,
    list: currUserList,
    hasMore: hasMoreCurrList,
    totalCount,
  } = useLoadMore((args) => {
    return getVisitHistory(pageId, { ...args });
  }, true);

  return (
    <Modal
      width={540}
      centered={true}
      destroyOnClose={true}
      className={cx('modal-setting')}
      title={intl.t('访问记录')}
      visible={show}
      footer={null}
      closeIcon={
        <i
          className={cx(
            'dk-iconfont',
            'dk-icon-guanbi',
            'modal-setting-close',
          )}
          onClick={onClose}
        />
      }
    >
      <div className={cx('list-wrap-content')}>
        {
          currUserList && currUserList.length > 0 ? (
            <InfiniteScroll
              initialLoad={false}
              pageStart={0}
              loadMore={() => loadMoreCurrList()}
              hasMore={!loadingMoreCurr && hasMoreCurrList}
              useWindow={false}
              getScrollParent={() => document.querySelector('.list-wrap-content')}
            >
              <MemberList
                list={currUserList}
                refresh={() => {
                  loadMoreCurrList({ resourceId: pageId }, true);
                }}
              />
            </InfiniteScroll>
          ) : loadingMoreCurr ? (
            <Spin loading={loadingMoreCurr} className={cx('list-wrap-loading')} />
          ) : (
            <EmptyInfo desc={intl.t('暂无访问记录')} />
          )
        }
      </div>
    </Modal>
  )
}

export default VisitHistoryModal;
