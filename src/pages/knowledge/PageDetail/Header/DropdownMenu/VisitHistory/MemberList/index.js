import React from 'react';
import classBind from 'classnames/bind';
import MemberItem from '../MemberItem';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const MemberList = ({ list, refresh }) => {
  return (
    <div className={cx('memberList')}>
      {
        list && list.map((item, index) => {
          return (
            <div
              className={cx('memberItem')}
              key={index}
            >
              <MemberItem
                memberInfo={item}
                refresh={refresh}
              />
            </div>
          );
        })
      }
    </div>
  );
};

export default MemberList;
