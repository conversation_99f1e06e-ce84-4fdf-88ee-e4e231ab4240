import { useMemo } from 'react';
import { connect } from 'react-redux';
import classNames from 'classnames/bind';
import BreadCrumb from '@/components/BreadCrumb';
import { TREE_VIEW_TYPE } from '@/constants';
import TagBreadCrumb from './TagBreadCrumb';
import * as styles from './style.module.less';

const cls = classNames.bind(styles);

function Navigation(props) {
  const { docInfo, treeViewType } = props;

  const { multiPath = [], knowledgeId } = docInfo;
  const routes = useMemo(() => {
    return multiPath.map((item) => ({
      path: `/knowledge/${knowledgeId}/${item.id}`,
      breadcrumbName: item.name,
    }));
  }, [multiPath]);
  const isTagView = useMemo(() => {
    return treeViewType === TREE_VIEW_TYPE.TAG;
  }, [treeViewType]);

  return (
    <div className={cls('navigation')}>
      { isTagView ? (
        <TagBreadCrumb/>
      ) : (
        <BreadCrumb routes={routes}/>
      ) }
    </div>
  );
}

function mapStateToProps({ pageDetail, KnowledgeData }) {
  const { docInfo } = pageDetail;
  const { treeViewType } = KnowledgeData;
  return {
    docInfo,
    treeViewType,
  };
}

export default connect(mapStateToProps)(Navigation);
