import { connect } from 'react-redux';
import classNames from 'classnames/bind';
import * as styles from '../style.module.less';

const cls = classNames.bind(styles);

function TagBreadCrumb(props) {
  const { docInfo, tag } = props;

  return (
    <div className={cls('tag-view')}>
      {
        // tag.name && (
        //   <>
        //     <span className={cls('tag')}>
        //       { tag.name }
        //     </span>
        //     <span className={cls('divider')}>/</span>
        //   </>
        // )
      }
      <span className={cls('title')}>{ docInfo.pageName }</span>
    </div>
  );
}

function mapStateToProps({ pageDetail, TagTree }) {
  const { docInfo } = pageDetail;
  const { choosePageMsg, tagData } = TagTree;
  const tag = tagData.find((item) => item.id === choosePageMsg.parentTagId) || {};
  return {
    docInfo,
    tag,
  };
}

export default connect(mapStateToProps)(TagBreadCrumb);
