import ConvertWikiToDidocModal from '@/components/common/ConvertWikiToDidoc';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import ShareModal from '@/components/serviceComponents/ShareModal';
import { SHARE_TYPE } from '@/constants';
import { getIsWikiHTML } from '@/constants/wikihtmlrenderHelper';
import usePermission from '@/hooks/usePermission';
import { getDidocDetail } from '@/service/knowledge/didoc';
import { convertWikiToDidoc } from '@/service/knowledge/page';
import { isDkSheet, isPreviewFile } from '@/utils';
import { Button, Tooltip } from 'antd';
import classNames from 'classnames/bind';
import { intl } from 'di18n-react';
import { useContext, useEffect, useState } from 'react';
import { connect, useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import * as styles from './style.module.less';

const cls = classNames.bind(styles);

function Options(props) {
  const { docInfo, changeInitLoading } = props;
  const { guid, permission, latestVersion, docType, externalSystemType } = docInfo;
  const isPreview = isPreviewFile(docType);
  const isSheet = isDkSheet(docType);
  const navigate = useNavigate();
  const { knowledgeId, pageId } = useContext(LayoutContext);
  const [tooltipHidden, setTooltipHidden] = useState(true);
  const [hasManagePermission, setHasManagePermission] = useState(false);
  const { checkOperationPermission } = usePermission();
  const { teamId } = useParams();
  const dispatch = useDispatch();
  const [showConvert, setShowConvert] = useState(false);
  const [convertError, setConvertError] = useState(false);

  const [hasSharePerm, setHasSharePerm] = useState(true);

  useEffect(() => {
    if (permission) {
      setHasManagePermission(
        checkOperationPermission('MANAGE_PAGE_CONTEXT', permission),
      );
      setHasSharePerm(
        checkOperationPermission('MANAGE_PAGE_SETTING_LIMIT', permission),
      );
    }
  }, [permission]);

  const editNavigate = () => {
    changeInitLoading(true);
    navigate(
      teamId
        ? `/team-file/${teamId}/knowledge/${knowledgeId}/${pageId}/edit`
        : `/knowledge/${knowledgeId}/${pageId}/edit`,
    );
  }

  const handleEdit = async () => {
    window.__OmegaEvent('ep_dkpc_pagedetail_edit_ck');
    // window.performance.mark(`editPage-start${pageId}`);

    // 如果是wiki html，需要先调用转换接口转换为didoc
    if (getIsWikiHTML(docInfo)) {
      setConvertError(false);
      setShowConvert(true);
    } else {
      editNavigate();
    }
  };

  const convertNext = async () => {
    try {
      await convertWikiToDidoc({
        pageId,
        guid,
        exteralSystemType: externalSystemType || 'html',
        toExteralSystemType: 'didoc',
      });
      // 更新之后重新请求获取Didoc文档ID
      const { getPageDetail } = dispatch.pageDetail;
      await getPageDetail({ pageId });
      // 关闭弹出层
      setShowConvert(false);
      editNavigate();
    } catch (error) {
      setConvertError(true);
    }
  }

  const onVisibleChange = async (value) => {
    if (value) {
      if (isPreview) {
        setTooltipHidden(latestVersion === guid);
      } else if (isSheet) {
        setTooltipHidden(true);
      } else if (!getIsWikiHTML(docInfo)) {
        const { version } = await getDidocDetail(guid);
        setTooltipHidden(Number(version) === Number(latestVersion));
      }
    }
  };

  return (
    <div className={cls('options')}>
      {hasManagePermission && (
        <Tooltip
          title={intl.t('有未发布内容')}
          placement="bottom"
          overlayClassName={cls({ 'hide-tooltip': tooltipHidden })}
          onVisibleChange={onVisibleChange}
        >
          <Button
            className={cls('edit-btn')}
            onClick={handleEdit}
          >
            <i className={cls('dk-icon-bianji-01','dk-iconfont')} />
            <span>{intl.t('编辑')}</span>

          </Button>
        </Tooltip>
      )}
      {
        isSheet || (
          <div className={cls('share-box')}>
            <ShareModal
              resourceName={docInfo.name}
              resourceId={pageId}
              shareType={SHARE_TYPE.PAGE}
              hasPermission={hasSharePerm}
            />
          </div>
        )
      }
      <ConvertWikiToDidocModal
        visible={showConvert}
        convertError={convertError}
        next={convertNext}
        close={() => setShowConvert(false)}
        retry={handleEdit}
      />
    </div>
  );
}

function mapStateToProps({ pageDetail }) {
  const { docInfo } = pageDetail;
  return { docInfo };
}

function mapDispatchToProps({ pageDetail }) {
  const { changeInitLoading } = pageDetail;
  return {
    changeInitLoading,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Options);
