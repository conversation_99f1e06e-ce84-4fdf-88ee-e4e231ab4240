import { useContext, useState } from 'react';
import { <PERSON>ton, Popover } from 'antd';
import { intl } from 'di18n-react';
import { connect } from 'react-redux';
import classNames from 'classnames/bind';
import PageTag from '@/components/serviceComponents/PageTag';
import { ASIDE_SHOW_TYPE } from '@/constants';
import DropdownMenu from './DropdownMenu';
import { isDkSheet } from '@/utils';
import Options from './Options';
import RecentKnowledge from './RecentKnowledge/index';
import MemberListOfDK from '@/components/serviceComponents/MemberListOfDK';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import AddNewCollaborator from '@/components/serviceComponents/AddNewCollaborator';
import { sendFileTypeEvent } from '@/utils/type'
import styles from './style.module.less';

const cls = classNames.bind(styles);

function Header(props) {
  const { isCollapse, docInfo } = props;
  const isSheet = isDkSheet(docInfo.docType);
  const { pageId, knowledgeId, visitPerms } = useContext(LayoutContext);
  const [ step, setStep ] = useState(0);

  const getContent = () => {
    if (step === 0) {
      return (
        <MemberListOfDK
          isDkPage={true}
          needPerm={true}
          info={{ fileType: docInfo.type }}
          permission={docInfo.permission}
          roleKey={docInfo.roleKey}
          teamId={knowledgeId}
          teamIdAllTeam={knowledgeId}
          resourceId={pageId}
          title={intl.t('协作者')}
          gotoNext={() => setStep(1)}
          handleBack={() => setStep(0)}
        />
      );
    }

    if (step === 1) {
      return (
        <AddNewCollaborator
          showBack={true}
          isDkPage={true}
          fileType={docInfo.type}
          teamId={knowledgeId}
          resourceId={pageId}
          handleBack={() => setStep(0)}
          backToFirstStep={() => {}}  // 不处理
          closeShare={() => {
            setStep(0);
          }}
        />
      );
    }
  }

  const handleVisibleChange = (visible) => {
    if (!visible) {
      setStep(0);
    } else {
      sendFileTypeEvent('ep_detail_collaborator_ck',docInfo.type)
    }
  }

  return (
    <div className={`${cls('header')} view-document-detail-header`}>
      <div className={cls('left-section', { collapse: isCollapse })}>
        <div className={cls('bottom-section')}>
          <PageTag overlayClassName={cls('page-tag')} />
        </div>
      </div>
      <div className={cls('right-section')}>
        <Options />
        {/* 非访客看到协作者 */}
        {
          visitPerms?.length === 0 && (
            <div className={cls('right-collaborator')}>
              <Popover
                destroyTooltipOnHide
                trigger='click'
                zIndex={1000}
                placement={'bottomRight'}
                overlayClassName={cls('right-collaborator-popover')}
                content={
                  <div className={cls('right-collaborator-content')}>
                    {getContent()}
                  </div>
                }
                onVisibleChange={handleVisibleChange}
              >
                <Button className={cls('collab-btn')}>
                  <i className={cls('dk-icon-duoren','dk-iconfont')} />
                  <span>{intl.t('协作者')}</span>
                </Button>
              </Popover>
            </div>
          ) 
        }
        { isSheet || <DropdownMenu /> }
        <RecentKnowledge />
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail, KnowledgeData }) {
  const { docInfo } = pageDetail;
  const { asideType } = KnowledgeData;
  const isCollapse = asideType === ASIDE_SHOW_TYPE.TILE;

  return {
    isCollapse,
    docInfo,
  };
}

export default connect(mapStateToProps)(Header);
