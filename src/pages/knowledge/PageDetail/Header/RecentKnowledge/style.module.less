.recentKnowledge {
  width: 390px;
  position: relative;

  .recentKnowledge-tab {
    :global {
      .ant-tabs-nav {
        margin-left: 20px;
        margin-right: 20px;
        margin-top: 6px;
        margin-bottom: 6px;
      }
    }
  }

  .toggle-switch {
    position: absolute;
    top: 14px;
    right: 10px;
    z-index: 1;
    display: flex;
    align-items: center;

    &-text {
      margin-left: 6px;
      font-size: 12px;
      font-weight: 400;
      color: #7C7C7C;
    }
  }

  .more {
    height: 32px;
    line-height: 32px;
    background: #F7F8F9;
    padding-left: 19px;
    font-size: 12px;
    font-weight: 400;
    color: #7D7D7D;
    border-radius: 0px 0px 6px 6px;

    &-click-area {
      cursor: pointer;
    }

    &-icon {
      margin-left: 6px;
      font-size: 12px;
      color: #9E9F9F;
    }
  }
}

.recent_btn {
  width: 26px;
  height: 26px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 18px;
  display: none;

  >.iocn-zuijin {
    font-size: 18px;
    color: @blueGray-1;
  }

  &:hover {
    background-color: #EBEEF1;
  }
}

.slide {
  width: 1px;
  height: 12px;
  background-color: #EBEEF1;
  margin-left: 17px;
  display: none;
}