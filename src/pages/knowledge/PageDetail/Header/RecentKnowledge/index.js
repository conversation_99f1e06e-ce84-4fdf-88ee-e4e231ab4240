import { intl } from 'di18n-react';
/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-07-07 15:21:30
 * @LastEditTime: 2023-11-10 11:18:15
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/pages/knowledge/PageDetail/Header/RecentKnowledge/index.js
 *
 */
import { Tabs, Switch, Popover, Tooltip } from 'antd';
import { useParams } from 'react-router-dom';
import { RECENT_TABS, OWNER_TYPE } from '@/constants/recent';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import RecentList from './RecentList';
import classBind from 'classnames/bind';
import useRecentData from '@/hooks/useRecentData';
import * as styles from './style.module.less';
import { openNewWindow } from '@/utils';
import { Fragment, useContext, useState } from 'react';

const cx = classBind.bind(styles);

const { TabPane } = Tabs;
const RecentKnowledge = () => {
  const {
    tabInfo,
    loading,
    hasMore,
    loadMore,
    handleTabChange,
    recentList,
    activeTab,
    setOnlyCurrentDk,
  } = useRecentData();
  const { knowledgeId } = useContext(LayoutContext);
  const [open, setOpen] = useState(false);
  const onChange = (checked) => {
    checked && window.__OmegaEvent('ep_dkpc_dkhead_currentdk_ck');
    setOnlyCurrentDk(checked);
    loadMore(true, OWNER_TYPE.ALL);
  };
  const gotoRecent = () => {
    openNewWindow(
      `${window.location.origin}/knowledge/recent?key=${activeTab}`,
    );
  };
  const onTabChange = (activeKey) => {
    if (activeKey === RECENT_TABS.VISIT) {
      window.__OmegaEvent('ep_dkpc_dkhead_recentview_ck');
    } else {
      window.__OmegaEvent('ep_dkpc_dkhead_recentedit_visitpage_ck');
    }
    handleTabChange(activeKey);
  };
  const changePopOpen = () => {
    setOpen(!open);
  };
  const NotInMenhu = !!knowledgeId;
  const _renderContent = () => {
    return (
      <div className={cx('recentKnowledge')}>
        {NotInMenhu && (
          <div className={cx('switch')}>
            <Switch
              size="small"
              onChange={onChange} />
            <span className={cx('switch-text')}>{intl.t('当前知识库')}</span>
          </div>
        )}
        <Tabs
          destroyInactiveTabPane={true}
          className={cx('recentKnowledge-tab')}
          onChange={onTabChange}
        >
          {tabInfo.map((item) => {
            return (
              <TabPane
                tab={item.name}
                key={item.key}>
                <RecentList
                  loading={loading}
                  hasMore={hasMore}
                  recentList={recentList}
                  activeTab={activeTab}
                  loadMore={loadMore}
                />
              </TabPane>
            );
          })}
        </Tabs>

        <div className={cx('more')}>
          <span
            className={cx('more-click-area')}
            onClick={gotoRecent}>
            {intl.t('查看更多')}

            <i
              className={cx('dk-iconfont', 'dk-icon-youjiantou1', 'more-icon')}
            />
          </span>
        </div>
      </div>
    );
  };
  return (
    <Fragment>
      <div className={cx('slide')} />
      <Tooltip
        title={intl.t('最近')}
        placement="center">
        <Popover
          content={_renderContent()}
          trigger="click"
          visible={open}
          onVisibleChange={changePopOpen}
          placement="bottomRight"
        >
          <div className={cx('recent_btn')}>
            <i className={cx('dk-iconfont', 'dk-icon-zuijin', 'iocn-zuijin')} />
          </div>
        </Popover>
      </Tooltip>
    </Fragment>
  );
};

export default RecentKnowledge;
