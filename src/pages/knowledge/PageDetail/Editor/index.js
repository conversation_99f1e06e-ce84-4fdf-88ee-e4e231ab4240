import { intl } from 'di18n-react';
/* eslint-disable camelcase */
import { EditorConfig, EditorMicroUrl, EditorName } from '@/constants/editor';
import USER_VIEW from '@/constants/userView';
import usePermission from '@/hooks/usePermission';
import { inPhone, parseUrlSearch } from '@/utils';
import { getLocalData, setLocalData } from '@/utils/localStorage';
import { getName, reportMeasure } from '@/utils/performance';
import { MrParcel } from '@/utils/tenon.js';
import { objMixin } from '@/utils/upload';
import { Tooltip } from 'antd';
import classnames from 'classnames/bind';
import { useEffect, useMemo, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { connect } from 'react-redux';
import { useLocation } from 'react-router-dom';
import UpdateInfo from '../UpdateInfo';
import styles from './style.module.less';

const cls = classnames.bind(styles);

const isInPhone = inPhone();
window.msContainer = true;

function Editor(props) {
  const {
    changeView,
    docInfo,
    changeInitLoading,
    profile,
    writerVisible,
    toggleWriterVisible,
    changeEditReady,
    editorKey,
    // 外层传值覆盖
    editorProps,
  } = props;

  const [view, setView] = useState();
  const boxRef = useRef(null);

  useEffect(() => {
    window.performance.mark(`editor-start${window.location.pathname}`);
  }, []);

  useEffect(() => {
    const updateWidth = () => {
      if (boxRef.current) {
        const newWidth = document.querySelector('#knowedge-body-content')?.clientWidth;
        boxRef.current.style.width = `${newWidth}px`;
      }
    };

    window.addEventListener('resize', updateWidth);
    updateWidth();

    return () => {
      window.removeEventListener('resize', updateWidth);
    };
  }, []);

  useEffect(async () => {
    const element = document.querySelector('#knowedge-body-content');

    if (window.ResizeObserver === undefined) {
      const module = await import('resize-observer-polyfill');
      window.ResizeObserver = module.default;
    }

    const resizeObserver = new window.ResizeObserver((entries) => {
      for (const entry of entries) {
        if (boxRef.current) {
          boxRef.current.style.width = `${entry.contentRect.width}px`;
        }
      }
    });

    if (element) {
      resizeObserver.observe(element);
    }

    return () => {
      if (element) {
        resizeObserver.unobserve(element);
      }
    };
  }, []);

  const {
    guid,
    accessToken,
    latestVersion,
    lastPublishTime,
    permission,
    pageId,
  } = docInfo;

  const { checkOperationPermission } = usePermission();

  const { pathname } = useLocation();
  // 设置大纲初始展示状态：如果用户设置过用设置过的状态，如果没有设置过默认展示，如果大纲长度为空【优先级最高】，则不展示。
  // defaultOpen：表示用户行为。大纲为空相关判断由编辑器内部控制
  const getDictionaryConfig = () => {
    let dictionaryConfig = {
      show: !isInPhone,
      onChange: (value) => {
        let currView = JSON.stringify(getLocalData('always:USER_VIEW')) !== '{}'
          ? getLocalData('always:USER_VIEW')
          : USER_VIEW;
        currView.Read_Page.Dictionary_State = value.open + 0 ?? true;
        setLocalData('always:USER_VIEW', currView);
      },
    };

    const view = JSON.stringify(getLocalData('always:USER_VIEW')) !== '{}'
      ? getLocalData('always:USER_VIEW')
      : USER_VIEW;
    const { Dictionary_State } = view?.Read_Page;
    if (Dictionary_State !== null) {
      dictionaryConfig = objMixin(dictionaryConfig, {
        defaultOpen: !!Dictionary_State,
      });
    }
    return dictionaryConfig;
  };

  const initAi = () => {
    const showAi = window.editorIframeABSwitch;

    if (showAi && !isInPhone) {
      try {
        window.CooperClientService?.create({
          resourceId: docInfo.pageId,
          version: docInfo.latestVersion,
        })
      } catch (error) {}
    }
  }

  const userInfo = {
    username: profile.username,
    username_zh: profile.username_zh,
    avatar: profile.avatar,
  };

  const editorConfig = {
    ...EditorConfig,
    eventHandler: {
      init: (state, view) => {
        if (!window.pageDetailFirstShow) {
          window.pageEditFirstShow = true;
          window.pageDetailFirstShow = true;
          reportMeasure({
            startName: `pageDetail-first-start${pageId}`,
            endName: `pageDetail-first-end${pageId}`,
            measureName: 'load-page-first',
          });
        } else {
          reportMeasure({
            startName: `pageDetail-start${pageId}`,
            endName: `pageDetail-end${pageId}`,
            measureName: 'load-page',
          });
        }

        changeInitLoading(false);
        changeView(view);
        changeEditReady(true);
        initAi(view);
        setView(view);
        if (document.querySelector('.page-detail-loading')) document.querySelector('.page-detail-loading').style.zIndex = -1;

        if (document.getElementById('didoc-editor-slot-sub-title')) {
          ReactDOM.render(
            <UpdateInfo
              time={lastPublishTime}
              pageId={pageId} />,
            document.getElementById('didoc-editor-slot-sub-title'),
          );
        }
        writerVisible
          ? view.dom.classList.add('collab-show')
          : view.dom.classList.remove('collab-show');

        let afterSlot = document.getElementById('didoc-editor-slot-after');
        let bottomContent = document.getElementById('dk-editor-bottom-wrap');
        if (bottomContent) {
          bottomContent.style = 'display:block';
          afterSlot?.appendChild(bottomContent);
        }
        reportMeasure({
          startName: `catalog-nodeClick-start${pageId}`,
          endName: `page-load-end${pageId}`,
          measureName: 'catalog-nodeClick-page-load',
        });

        /**
         * 发布也会重新加载预览页，会再次打点但时间不变
         */
        scrollToPoint();
        scrollToMention();

        /**
         * 切换页面等会多次执行，设置flag，全局只上报最开始进来那次
         */
        if (window.isInitViewMark) {
          window.isInitViewMark = false;
          window.endTime = window.performance.now();
          if (window?.startTime) {
            window.__OmegaEvent(
              'tech_performance_custom',
              intl.t('性能自定义埋点'),
              {
                timingCategory: 'load-page-global',
                timingName: getName(pathname),
                timingValue: window?.endTime - window?.startTime ?? 0,
                isFromCache: window?.isFromCache,
              },
            );
          }
          console.log(
            '*load-page-global*',
            window?.endTime - window?.startTime,
          );
        }

        // reportMeasure({
        //   startName: `editor-start${window.location.pathname}`,
        //   endName: `tenon-load-end${window.location.pathname}`,
        //   measureName: 'tenon-load',
        // });

        // reportMeasure({
        //   startName: `page-publish-start${pageId}`,
        //   endName: `page-publish-end${pageId}`,
        //   measureName: 'page-publish-load-time',
        // });
      },
      onChange(state, view, action) {},
    },
    title: {
      value: docInfo.pageName,
      withTitle: true,
      placeholder: intl.t('无标题页面'),
      maxLength: 200,
      priority: 0,
    },
    menu: {
      floatingMenuContent: !isInPhone && {
        comment: {
          text: intl.t('评论'),
          icon: 'icon-tianjiapinglun',
        },
      },
    },
    app: {
      sourceId: guid,
      accessToken,
      sourceType: '6',
      content: '',
      dataLocation: 'self',
      version: latestVersion,
    },
    placeholder: {
      content: '',
      switch: true,
    },
    editable: false,
    user: {
      ...userInfo,
      nameCn: userInfo.username_zh,
    },
    comment: {
      show: !isInPhone,
      editable: true,
    },
    dictionary: getDictionaryConfig(),
    collab: {
      collabUser: true,
      collabUnderLine: true,
      collabCursor: true,
    },
    filePermission: {
      download: checkOperationPermission('MANAGE_PAGE_CONTEXT', permission),
    },
    draggable: !isInPhone,
    ...editorProps,
  };

  const editorShow = useMemo(() => {
    return guid && userInfo.username;
  }, [guid, profile]);

  const scrollToPoint = () => {
    let msgId = parseUrlSearch('msgId');
    if (msgId) {
      let operateDiv = document.getElementById(msgId);
      operateDiv?.scrollIntoView(true);
    }
  };

  const scrollToMention = () => {
    let mentionId = parseUrlSearch('mentionId');
    if (mentionId) {
      let operateDiv = document.querySelector(
        `span[data-mention-id=${mentionId}]`,
      );
      operateDiv?.scrollIntoView(true);
    }
  };

  // 编辑器埋点
  const handleOmega = (event) => {
    const insertCommentEle = document.querySelector(
      '#float-menu-inner-box .button-menu-icon:last-child',
    );
    const outlineEle = document.querySelector(
      '.tab-intergation-float-container .didoc-button-two-chinese-chars:first-child',
    );
    const commentListEle = document.querySelector(
      '.tab-intergation-float-container .didoc-button-two-chinese-chars:last-child',
    );
    const map = {
      ep_dkpc_pagedetail_view_comment_ck: insertCommentEle,
      ep_dkpc_pagedetail_commentlist_ck: commentListEle,
      ep_dkpc_pagedetail_outlinelist_ck: outlineEle,
    };

    Object.entries(map).forEach(([key, dom]) => {
      if (dom && dom.contains(event.target)) {
        window.__OmegaEvent(key);
      }
    });
  };

  useEffect(() => {
    if (!view?.dom) return;
    writerVisible
      ? view.dom.classList.add('collab-show')
      : view.dom.classList.remove('collab-show');
  }, [writerVisible]);

  useEffect(() => {
    document.body.addEventListener('click', handleOmega, { capture: true });

    return () => {
      document.body.removeEventListener('click', handleOmega, {
        capture: true,
      });
    };
  }, []);

  useEffect(() => {
    return () => {
      // 页面切换时卸载编辑器
      const TenonEditor = window[EditorName];
      if (TenonEditor && typeof TenonEditor.unmount === 'function') { // 监控到白屏报错做修复
        TenonEditor.unmount();
      }
      changeEditReady(false);
      view?.destroy && view.destroy();
    };
  }, []);

  useEffect(() => {
    try {
      window.CooperClientService?.destroy();
    } catch (error) {}
  }, [])

  return (
    <div
      ref={boxRef}
      id='knowledge_editor_box'
      className={cls('editor')}
    >
      {writerVisible && !isInPhone && (
        <div className={cls('closure-tip-box')}>
          <Tooltip title={intl.t('隐藏编写者')}>
            <i
              className={cls('dk-iconfont', 'dk-icon-shanchu1', 'closure-tip')}
              onClick={toggleWriterVisible}
            />
          </Tooltip>
        </div>
      )}

      <div className={cls('editor-container', { 'writer-hidden': !writerVisible })}>
        {useMemo(() => {
          if (!editorShow) return <></>;
          return (
            <MrParcel
              key={editorKey}
              entry={EditorMicroUrl}
              name={EditorName}
              appProps={editorConfig}
            />
          );
        }, [editorShow, latestVersion])}
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail, CooperIndex }) {
  const { docInfo, writerVisible, editorKey, editReady } = pageDetail;
  const { profile } = CooperIndex;

  return {
    docInfo,
    profile,
    writerVisible,
    editorKey,
    editReady,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const {
    changeView,
    changeInitLoading,
    changeEditReady,
    toggleWriterVisible,
  } = pageDetail;
  return {
    toggleWriterVisible,
    changeView,
    changeInitLoading,
    changeEditReady,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Editor);
