import { intl } from 'di18n-react';
import { Spin } from 'antd';
import { OwnerPageNotPublishIcon, PageNotPublishIcon } from '@/assets/icon';
import ErrorTips from '@/components/ErrorTipsDk';
import Comment from '@/components/serviceComponents/Comment';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import NotificationStatus from '@/constants/notification';
// import { getIsWikiHTML } from '@/constants/wikihtmlrenderHelper';
import useNotification from '@/hooks/useNotification';
import usePermission from '@/hooks/usePermission';
import ExpireLink from '@/pages/knowledge/ExpireLink';
import Header from '@/pages/knowledge/PageDetail/Header';
import { inPhone } from '@/utils';
import classNames from 'classnames/bind';
import { useContext, useEffect, useMemo, useState } from 'react';
import { connect, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import HeaderSimple from './Header';
import styles from './style.module.less';

const isInPhone = inPhone();

const cls = classNames.bind(styles);

function Authority(props) {
  const {
    children,
    docInfo = {},
    profile = {},
    isPreview,
    initLoading,
    editReady,
    changeInitLoading,
    setPageDetail,
    resetState,
    // 是否展示header
    // showHeader = true,
    // 是否展示评论
    // showComment = true,
  } = props;
  const navigate = useNavigate();
  const { knowledgeId, pageId, pageInfo, isDelete, hasPageEditPerm, setPageInfo } = useContext(LayoutContext);
  const { checkOperationPermission } = usePermission();
  const [loading, setLoading] = useState(true);
  const [isReleased, setIsReleased] = useState(false);
  const notification = useNotification();
  const { permission: dkPerm } = useSelector((state) => state.KnowledgeData);
  const { teamId } = useParams();
  const permission = docInfo?.permission;

  const isSelf = (profile.username === docInfo.createBy) || (profile.username === pageInfo?.createBy);
  // const isReleased = (latestVersion === undefined || latestVersion === null) ? false : !!latestVersion;

  const hasAsidePerm = useMemo(() => {
    return checkOperationPermission('READ_DK', dkPerm.perm);
  }, [dkPerm]);

  useEffect(() => {
    setLoading(initLoading);
  }, [initLoading]);

  useEffect(() => {
    setLoading(true);
    const latestVersion = docInfo?.latestVersion;
    setIsReleased((latestVersion === undefined || latestVersion === null) ? false : !!latestVersion);
    setLoading(false);
  }, [docInfo?.latestVersion]);

  useEffect(() => {
    if (hasPageEditPerm !== null && !hasPageEditPerm) {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
  }, [hasPageEditPerm]);

  useEffect(() => {
    if (pageInfo.pageName) {
      setLoading(true);
      setPageDetail(pageInfo).then(() => {
        setLoading(false);
      });
    }
  }, [pageInfo, setPageDetail]);

  useEffect(() => {
    setLoading(true);
    changeInitLoading(true);


    if (!window.pageDetailFirstShow) {
      window.performance.mark(`pageDetail-first-start${pageId}`);
    } else {
      window.performance.mark(`pageDetail-start${pageId}`);
    }
  }, [location.pathname, changeInitLoading])

  useEffect(() => {
    if (docInfo?.pageName) {
      window.document.title = docInfo.pageName;
    }
  }, [docInfo?.pageName]);

  useEffect(() => {
    if (isDelete) {
      setLoading(false);
    }
  }, [isDelete]);

  // TODO: 需要改成有完整目录权限时才跳转
  useEffect(() => {
    // 有知识库的查看权限时被删除，跳转到首页，没有的话不动
    if (hasAsidePerm && isDelete && !inPhone()) {
      setLoading(false);
      notification(
        NotificationStatus.ERROR,
        intl.t('页面已被删除，即将跳转到首页'),
      );
      setTimeout(() => {
        // window.location.href = teamId ? `/team-file/${teamId}/knowledge/${knowledgeId}/home` : `/knowledge/${knowledgeId}/home`;
        const url = teamId
          ? `${window.location.origin}/team-file/${teamId}/knowledge/${knowledgeId}/home`
          : `${window.location.origin}/knowledge/${knowledgeId}/home`;

        window.location.href = url;
      }, 1000);
    }
  }, [hasAsidePerm, isDelete]);

  const goToEditor = () => {
    changeInitLoading(true);
    if (window.top !== window.self) {
      window.open(`/knowledge/${knowledgeId}/${pageId}/edit`)
    } else {
      changeInitLoading(true);
      navigate(
        teamId
          ? `/team-file/${teamId}/knowledge/${knowledgeId}/${pageId}/edit`
          : `/knowledge/${knowledgeId}/${pageId}/edit`,
      );
    }
  };

  const hasEditPermission = useMemo(() => {
    return checkOperationPermission('MANAGE_PAGE_CONTEXT', permission);
  }, [permission]);

  const title = useMemo(() => {
    if (!hasEditPermission) {
      return (
        <div className={cls('no-permission-tip')}>
          {intl.t('此页面暂未发布，暂不可见')}
        </div>
      );
    }
    return (
      <div className={cls('no-permission-tip')}>
        {isSelf ? intl.t('你尚未发布页面') : intl.t('该页面尚未发布')}
        {!isInPhone && <span>，</span>}

        {!isInPhone && (
          <span
            className={cls('link')}
            onClick={goToEditor}>
            {intl.t('继续编辑')}
          </span>
        )}
      </div>
    );
  }, [goToEditor]);

  return (
    <div className={cls('authority')}>
      {!isInPhone && (isReleased ? (editReady ? <Header /> : null) : <HeaderSimple />)}

      {
        loading ? (
          <div className={'page-detail-loading'}>
            <Spin />
          </div>
        ) : (
          <>
            {isDelete && <ExpireLink />}

            {!isDelete && (
              <>
                {isReleased && (
                  <>
                    {children}
                    {editReady && !isInPhone && !isPreview && <Comment/>}
                  </>
                )}

                {isReleased === false && (
                  <ErrorTips
                    imgClassName={'publish-icon'}
                    img={isSelf ? OwnerPageNotPublishIcon : PageNotPublishIcon}
                    title={title}
                  />
                )}
              </>
            )}
          </>
        )
      }
    </div>
  );
}

function mapStateToProps({ pageDetail, CooperIndex }) {
  const { docInfo, editReady, isFirstInit, initLoading } = pageDetail;
  const { profile } = CooperIndex;
  return {
    docInfo,
    initLoading,
    profile,
    editReady,
    isFirstInit,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const { getPageDetail, changeInitLoading, changeDocInfo, setPageDetail } = pageDetail;
  return {
    getPageDetail,
    changeInitLoading,
    changeDocInfo,
    setPageDetail,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Authority);
