import { Spin } from 'antd';
import classNames from 'classnames/bind';
import { useContext, useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import { Helmet } from 'react-helmet';
import Authority from './Authority';
import Editor from './Editor';
import { addRecentVisit } from '@/service/knowledge/recent';
import { inPhone, isDkSheet, isPreviewFile, isAndroid, isIpad } from '@/utils';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import Preview from '@/components/serviceComponents/Preview';
import ShimoExcelPreview from '@/components/ShimoExcelPreview';
import { RECENT_VISIT_TYPE } from '@/constants/recent';
import useGetPreviewFileInfo from '@/hooks/useGetPreviewFileInfo';
import WikiHTMLRender from './Wiki';
import { getIsWikiHTML } from '@/constants/wikihtmlrenderHelper';
import ErrorBoundary from '@/components/ErrorBoundary';
import * as styles from '@/pages/knowledge/PageDetail/style.module.less';
import { getDcVersion, isBeforeVersion } from '@/utils/dc-helper';

const cls = classNames.bind(styles);

const isInPhone = inPhone();
const isInAndroid = isAndroid();
function PageDetail({ initLoading, docType, docInfo, fileId, permission, changeEditReady, multiPath = [], pageStyle = {}}) {
  const { pageId } = useContext(LayoutContext);
  const isPreview = isPreviewFile(docType);
  const isWikiHTML = getIsWikiHTML(docInfo);
  const isSheet = isDkSheet(docType);
  const { loading } = useGetPreviewFileInfo(isPreview, fileId, 0);

  useEffect(() => {
    setTimeout(() => {
      addRecentVisit({
        resourceId: pageId,
        sourceType: RECENT_VISIT_TYPE.NORMAL,
      });
    }, 4000);
    window.performance.mark(`catalog-nodeClick-start${pageId}`);
  }, [pageId]);

  const parentId = useMemo(() => {
    if (!multiPath) return;
    return multiPath[multiPath?.length - 2 ?? 0]?.id;
  }, [multiPath, pageId]);

  const getDocView = () => {
    if (isPreview) {
      window.isInitViewMark = false;
      changeEditReady(true);
      return <Preview
        permission={permission}
        parentId={parentId}
        isEdit={false}
      />
    }
    if (isWikiHTML) {
      window.isInitViewMark = false;
      changeEditReady(true);
      return <WikiHTMLRender />;
    }
    if (isSheet) {
      window.isInitViewMark = false;
      changeEditReady(true);
      return <ShimoExcelPreview/>;
    }
    return <Editor />;
  }

  // 手机端，隐藏底部导航栏
  useEffect(() => {
    if(inPhone() && !isIpad()) {
      const dcVersion = getDcVersion();
      if(!isBeforeVersion(dcVersion, '4.12.0')) {
        window.dcH5Sdk.app.appSetOrientation({
          orientation: 'all',
          onSuccess: () => console.log('appSetOrientation is success!!'),
          onFail: () => {},
        });
        if(!isInAndroid) {
          window.dcH5Sdk.event.on("orientationChange", (data)=>{
            if(data.orientation === 'portrait') {
              window.dcH5Sdk.navigation.hideToolBar({
                visibility: true,
                onSuccess: () => {},
                onFail : () => {}
              });
            }else {
              window.dcH5Sdk.navigation.hideToolBar({
                visibility: false,
                onSuccess:() => {},
                onFail: () => {}
              });
            }
          })
        }
      }
    }
  }, [])

  return (
    <div
      className={
        cls(
          'page-detail',
          'page-style-root',
          `font-size-${pageStyle?.preFontSize}`,
          `screen-${pageStyle?.preScreen}`,
          {
            'page-detail-inphone': isInPhone,
          },
        )
      }
    >
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png"
        />
      </Helmet>
      <Authority isPreview={isPreview}>
        {
          (isPreview ? loading : initLoading) && (
            <div className={'page-detail-loading'}>
              <Spin />
            </div>
          )
        }
        <div
          className={cls('page-content', {
            'no-header': isInPhone,
            'dk-editor-in-phone_reset': isInPhone,
          })}
        >
          <ErrorBoundary>
            { getDocView() }
          </ErrorBoundary>
        </div>
      </Authority>
    </div>
  );
}

function mapStateToProps({ pageDetail }) {
  const { initLoading, docInfo, previewFileId } = pageDetail;
  const { docType, permission, multiPath, pageStyle } = docInfo;
  return {
    initLoading,
    fileId: previewFileId.inDetail,
    docType,
    permission,
    multiPath,
    pageStyle,
    docInfo,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const { changeInitLoading, changeEditReady } = pageDetail;
  return {
    changeInitLoading,
    changeEditReady,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(PageDetail);
