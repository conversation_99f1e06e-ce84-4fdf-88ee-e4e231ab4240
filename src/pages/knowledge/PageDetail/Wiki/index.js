/* eslint-disable camelcase */
import { connect } from 'react-redux';
import ReactDOM from 'react-dom';
import { MrParcel } from '@/utils/tenon.js';
import classnames from 'classnames/bind';
import UpdateInfo from '../UpdateInfo';
import styles from './style.module.less';
import { tenonHtmlBootstrap } from '@/constants/wikihtmlrenderHelper';
import UtilsDrawer from '@/components/UtilsDrawer';
import { getWikiUrl } from '@/service/knowledge/global';

const cls = classnames.bind(styles);

function Wiki(props) {
  const {
    docInfo,
    changeInitLoading,
    changeEditReady,
    editorKey,
  } = props;

  const {
    lastPublishTime,
    pageId,
    contentUrl,
  } = docInfo;

  const handleLinkJump = (root) => {
    root.shadowRoot.addEventListener('click', (e) => {
      // 检查点击目标是否为链接
      const link = e.target.closest('a');
      if (link) {
        const href = link.getAttribute('href')
        const url = new URL(href);
        const isWiki = url.hostname.startsWith('wiki')
        if (isWiki) {
          e.preventDefault(); // 阻止默认跳转
          e.stopPropagation()
          getWikiUrl({
            wikiLink: href,
            currentResourceId: pageId,
          })
            .then((res) => {
              console.log('*** res', res)
              window.open(res, '_blank')
            })
            .catch((err) => {
              console.log('*** err', err)
              window.open(href, '_blank')
            })
        }
      }
    });
  }

  // 后端wiki迁移失败后，contentUrl字段为空，前端做个兜底提示
  const handleTransferError = () => {
    changeInitLoading(false);
    changeEditReady(true);
  }

  if (!contentUrl) {
    return (
      <div className={cls('wiki-transfer-fail-tip')} ref={handleTransferError}>
        数据迁移失败，请进群反馈。点击 <a target="_blank" href="https://im.xiaojukeji.com/channel?uid=616708&token=e1aeef99b7a5b504d1ef1d79083ba388&id=9510" className={cls('wiki-transfer-fail-tip__link')}>https://im.xiaojukeji.com/channel?uid=616708&token=e1aeef99b7a5b504d1ef1d79083ba388&id=9510</a>，加入D-Chat“Wiki问题处理群①（问题请联系Wiki小助手）”
      </div>
    )
  }

  return (
    <div
      id="knowledge_editor_box"
      className={cls('wiki-editor')}
      style={{ overflow: 'scroll' }}
    >
      <div className={cls('wiki-editor-container')}>
        <MrParcel
          key={editorKey}
          entry={contentUrl}
          name="knowledge_wiki_html"
          appProps={{
            app: tenonHtmlBootstrap(() => {
              changeInitLoading(false);
              changeEditReady(true);
              if (document.querySelector('.page-detail-loading')) document.querySelector('.page-detail-loading').style.zIndex = -1;
              // 挂载全文评论
              let afterSlot = document.querySelector('div[class^=wiki-editor-container]');
              let bottomContent = document.getElementById('dk-editor-bottom-wrap');
              if (bottomContent) {
                bottomContent.style = 'display:block';
                afterSlot?.appendChild(bottomContent);
              }
              // 挂载updateInfo
              const updateInfo = document.createElement('div');
              updateInfo.classList.add('wiki-editor-container-update-info');
              updateInfo.style.margin = '18px 20px 0 20px'
              afterSlot.insertBefore(updateInfo, afterSlot.firstChild);
              ReactDOM.render(
                <UpdateInfo
                  time={lastPublishTime}
                  pageId={pageId} />,
                updateInfo,
              );
              const tenonRoot = afterSlot.querySelector('[id^="_tenon_"]')
              // 拦截a标签跳转
              handleLinkJump(tenonRoot)
            }),
          }}
        />
        <UtilsDrawer
          sourceType="6"
          editable={false}
          commentConfig={{ show: true }}
          markdownGuide={false}
          draggable={false}
          offline={false}
          backTopVisible={true}
        />
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail, CooperIndex }) {
  const { docInfo, writerVisible, editorKey, editReady } = pageDetail;
  const { profile } = CooperIndex;
  return {
    docInfo,
    profile,
    writerVisible,
    editorKey,
    editReady,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const {
    changeView,
    changeInitLoading,
    changeEditReady,
    toggleWriterVisible,
  } = pageDetail;
  return {
    toggleWriterVisible,
    changeView,
    changeInitLoading,
    changeEditReady,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Wiki);
