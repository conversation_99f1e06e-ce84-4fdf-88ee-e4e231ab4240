/*
 * @Date: 2023-11-16 12:00:36
 * @LastEditors: guanzhong
 * @LastEditTime: 2023-11-16 12:08:56
 * @FilePath: /knowledgeforge/src/pages/knowledge/ShareBook/SharePageDetail/snapshoot.js
 * @Description: 描述文件功能
 */
import ShimoExcelPreview from './ShimoExcelPreview';
import { Spin } from 'antd';
import classNames from 'classnames/bind';
import { useEffect, useState, useMemo, useContext } from 'react';
import { connect, useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import { inPhone, getUrlParams, isPreviewFile } from '@/utils';
import SharePreview from '@/components/serviceComponents/SharePreview';
import Editor from './Editor';
import * as styles from '@/pages/knowledge/PageDetail/style.module.less';
import { Helmet } from 'react-helmet';

const cls = classNames.bind(styles);

const isInPhone = inPhone();
const SharePageDetail = (props) => {
  const { getSnapshootDetail, initLoading, changeInitLoading } = props;
  const notification = useNotification();
  const [type, setType] = useState(null)
  const { snapshootId, version } = useParams()
  const pageName = decodeURIComponent(getUrlParams('pageName') || '')
  const [isDKSheet, setIsDKSheet] = useState(false);

  useEffect(() => {
    changeInitLoading(true)
    if (!snapshootId) return
    const Url = window.location.href.split('&')[1]
    const mapId = Url ? Url.split('=')[1] : ''
    getSnapshootDetail({ pageId: snapshootId, version, mapId, pageName }).then((data) => {
      setIsDKSheet(data.docType === 'DK_SHEET');
      setType(data.type);
    }).catch((errRes) => {
      changeInitLoading(false)
      if (!errRes.errorMessage) return;
      notification(NotificationStatus.ERROR, errRes.errorMessage || errRes.errorType);
    });
  }, [snapshootId])
  const isReleased = useMemo(() => {
    return snapshootId && version;
  }, [snapshootId, version]);

  return (
    <div
      className={cls('page-detail')}
    >
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png" />
      </Helmet>
      {<>
        {
        isReleased && <>
          <div
            className={cls('page-content', {
              'no-header': true,
              'dk-editor-in-phone_reset': isInPhone,
            })}>
            {
              initLoading && (
                <div className={'page-detail-loading'}>
                  <Spin />
                </div>
              )
            }
            {isPreviewFile(type) && <SharePreview
              fileId={version}
              pageId={snapshootId}
              fileName={pageName}
              isSnapshot={true}
              changeInitLoading={changeInitLoading}
            />}
            {type !== null && isDKSheet && <ShimoExcelPreview />}
            {(type !== null && type !== 'DK_FILE' && !isDKSheet) && <Editor isSnapshoot={true} />}
          </div>
        </>
      }
      </>}
    </div>
  );
}

function mapStateToProps({ SharePage }) {
  const { initLoading } = SharePage;
  return {
    initLoading,
  };
}


function mapDispatchToProps({ SharePage }) {
  const { changeInitLoading, getSnapshootDetail } = SharePage;
  return {
    getSnapshootDetail,
    changeInitLoading,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(SharePageDetail);
