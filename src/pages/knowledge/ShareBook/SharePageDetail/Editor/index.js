import { intl } from 'di18n-react';
import { useEffect, useMemo, useRef } from 'react';
import ReactDOM from 'react-dom';
import { connect } from 'react-redux';
import { MrParcel } from '@didi/mf-tenon';
import classnames from 'classnames/bind';
import { EditorConfig, EditorMicroUrl, EditorName } from '@/constants/editor';
import UpdateInfo from '@/pages/knowledge/PageDetail//UpdateInfo';
import { inPhone, parseUrlSearch } from '@/utils';
import * as styles from './style.module.less';

const cls = classnames.bind(styles);

const isInPhone = inPhone();
window.msContainer = true;
function Editor(props) {
  const {
    docInfo,
    changeInitLoading,
    profile,
    changeEditReady,
    editorKey,
    isSnapshoot,
    hasDownloadPerm,
    hasCommentPerm,
  } = props;
  const { guid, accessToken, latestVersion, lastPublishTime, pageId } = docInfo;
  const userInfo = {
    username: profile.username,
    username_zh: profile.username_zh,
    avatar: profile.avatar,
  };
  const editorView = useRef();

  const addDom = (id) => {
    let afterSlot = document.getElementById('didoc-editor-slot-after');
    let bottomContent = document.getElementById(id);
    if (bottomContent) {
      bottomContent.style = 'display:block';
      afterSlot?.appendChild(bottomContent);
    }
  };
  const scrollToPoint = () => {
    let msgId = parseUrlSearch('msgId');
    if (msgId) {
      let operateDiv = document.getElementById(msgId);
      operateDiv?.scrollIntoView(true);
    }
  };

  const initAi = () => {
    const showAi = window.editorIframeABSwitch;

    if (showAi && !isInPhone) {
      try {
        window.CooperClientService?.create({
          resourceId: docInfo.pageId,
          version: docInfo.latestVersion,
        })
      } catch (error) {}
    }
  }

  const commnetFloatMenu = hasCommentPerm
    ? {
      comment: {
        text: intl.t('评论'),
        icon: 'icon-tianjiapinglun',
      },
    }
    : false;
  const editorConfig = {
    ...EditorConfig,
    menu: {
      floatingMenuContent: !isInPhone && commnetFloatMenu,
      menuBarContent: null,
    },
    eventHandler: {
      init: (state, view) => {
        changeInitLoading(false);
        changeEditReady(true);

        addDom('dk-editor-bottom-wrap');
        addDom('dk-editor-bottom-wrap-gotoDkhome');
        scrollToPoint();
        initAi(view);
        if (!isSnapshoot) {
          ReactDOM.render(
            <UpdateInfo
              time={lastPublishTime}
              pageId={pageId} />,
            document.getElementById('didoc-editor-slot-sub-title'),
          );
        }
        // 添加Command + A监听
        editorView.current = view;
        window.addEventListener('keydown', handleSelectAll);
      },
    },
    title: {
      value: docInfo.pageName || intl.t('无标题页面'),
      withTitle: true,
      placeholder: intl.t('无标题页面'),
      maxLength: 200,
    },
    app: {
      sourceId: guid,
      accessToken,
      sourceType: '6',
      content: '',
      dataLocation: 'self',
      version: latestVersion,
    },
    placeholder: {
      content: '',
      switch: true,
    },
    editable: false,
    draggable: !isInPhone,
    user: {
      ...userInfo,
      nameCn: userInfo.username_zh,
    },
    comment: {
      show: !isInPhone && !isSnapshoot && hasCommentPerm,
      editable: true,
    },
    dictionary: {
      show: !isInPhone,
    },
    filePermission: {
      download: hasDownloadPerm,
    },
    pageId,
  };

  const editorShow = useMemo(() => {
    return guid && userInfo.username && latestVersion;
  }, [guid, profile, latestVersion]);

  useEffect(() => {
    if (docInfo.pageName) {
      window.document.title = docInfo.pageName;
    }
  }, [docInfo.pageName]);

  useEffect(() => {
    return () => {
      // 页面切换时卸载编辑器
      const TenonEditor = window[EditorName];
      if (TenonEditor && typeof TenonEditor.unmount === 'function') { // 监控到白屏报错做修复
        TenonEditor.unmount();
      }
      changeEditReady(false);
      window.removeEventListener('keydown', handleSelectAll);
    };
  }, []);

  useEffect(() => {
    return () => {
      try {
        window.CooperClientService?.destroy();
      } catch (error) {}
    }
  }, [])

  const handleSelectAll = (event) => {
    try {
      if (
        event.keyCode === 65
        && (navigator.platform.match('Mac') ? event.metaKey : event.ctrlKey)
      ) {
        event.preventDefault();
        editorView.current.selectAll();
      }
    } catch (error) {
      console.error('Share Page Select All error', error);
    }
  };

  return (
    <div
      id="knowledge_editor_box"
      className={cls('editor')}>
      <div className={cls('editor-container')}>
        {editorShow && (
          <MrParcel
            key={editorKey}
            entry={EditorMicroUrl}
            name={EditorName}
            appProps={editorConfig}
          />
        )}
      </div>
    </div>
  );
}

function mapStateToProps({ SharePage, CooperIndex }) {
  const { docInfo, editReady, editorKey } = SharePage;
  const { profile } = CooperIndex;
  return {
    docInfo,
    profile,
    editReady,
    editorKey,
  };
}

function mapDispatchToProps({ SharePage }) {
  const { changeInitLoading, changeEditReady } = SharePage;
  return {
    changeInitLoading,
    changeEditReady,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Editor);
