import { intl } from 'di18n-react';
import { Spin } from 'antd';
import classNames from 'classnames/bind';
import { useEffect, useState, useMemo, useContext } from 'react';
import { connect } from 'react-redux';
import { PageNotPublishIcon } from '@/assets/icon';
import { addRecentVisit } from '@/service/knowledge/recent';
import { RECENT_VISIT_TYPE } from '@/constants/recent';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import ErrorTips from '@/components/ErrorTips';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import GotoDkHome from '@/components/serviceComponents/GotoDkhome';
import Comment from '@/components/serviceComponents/Comment';
import Header from '../Header';
import { inPhone } from '@/utils';
import ExpireLink from '@/pages/knowledge/ExpireLink';
import styles from '@/pages/knowledge/PageDetail/style.module.less';
import { getUrlParams } from '@/utils/index';

const cls = classNames.bind(styles);

const isInPhone = inPhone();
function Authority(props) {
  const { children, docInfo = {}, shareModalSetting, initLoading } = props;
  const { pageId, shareId } = useContext(LayoutContext);
  const [isDelete, setIsDelete] = useState(null);
  const notification = useNotification();
  const { getSharePageDetail, changeInitLoading } = props;
  const { latestVersion, pageName } = docInfo;
  const [loading, setLoading] = useState(true);

  let uniqueId = pageId || shareId;

  useEffect(() => {
    setLoading(initLoading);
  }, [initLoading]);

  useEffect(() => {
    changeInitLoading(true);
    let baseId = getUrlParams('base');
    getSharePageDetail({ pageId, shareId, baseId }).then((docInfoNew) => {
      window.document.title = `${docInfoNew.pageName}`;
      if (!docInfoNew.latestVersion) {
        changeInitLoading(false);
      }
      setTimeout(() => {
        const params = {
          resourceId: docInfoNew.pageId,
          shareId,
          sourceType: pageId ? RECENT_VISIT_TYPE.DK_SHARE : RECENT_VISIT_TYPE.PAGE_SHARE,
        }
        if(baseId){
          params.base = baseId
        }
        addRecentVisit(params);
      }, 4000);
    }).catch((errRes) => {
      if (!errRes.errorMessage) return;
      if (errRes.errorCode === 201003 || errRes.errorCode === 501003) {
        // 显示删除内容
        setIsDelete(true);
      } else {
        notification(NotificationStatus.ERROR, errRes.errorMessage || errRes.errorType);
      }
    }).finally(() => {
      setLoading(false);
    })
  }, [uniqueId]);

  const hasCommentPerm = useMemo(() => {
    return shareModalSetting?.sharePermList?.comment === 1;
  }, [shareModalSetting])

  const isReleased = useMemo(() => {
    if (docInfo.latestVersion === undefined) return null;
    return !!latestVersion;
  }, [latestVersion]);

  const needHeader = useMemo(() => {
    return !isInPhone;
  }, []);

  return (
    <div className={cls('authority')}>
      {needHeader && <Header
        shareId={shareId}
        pageName={pageName} />}
        
      {
        loading ? (
          <div className={'page-detail-loading'}>
            <Spin />
          </div>
        ) : (
          <>
            {isDelete && <ExpireLink />}

            {!isDelete && (
              <>
                {isReleased && (
                  <>
                    {children}
                    {!isInPhone && hasCommentPerm && <Comment />}
                    <GotoDkHome />
                  </>
                )}

                {isReleased === false && (
                  <ErrorTips
                    img={PageNotPublishIcon}
                    title={
                      <div className={cls('no-permission-tip')}>
                        {intl.t('此页面暂未发布，暂不可见')}
                      </div>
                    }
                  />
                )}
              </>
            )}
          </>
        )
      }
    </div>
  );
}

function mapStateToProps({ SharePage }) {
  const { initLoading, docInfo, shareModalSetting } = SharePage;
  return {
    initLoading,
    docInfo,
    shareModalSetting,
  };
}


function mapDispatchToProps({ SharePage }) {
  const { getSharePageDetail, changeInitLoading } = SharePage;
  return {
    getSharePageDetail,
    changeInitLoading,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Authority);
