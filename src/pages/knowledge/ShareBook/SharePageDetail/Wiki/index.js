import { intl } from 'di18n-react';
/* eslint-disable camelcase */
import { useEffect, useMemo, useState } from 'react';
import { connect } from 'react-redux';
import ReactDOM from 'react-dom';
import { Tooltip } from 'antd';
import { MrParcel, loadParcel } from '@/utils/tenon.js';
import classnames from 'classnames/bind';
import usePermission from '@/hooks/usePermission';
import { EditorConfig, EditorMicroUrl, EditorName } from '@/constants/editor';
import { inPhone, parseUrlSearch } from '@/utils';
import { reportMeasure } from '@/utils/performance';
// import UpdateInfo from '../UpdateInfo';
import * as styles from './style.module.less';
import { useLocation } from 'react-router-dom';
import { getLocalData, setLocalData } from '@/utils/localStorage';
import USER_VIEW from '@/constants/userView';
import { objMixin } from '@/utils/upload';
import { tenonHtmlBootstrap } from '@/constants/wikihtmlrenderHelper';
import UpdateInfo from '@/pages/knowledge/PageDetail//UpdateInfo';
import UtilsDrawer from '@/components/UtilsDrawer';

const cls = classnames.bind(styles);

const isInPhone = inPhone();
function Wiki(props) {
  const {
    changeView,
    docInfo,
    changeInitLoading,
    profile,
    writerVisible,
    toggleWriterVisible,
    changeEditReady,
    editorKey,
    editReady,
    // 外层传值覆盖
    editorProps,
    shareModalSetting,
  } = props;

  const {
    guid,
    accessToken,
    latestVersion,
    lastPublishTime,
    permission,
    pageId,
    contentUrl,
  } = docInfo;
  const { checkOperationPermission } = usePermission();
  const { pathname } = useLocation();

  const hasCommentPerm = useMemo(() => {
    return shareModalSetting?.sharePermList?.comment === 1;
  }, [shareModalSetting])

  const userInfo = {
    username: profile.username,
    username_zh: profile.username_zh,
    avatar: profile.avatar,
  };

  // 后端wiki迁移失败后，contentUrl字段为空，前端做个兜底提示
  const handleTransferError = () => {
    changeInitLoading(false);
    changeEditReady(true);
  }

  if (!contentUrl) {
    return (
      <div className={cls('wiki-transfer-fail-tip')} ref={handleTransferError}>
        数据迁移失败，请进群反馈。点击 <a target="_blank" href="https://im.xiaojukeji.com/channel?uid=616708&token=e1aeef99b7a5b504d1ef1d79083ba388&id=9510" className={cls('wiki-transfer-fail-tip__link')}>https://im.xiaojukeji.com/channel?uid=616708&token=e1aeef99b7a5b504d1ef1d79083ba388&id=9510</a>，加入D-Chat“Wiki问题处理群①（问题请联系Wiki小助手）”
      </div>
    )
  }

  return (
    <div
      id="knowledge_editor_box"
      className={cls('wiki-editor')}
      style={{ overflow: 'scroll' }}
    >
      <div className={cls('wiki-editor-container')}>
        <MrParcel
          key={editorKey}
          entry={contentUrl}
          name="knowledge_wiki_html"
          appProps={{
            app: tenonHtmlBootstrap(() => {
              changeInitLoading(false);
              changeEditReady(true);
              if (document.querySelector('.page-detail-loading')) document.querySelector('.page-detail-loading').style.zIndex = -1;
              // 挂载全文评论
              let afterSlot = document.querySelector('div[class^=wiki-editor-container]');
              let bottomContent = document.getElementById('dk-editor-bottom-wrap');
              if (bottomContent) {
                bottomContent.style = 'display:block';
                afterSlot?.appendChild(bottomContent);
                // bottomContent.style.padding = '0 70px';
              }
              // 挂载footer
              let footerContent = document.getElementById('dk-editor-bottom-wrap-gotoDkhome');
              if (footerContent) {
                footerContent.style = 'display:block';
                afterSlot?.appendChild(footerContent);
                // footerContent.style.padding = '0 70px';
              }
              // 挂载updateInfo
              const className = 'wiki-editor-container-update-info';
              const updateInfo = document.querySelector(`.${className}`) || document.createElement('div');
              updateInfo.classList.add(className);
              updateInfo.style.margin = '18px 20px 0 20px'
              afterSlot.insertBefore(updateInfo, afterSlot.firstChild);
              ReactDOM.render(
                <UpdateInfo
                  time={lastPublishTime}
                  pageId={pageId} />,
                updateInfo,
              );
            }),
          }}
        />
        <UtilsDrawer
          sourceType="6"
          editable={false}
          commentConfig={{ show: hasCommentPerm }}
          markdownGuide={false}
          draggable={false}
          offline={false}
          backTopVisible={true}
        />
      </div>
    </div>
  );
}
function mapStateToProps({ SharePage, CooperIndex }) {
  const { docInfo, editReady, editorKey, shareModalSetting } = SharePage;
  const { profile } = CooperIndex;
  return {
    docInfo,
    profile,
    editReady,
    editorKey,
    shareModalSetting,
  };
}

function mapDispatchToProps({ SharePage }) {
  const { changeInitLoading, changeEditReady } = SharePage;
  return {
    changeInitLoading,
    changeEditReady,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Wiki);
