import { intl } from 'di18n-react';
import { Tooltip, Popover } from 'antd';
import classnames from 'classnames/bind';
import * as styles from './style.module.less';
import Content from './Popover';

const cls = classnames.bind(styles);

function PageStyle(props) {
  return (
    <Popover
      title={null}
      content={<Content {...props} />}
      trigger="click"
      placement="bottomRight"
      showArrow={false}
      overlayClassName={cls('page-style-popover-container')}
    >
      <Tooltip
        title={intl.t('阅读样式')}
        overlayClassName="dk-ant-tooltip__reset"
      >
        <span className={cls('page-style-btn')}>
          <i className={cls('dk-iconfont', 'dk-icon-wendangyangshi', 'icon')} />
        </span>
      </Tooltip>
    </Popover>
  );
}

export default PageStyle;
