import { useMemo, useContext, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import classNames from 'classnames/bind';
import BreadCrumb from '@/components/BreadCrumb';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import * as styles from './style.module.less';
import { getUrlParams } from '@/utils/index';
import { SHARE_TYPE, SHARE_SCOPE } from '@/constants/index';
import { getShareSetting } from '@/service/knowledge/share';

const cls = classNames.bind(styles);

function Navigation(props) {
  const { docInfo, getShareModalSetting } = props;
  const { multiPath = [] } = docInfo;
  const { shareId } = useContext(LayoutContext);
  const [isShareBatchPageFromBase, setIsShareBatchPageFromBase] = useState(); // 分享范围是否为当前页面

  /**
   * 批量分享的routes情况：
   * 无base
   *      显示自身名称
   * 有Base
   *     base是批量分享，截取base以及所有子页面
   *     base是单页面，截取自身以及所有子页面
   * 显示自身名称的情况，用后端返回的multiPath
   */

  useEffect(() => {
    let baseId = getUrlParams('base');
    if (baseId) {
      getShareSetting({
        resourceType: SHARE_TYPE.SHARE,
        resourceId: baseId,
      }).then((res) => {
        if (res.status === 1) {
          setIsShareBatchPageFromBase(res.isOpenSubPage === SHARE_SCOPE.IncludeChild)
        }
      });
    }
    if (shareId) {
      // 为了获取当前页面的设置---页面渲染时候用
      getShareModalSetting({
        resourceType: SHARE_TYPE.SHARE,
        resourceId: shareId,
      })
    }
  }, [shareId]);

  const isDkShare = useMemo(() => {
    return window.location.pathname.indexOf('book') !== -1;
  }, []);

  const routes = useMemo(() => {
    if (isDkShare) {
      return multiPath.map((item) => ({
        path: `/knowledge/share/book/${shareId}/${item.id}`,
        breadcrumbName: item.name,
      }));
    }

    if (getUrlParams('base')) {
      let referenceId = isShareBatchPageFromBase ? getUrlParams('base') : shareId;
      let first = null;

      let res = [];
      multiPath.forEach((item, index) => {
        if (item.shareId === referenceId) {
          first = index;
        }
        if (first !== null && index >= first) {
          res.push({
            path: `/knowledge/share/page/${item.shareId}?base=${getUrlParams('base')}`,
            breadcrumbName: item.name,
          })
        }
      });
      return res;
    }

    let first = null;
    let res = [];
    multiPath.forEach((item, index) => {
      if (item.shareId === shareId) {
        first = index;
      }
      if (first !== null && index >= first) {
        res.push({
          path: `/knowledge/share/page/${item.shareId}?base=${shareId}`,
          breadcrumbName: item.name,
        })
      }
    });
    return res;
  }, [isShareBatchPageFromBase, multiPath]);

  return (
    <div className={cls('navigation')}>
      {routes.length > 0 && <BreadCrumb routes={routes} />}
    </div>
  );
}

function mapStateToProps({ SharePage }) {
  const { docInfo } = SharePage;
  return {
    docInfo,
  };
}

function mapDispatchToProps({ SharePage }) {
  const { getShareModalSetting } = SharePage;
  return {
    getShareModalSetting,
  };
}


export default connect(mapStateToProps, mapDispatchToProps)(Navigation);
