import { intl } from 'di18n-react';
import { useContext, useMemo, useEffect, useState } from 'react';
import { Button, Tooltip } from 'antd';
import Navigation from './Navigation';
import GlobalSearch from '@/components/serviceComponents/Header/SearchContent/GlobalSearch';
import * as styles from './style.module.less';
import { useSelector, useDispatch } from 'react-redux';
import { getPagePermission, convertWikiToDidoc } from '@/service/knowledge/page';
import { useLocation } from 'react-router-dom';
import usePermission from '@/hooks/usePermission';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import Star from '@/pages/knowledge/PageDetail/Header/Star';
import { getUrlParams, isDkPage, isDkSheet } from '@/utils/index';
import { getIsWikiHTML } from '@/constants/wikihtmlrenderHelper';
import ConvertWikiToDidocModal from '@/components/common/ConvertWikiToDidoc';
import { entryEnhance } from '@/utils/entryEnhance'
import PageStyle from './PageStyle';

function Header({ shareId, pageName }) {
  const { normalIds, docInfo, tags } = useSelector((state) => state.SharePage);
  const { pageStyle = {}, externalSystemType = '', guid, docType } = docInfo;
  const { search } = useLocation();
  const { pageId } = useContext(LayoutContext);
  const dispatch = useDispatch();
  const { getIdsFormShare, setPageStyle } = dispatch.SharePage;
  const [pagePerm, setPagePerm] = useState();
  const [showConvert, setShowConvert] = useState(false);
  const [convertError, setConvertError] = useState(false);
  const isWikiHtml = getIsWikiHTML(docInfo);
  const isSheet = isDkSheet(docType)
  const isPage = isDkPage(docType);
  const { checkOperationPermission } = usePermission();
  let baseId = getUrlParams('base');
  // 知识库分享/批量分享：搜索
  // 页面成员：编辑
  const isDkShare = useMemo(() => {
    return window.location.pathname.indexOf('book') !== -1;
  }, []);

  const _starInfo = useMemo(() => {
    const isBaseInfo = !!baseId ? `${shareId}?base=${baseId}` : shareId
    return isDkShare ? shareId : isBaseInfo
  },[isDkShare,baseId,shareId])

  const uniqueId = pageId || shareId;
  const showPageStyle = !isWikiHtml;
  const prePageStyle = useMemo(() => {
    return {
      fontSize: pageStyle?.preFontSize,
      screen: pageStyle?.preScreen,
    };
  }, [pageStyle]);

  const changePageStyle = ({ fontSize, screen }) => {
    const params = {};
    if (fontSize !== undefined) {
      params.preFontSize = fontSize;
    }
    if (screen !== undefined) {
      params.preScreen = screen;
    }
    setPageStyle(params);
  };

  const notSafe = useMemo(() => {
    const isHighSecurity = tags?.some((item) => (item.sourceType === 'SYSTEM_SECURE' && item.name > 3));

    return isHighSecurity;
  }, [tags]);

  useEffect(() => {
    const isInGray = window.editorIframeABSwitch;

    window.showxiaodi = isInGray && !notSafe;
  }, [window.editorIframeABSwitch, notSafe]);

  useEffect(() => {
    getPagePerm();
  }, [uniqueId]);

  const getPagePerm = async () => {
    try {
      let res = await getIdsFormShare({ shareId });

      let pageIdParam = isDkShare ? pageId : res.pageId;
      await getPagePermission({ pageId: pageIdParam }).then((permission) => {
        setPagePerm(permission.perm);
      });
    } catch (error) {
      // do nothing
    }
  };

  const hasToNormalEditPerm = useMemo(() => {
    return checkOperationPermission('MANAGE_PAGE_CONTEXT', pagePerm);
  }, [pagePerm]);

  const editNavigate = () => {
    let searchParams = new URLSearchParams(search);
    searchParams.append('from', 'share');
    if (isDkShare) {
      window.__OmegaEvent('ep_dkpc_sharebook_edit_ck');
      window.location.href = `/knowledge/${
        normalIds.metisId
      }/${pageId}/edit?${searchParams?.toString()}`;
    } else {
      window.__OmegaEvent('ep_dkpc_sharepage_edit_ck');
      window.location.href = `/knowledge/${normalIds.metisId}/${
        normalIds.pageId
      }/edit?${searchParams?.toString()}`;
    }
  }

  const convertNext = async () => {
    try {
      await convertWikiToDidoc({
        pageId: normalIds.pageId,
        guid,
        exteralSystemType: 'html',
        toExteralSystemType: 'didoc',
      });
      // 关闭弹出层
      setShowConvert(false);
      editNavigate();
    } catch (error) {
      setConvertError(true);
    }
  }

  const goToNormalPage = async () => {
    if (isWikiHtml) {
      setShowConvert(true);
    } else {
      editNavigate();
    }
  };

  return (
    <div className={styles.header}>
      <div className={('left-section')}>
        <Navigation />
      </div>
      <div className={('right-section')}>
        {entryEnhance(isDkShare && (
          <div className={('search-content')}>
            <GlobalSearch
              knowledge={{ id: shareId, name: pageName, isShare: true }}
              openSearchModal={() => {}}
              setClear={() => {}}
            />
          </div>
        ))}

        {hasToNormalEditPerm && (
          <Button
            className={('edit-btn')}
            onClick={goToNormalPage}>
            {intl.t('编辑')}
          </Button>
        )}

        {
          isSheet || (
            <Star
              docInfo={docInfo}
              source={isDkShare ? 'DK_SHARE' : 'PAGE_SHARE'}
              starInfo={ _starInfo }
            />
          )
        }
        {
          showPageStyle && (
            <PageStyle
              title={intl.t('阅读样式')}
              tips={intl.t('该样式仅供阅读，不影响页面设置，刷新后还原。')}
              pageStyle={prePageStyle}
              changePageStyle={changePageStyle}
            />
          )
        }
        <ConvertWikiToDidocModal
          visible={showConvert}
          convertError={convertError}
          next={convertNext}
          close={() => setShowConvert(false)}
          retry={goToNormalPage}
        />
      </div>
    </div>
  );
}

export default Header;
