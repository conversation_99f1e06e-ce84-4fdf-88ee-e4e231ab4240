/*
 * @Date: 2023-11-16 12:00:36
 * @LastEditors: guanzhong
 * @LastEditTime: 2023-11-16 12:08:44
 * @FilePath: /knowledgeforge/src/pages/knowledge/ShareBook/SharePageDetail/index.js
 * @Description: 描述文件功能
 */
import ShimoExcelShare from '@/components/ShimoExcelShare';
import { Spin } from 'antd';
import { Helmet } from 'react-helmet';
import classNames from 'classnames/bind';
import { useEffect, useMemo, useContext } from 'react';
import { connect, useSelector } from 'react-redux';
import { inPhone, isDkSheet, isPreviewFile, isAndroid, isIpad } from '@/utils';
import Editor from './Editor';
import Authority from './Authority';
import SharePreview from '@/components/serviceComponents/SharePreview';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { getIsWikiHTML } from '@/constants/wikihtmlrenderHelper';
import WikiHTMLRender from './Wiki';
import ErrorBoundary from '@/components/ErrorBoundary';
import * as styles from '@/pages/knowledge/PageDetail/style.module.less';
import { getDcVersion, isBeforeVersion } from '@/utils/dc-helper';

const cls = classNames.bind(styles);

const isInPhone = inPhone();
const isInAndroid = isAndroid();
function SharePageDetail(props) {
  const { docInfo, getPageTags } = props;
  const { latestVersion, docType, pageName, pageStyle = {}} = docInfo;
  const { initLoading } = props;
  const isPreview = isPreviewFile(docType);
  const isSheet = isDkSheet(docType);
  const { shareId, pageId } = useContext(LayoutContext);
  const { shareModalSetting, normalIds } = useSelector((state) => state.SharePage);
  const isWikiHTML = getIsWikiHTML(docInfo);


  useEffect(() => {
    if (!normalIds.pageId) return;
    getPageTags({ pageId: normalIds.pageId });
  }, [normalIds]);


  // 0 不可下载；1可下载
  const hasCommentPerm = useMemo(() => {
    return shareModalSetting?.sharePermList?.comment === 1;
  }, [shareModalSetting]);

  const hasDownloadPerm = useMemo(() => {
    return shareModalSetting?.sharePermList?.download === 1;
  }, [shareModalSetting])


  const needHeader = useMemo(() => {
    return !isInPhone;
  }, []);

  const getDocView = () => {
    if (isPreview) {
      return <SharePreview
        fileId={latestVersion}
        fileName={pageName}
        pageId={docInfo.pageId}
        shareId={shareId}
        isDkShare={!!pageId}
      />
    }
    if (isWikiHTML) return <WikiHTMLRender />;
    if (isSheet) return <ShimoExcelShare/>;
    return <>
      <Editor
        hasDownloadPerm={hasDownloadPerm}
        hasCommentPerm={hasCommentPerm}
      />
    </>;
  }

  // 手机端，隐藏底部导航栏
  useEffect(() => {
    if(isInPhone && !isIpad()) {
      const dcVersion = getDcVersion();
      if(!isBeforeVersion(dcVersion, '4.12.0')) {
        window.dcH5Sdk.app.appSetOrientation({
          orientation: 'all',
          onSuccess: () => console.log('appSetOrientation is success!!'),
          onFail: () => {},
        });
        if(!isInAndroid) {
          window.dcH5Sdk.event.on("orientationChange", (data)=>{
            if(data.orientation === 'portrait') {
              window.dcH5Sdk.navigation.hideToolBar({
                visibility: true,
                onSuccess: () => {},
                onFail : () => {}
              });
            }else {
              window.dcH5Sdk.navigation.hideToolBar({
                visibility: false,
                onSuccess:() => {},
                onFail: () => {}
              });
            }
          })
        }
      }
    }
  }, [])

  return (
    <div
      className={cls(
        'page-detail',
        'page-style-root',
        `font-size-${pageStyle?.preFontSize}`, `screen-${pageStyle?.preScreen}`,
        {
          'page-detail-inphone': isInPhone,
        },
      )
      }
    >
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/ico-dbook.png" />
      </Helmet>
      <Authority>
        <div
          className={cls('page-content', 'page-content-book', {
            'no-header': !needHeader,
            'dk-editor-in-phone_reset': isInPhone,
          })}>
          {
            !isPreview && initLoading && (
              <div className={'page-detail-loading'}>
                <Spin />
              </div>
            )
          }
          <ErrorBoundary>
            { getDocView() }
          </ErrorBoundary>

        </div>
      </Authority>
    </div>
  );
}

function mapStateToProps({ SharePage }) {
  const { initLoading, docInfo } = SharePage;
  return {
    initLoading,
    docInfo,
  };
}


function mapDispatchToProps({ SharePage }) {
  const { getSharePageDetail, changeInitLoading, getPageTags } = SharePage;
  return {
    getSharePageDetail,
    changeInitLoading,
    getPageTags,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(SharePageDetail);
