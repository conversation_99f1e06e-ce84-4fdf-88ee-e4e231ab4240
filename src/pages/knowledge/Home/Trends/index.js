import { intl } from 'di18n-react';
import { useEffect, useContext, useRef, useState } from 'react';
import classBind from 'classnames/bind';
import { Avatar, List, Tooltip } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { computeTimeInterval } from '@/utils/index';
import { getTrendData } from '@/service/knowledge/home';
import { TrendEmptyTagIcon } from '@/assets/icon';
import InfiniteScroll from 'react-infinite-scroller';
import SpinRender from '@/components/SpinRender';
import { PAGE_SIZE } from '@/constants';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const Trends = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [code, setCode] = useState();
  const navigate = useNavigate();
  const { knowledgeId, setPageInfo } = useContext(LayoutContext);
  const { teamId } = useParams();

  const getTrendList = async (pageNum = 0, initData) => {
    let currentData = initData ?? data;

    setLoading(true);
    let res = await getTrendData(knowledgeId, {
      pageNum,
      pageSize: PAGE_SIZE,
      code,
    });

    if (res.list.length < 10) {
      setHasMore(false);
    }
    setData([...currentData, ...res.list]);
    setCode(res.code);
    setLoading(false);
  };
  const onDragStart = (event, { operateContent }) => {
    event.dataTransfer.setData('livechatDocInfo', JSON.stringify({ resourceId: operateContent.id, title: operateContent.name }))
    console.log('*** onDragStart', data);
  };
  useEffect(() => {
    setHasMore(true);
    setCode();
    getTrendList(0, []);
  }, [knowledgeId]);
  return (
    <div className={cx('trends-page-wrap')}>
      {!!data.length && (
        <h1 className={cx('trends-page-title')}>{intl.t('动态')}</h1>
      )}
      <InfiniteScroll
        initialLoad={false}
        pageStart={0}
        loadMore={getTrendList}
        hasMore={!loading && hasMore}
        useWindow={false}
        getScrollParent={() => document.querySelector('#knowedge-body-content')}
      >
        {data.map((item, index) => (
          <li
            key={index}
            draggable={'true'}
            onDragStart={(event) => onDragStart(event, item)}
            onClick={() => {
              setPageInfo({});
              navigate(
                teamId
                  ? `/team-file/${teamId}/knowledge/${item.knowledgeId}/${item.operateContent.id}`
                  : `/knowledge/${item.knowledgeId}/${item.operateContent.id}`,
              );
              window.__OmegaEvent('ep_dkpc_dynamic_pageview_ck');
            }}
            className={cx('trend-item')}
          >
            <div className={cx('trend-operator')}>
              <p>
                <Avatar
                  className={cx('avatar')}
                  alt=""
                  src={item.operator?.avatar}
                />
                <Tooltip title={`${item.operator.ldap}@didiglobal.com`}>
                  <span className={cx('trend-operator-name')}>
                    {item.operator.name.chineseName}{' '}
                  </span>
                </Tooltip>
                <span className={cx('trend-operator-type')}>
                  {item.operateInfo}
                </span>
              </p>
              <span className={cx('trend-time')}>
                {computeTimeInterval(item.operateTime)}
              </span>
            </div>
            <Tooltip
              placement="topLeft"
              title={
                item.operateContent.name.length > 0
                  ? item.operateContent.name
                  : undefined
              }
            >
              <p className={cx('trend-title')}>{item.operateContent.name}</p>
            </Tooltip>
            <div className={cx('trend-content')}>
              {item.operateContent.briefInfo}
            </div>
          </li>
        ))}
      </InfiniteScroll>
      <SpinRender loading={loading} />
      {!hasMore && !loading && (
        <div className={cx('trend-empty')}>
          {data.length ? (
            intl.t('暂无更多动态')
          ) : (
            <>
              <img
                src={TrendEmptyTagIcon}
                style={{ width: 240 }} />
              <p style={{ color: '#333', marginTop: 10 }}>
                {intl.t('暂无动态')}
              </p>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default Trends;
