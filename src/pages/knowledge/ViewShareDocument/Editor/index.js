import { useEffect, useMemo, useRef } from 'react';
import { connect } from 'react-redux';
import { MrParcel } from '@didi/mf-tenon';
import classnames from 'classnames/bind';
import { EditorConfig, EditorMicroUrl, EditorName } from '@/constants/editor';
import * as styles from './style.module.less';

const cls = classnames.bind(styles);

window.msContainer = true;
function Editor(props) {
  const {
    docInfo,
    changeInitLoading,
    profile,
    changeEditReady,
    editor<PERSON>ey,
    hasDownloadPerm,
  } = props;
  const { guid, accessToken, latestVersion, pageId } = docInfo;
  const userInfo = {
    username: profile.username,
    username_zh: profile.username_zh,
    avatar: profile.avatar,
  };
  const editorConfig = {
    ...EditorConfig,
    eventHandler: {
      init: (state, view) => {
        changeInitLoading(false);
        changeEditReady(true);
        if (document.querySelector('.page-detail-loading')) document.querySelector('.page-detail-loading').style.zIndex = -1;
      },
    },
    app: {
      sourceId: guid,
      accessToken,
      sourceType: '6',
      content: '',
      dataLocation: 'self',
      version: latestVersion,
    },
    placeholder: {
      content: '',
      switch: true,
    },
    editable: false,
    user: {
      ...userInfo,
      nameCn: userInfo.username_zh,
    },
    filePermission: {
      download: hasDownloadPerm,
    },
    pageId,
    comment: {
      show: false,
      editable: false,
    },
    dictionary: {
      show: false,
      editable: false,
    },
    draggable: false,
    title: false,
    menu: {},
    collab: {
      collabUser: false,
      collabUnderLine: false,
      collabCursor: false,
    },
  };

  const editorShow = useMemo(() => {
    return guid && userInfo.username && latestVersion;
  }, [guid, profile, latestVersion]);


  useEffect(() => {
    if (docInfo.pageName) {
      window.document.title = docInfo.pageName;
    }
  }, [docInfo.pageName]);

  useEffect(() => {
    return () => {
      // 页面切换时卸载编辑器
      const TenonEditor = window[EditorName];
      if (TenonEditor && typeof TenonEditor.unmount === 'function') { // 监控到白屏报错做修复
        TenonEditor.unmount();
      }
      changeEditReady(false);
    };
  }, []);

  return (
    <div
      id='knowledge_editor_box'
      className={cls('editor')}
    >
      <div
        className={cls('editor-container')}
      >
        {
          editorShow && <MrParcel
            key={editorKey}
            entry={EditorMicroUrl}
            name={EditorName}
            appProps={editorConfig}
          />
        }
      </div>
    </div>
  );
}

function mapStateToProps({ SharePage, CooperIndex }) {
  const { docInfo, editReady, editorKey } = SharePage;
  const { profile } = CooperIndex;
  return {
    docInfo,
    profile,
    editReady,
    editorKey,
  };
}

function mapDispatchToProps({ SharePage }) {
  const {
    changeInitLoading,
    changeEditReady,
  } = SharePage;
  return {
    changeInitLoading,
    changeEditReady,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Editor);
