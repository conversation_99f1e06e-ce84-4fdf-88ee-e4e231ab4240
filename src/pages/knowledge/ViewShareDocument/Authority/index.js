import classNames from 'classnames/bind';
import { useEffect, useState, useMemo, useContext } from 'react';
import { connect } from 'react-redux';
import { PageNotPublishIcon } from '@/assets/icon';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import ErrorTips from '@/components/ErrorTips';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import ExpireLink from '@/pages/knowledge/ExpireLink';
import * as styles from '@/pages/knowledge/PageDetail/style.module.less';
import { getUrlParams } from '@/utils/index';

const cls = classNames.bind(styles);

function Authority(props) {
  const { children, docInfo = {}} = props;
  const { pageId, shareId } = useContext(LayoutContext);
  const [isDelete, setIsDelete] = useState(null);
  const notification = useNotification();
  const { getSharePageDetail, changeInitLoading } = props;
  const { latestVersion } = docInfo;


  let uniqueId = pageId || shareId;

  useEffect(() => {
    changeInitLoading(true);
    let baseId = getUrlParams('base');
    getSharePageDetail({ pageId, shareId, baseId }).then((docInfoNew) => {
      if (!docInfoNew.latestVersion) {
        changeInitLoading(false);
      }
    }).catch((errRes) => {
      if (!errRes.errorMessage) return;
      if (errRes.errorCode === 201003 || errRes.errorCode === 501003) {
        // 显示删除内容
        setIsDelete(true);
      } else {
        notification(NotificationStatus.ERROR, errRes.errorMessage || errRes.errorType);
      }
    })
  }, [uniqueId]);

  const isReleased = useMemo(() => {
    if (docInfo.latestVersion === undefined) return null;
    return !!latestVersion;
  }, [latestVersion]);

  return (
    <div className={cls('authority')}>
      {
        isDelete && <ExpireLink />
      }
      {
        !isDelete && (
          <>
            {
              isReleased && (
                <>
                  {children}
                </>
              )
            }
            {
              isReleased === false
              && <ErrorTips
                img={PageNotPublishIcon}
                title={<div className={cls('no-permission-tip')}>
                  此页面暂未发布，暂不可见
                </div>} />

            }
          </>
        )
      }
    </div>
  );
}

function mapStateToProps({ SharePage }) {
  const { initLoading, docInfo, shareModalSetting } = SharePage;
  return {
    initLoading,
    docInfo,
    shareModalSetting,
  };
}


function mapDispatchToProps({ SharePage }) {
  const { getSharePageDetail, changeInitLoading } = SharePage;
  return {
    getSharePageDetail,
    changeInitLoading,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Authority);
