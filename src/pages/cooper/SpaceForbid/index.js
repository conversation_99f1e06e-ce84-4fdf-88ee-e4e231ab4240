/*
 * @Date: 2024-02-28 17:03:05
 * @LastEditors: guanzhong <EMAIL>
 * @LastEditTime: 2024-04-12 11:11:20
 * @FilePath: /knowledgeforge/src/pages/cooper/SpaceForbid/index.js
 * @Description: 空间因为跨租户不可访问页面
 */
import classBind from 'classnames/bind';
import * as styles from './style.module.less';
import CooperHeaderOld from '@/components/common/OldGlobalHeader';
import ForbidUi from './Forbid-ui/index'
import CopyRight from '@/components/common/CopyRight'
import { Helmet } from 'react-helmet';


const cx = classBind.bind(styles);


const SpaceForbid = () => {
  return (
    <div className={cx('spaceInvalid')}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
      </Helmet>
      <CooperHeaderOld />
      <ForbidUi />
      <CopyRight />
    </div>
  )
};

// routes: /team-file/team-invalid
export default SpaceForbid;
