/* eslint-disable camelcase */
/*
 * @Date: 2024-03-19 17:08:34
 * @LastEditors: guanzhong <EMAIL>
 * @LastEditTime: 2024-04-23 17:25:25
 * @FilePath: /knowledgeforge/src/pages/cooper/SpaceForbid/Forbid-ui/index.js
 * @Description: 空间失效的展示UI
 */
import classBind from 'classnames/bind';
import * as styles from './style.module.less';
import { SpaceInvalid } from '@/assets/icon/index'
import { intl, getLocale } from 'di18n-react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { isDiDiTenant } from '@/utils/entryEnhance';

const cx = classBind.bind(styles);

const ForbidUi = () => {
  const { type } = useParams()
  const { profile } = useSelector((state) => state.CooperIndex);
  const { tenantName, username, username_zh } = profile;
  return (
    <div className={cx('invalid-ui')}>
      <img src={SpaceInvalid} />
      <div className={cx('right-div')}>
        <p className={cx('title')}>{intl.t('暂无权限访问')}</p>
        <p className={cx('desc')}>
          {intl.t('当前登录账号为')}
          <span className={cx('name')}>
            {getLocale() === 'en-US' ? username : username_zh}
            {`（${tenantName}）`}，
          </span>
          { intl.t('由于该空间的所有者未开通对外分享能力，你无法申请权限')}
        </p>
        {
          type === 'space' && <p className={cx('tip')}>{intl.t('你可以联系分享者邀请你成为成员')}</p>
        }
        {
          type === 'file' && isDiDiTenant() && <p className={cx('tip')}>{intl.t('你可以联系分享者开启空间对外分享能力后获得有效的分享链接')}</p>
        }
      </div>
    </div>
  )
}

export default ForbidUi
