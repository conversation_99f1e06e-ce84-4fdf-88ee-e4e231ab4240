/* eslint-disable camelcase */
import saveFile from '@/components/CooperOperation/SaveFile';
import CooperPreview from '@/components/FilePreview';
import Table from '@/components/common/CooperTable';
import { Button, Divider, Input, Modal, Tooltip, message } from 'antd';
import Base64 from 'crypto-js/enc-base64';
import sha256 from 'crypto-js/sha256';
import dayjs from 'dayjs';
import { intl, getLocale } from 'di18n-react';
import React from 'react';
import { fileTxt, folder } from '@/assets/icon/fileIcon';
import { AvatarIcon, SpaceInvalid } from '@/assets/icon/index';
import CooperHeaderOld from '@/components/common/OldGlobalHeader';
import { Coop, DiDoc, Dir, FlowChart, POFile } from '@/constants/cooperConstants';
import { Helmet } from 'react-helmet';
import { getType } from '@/utils/type';
import {
  blurTime,
  deepCopy,
  formatSize,
  noopenerOpen,
} from '@/utils/cooperutils';
import { dealDownloadRes } from '@/utils/file';
import pathUtil from '@/utils/path';
import api from '@/utils/request/api/CooperApi';
import { get, post } from '@/utils/request/cooper';
import classBind from 'classnames/bind';
import * as styles from './style.module.less';
import { columns } from './table.conf';
import CopyRight from '@/components/common/CopyRight'
import withRouter from '@/hooks/withRouter';
import { isDiDiTenant } from '@/utils/entryEnhance';
import { SHARE_AUTH } from '@/components/OperateMenu/constant'

const folderIcon = folder;
const fileIcon = fileTxt;
const defaultAvatar = AvatarIcon

const cx = classBind.bind(styles);


function InvalidShare() {
  return (
    <div className={cx('invalid-share')}>
      <div className={cx('invalid-share-box')}>
        <img
          src={require('./empty-box.png')}
          className={cx('invalid-img')}
        />
        <div className={cx('invalid-text')}>
          {intl.t('链接地址已经失效')}
        </div>
        <div className={cx('invalid-text')}>
          {intl.t('请联系该链接的分享者')}
        </div>
      </div>
    </div>
  );
}

function LimitedShare({ user, avatar, code, changeCode, checkCode, userInfo }) {
  const { orgName, name, nameCN } = userInfo;
  return (
    <div className={cx('limited-share')}>
      <div className={cx('panel')}>
        {
          !isDiDiTenant() && <div className={cx('panel-top')}>
            <img
              src={SpaceInvalid}
              className={cx('panel-top-img')} />
            <p className={cx('panel-top-title')}>{intl.t('暂无权限访问')}</p>
            <p className={cx('panel-top-text')}>{intl.t('当前登录账号为')}
              <span className={cx('panel-top-text-name')}> {getLocale() === 'en-US' ? name : nameCN}
                {`（${orgName}）`}</span>，
              {intl.t('由于文档所在空间的所有者未开通对外分享能力，你可以')}
            </p>
          </div>
        }
        <div className={cx('body')}>
          {
            isDiDiTenant() && <span className={cx('body-title')}>
              <img src={avatar} />
              <span className={cx('body-title-name')}> {user}</span>
              {intl.t('给您分享加密文件')}
            </span>
          }
          <div className={cx('forms')}>
            <p className={cx('forms-label')}>
              {intl.t('输入密码')}:
            </p>
            <p className={cx('forms-div')}>
              <Input
                type='password'
                value={code}
                onChange={(e) => changeCode(e.target.value)}
                placeholder={intl.t('请输入密码')}
                autocomplete="new-password"
                onPressEnter={() => code.length > 0 && checkCode()}
            />
              <button
                type='button'
                onClick={() => code.length > 0 && checkCode()}
                className={cx({
                  'ant-btn': true,
                  'ant-btn-primary': true,
                  'ant-btn-disabled': code.length === 0,
                })}
            >
                <span>{intl.t(' 确定')}</span>
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function ValidShare({
  setSelectedFiles,
  selectedRowKeys,
  bread,
  info,
  files,
  previewFile,
  downloadChecked,
  downloading,
  clickSaveChecked,
  goToLink,
  goToPath,
  isCanDownload,
}) {
  for (let f of files) {
    f.key = f.id;
  }
  return (
    <div className={cx('valid-share')}>
      <div className={cx('head')}>
        <div className={cx('desc')}>
          <div className={cx('title-container')}>
            <div className={cx('title-container-title')}>
              <img
                src={
                  info.isFolder
                    ? info.linkImage || folderIcon
                    : info.linkImage || fileIcon
                }
              />

              <p>{info.name}</p>
            </div>
            <div className={cx('title-container-btn')}>
              <Button
                className={cx('save')}
                onClick={() => clickSaveChecked(isCanDownload)}>
                {intl.t('保存到空间')}
              </Button>
              <Button
                className={cx('downloads')}
                type='primary'
                onClick={() => downloadChecked(isCanDownload)}
                loading={downloading}
              >
                {intl.t('下载')}
              </Button>
              {/* {
                isCanDownload ? <Button
                  className={cx('downloads')}
                  type='primary'
                  onClick={downloadChecked}
                  loading={downloading}
              >
                  {intl.t('下载')}
                </Button> : <Popover
                  content={() => getIsCanDownloadContent(info)}
                  placement='bottom'
                  >
                  <Button
                    className={cx('downloads')}
                    type='primary'
                >
                    {intl.t('下载')}
                  </Button>
                </Popover>
              } */}
            </div>
          </div>
          <div className={cx('info-container')}>
            <span>
              {intl.t('分享人')}：{info.shareFrom}
            </span>
            <span>
              {intl.t('分享时间')}：{info.shareTime}
            </span>
            <span>
              {intl.t('失效时间')}：{info.endTime}
            </span>
          </div>
        </div>
      </div>
      <div className={cx('body')}>
        <div className={cx('bread-menu')}>
          {bread.map((b, idx, arr) => {
            if (idx < arr.length - 1) {
              return (
                <span key={b.id}>
                  <Tooltip
                    title={b.name}
                    placement='top'>
                    <a
                      href='#'
                      className={cx('sub-path')}
                      onClick={(e) => goToLink(e, b)}
                    >
                      {b.name}
                    </a>
                  </Tooltip>

                  <img
                    className={cx('sub-img')}
                    src={require('./icon/arror.png')} />
                </span>
              );
            }

            return null;
          })}
          <span className={cx('sub-path')}>
            <Tooltip
              title={bread[bread.length - 1].name}
              placement='top'>
              {bread[bread.length - 1].name}

            </Tooltip>
          </span>
        </div>
        <Table
          rowSelection={{
            onChange: (_, fileItems) => {
              setSelectedFiles(fileItems);
            },
            selectedRowKeys,
          }}
          columns={columns({
            goToPath,
            previewFile,
            selectedRowKeys,
          })}
          dataSource={files}
          pagination={false}
          className={cx('cooper-file-table')}
        />
      </div>
    </div>
  );
}

class CooperShareDownload extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      type: '',
      loaded: false,
      // 文件类型、文件名、分享人、分享时间、失效时间
      info: {
        isFolder: false,
        name: '',
        cnName: '',
        avatar: '',
        shareFrom: '',
        beginTime: '',
        endTime: '',
      },

      // 分享人中文名、头像和链接密码
      user: '',
      avatar: '',
      code: '',
      files: [
        // { checked: false, id: 1, type: 1, name: 't.txt', size: '1KB', updateTime: '2018-03-30 14:29' }
      ],

      // 勾选的文件
      selectedFiles: [],

      bread: [
        {
          id: 0,
          name: intl.t('全部文件'),
        },
      ],

      preview: false,
      downloading: false,
      isCanDownload: true,
    };
    // shared link id

    this.linkId = '';
    this.shareId = 0;
    this.shareType = 'Link'
    this.fileList = [];
    this.fileIdx = 0;
  }

  componentDidMount() {
    // eslint-disable-next-line prefer-destructuring
    this.linkId = window.location.pathname.split('/').reverse()[0]; // 尝试直接获取文件列表

    get(api.SHARE_DIRECTORY.replace(':shareLink', this.linkId).replace(
      ':resourceId',
      0,
    )).then((data) => {
      // 901043密码错误 900013 需要密码 901034 链接过期 901035 链接无效 901036 所在空间非内外协作，不可访问 901037 存在其他租户账号可以访问
      if (data.code === 0) {
        this.initShare(data);
      } else if (data?.errorCodes?.includes(900013)) {
        const d = data;
        this.setState({
          type: 'limited-share',
          user: d.cn,
          userInfo: d?.currentAccount,
          avatar: d.avatar || defaultAvatar,
        });
      } else if (data?.errorCodes?.includes(901035) || data.code === 901035) {
        this.setState({
          type: 'invalid-share',
        });
      } else if (data?.errorCodes?.includes(901036)) {
        this.props.navigate('/team-file/team-forbid/file')
      } else if (data?.errorCodes?.includes(901043)) {
        message.error(intl.t('密码错误'));
      } else if (data?.errorCodes?.includes(901037)) {
        console.log('其他账户可以访问、可以切换账号访问');
      } else {
        this.setState({
          type: data.message,
        });
      }

      this.setState({
        loaded: true,
      });
    });
  }

  initShare = (share) => {
    const m = share.meta;
    this.shareId = m.share_id;
    let endTime = '';
    if (new Date(m.expiration_time) - new Date(m.share_time) < 0) {
      endTime = intl.t('永久有效');
    } else {
      endTime = dayjs(new Date(m.expiration_time)).format('YYYY-MM-DD HH:mm:ss');
    }

    this.setState({
      type: 'valid-share',
      info: {
        isFolder: m.type !== 0,
        name: m.name,
        cnName: m.from_cn,
        avatar: m.from_avatar,
        linkImage: m.link_image,
        shareFrom: m.from,
        shareTime: dayjs(new Date(m.share_time)).format('YYYY-MM-DD HH:mm'),
        endTime,
      },
    });

    const items = share.items || [];
    items.sort((a, b) => {
      const timeA = new Date(a.modify_time).getTime();
      const timeB = new Date(b.modify_time).getTime();
      if (timeA > timeB) return -1;
      if (timeA < timeB) return 1;
      return 0;
    });
    const files = items.map((f) => ({
      ...f,
      checked: false,
      id: f.id,
      type: f.type,
      name: f.display_name,
      teamId: f.team,
      icon: f.tiny_image,
      size: formatSize(f.size),
      updateTime: blurTime(f.modify_time),
    }));

    this.setState({
      files,
      isCanDownload: !m.read_only,
    });
  };

  changeCode = (value) => {
    this.setState({
      code: value,
    });
  };

  checkCode = async () => {
    if (!this.state.code) {
      message.error(intl.t('密码不能为空'));
      return;
    }

    let data = {};
    try {
      data = await get(
        api.SHARE_DIRECTORY.replace(':shareLink', this.linkId).replace(
          ':resourceId',
          0,
        ),
        {
          params: {
            password: this.state.code,
          },
        },

        {
          keepSilent: true,
        },
      );
    } catch (e) {
      // 兼容之前的 sha256+base64 形式密码
      data = await get(
        api.SHARE_DIRECTORY.replace(':shareLink', this.linkId).replace(
          ':resourceId',
          0,
        ),
        {
          params: {
            password: Base64.stringify(sha256(this.state.code)),
          },
        },
      );
    }
    if (data.code === 0) {
      this.initShare(data);
    } else if (data.errorCodes.includes(900013)) {
      const d = data;
      this.setState({
        type: 'limited-share',
        user: d.cn,
        userInfo: d?.currentAccount,
        avatar: d.avatar || defaultAvatar,
      });
    } else if (data.errorCodes.includes(900035)) {
      this.setState({
        type: 'invalid-share',
      });
    } else if (data.errorCodes.includes(900036)) {
      navigator.push('/team-file/team-invalid')
    } else if (data.errorCodes.includes(901043)) {
      message.error(intl.t('密码错误'));
    } else {
      console.log('未定义的错误');
    }
    // this.setState({
    //   type: 'valid-share',
    // });
    // console.log(data);
    // debugger
    // this.initShare(data);
  };

  download = (files) => {
    const connectSiteId = window.__connectSiteId || 0
    this.setState({ downloading: true });
    const ids = files.map((file) => file.id);
    let url = `${api.API_LINK_DOWNLOAD.replace(':linkId', this.shareId)}?region=${connectSiteId}`;
    post(url, ids).then((res) => {
      dealDownloadRes(res);
    }).catch((err) => {
      console.log(err, 'err');
    }).finally(() => {
      this.setState({ downloading: false });
    });
  }

  previewFile = (e, id, name, idx, resource_type) => {
    const params = {
      resourceid: id,
      resource_id: id,
      position: idx,
    }
    const isType = ['docs', 'sheets', 'slides', 'flowchart', 'xmind', 'files', 'pages'].includes(getType(resource_type))
    if (isType) {
      params.type = getType(resource_type)
    }
    window.__OmegaEvent('ep_share_visit_ck', '', {
      ...params
    });
    e && e.preventDefault(); // eslint-disable-line no-unused-expressions

    this.showPreview({
      id,
      display_name: name,
    });
  };

  downloadChecked = (isCanDownload) => {
    let { selectedFiles } = this.state;

    if (selectedFiles.length === 0) {
      message.error(intl.t('未选择任何文件'));
      return;
    }
    if (!isCanDownload) {
      Modal.warning({
        width: 416,
        title: intl.t('暂无权限'),
        content: <div className={cx('no-auth-modal-content')}>
          <div className={cx('no-auth-modal-text')}>
            {intl.t('如需操作可联系分享者')}
            <a
              className={cx('no-auth-modal-text-name')}
              target='_blank'
              href={`dchat://im/start_conversation?name=${this.state.info.shareFrom}`} >
              {getLocale() === 'zh-CN' ? this.state.info.cnName : this.state.info.shareFrom }
            </a>
            {getLocale() === 'zh-CN' ? intl.t('授予') : ''}
            <span className={cx('no-auth-modal-text-auth')}>
              {intl.t('下载')}
            </span>
            {intl.t('权限')}
          </div>
          <Divider style={{ marginTop: '12px', marginBottom: '12px' }}/>
          <a
            className={cx('no-auth-modal-link')}
            href={SHARE_AUTH.link}
            target='_blank'>
            {intl.t(SHARE_AUTH.text)}
          </a>
        </div>,
        okText: intl.t('我知道了'),
        onOk: () => {
          // Noop
        },
      });
      return;
    }
    this.download(selectedFiles);
    window.__OmegaEvent('ep_download_sharelink_ck');
  };

  clickSaveChecked = (isCanDownload) => {
    const { selectedFiles } = this.state;

    if (selectedFiles.length === 0) {
      message.error(intl.t('未选择任何文件'));
      return;
    }
    if (!isCanDownload) {
      Modal.warning({
        width: 416,
        title: intl.t('暂无权限'),
        content: <div className={cx('no-auth-modal-content')}>
          <div className={cx('no-auth-modal-text')}>
            {intl.t('如需操作可联系分享者')}
            <a
              className={cx('no-auth-modal-text-name')}
              target='_blank'
              href={`dchat://im/start_conversation?name=${this.state.info.shareFrom}`} >
              {getLocale() === 'zh-CN' ? this.state.info.cnName : this.state.info.shareFrom }
            </a>
            {getLocale() === 'zh-CN' ? intl.t('授予') : ''}
            <span className={cx('no-auth-modal-text-auth')}>
              {intl.t('下载')}
            </span>
            {intl.t('权限')}
          </div>
          <Divider style={{ marginTop: '12px', marginBottom: '12px' }}/>
          <a
            className={cx('no-auth-modal-link')}
            href={SHARE_AUTH.link}
            target='_blank'>
            {intl.t(SHARE_AUTH.text)}
          </a>
        </div>,
        okText: intl.t('我知道了'),
        onOk: () => {
          // Noop
        },
      });
      return;
    }
    const files = selectedFiles.map((file) => ({ ...file, shareId: this.shareId, shareType: 2 }));
    saveFile(files, files.length > 1);
    window.__OmegaEvent('ep_sharelink_savetodisk_ck');
  };

  goToPath = ({ type, mime_type, id, name, resource_type }, idx) => { // eslint-disable-line camelcase
    const params = {
      resourceid: id,
      resource_id: id,
      position: idx,
    }
    const isType = ['docs', 'sheets', 'slides', 'flowchart', 'xmind', 'files', 'pages'].includes(getType(resource_type))
    if (isType) {
      params.type = getType(resource_type)
    }
    window.__OmegaEvent('ep_share_visit_ck', '', {
      ...params
    });

    if (type === Coop || type === POFile) {
      noopenerOpen(pathUtil.getCoopPath(id, mime_type));
    }

    if (type === DiDoc) {
      noopenerOpen(pathUtil.getDiDocPath(id));
    }

    if (type === FlowChart) {
      pathUtil.getFlowChart(id)
        .then((url) => {
          noopenerOpen(url);
        });
    }

    if (type === Dir) {
      this.goToLink(null, {
        id,
        name,
      });
    }
  };

  goToLink = (e, { id, name }) => {
    e && e.preventDefault(); // eslint-disable-line no-unused-expressions

    get(api.SHARE_DIRECTORY.replace(':shareLink', this.linkId).replace(
      ':resourceId',
      id,
    )).then((data) => {
      const cp = deepCopy(this.state.bread);
      let i;

      for (i = 0; i < cp.length; i++) {
        if (cp[i].id === id) {
          break;
        }
      }

      if (i < cp.length) cp.splice(i + 1);
      else {
        cp.push({
          id,
          name,
        });
      }

      this.setState({
        bread: cp,
      });

      this.setSelectedNull();
      this.initShare(data);
    });
  }; // 重置勾选item为，无选中

  setSelectedNull = () => {
    this.setState({
      selectedFiles: [],
    });
  };

  showPreview = (record) => {
    for (let i = 0; i < this.fileList.length; i++) {
      if (this.fileList[i].name === record.display_name) {
        this.fileIdx = i;
      }
    }
    // if (isVideo(record.display_name)) {
    //   openVideoPreview(record.resource_id || record.id, this.shareId, 'link');
    //   return;
    // }
    this.setState({
      preview: true,
    });
  };

  updateShareFiles = (file) => {
    let updateFiles = this.state.files.map((item) => {
      if (item.id === file.id) {
        return {
          ...item,
          marked_quick_visit: !item.marked_quick_visit,
        };
      }
      return item;
    })
    this.setState({
      files: updateFiles,
    })
  };

  closePreview = () => this.setState({
    preview: false,
  });

  setSelectedFiles = (selectedFiles) => {
    this.setState({ selectedFiles });
  }

  render() {
    const {
      bread,
      info,
      files,
      code,
      user,
      avatar,
      loaded,
      type,
      preview,
      downloading,
      selectedFiles,
      userInfo,
    } = this.state;

    this.fileList = [];

    for (const d of files) {
      this.fileList.push({
        ...d,
        id: d.id,
        name: d.name,
        size: d.size,
        shareId: this.shareId,
        shareType: this.shareType,
        objectType: d.resource_type,
      });
    }

    if (!loaded) {
      return null;
    }

    let renderSharePage;

    switch (type) {
      case 'invalid-share':
        renderSharePage = <InvalidShare />;
        break;

      case 'limited-share':
        renderSharePage = (
          <LimitedShare
            user={user}
            userInfo={userInfo}
            avatar={avatar}
            code={code}
            changeCode={this.changeCode}
            checkCode={this.checkCode}
          />
        );
        break;

      case 'valid-share':
        renderSharePage = (
          <div className={cx('share-dl-container')}>
            <ValidShare
              setSelectedFiles={this.setSelectedFiles}
              selectedRowKeys={selectedFiles.map((file) => file.id)}
              bread={bread}
              files={files}
              info={info}
              previewFile={this.previewFile}
              downloadChecked={this.downloadChecked}
              downloading={downloading}
              clickSaveChecked={this.clickSaveChecked}
              goToLink={this.goToLink}
              goToPath={this.goToPath}
              isCanDownload={this.state.isCanDownload}
            />

            {preview ? (
              <CooperPreview
                type='link'
                files={this.fileList}
                fidx={this.fileIdx}
                hasOperation={true}
                closePreview={this.closePreview}
                updateShareFiles={this.updateShareFiles}
              />
            ) : null}
          </div>
        );
        break;

      default:
        if (type) {
          message.error(type);
        }
        renderSharePage = null;
        break;
    }
    // const fileName = info && files[0] && files[0].display_name
    return (
      <div className={cx({ 'share-page': true, 'share-page-valid': type === 'valid-share' })}>
        <Helmet>
          <title>{info.name ? `${info.name}-Cooper` : 'Cooper'}</title>
          <link
            rel="shortcut icon"
            type="image/png"
            href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
        </Helmet>

        <CooperHeaderOld />
        {renderSharePage}
        <CopyRight />
      </div>
    );
  }
}
// routes: shares/:shareid
export default withRouter(CooperShareDownload)
