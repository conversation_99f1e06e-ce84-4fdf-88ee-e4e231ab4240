/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-08-09 14:01:29
 * @LastEditTime: 2023-08-09 14:10:38
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/pages/cooper/Share/CooperShareDownload/table.conf.js
 *
 */
import { intl } from 'di18n-react';
import React from 'react';
import FileEllipsis from '@/components/common/FileEllipsis/index';
import { cmpName } from '@/utils/cooperutils';
import { Dir, Coop, DiDoc, FlowChart, POFile } from '@/constants/cooperConstants';
import classBind from 'classnames/bind';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

export const columns = (self) => {
  return [
    {
      title:
        self.selectedRowKeys && self.selectedRowKeys.length > 0
          ? intl.t('已选中{one}个文件/文件夹', {
            one: self.selectedRowKeys.length,
          })
          : intl.t('名称'),
      dataIndex: 'name',
      render: (name, record, idx) => {
        return (
          <div className={cx('table-td-content')}>
            <div
              className={cx('ttc-file-name-box')}
              onClick={() => {
                if ([Dir, Coop, DiDoc, FlowChart, POFile].includes(record.type)) {
                  self.goToPath(record, idx);
                } else {
                  self.previewFile(null, record.id, record.name, idx, record.resource_type);
                }
              }}
              data-e2e='file_name_box'
            >
              <div className={cx('ttc-file-icon-box')}>
                <img
                  className={cx('ttc-file-icon')}
                  src={record.icon} />
              </div>
              <FileEllipsis
                value={name}
                record={record} />
            </div>
          </div>
        );
      },
      sorter: cmpName('name'),
    },
    {
      width: 116,
      title: intl.t('大小'),
      dataIndex: 'size',
      render: (size) => <span className='td-100'>{size}</span>,
    },
    {
      width: 130,
      title: intl.t('更新时间'),
      dataIndex: 'updateTime',
      render: (updateTime) => <span className='td-120'>{updateTime}</span>,
    },
  ];
};
