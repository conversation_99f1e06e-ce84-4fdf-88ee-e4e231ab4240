import { useState, useEffect } from 'react';
import classBind from 'classnames/bind';
import PageTitleContent from '@/components/LayoutCooper/PageTitleContent'
import { intl } from 'di18n-react';
import ShareToMeList from './ShareToMeList';
import ShareFromMeList from './ShareFromMeList';
import { SHARE_TYPE } from '@/constants/share';
import { useLocation, useNavigate } from 'react-router-dom';
import { CooperTabs, CooperTabsPane } from '@/components/common/CooperTabs';
import { apolloSwitch, HideCooperTome } from '@/utils/ab';
import ErrorTips from '@/components/ErrorTips';
import * as styles from './style.module.less';
import { Helmet } from 'react-helmet';

const cx = classBind.bind(styles);

let _tabInfo = [
  {
    key: SHARE_TYPE.TO_ME,
    title: intl.t('分享给我'),
  },
  {
    key: SHARE_TYPE.FORM_ME,
    title: intl.t('我分享的'),
  },
];

function Share() {
  const [tabType, setTabType] = useState();
  const [hideTome, setHideTome] = useState(false);
  const navigate = useNavigate();

  const { pathname } = useLocation();

  useEffect(() => {
    window.document.title = `${intl.t('分享')} - Cooper`;
    initGrey();
  }, []);

  const initGrey = async () => {
    const data = await apolloSwitch(HideCooperTome);
    setHideTome(data);
    // if (data && _tabInfo.length === 2) {
    //   _tabInfo = _tabInfo.slice(1);
    // }
  };

  useEffect(() => {
    if (pathname.includes('/tome')) {
      setTabType(SHARE_TYPE.TO_ME);
    } else if (pathname.includes('/fromme')) {
      setTabType(SHARE_TYPE.FORM_ME);
    }
    // if (hideTome) {
    //   setTabType(SHARE_TYPE.FORM_ME);
    //   navigate('/fromme')
    // }
  }, [pathname])


  const handleTabChange = (activeKey) => {
    if (activeKey === SHARE_TYPE.TO_ME) {
      window.__OmegaEvent('ep_leftbar_share_shared_ck');
      navigate('/tome')
    } else {
      window.__OmegaEvent('ep_leftbar_share_ishare_ck');
      navigate('/fromme')
    }
    setTabType(activeKey);
  }

  return (
    <div className={cx('share-wrap')}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
      </Helmet>
      <PageTitleContent />
      {
        hideTome ? (
          <div className={cx('share-content-container')}>
            <ErrorTips
              title={intl.t('暂无分享内容')}
              img={
                'http://img-ys011.didistatic.com/static/cooper_cn/noSearchResult.png'
              }
            />
          </div>
        ) : (
          <div className={cx('share-content')}>
            <CooperTabs
              defaultActiveKey={tabType}
              activeKey={tabType}
              destroyInactiveTabPane={true}
              onChange={handleTabChange}
              tabsize='large'
            >
              {_tabInfo.map((item) => {
                return (
                  <CooperTabsPane
                    tab={
                      <span>
                        <span>{item.title}</span>
                      </span>
                    }
                    key={item.key}
                  >
                    {
                      item.key === SHARE_TYPE.TO_ME && !hideTome && <ShareToMeList />
                    }
                    {
                      item.key === SHARE_TYPE.FORM_ME && !hideTome && <ShareFromMeList />
                    }
                  </CooperTabsPane>
                );
              })}
            </CooperTabs>
          </div>
        )
      }
    </div>
  );
}
// routes: /tome
export default Share;
