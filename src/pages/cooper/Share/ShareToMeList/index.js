/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-06-08 11:04:30
 * @LastEditTime: 2023-08-07 10:48:23
 * @Description: 个人空间
 * @FilePath: /knowledgeforge/src/pages/cooper/SpaceDetail/SpacePersonFiles/index.js
 *
 */
import { useEffect, useState } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import classBind from 'classnames/bind';
import { intl } from 'di18n-react';
import CooperSubHead from '@/components/CooperSubHead';
import { getShareCrumbs } from '@/service/cooper/index';
import FoldTree from './FoldTree';
import * as styles from './style.modules.less';
import { SHARE_LIST_OPT } from '@/components/OperateMenu/constant';

const cx = classBind.bind(styles);

function ShareToMeList() {
  const params = useParams();
  const { pathname } = useLocation();

  const [state, setState] = useState({
    crumbs: [
      {
        path: '/tome',
        breadcrumbName: intl.t('全部文件'),
      },
    ],
  });

  const getFoldId = () => {
    return Number(params.foldId || 0) || 0;
  };

  const initCrumbs = async () => {
    let id = getFoldId();
    const crumbs = [
      {
        path: '/tome',
        breadcrumbName: intl.t('全部文件'),
      },
    ]; // 非根节点

    if (id !== 0) {
      const cbs = await getShareCrumbs({ shareId: params.shareId, id });
      // 为了兼容后端接口返回一些有的没的数据，id不为0才放入面包屑中
      cbs
        && cbs?.items?.forEach((c) => {
          if (c.id) {
            crumbs.push({
              path: `/tome/${params.shareId}/${c.id}`,
              breadcrumbName: c.name,
            })
          }
        });
    }

    setState({
      ...state,
      crumbs,
    });
  };

  useEffect(() => {
    if (!pathname.includes('tome')) return;
    initCrumbs();
  }, [pathname]);

  const routes = state.crumbs;
  return (
    <>
      {
        pathname.includes('tome') ? <div className={cx('share-to-me-wrap')}>
          <div className={cx('cooper-file-title-bottom')}>
            <CooperSubHead
              title={true}
              routes={routes}
              className={'sub-header-share'}
            />
          </div>
          <FoldTree isTeam={false} location={SHARE_LIST_OPT} />
        </div> : null
      }
    </>
  );
}

export default ShareToMeList;
