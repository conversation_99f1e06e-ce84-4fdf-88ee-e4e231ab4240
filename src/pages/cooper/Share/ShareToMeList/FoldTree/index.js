import { FILETYPE } from '@/components/CooperFilesList/FoldTree/utils';
import SortIcon from '@/components/CooperFilesList/SortIcon';
import ErrorTips from '@/components/ErrorTips';
import NoMore from '@/components/NoMore';
import OperateMenu from '@/components/OperateMenu';
import FileTableSkeleton from '@/components/SkeletonPage/common/FileTableSkeleton';
import SpinRender from '@/components/SpinRender';
import FileEllipsis from '@/components/common/FileEllipsis';
import ImageEnlarger from '@/components/common/ImageEnlarger';
import { PAGE_SIZE } from '@/constants';
import {
  FlowChart,
  SHARE_WITH_ME,
} from '@/constants/cooperConstants';
import { setImgUrl } from '@/utils/cooperutils';
import classBind from 'classnames/bind';
import { intl } from 'di18n-react';
import { useCallback, useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import * as styles from './style.module.less';
import GetHtml from '@/utils/DOMPurify';
import SearchFileType from '@/components/RecentActivity/searchType'
import SearchFileOwner from '@/components/RecentActivity/searchOwner'
import {
  handleFileClick,
} from './utils';

const config = {
  shareOperate: true,
  moveOperate: false,
  deleteOperate: false,
  duplicateOperate: false,
  multiSelectOperate: false,
};

const filterInfo = {
  initFilter: ['all'],
  initTenantFilter: ['all'],
  filterAll: ['coo_doc', 'coo_sheet', 'coo_ppt', 'flow', 'mind', 'coo_file', 'coo_dir'],
  filterTenantAll: ['coo_doc', 'coo_sheet', 'coo_file', 'coo_dir'],
}

const filterCof = [
  {
    text: () => intl.t('全部类型'),
    value: 'all',
  },
  {
    text: () => intl.t('文档'),
    value: 'coo_doc',
  },
  {
    text: () => intl.t('表格'),
    value: 'coo_sheet',
  },
  {
    text: () => intl.t('流程图'),
    value: 'flow',
  },
  {
    text: () => intl.t('思维导图'),
    value: 'mind',
  },
  {
    text: () => intl.t('幻灯片'),
    value: 'coo_ppt',
  },
  {
    text: () => intl.t('文件'),
    value: 'coo_file',
  },
  {
    text: () => intl.t('文件夹'),
    value: 'coo_dir',
  },
]
const cx = classBind.bind(styles);

function FoldTree(props) {
  const [loading, setLoading] = useState(true);
  const [pageNumber, setPageNumber] = useState(0);
  const [sortedInfo, setSortedInfo] = useState({
    sortBy: 'time',
    orderAsc: 0,
  });
  const [filterMsg, setFilterMsg] = useState({
    type: 'all',
    owner: [],
  });

  const { shareId, foldId = 0 } = useParams();
  const dispatch = useDispatch();

  const { needShareToMeSkeleton, IsExternalTenant } = useSelector((state) => state.GlobalData);
  const { setNeedShareToMeSkeleton } = useDispatch().GlobalData;
  const { shareLists, hasMore } = useSelector((state) => state.ShareToMe);
  const { getShareDetailList, getShareTomeList } = dispatch.ShareToMe;


  useEffect(() => {
    refreshList();
  }, [shareId, foldId, sortedInfo, filterMsg]);

  const getShareToMeFile = async (refresh = false) => {
    const { type, owner } = filterMsg;
    const ownerIds = owner.filter((item) => item.checked).map((item) => item.id).join(',');
    const pageNum = refresh ? 0 : pageNumber;
    if (sortedInfo.sortBy === undefined || sortedInfo.orderAsc === undefined) return;
    // eslint-disable-next-line no-unused-expressions
    (shareId && foldId) ? await getShareDetailList({
      ...sortedInfo,
      shareId,
      foldId,
      pageNum,
      pageSize: PAGE_SIZE,
      ownerIds,
      types: type,
      refresh,
    }) : await getShareTomeList({
      ...sortedInfo,
      pageNum,
      ownerIds,
      types: type,
      pageSize: PAGE_SIZE,
      refresh,
    });

    if (refresh) {
      setNeedShareToMeSkeleton(false);
    }
    setPageNumber(pageNum + 1);
    setLoading(false);
  }

  // 判断当前排序方式
  const getSorterStatus = useCallback(
    (itemSortBy, itemOrderAsc) => {
      return (
        sortedInfo.sortBy === itemSortBy
        && sortedInfo.orderAsc === itemOrderAsc
      );
    },
    [sortedInfo.sortBy, sortedInfo.orderAsc],
  );

  const toggleSort = async (s, oder) => {
    setSortedInfo({ sortBy: s, orderAsc: oder });
    let document = window.document.getElementById('tb-body-shareTo');
    document.scrollTop = 0;
  };

  const clickFile = (item, idx) => {
    window.__OmegaEvent('ep_shared_visit_ck', '', {
      resourceid: item.id,
      resource_id: item.id,
      position: idx,
      platform: 'new',
      type: FILETYPE[item.type],
    });
    handleFileClick(item, '', SHARE_WITH_ME, false, () => {
      refreshList();
    });
  };

  const operateCallback = () => {
    getShareToMeFile(true);
  };

  const refreshList = () => {
    getShareToMeFile(true);
  }

  const optionEnhance = (options) => {
    // 判断是否为多租户，否直接返回入参。是经过处理
    if (IsExternalTenant) {
      const exceptList = ['coo_ppt', 'wiki', 'flow', 'mind'];
      return options.filter((v) => !exceptList.includes(v.value));
    }
    return options;
  };
  const doFilters = () => {
    let document = window.document.getElementById('tb-body-shareTo');
    document.scrollTop = 0;
  }

  const updateFilteredMsg = (key, value) => {
    let temp = { ...filterMsg, [key]: key === 'type' ? value.toString() : value };
    setFilterMsg(temp);
    return temp;
  }
  return (
    <>
      {
        needShareToMeSkeleton ? (
          <div style={{ paddingTop: '7px' }}>
            <div
              dangerouslySetInnerHTML={{ __html: GetHtml(FileTableSkeleton) }} />
          </div>
        ) : <div
          className={cx({
            'folder-tree': true,
            'list-view': true,
          })}
        >
          <div className={cx('tb-header')}>
            <div className={cx('tb-header-div')}>
              <span
                className={cx('file-name')}
              >
                <SearchFileType
                  filterMsg= {filterMsg.type.split(',')}
                  valueMap = { optionEnhance(filterCof) }
                  filterInfo={ filterInfo }
                  updateFilteredMsg={updateFilteredMsg}
                  doFilters={doFilters}
                />
              </span>

              <span className={cx('file-owner')}>
                <SearchFileOwner
                  filterMsg= {filterMsg.owner}
                  updateFilteredMsg={updateFilteredMsg}
                  doFilters={doFilters}
                  isNeedAlowMe={false}
                />
              </span>
              <span
                className={cx('file-time')}
                style={{ cursor: 'pointer' }}
                onClick={() => toggleSort('time', sortedInfo.orderAsc === 1 ? 0 : 1)}>
                <span>{intl.t('分享时间')}</span>
                <SortIcon
                  iconUp={getSorterStatus('time', 1)}
                  iconDown={getSorterStatus('time', 0)}
                />
              </span>
              <span className={cx('file-operate')}>
                {intl.t('操作')}
              </span>
            </div>
          </div>

          <div
            className={cx('tb-body', 'os-scrollbar', 'os-scrollbar-width')}
            id='tb-body-shareTo'
          >
            <InfiniteScroll
              initialLoad={false}
              pageStart={0}
              loadMore={() => getShareToMeFile(false)}
              hasMore={!loading && hasMore}
              useWindow={false}
              getScrollParent={() => document.querySelector('.folder-tree')}
            >
              {
                (shareLists ?? []).map((item, idx) => {
                  return (
                    <li
                      key={idx}
                      className={cx('tb-body-row')}
                    >
                      <span
                        className={cx('file-name')}
                      >
                        <ImageEnlarger
                          src={setImgUrl(item)}
                          isTiny={!!item.tiny}
                          mimeType={item.mime_type || item.mimeType}
                          resourceType={item.resourceType}
                        />
                        <div
                          onClick={() => clickFile(item)}
                          className={cx('file-name-display')}
                          key={new Date()}
                        >
                          <FileEllipsis
                            value={item.name}
                            isShowStar={true}
                            doneCallback={refreshList}
                            record={item}
                          />
                        </div>
                      </span>
                      <span className={cx('file-owner')}>
                        <div className={cx('item-display')}>{item.owner}</div>

                      </span>
                      <span className={cx('file-time')}>
                        <div className={cx('item-display')}>
                          {item.createTime}
                        </div>
                      </span>

                      <span className={cx('file-operate')}>
                        <OperateMenu
                          key={new Date()}
                          file={item}
                          config={config}
                          doneCallback={() => operateCallback(item)}
                          originFileType={SHARE_WITH_ME}
                          isFlowChart={
                            item.type === FlowChart && item.mime_type === 9
                          }
                          location={props.location}
                        />
                      </span>
                    </li>
                  );
                })
              }
              {
                !loading
                && !shareLists.length
                && <ErrorTips
                  title={intl.t('暂无分享内容')}
                  img={
                    'http://img-ys011.didistatic.com/static/cooper_cn/noSearchResult.png'
                  }
                />
              }
              <SpinRender loading={loading} />
              {!hasMore && !loading && shareLists.length !== 0 && <NoMore />}
            </InfiniteScroll>
          </div>

        </div>
      }
    </>
  );
}

export default FoldTree;
