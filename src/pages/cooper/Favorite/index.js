import classBind from 'classnames/bind';
// import PageTitleContent from '@/components/LayoutCooper/PageTitleContent'
import CooperStars from './Table/index';
import { intl } from 'di18n-react';
import * as styles from './style.module.less';
import { useEffect } from 'react';
import { Helmet } from 'react-helmet';

const cx = classBind.bind(styles);

function Favorite() {
  useEffect(() => {
    window.document.title = `${intl.t('收藏')} - Cooper`;
  }, []);

  return (
    <div className={cx('favorite-wrap')}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
      </Helmet>
      {/* <PageTitleContent value={intl.t('我的收藏')} /> */}
      <div className={cx('page-title-wrap')}>
        <div className={cx('title-name')}>
          {intl.t('我的收藏')}
        </div>
      </div>
      <CooperStars />
    </div>
  );
}
// routes: /favorite
export default Favorite;
