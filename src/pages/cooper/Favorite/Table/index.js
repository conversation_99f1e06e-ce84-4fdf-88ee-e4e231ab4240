/* eslint-disable camelcase */
import CooperEmptyTip from '@/components/CooperEmptyTip';
import { FILETYPE } from '@/components/CooperFilesList/FoldTree/utils';
import SortIcon from '@/components/CooperFilesList/SortIcon/index';
import handleFileClick from '@/components/CooperOperation/HandleFileClick';
import CooperPreview from '@/components/FilePreview/index';
import NoMore from '@/components/NoMore';
import OperateMenu from '@/components/OperateMenu';
import goToPath from '@/components/RecentActivity/goToPath';
import FileTableSkeleton from '@/components/SkeletonPage/common/FileTableSkeleton';
import SpinRender from '@/components/SpinRender';
import FileEllipsis from '@/components/common/FileEllipsis';
import ImageEnlarger from '@/components/common/ImageEnlarger';
import { FileType } from '@/constants/cooper';
import { FlowChart } from '@/constants/cooperConstants';
import withRouter from '@/hooks/withRouter';
import { STAR } from '@/constants/cooperConstants';
import {
  getGlobalFileDir,
} from '@/service/cooper/home';
import { getUserNameFromCookie, setImgUrl } from '@/utils/cooperutils';
import { formatRecentTime } from '@/utils/index';
import { Tooltip } from 'antd';
import classBind from 'classnames/bind';
import { getLocale, intl } from 'di18n-react';
import { fromJS } from 'immutable';
import InfiniteScroll from 'react-infinite-scroller';
import { connect } from 'react-redux';
import USER_VIEW from '@/constants/userView';
import * as styles from './style.module.less';
import GetHtml from '@/utils/DOMPurify';
import { STAR_LIST_OPT } from '@/components/OperateMenu/constant';

const cx = classBind.bind(styles);

const isDk = (type) => {
  return type === FileType.DK_FILE || type === FileType.DK_PAGE;
};

const operateConfig = (type) => {
  if (isDk(type)) {
    return {
      copyLink: true,
      duplicateOperate: false,
      multiSelectOperate: false,
      starOperate: true,
      moveOperate: false,
      copyOperate: false,
      deleteOperate: false,
      downloadOperate: true,
      renameOperate: false,
      shareOperate: true,
    };
  }
  return {
    shareOperate: true,
    moveOperate: false,
    deleteOperate: false,
    duplicateOperate: false,
    multiSelectOperate: false,
  }
}

export class CooperStars extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      editable: false,
      renameId: null,
      preview: false,
      scrollTop: 0,
      sortedInfo: {},
      loading: true,
      hasMore: false,
      pageNum: 0,
      end: false,
    }; // 用于文件预览
    this.list = [];

    this.fileList = [];
    this.fileIdx = 0;

    this.lastDate = 0;
    this.isAddeventListener = false;
    this.dom = null;
  }

  componentDidMount() {
    window.document.title = `${intl.t('收藏文件')}-Cooper`;
    const { FAVORITE_FILE = {}} = this.props?.userViewData ?? {};
    const { sort = USER_VIEW.FAVORITE_FILE.sort } = FAVORITE_FILE;

    if (JSON.stringify(sort) !== JSON.stringify(this.state.sortedInfo)) {
      this.setState({
        ...this.state,
        sortedInfo: {
          sortBy: sort.sortBy,
          orderAsc: sort.orderAsc,
        },
      }, () => {
        this.refresh();
      })
    }
  }

  componentWillReceiveProps(nextProps) {
    const { FAVORITE_FILE = {}} = nextProps?.userViewData ?? {};
    const { sort = USER_VIEW.FAVORITE_FILE.sort } = FAVORITE_FILE;

    if (JSON.stringify(sort) !== JSON.stringify(this.state.sortedInfo)) {
      this.setState({
        ...this.state,
        sortedInfo: {
          sortBy: sort.sortBy,
          orderAsc: sort.orderAsc,
        },
      }, () => {
        this.refresh();
      })
    }
  }

  showPreview = (record, idx) => {
    for (let i = 0; i < this.fileList.length; i++) {
      if (this.fileList[i].name === record.display_name) {
        this.fileIdx = i;
      }
    }

    this.setState({
      preview: true,
    });
  };

  closePreview = () => this.setState({
    preview: false,
  });

  toggleSort = async (sortBy, orderAsc) => {
    const { userViewData, setUserViewDataRq } = this.props;
    const data = { ...userViewData };
    data.FAVORITE_FILE = {
      ...userViewData.FAVORITE_FILE,
      sort: {
        sortBy,
        orderAsc,
      },
    }
    if (JSON.stringify(userViewData) === '{}') return;
    setUserViewDataRq(data);

    this.setState({
      sortedInfo: {
        sortBy,
        orderAsc,
      },
    }, () => {
      this.refresh();
    });
  }

  getWholePath = (record, idx) => {
    const {
      parent_id,
      team_id,
      share_type,
      team_name,
      resource_type,
    } = record;

    let currentList = fromJS(this.list);
    if (currentList.getIn([idx, 'globalDir'])) {
      return;
    }
    // 分享给我
    if (share_type === 'Direct') {
      currentList = currentList.setIn([idx, 'globalDir'], intl.t('分享给我')).toJS()
      this.props.updateGlobDir(currentList)
      return;
    }

    // 链接分享
    if (share_type === 'Link') {
      currentList = currentList.setIn([idx, 'globalDir'], intl.t('链接分享')).toJS()
      this.props.updateGlobDir(currentList)
      return;
    }

    // 个人空间
    if (team_id === 0 && parent_id === 0) {
      currentList = currentList.setIn([idx, 'globalDir'], intl.t('个人空间')).toJS()
      this.props.updateGlobDir(currentList)
      return;
    }

    // 知识库页面
    if (resource_type === FileType.DK_FILE || resource_type === FileType.DK_PAGE) {
      currentList = currentList.setIn([idx, 'globalDir'], intl.t(`${intl.t('知识库')}/${team_name}`)).toJS()
      this.props.updateGlobDir(currentList)
      return;
    }
    getGlobalFileDir(parent_id, team_id).then((res) => {
      currentList = currentList.setIn([idx, 'globalDir'], res).toJS()
      this.props.updateGlobDir(currentList)
    });
  }

  getSorterStatus(itemSortBy, itemOrderAsc) {
    const { sortBy, orderAsc } = this.state.sortedInfo;
    return sortBy === itemSortBy && orderAsc === itemOrderAsc;
  }

  loadMore = (refresh = false) => {
    const { pageNum, sortedInfo } = this.state;
    const { setNeedFavoriteSkeleton } = this.props
    const pageNumber = refresh ? 0 : pageNum;
    if (sortedInfo.sortBy === undefined || sortedInfo.orderAsc === undefined) return;
    this.setState({
      loading: true,
    });
    this.props.getStarFileList({
      pageNum: pageNumber,
      pageSize: 20,
      ...sortedInfo,
      refresh,
    }).then((data) => {
      if (refresh) { setNeedFavoriteSkeleton(false); }
      const { currentPage, pageSize, totalCount } = data;
      this.setState({
        loading: false,
        pageNum: pageNumber + 1,
        hasMore: (currentPage + 1) * pageSize < totalCount,
      });
    });
  }

  handlePathClick = (f, navigate) => {
    const params = {
      resourceTypeStr: f.resource_type,
      teamId: f.team_id,
      shareLink: f.share_link,
      shareId: f.share_id,
      id: f.parent_id,
      deleted: f.deleted,
    }
    goToPath(params, '', navigate);
  }

  refresh = () => {
    this.loadMore(true);
  }


  getWholFileAdress = (item) => {
    // eslint-disable-next-line max-len
    const { space_type_str: spaceType, team_name: fileDir, resource_type: resourceTypeStr, share_type: shareType } = item
    if (spaceType === 'TEAM_SPACE' && !shareType) {
      return `${intl.t('团队空间')}/${fileDir}`
    } if (isDk(resourceTypeStr)) {
      if (fileDir === '分享给我') {
        return intl.t('分享给我')
      }
      return `${intl.t('知识库')}/${fileDir}`
    } if (spaceType === 'PERSONAL_SPACE' && fileDir !== intl.t('个人空间') && !shareType) {
      return `${intl.t('个人空间')}/${fileDir}`
    } if (spaceType === 'WIKI_SPACE') {
      return `${intl.t('Wiki')}/${fileDir}`
    }
    return fileDir
  }


  render() {
    const { list = [], navigate, needFavoriteSkeleton } = this.props;
    this.list = list
    const { preview, loading, hasMore } = this.state;
    this.fileList = [];

    for (const d of list) {
      this.fileList.push({
        id: d.resource_id,
        name: d.display_name,
        size: d.size,
        marked_quick_visit: d.marked_quick_visit,
        marked_star: d.marked_star,
        isStar: true,
        teamId: d.team_id,
      });
    }
    if (list.length === 0 && !loading) {
      return (
        <CooperEmptyTip title={intl.t('还没有收藏文件，快去收藏吧')} />
      )
    }

    return (
      <>
        {needFavoriteSkeleton
          ? <>
            <div
              style={{ padding: '0 28px 0 32px' }}
              dangerouslySetInnerHTML={{ __html: GetHtml(FileTableSkeleton) }} />
          </>
          : <div className={cx('cooper-stars-table')}>
            <div className={cx('tb-header')}>
              <div className={cx('tb-header-div')}>
                <span
                  className={cx('file-name')}
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    this.toggleSort('display_name', this.state.sortedInfo.orderAsc === 1 ? 0 : 1);
                  }}>
                  {intl.t('名称')}
                  <SortIcon
                    iconUp={this.getSorterStatus('display_name', 1)}
                    iconDown={this.getSorterStatus('display_name', 0)}
                  />
                </span>
                <span className={cx('file-owner')}>
                  {intl.t('所有者')}
                </span>
                <span className={cx('file-address')}>
                  {intl.t('文件位置')}
                </span>
                <span
                  className={cx('file-time')}
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    this.toggleSort('time', this.state.sortedInfo.orderAsc === 1 ? 0 : 1);
                  }}>
                  {intl.t('收藏时间')}
                  <SortIcon
                    iconUp={this.getSorterStatus('time', 1)}
                    iconDown={this.getSorterStatus('time', 0)}
                    // sort={(value) => this.toggleSort('time', Number(value))}
                  />
                </span>
                <span className={cx('file-operate')}>
                  {intl.t('操作')}
                </span>
              </div>

            </div>
            <InfiniteScroll
              initialLoad={false}
              pageStart={0}
              loadMore={() => this.loadMore(false)}
              className={cx('InfiniteScroll')}
              hasMore={!loading && hasMore}
              useWindow={false}
              getScrollParent={() => document.querySelector('#tb-body-star')}
            >
              <div
                className={cx('tb-body', 'os-scrollbar', 'os-scrollbar-width')}
                id='tb-body-star'>
                {
                  list.map((item, index) => {
                    return <li
                      key={index}
                      className={cx('tb-body-row')}
                    >
                      <span className={cx('file-name')}>
                        <ImageEnlarger
                          src={setImgUrl(item)}
                          isTiny={!!item.tiny}
                          mimeType={item.mime_type || item.mimeType}
                          resourceType={item.resource_type}
                        />
                        <div
                          onClick={() => {
                            handleFileClick({
                              fileTypeStr: item.resource_type,
                              resourceId: item.id,
                              resourceName: item.display_name,
                              knowledgeId: item.team_id,
                              dkShareType: item.star_type,
                              pageId: item.resource_id,
                              shareId: item.share_id,
                              shareLink: item.star_info,
                              markedQuickVisit: item.marked_quick_visit,
                              teamId: item.team_id,
                              quickId: item.quick_id,
                              parentId: item.parent_id,
                              spaceId: item.spaceId,
                              relationTypeTags: item.relationTypeTags,
                              filesList: list,
                              fidx: index,
                              doneCallback: () => {
                                this.refresh();
                              },
                            });
                            window.__OmegaEvent('ep_starred_visit_ck', '', {
                              resource_id: item.resourceId,
                              position: index,
                              platform: 'new',
                              type: FILETYPE[item.type],
                            });
                            window.__OmegaEvent('ep_favorites_location_ck');
                          }}
                          className={cx('file-name-display')}
                          key={new Date()}
                        >
                          <FileEllipsis
                            value={item.display_name}
                            isShowStar={false}
                            doneCallback={() => this.refresh()}
                            record={item}
                            hasVisitorTag={item.star_type === FileType.PAGE_SHARE || item.star_type === FileType.DK_SHARE}
                          />
                        </div>
                      </span>
                      <span className={cx('file-owner')}>
                        {
                          getUserNameFromCookie() === item.create_by ? intl.t('我') : getLocale() === 'en-US' ? item.create_by : item.create_by_cn
                        }
                      </span>
                      <span className={cx('file-address')}>
                        <Tooltip
                          title={item.globalDir || '--'}
                          placement='topLeft'
                        >
                          <div
                            className={cx('file-content-box')}
                            onMouseOver={() => {
                              if (!item.globalDir) {
                                this.getWholePath(item, index);
                                // this.forceUpdate()
                              }
                            }}
                            onClick={() => {
                              this.handlePathClick(item, navigate);
                            }}
                          >

                            {this.getWholFileAdress(item)}
                          </div>
                        </Tooltip>
                      </span>
                      <span className={cx('file-time')}>
                        {
                          formatRecentTime(item.create_on)
                        }
                      </span>
                      <span className={cx('file-operate')}>
                        <OperateMenu
                          file={{
                            name: item.display_name,
                            ...item,
                            permission: 0b111111,
                          }}
                          doneCallback={() => this.refresh()}
                          config={operateConfig(item.resource_type)}
                          originFileType={STAR}
                          isFlowChart={item.type === FlowChart && item.mime_type === 9}
                          location={STAR_LIST_OPT}
                        />
                      </span>
                    </li>
                  })
                }
                <SpinRender loading={loading} />
                {!loading && !hasMore && list.length !== 0 && <NoMore customClass={cx('noMore')} />}
              </div>
            </InfiniteScroll>
            {preview && (
              <CooperPreview
                files={this.fileList}
                fidx={this.fileIdx}
                closePreview={this.closePreview}
                refreshFolder={() => this.refresh()}
                type='file'
              />
            )}
          </div>}</>
    )
  }
}

function mapStateToProps(state) {
  return {
    list: state.cooperStarFile.list,
    userViewData: state.GlobalData.userViewData,
    needFavoriteSkeleton: state.GlobalData.needFavoriteSkeleton,
  };
}

const mapDispatchToProps = (dispatch) => {
  const { getStarFileList, updateGlobDir } = dispatch.cooperStarFile
  const { setUserViewDataRq, setNeedFavoriteSkeleton } = dispatch.GlobalData
  return {
    getStarFileList,
    updateGlobDir,
    setUserViewDataRq,
    setNeedFavoriteSkeleton,
  }
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(CooperStars));
