import classBind from 'classnames/bind';
import * as styles from './style.module.less';
// import PageTitleContent from '@/components/LayoutCooper/PageTitleContent';
import { useLocation } from 'react-router-dom';
import { useEffect } from 'react';
import { Helmet } from 'react-helmet';
import QuickAccess from '@/components/QuickAccess/index';
import RecentActivity, { handleScrollTable } from '@/components/RecentActivity/index';
import Banner from '@/components/Banner';
import { isDiDiTenant } from '@/utils/entryEnhance';

const cx = classBind.bind(styles);


function Home() {
  const { pathname } = useLocation()

  useEffect(() => {
    window.document.title = '首页-Cooper';
  }, []);

  return (
    <div
      className={cx('home-wrap', 'home-wrap-os-flag')}
      id='home-wrap'
      onScroll={(e) => {
        if (pathname === '/') {
          handleScrollTable(e);
        }
      }}
    >
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
      </Helmet>
      <div
        id='home-main'
        className={cx('home-main', 'home-main-os-flag')}
      >
        { isDiDiTenant() && (
          <Banner />
        )}
        <QuickAccess />
        <RecentActivity />
      </div>
    </div>
  );
}
// routes: /
export default Home;
