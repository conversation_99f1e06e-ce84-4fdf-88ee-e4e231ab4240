import { intl } from 'di18n-react';
import React, { useEffect, useState, useRef, useCallback } from 'react';
import { noop } from 'lodash-es';
import SearchBox from './search-box';
import FileList from './file-list';
import NoResult from './no-result';
import { 
  dynamicGetRecent, 
  dynamicGetSearch, 
  hideDialog, 
  sendFiles, 
  dcToast, 
  getSelectFromDC,
  getUrlInfo, 
  getUrlParam } from './services';
import { iconClose, iconSpin } from './icons';
import './index.less';
import { Helmet } from 'react-helmet';
import {isBeforeDcVersion415} from './dc-helper';

const MAX_SELECTS = getUrlParam("max_selects") || 9;
const PRECISION = 10;

function CooperSelectfile() {
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);

  const SelectedListFromDC = useRef([]);

  const [searchMode, setSearchMode] = useState(false);
  const [recentList, setRecentList] = useState([]);
  const [searchList, setSearchList] = useState([]);
  const [selectedList, setSelectedList] = useState([]);
  const [isLoadEnd, setIsLoadEnd] = useState(false);
  const [invalidCount, setInvalidCount] = useState(0);
  const [validUrlArrInit, setValidUrlArrInit] = useState([]);

  const loadFuncs = useRef({
    getRecent: noop,
    getSearch: noop,
  });

  const loadingMore = useRef(false);

  useEffect(() => {
    (async function () {
      try {
        loadFuncs.current.getRecent = dynamicGetRecent();
        const originList = getSelectFromDC();
        SelectedListFromDC.current = originList;
        await loadSelectedList();
        await loadRecent();
      } finally {
        setLoading(false);
      }
    }());
  }, []);

  const handleSearch = useCallback((value, cancelToken) => {
    if (value) {
      dynamicGetSearch.cancelToken = cancelToken;
      loadFuncs.current.getSearch = dynamicGetSearch(value);
      loadSearch();
    } else {
      setSearchMode(false);
    }
  }, []);

  const loadSelectedList = useCallback(async () => {
    const result = await getUrlInfo([...SelectedListFromDC.current]);
    setSelectedList(result);
    setValidUrlArrInit(result.map(it => it.fileUrl));
    setInvalidCount(SelectedListFromDC.current.length - result.length);
  }, []);

  const loadRecent = useCallback(async () => {
    if (!loadingMore.current) {
      loadingMore.current = true;

      try {
        const { isEnd, result } = await loadFuncs.current.getRecent();
        setRecentList(result);
        setIsLoadEnd(isEnd);
      } finally {
        loadingMore.current = false;
      }
    }
  }, [loadFuncs.current.getRecent])

  const loadSearch = useCallback(async () => {
    if (!loadingMore.current) {
      loadingMore.current = true;

      try {
        const { isEnd, result } = await loadFuncs.current.getSearch();
        setSearchList(result);
        setIsLoadEnd(isEnd);
        setSearchMode(true);
      } finally {
        loadingMore.current = false;
      }
    }
  }, [loadFuncs.current.getSearch])

  const handleScroll = useCallback((e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;

    if (scrollTop + clientHeight + PRECISION >= scrollHeight) {
      const loadFunc = searchMode ? loadSearch : loadRecent;
      loadFunc();
    }
  }, [loadSearch, loadRecent, searchMode])

  const handleCheck = useCallback((item) =>{
    const idx = selectedList.findIndex((it) => it.fileUrl === item.fileUrl);
    if (idx > -1) {
      selectedList.splice(idx, 1);
    } else if (selectedList.length < MAX_SELECTS - invalidCount) {
      selectedList.push(item);
    } else {
      dcToast('warning', intl.t('最多支持关联{count}个会议文档', {count: MAX_SELECTS}));
    }
    setSelectedList([...selectedList]);
  }, [selectedList, invalidCount])

  const _sendFiles = useCallback(async () => {
    if(isBeforeDcVersion415 && selectedList.length === 0) return;
    setSending(true);
    try {
      await sendFiles(selectedList, [...SelectedListFromDC.current], validUrlArrInit);
      hideDialog();
    } catch (e) {
    } finally {
      setSending(false);
    }
  }, [selectedList, validUrlArrInit])

  const list = searchMode ? searchList : recentList;

  return (
    <div className='cooper-sendfile-dc'>
       <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
      </Helmet>
      <div className='container'>
        <div className='header'>
          <h4>{intl.t('关联企业内部文档')}</h4>
          <img
            src={iconClose}
            alt=''
            onClick={() => {
              hideDialog();
            }}
          />
        </div>
        <div className='body'>
          <div className='selector'>
            <SearchBox onSearch={handleSearch}/>
            <div
              className='file-container'
              onScroll={handleScroll}>
              {
                !loading && list.length > 0
                  && <React.Fragment>
                    <FileList
                      isSelectedList={false}
                      isMaxCount={selectedList.length >= MAX_SELECTS - invalidCount}
                      files={list}
                      selectedList={selectedList}
                      toggleSelect={handleCheck}
                    />
                    <div className='end-tip'>
                      {
                        isLoadEnd
                          ? intl.t('- 已显示所有结果 -')
                          : <div>
                            <img
                              src={iconSpin}
                              alt=''/>
                            {intl.t('加载中...')}
                          </div>
                      }
                    </div>
                  </React.Fragment>
              }
              {
                !loading && list.length === 0
                  && <NoResult isRecentEmpty={!searchMode}/>
              }
            </div>
          </div>
          <div className='selected'>
            <div className='title'>
              {intl.t('剩余可关联：{selected}个', {
                selected: MAX_SELECTS - invalidCount - selectedList.length,
              })}
            </div>
            <div className='file-container'>
              <FileList
                isSelectedList={true}
                files={selectedList}
                selectedList={selectedList}
                toggleSelect={handleCheck}
                isSimpleShow={true}
              />
            </div>
          </div>
        </div>
        <div className='footer'>
          <div className='btn-pair'>
            <button
              onClick={() => {
                hideDialog();
              }}
            >
              {intl.t('取消')}
            </button>
            <button
              disabled={isBeforeDcVersion415 && selectedList.length === 0}
              onClick={() => !sending && _sendFiles()}
            >
              {sending ? <img
                src={iconSpin}
                alt=''/> : intl.t('确定')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
export default CooperSelectfile;
