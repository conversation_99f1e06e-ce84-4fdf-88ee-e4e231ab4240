import { getDkPageUrl } from "@/components/CooperOperation/OpenDkPage";
import { intl } from "di18n-react";
import * as dc from "./dc-helper";
import { get, post } from "@/utils/request/cooper";
import api from "@/utils/request/api/CooperApi";
import dkApi from "@/utils/request/api/DkApi";
import { FileType } from "@/constants/cooper";
import { inPhone } from "@/utils";

const FLOWCHART_ICON =
  "https://img-ys011.didistatic.com/static/cooper_cn/flowChart.png";

export const VALID_DOMAINS = [
  "cooper.didichuxing.com",
  "cooper-vpn.xiaojukeji.com",
  "cooper.didiglobal.com",
  "cooper-test.didichuxing.com",
  "cooper-qa.didichuxing.com",
];

if (process.env.NODE_ENV === "development") {
  VALID_DOMAINS.push("localhost:4001");
}

export const REGEX_DOC2 = /docs2\/(sheet|document)\/(\d+)/;
export const REGEX_DOC =
  /\/docs\/(sheet|document|slide|diagraming|mindmap)\/(\d+)/;
export const REGEX_MOBILE_DOC =
  /\/m\/c\/docs\/(sheet|document|slide|diagraming|mindmap)\/(\d+)/;
export const REGEX_KNOWLEDGE =
  /\/knowledge\/(?:(?:share\/book\/[^/]+)|(?:\d+))\/(\d+)(?:\/edit)?/;

// && (domain.includes('didichuxing.com') ? window.location.host === domain  : true)
const isValidUrl = (url) => {
  try {
    const parsedUrl = new URL(url);
    const path = parsedUrl.pathname;
    const domain = parsedUrl.host;
    return (
      VALID_DOMAINS.includes(domain) &&
      (REGEX_DOC.test(path) ||
        REGEX_MOBILE_DOC.test(path) ||
        REGEX_KNOWLEDGE.test(path) ||
        REGEX_DOC2.test(path))
    );
  } catch (e) {
    return false;
  }
};

export function dynamicGetRecent() {
  let result = [];
  let pageNum = 0;
  let isEnd = false;

  function commonQuery(pageNumP = 0, pageSizeP = 50) {
    return `queryType=1&type=coo_doc,coo_sheet,coo_ppt,coo_file,flow,mind&ownerType=all&pageSize=${pageSizeP}&pageNum=${pageNumP}&latestTs=${new Date().getTime()}`;
  }

  return async function () {
    if (isEnd) return { isEnd, result };
    const url = api.RECENT_FILE_LIST.replace(":queryType", "1");
    const res = await get(`${url}?${commonQuery(pageNum++)}`);

    result.push(
      ...res.items.map((item) => ({
        id: item.resourceId,
        docType: item.resourceTypeStr,
        name: item.resourceName,
        image: item.tinyImage,
        owner: item.userInfo.userCn,
        isDeleted: item.deleted,
        canShare: true,
        spaceId: item.spaceId,
        shareType: item.shareType,
        shareLink: item.shareLink,
        shareId: item.shareId || item?.manageQuickAccessInfo?.shareId || null,
        spaceType: item.spaceType,
        isShowOuter: item?.relationTypeTags?.includes("IN_OUT"),
      }))
    );

    for (const item of result) {
      item.fileUrl = await getShareLink(item);
    }

    const { pageSize, totalCount, currentPage } = res;

    if (Math.ceil(totalCount / pageSize) === currentPage + 1) {
      isEnd = true;
    }

    const resultReal = result.filter(
      (item) => !!item.fileUrl && isValidUrl(item.fileUrl)
    );
    return { isEnd, result: resultReal };
  };
}

export function dynamicGetSearch(keyWord, pageSize) {
  let result = [];
  let pageNum = 0;
  let isEnd = false;

  return async function () {
    if (isEnd) return { isEnd, result };

    const { allPages: res } = await post(dkApi.GET_SEARCH_DATA_V2, {
      key: keyWord,
      pageNum: pageNum++,
      pageSize: 50,
      scopeType: 0,
      searchType: 0,
    });

    result.push(
      ...res.items.map((item) => ({
        id: item.resourceId,
        docType: item.resourceType,
        name: item.resourceName,
        image: item.iconImage,
        owner: item.resourceOwner,
        // isDeleted: item.deleted,
        isDeleted: false,
        // eslint-disable-next-line no-bitwise
        // canShare: !!(item.perm & SHARE_BIT)
        canShare: true,
        spaceId: item.spaceId,
        shareType: item.shareType,
        shareLink: item.shareLink,
        shareId: item.shareId || item?.manageQuickAccessInfo?.shareId || null,
        spaceType: item.spaceType,
        isShowOuter: item?.relationTypeTags?.includes("IN_OUT"),
      }))
    );

    for (const item of result) {
      item.fileUrl = await getShareLink(item);
    }

    if (result.length >= res.totalCount) {
      isEnd = true;
    }

    const resultReal = result.filter(
      (item) => !!item.fileUrl && isValidUrl(item.fileUrl)
    );

    return { isEnd, result: resultReal };
  };
}

export async function createShareLink(id) {
  const res = await post(
    api.CREATE_SHARE,
    {
      share_type: 2,
      resource_id: id,
      expiration: 1,
      password: "",
      read_only: true,
    },
    { top: 80 }
  );

  return `${window.location.origin}/shares/${res.link_id}`;
}

export function hideDialog() {
  const closeEnable = getUrlParam("closeEnable");
  if (closeEnable === "false") return;
  if (inPhone()) {
    window.dcH5Sdk.navigation.close({
      onSuccess: () => {},
      onFail: () => {},
    });
    return;
  }
  if (dc.inDC) dc.hideDialog();
}

async function getShareLink(file) {
  switch (file.docType) {
    case FileType.COOPER_DOC:
    case FileType.COOPER_ANONYMOUS_DOC:
      return `${window.location.origin}/docs/document/${file.id}`;
    case FileType.COOPER_SHEET:
      return `${window.location.origin}/docs/sheet/${file.id}`;
    case FileType.COOPER_SLICE:
      return `${window.location.origin}/docs/slide/${file.id}`;
    case FileType.SHIMO2_WORD:
      return `${window.location.origin}/docs2/document/${file.id}`;
    case FileType.SHIMO2_EXCEL:
      return `${window.location.origin}/docs2/sheet/${file.id}`;
    case FileType.SHIMO2_PPT:
      return `${window.location.origin}/docs2/slide/${file.id}`;
    case FileType.DI_DOC:
      return `${window.location.origin}/didoc/file/${file.id}`;
    case FileType.FLOWCHART:
      return `${window.location.origin}/docs/flow/draw?resourceId=${file.id}`;
    case FileType.DIAGRAMING:
      return `${window.location.origin}/docs/diagraming/${file.id}`;
    case FileType.MINDMAP:
      return `${window.location.origin}/docs/mindmap/${file.id}`;
    case FileType.DK_PAGE:
    case FileType.DK_FILE:
      return getDkPageUrl({
        dkShareType: file.shareType,
        pageId: file.id,
        knowledgeId: file.spaceId,
        shareId: file.shareId,
        shareLink: file.shareLink,
      });
    default:
      return "";
  }
}

export function getUrlParam(name) {
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
  const r = window.location.search.substr(1).match(reg);
  if (r !== null) return unescape(r[2]);
  return "";
}

export function sendFiles(files = [], originalUrls = [], validUrlArrInit = []) {
  const mapUrls = files.map((it) => it.fileUrl);
  const filterOriginal = originalUrls.filter((it) => {
    if (validUrlArrInit.includes(it)) {
      if (mapUrls.includes(it)) return true;
      return false;
    }
    return true;
  });

  const realUrls = [...new Set([...filterOriginal, ...mapUrls])];
  return new Promise((resolve, reject) => {
    if (!dc.inDC) return Promise.resolve();
    const resource = getUrlParam("resource");
    if (inPhone()) {
      window.dcH5Sdk.cooper.selectCooperDocs({
        docUrls: realUrls,
        resource,
        businessType: "calendar",
        onSuccess: (res) => resolve(res),
        onFail: (err) => reject(err),
      });
    } else {
      if(dc.isBeforeDcVersion415) {
        window
        .require("dc")
        .ShellAPI.app.rpc.invokeRenderer(
          "calendar-create-window-application",
          "cooper_select_cooper_docs",
          realUrls.join(","),
          resource
        );
        window
          .require("dc")
          .ShellAPI.app.rpc.invokeRenderer(
            "calendar-modal-application",
            "cooper_select_cooper_docs",
            realUrls.join(","),
            resource
          );
      } else {
        window
          .require("dc")
          .ShellAPI.app.rpc.invokeRenderer(
            resource,
            "cooper_select_cooper_docs",
            realUrls.join(","),
            resource
          );
      }
      
      resolve();
    }
  });
}

const { toast } = dc;

export const dcToast = (type = "info", msg = "") => {
  if (inPhone()) {
    window.dcH5Sdk.notification.toast({
      text: msg,
      type,
    });
  } else {
    console.log("pc toast params:", type, msg);
    window.require("dc").ShellAPI.system.shell.toast({
      type,
      text: msg,
    });
  }
};

export { FLOWCHART_ICON };

export const getSelectFromDC = () => {
  const docUrls = getUrlParam("doc_urls");
  const docUrlsArr = !!docUrls ? docUrls.split(",") : [];
  const result = [...new Set(docUrlsArr)].map((it) => {
    try {
      const parsedUrl = new URL(it);
      const path = parsedUrl.pathname;
      const domain = parsedUrl.host;
      if (REGEX_MOBILE_DOC.test(path) && VALID_DOMAINS.includes(domain)) {
        return it.replace("/m/c", "");
      }
      return it;
    } catch (e) {
      return it;
    }
  });
  return result;
};

export const getUrlInfo = async (urls = []) => {
  if (urls.length === 0) return [];
  const realUrls = urls.filter((it) => isValidUrl(it));
  try {
    const results = await Promise.all(
      realUrls.map((it) => {
        return post(dkApi.GET_DOC_INFO_BY_URL, {
          source_url: it,
        });
      })
    );
    return results
      .filter((it) => !!it.inline)
      .map((it) => ({
        fileUrl: it?.url,
        name: it.inline?.title,
        image: it.inline?.image_url,
      }));
  } catch (e) {
    return [];
  }
};
