import { getDcVersion, isBeforeVersion } from "@/utils/dc-helper";

export const inDC = !!window.dcJSSDK || !!window.dcH5Sdk;

const getDcVersionR = () => {
  try {
    return window.require("dc").ShellAPI.app.environment.appVersion;
  } catch (e) {
    return getDcVersion();
  }
};

const dcVersion = getDcVersionR();
export const isBeforeDcVersion415 = isBeforeVersion(dcVersion, "4.15.0");

export function hideDialog() {
  if (!inDC) return;
  const dcVersion = getDcVersionR();
  try {
    if (isBeforeVersion(dcVersion, "4.14.0")) {
      return window.dcJSSDK.hideDialog();
    } else {
      return window.require('dc').ShellAPI.app.mainWindow.closeAppWebviewModal();
    }
  } catch (e) {
    console.error("hideDialog calling error: ", e);
  }
}

export function shareLink({
  link = "",
  targetVchannelIds,
  title,
  desc,
  image,
}) {
  return window.dcJSSDK.shareLink({
    link,
    targetVchannelIds,
    title,
    desc,
    image,
  });
}

export const toast = {
  info(text) {
    return window.require("dc").ShellAPI.system.shell.toast({
      type: "info",
      text,
    });
  },

  error(text) {
    return window.require("dc").ShellAPI.system.shell.toast({
      type: "error",
      text,
    });
  },

  warn(text) {
    return window.require("dc").ShellAPI.system.shell.toast({
      type: "warning",
      text,
    });
  },
};
