import axios from 'axios';
import React, { useEffect, useRef, useState } from 'react';
import { intl } from 'di18n-react';
import { debounce, noop } from 'lodash-es';
import './index.less';
import { iconSearch1, iconCloseCircle } from '../icons';

function SearchBox({
  onSearch = noop,
  type = 'pc'
}) {
  const [keyword, setKeyword] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [composingValue, setComposingValue] = useState(''); 
  const searchRef = useRef(noop);
  const cancelTokenRef = useRef(null);
  

  useEffect(() => {
    searchRef.current = debounce(onSearch, 300);
  }, []);

  function handleChange(e) {
    const { value } = e.target;
    setKeyword(value);
    setComposingValue(value);
    cancelTokenRef.current?.cancel('Operation canceled');
    cancelTokenRef.current = axios.CancelToken.source();
    searchRef.current(value, cancelTokenRef.current);
  }

  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  const handleCompositionEnd = (e) => {
    setIsComposing(false);
    setKeyword(e.target.value);
    setComposingValue(e.target.value);
    handleChange(e);
  };

  const handleChangeFn = (e) => {
    const currentValue = e.target.value;
    if (!isComposing) {
      setKeyword(currentValue);
      setComposingValue(currentValue);
      handleChange(e);
    } else {
      setComposingValue(currentValue);
    }
  };

  const displayValue = isComposing ? composingValue : keyword;

  return (
    <div className={`search-box ${type === 'mobile' ? 'search-box-mobile' : 'search-box-pc'}`}>
      <img
        className='icon-search'
        src={iconSearch1}
        alt='' />
      <input
        type="text"
        placeholder={intl.t('搜索云空间文件')}
        value={displayValue}
        onChange={handleChangeFn}
        onCompositionEnd={handleCompositionEnd}
        onCompositionStart={handleCompositionStart}/>
      {
        keyword
          && <img
            className='icon-close-circle'
            src={iconCloseCircle}
            alt=''
            onClick={() => handleChange({ target: { value: '' }})}
          />
      }
    </div>
  );
}

export default SearchBox;
