import { intl } from 'di18n-react';
import { useEffect, useMemo } from 'react';
import classBind from 'classnames/bind';
import { openNewWindow, isDesktopDC } from '@/utils';
import { PROCESS_ENV_MENHU_URL, SYMOL_HTTP_URL } from '@/constants';
import * as styles from './style.module.less';
import bujutupian from '@/assets/icon/bujutupian.png';

const cx = classBind.bind(styles);

const url = PROCESS_ENV_MENHU_URL[process.env.APP_ENV];

const MHCard = ({ mhInfo }) => {
  // 4级域名 使用 http 不能使用 https， 没有证书
  const domainSymbol = (symbol = '') => {
    return `http://${symbol}.${SYMOL_HTTP_URL[process.env.APP_ENV]}`;
  };
  const gotoMH = ({ symbol = '', useDomain = false }) => {
    const gotoUrl = useDomain ? domainSymbol(symbol) : `${url}${symbol}`;
    openNewWindow(`${gotoUrl}`);
  };

  const gotoMHdesigner = ({ symbol = '', useDomain = false }, e) => {
    e.stopPropagation();
    const gotoUrl = useDomain
      ? `${domainSymbol(symbol)}/designer`
      : `${url}${symbol}/designer`;
    openNewWindow(`${gotoUrl}`);
  };

  return (
    <>
      <li
        className={cx('team-mh', {
          'team-mh-dc': isDesktopDC,
        })}
        onClick={() => gotoMH(mhInfo)}
      >
        <div
          className={cx('team-mh-pic')}
          style={{ backgroundImage: `url(${bujutupian})` }}
        >
          {mhInfo.canManage && (
            <div
              className={cx('mh-tags-btn')}
              onClick={(e) => gotoMHdesigner(mhInfo, e)}
            >
              <i
                className={cx('dk-iconfont', 'dk-icon-shezhi4')}
                style={{ marginRight: '4px', fontSize: '14px' }}
              />
              {intl.t('门户配置后台')}
            </div>
          )}
        </div>

        <p className={cx('team-mh-name')}>{mhInfo.name}</p>
      </li>
    </>
  );
};

export default MHCard;
