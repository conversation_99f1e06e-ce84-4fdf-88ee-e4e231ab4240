import { intl } from 'di18n-react';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Tabs, Tooltip } from 'antd';
import classBind from 'classnames/bind';
import { getMyMHList, getCountNumber } from '@/service/knowledge/menhuIndex';
import DKList from '@/components/DKList';
import HelpLink from '@/components/HelpLink';
import PopoverSort from '@/pages/cooper/KnowledgeList/PopoverSort';
import CreateMenHu from '@/pages/cooper/PortalList/CreateMenHu';
import PageTitleContent from '@/components/LayoutCooper/PageTitleContent';
import { TAB_TYPE } from '@/constants/dkList';
import * as styles from '@/pages/cooper/KnowledgeList/style.module.less';

// 门户
const { TabPane } = Tabs;

const cx = classBind.bind(styles);

const _tabInfo = [
  {
    key: TAB_TYPE.ALL,
    title: intl.t('全部'),
    tip: '',
  },
  {
    key: TAB_TYPE.OWN,
    title: intl.t('我管理的'),
    tip: intl.t('我是知识门户管理员'),
  },
  {
    key: TAB_TYPE.JOIN,
    title: intl.t('我参与的'),
    tip: intl.t('我是知识门户成员'),
  },
];


const PortalList = () => {
  const { globalOutsideChain } = useSelector((state) => state.CooperIndex);

  const [initLoading, setInitLoading] = useState(true);
  const [errorData, setErrorData] = useState(false);
  const [tabType, setTabType] = useState(TAB_TYPE.ALL);
  const [cookieSortObj, setCookieSortObj] = useState();

  useEffect(() => {
    (async function () {
      const { Sort_Type: sortType } = await getUserView();
      setCookieSortObj(sortType)
    }());
  }, []);

  const [MHsort, setMHSort] = useState({
    [TAB_TYPE.ALL]: cookieSortObj[TAB_TYPE.ALL] ?? 0,
    [TAB_TYPE.OWN]: cookieSortObj[TAB_TYPE.OWN] ?? 0,
    [TAB_TYPE.JOIN]: cookieSortObj[TAB_TYPE.JOIN] ?? 0,
  });
  const [listData, setListData] = useState();
  const [countObj, setCountObj] = useState([]);

  const dispatch = useDispatch();
  const { getPortalABSwitch } = dispatch.KnowledgePortal;
  const { portalABSwitch } = useSelector((state) => state.KnowledgePortal);

  useEffect(() => {
    if (portalABSwitch === null) {
      getPortalABSwitch();
    }
    updateData(tabType);
  }, []);

  const updateData = (type, activeSort) => {
    setInitLoading(true);
    const params = {
      type,
      orderType: activeSort ?? MHsort[type],
    };
    getMyMHList(params)
      .then((res) => {
        setListData(res);
      })
      .catch(() => {
        setErrorData(true);
      })
      .finally(() => {
        setInitLoading(false);
      });

    getCountNumber(false).then((res) => {
      setCountObj(res);
    });
  };

  const handleSort = async (activeSort) => {
    setInitLoading(true);

    let newSort = { ...MHsort, [tabType]: activeSort };
    setMHSort(newSort);
    // await setUserView('MH_Knowledge', {
    //   Sort_Type: newSort,
    // });
    updateData(tabType, activeSort);
  };

  const handleTabChange = (activeKey) => {
    setTabType(activeKey);
    updateData(activeKey);
    setInitLoading(true);

    switch (activeKey) {
      case activeKey == TAB_TYPE.ALL:
        window.__OmegaEvent('ep_kg_home_all_ck');
        break;
      case activeKey == TAB_TYPE.OWN:
        window.__OmegaEvent('ep_kg_home_manage_ck');
        break;
      case activeKey == TAB_TYPE.JOIN:
        window.__OmegaEvent('ep_kg_home_joined_ck');
        break;
      default:
        break;
    }
  };

  return (
    <div className={cx('my-dk-wrap')}>
      <PageTitleContent
        titleRight={
          <HelpLink
            title={intl.t('了解更多知识门户用法')}
            jumpUrl={globalOutsideChain?.portal_help || ''}
          />
        }
      >
        {portalABSwitch && (
          <CreateMenHu
            upDataFn={() => {
              setInitLoading(true);
              updateData(tabType);
            }}
          />
        )}
      </PageTitleContent>

      <Tabs
        defaultActiveKey={tabType}
        destroyInactiveTabPane={true}
        onChange={handleTabChange}
        className={`dk-tabs-has-fit ${cx('tab-top-title')}`}
        tabBarExtraContent={{
          right: (
            <PopoverSort
              isMyKnowledge={false}
              type={tabType}
              chooseValue={MHsort[tabType]}
              sortChoose={{
                [TAB_TYPE.ALL]: [intl.t('访问时间')],
                [TAB_TYPE.OWN]: [intl.t('访问时间'), intl.t('创建时间')],
                [TAB_TYPE.JOIN]: [intl.t('访问时间'), intl.t('创建时间')],
              }}
              onChange={handleSort}
            />
          ),
        }}
      >
        {_tabInfo.map((item) => {
          return (
            <TabPane
              tab={
                <Tooltip
                  placement="top"
                  title={item.tip}>
                  <span>
                    <span>{item.title}</span>
                    <span> {countObj[item.key]?.count}</span>
                  </span>
                </Tooltip>
              }
              key={item.key}
            >
              <DKList
                type={tabType}
                errorData={errorData}
                initLoading={initLoading}
                dkData={listData}
                isMyKnowledge={false}
              />
            </TabPane>
          );
        })}
      </Tabs>
    </div>
  );
};

export default PortalList;
