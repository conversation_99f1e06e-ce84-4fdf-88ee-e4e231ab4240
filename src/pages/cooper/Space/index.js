/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-06-05 19:58:59
 * @LastEditTime: 2023-07-03 17:05:19
 * @Description: 团队空间
 * @FilePath: /knowledgeforge/src/pages/cooper/Space/index.js
 *
 */
import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import { useEffect } from 'react';
import { Helmet } from 'react-helmet';
// import PageTitleContent from '@/components/LayoutCooper/PageTitleContent';
// import NewCreateTeam from '@/components/LayoutCooper/Header/NewCreateTeam';
import CooperTeamList from '@/components/CooperTeamList';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

function Space() {
  useEffect(() => {
    window.document.title = `${intl.t('团队空间')} - Cooper`;
  }, []);

  return (
    <div className={cx('space-wrap')}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
      </Helmet>
      {/* <PageTitleContent value={' '}>
        <div>
          <NewCreateTeam />
        </div>
      </PageTitleContent> */}
      <CooperTeamList />
    </div>
  );
}
// routes: /team-folder
export default Space;
