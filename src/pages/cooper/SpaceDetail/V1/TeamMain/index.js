import { intl } from 'di18n-react';
import { Tooltip, Popover, message } from 'antd';
import classBind from 'classnames/bind';
import { useCallback, useState, useEffect, useMemo } from 'react';
import { TRASH, SETTING, MEMBER, DYNAMIC, IN_OUT } from '@/constants/space';
import { TEAM_ADMIN, TEAM_OWNER } from '@/utils/cooperutils';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import TeamDk from '../TeamDk';
import { useDispatch, useSelector } from 'react-redux';
import { setVisitedSuit, getRelateTeamDk, reqSuite } from '@/service/cooper/teamSpace';
import SpaceTeamFiles from '@/pages/cooper/SpaceDetail/SpaceTeamFiles';
import * as styles from './style.module.less';
import SpaceTrash from '@/pages/cooper/SpaceDetail/SpaceTrash';
import Tips from '@/components/Tips';
import SpaceSetting from '@/pages/cooper/SpaceDetail/SpaceSetting';
import SpaceMember from '@/pages/cooper/SpaceDetail/SpaceMember';
import { openNewWindow } from '@/utils';
import { doPinned, doUnPinned } from '@/service/cooper';
import SpaceDynamic from '@/pages/cooper/SpaceDetail/SpaceDynamic';
import { isDiDiTenant } from '@/utils/entryEnhance'


const cx = classBind.bind(styles);


export const ADMIN_SUITE_TIP = {
  IM: intl.t('启用后在此可一键触达指定群聊'),
  JZD: intl.t('启用后在此可一键触达指定圈子'),
};

export const MEMBER_SUITE_TIP = {
  IM: intl.t('请联系团队空间所有者或管理员开启该套件'),
  JZD: intl.t('请联系团队空间所有者或管理员开启该套件'),
};

const operations = [
  {
    function: 'pinned',
    title: intl.t('置顶'),
    icon: (
      <i className={cx('dk-iconfont', 'dk-icon-zhiding4px', 'space-operation')} />
    ),
  },
  {
    function: DYNAMIC,
    title: intl.t('空间动态'),
    icon: (
      <i className={cx('dk-iconfont', 'dk-icon-dongtai', 'space-operation')} />
    ),
  },
  {
    function: MEMBER,
    title: intl.t('成员管理'),
    icon: (
      <i className={cx('dk-iconfont', 'dk-icon-duoren', 'space-operation')} />
    ),
  },
  {
    function: TRASH,
    title: intl.t('团队空间回收站'),
    icon: (
      <i
        className={cx(
          'dk-iconfont',
          'dk-icon-huishouzhan4px',
          'space-operation',
        )}
      />
    ),
  },

  {
    function: SETTING,
    title: intl.t('空间设置'),
    icon: (
      <i className={cx('dk-iconfont', 'dk-icon-shezhi4', 'space-operation')} />
    ),
  },
];

// 在这里获取当前选中的Tab信息，放在store中
// const defaultTabs = isDiDiTenant() ? [{
//   tabKey: '0',
//   title: intl.t('文件'),
// }, {
//   tabKey: '1',
//   title: intl.t('知识库'),
// }] : [{
//   tabKey: '0',
//   title: intl.t('文件'),
// }]

const defaultTabs = (type) => {
  if (type === IN_OUT) {
    return [{
      tabKey: '0',
      title: intl.t('文件'),
    }]
  }
  return [{
    tabKey: '0',
    title: intl.t('文件'),
  }, {
    tabKey: '1',
    title: intl.t('知识库'),
  }]
}

const config = {
  IM: '群聊',
  KNOWLEDGE: '知识库',
  JZD: '圈子',
  SPACE_DEPART: 'http://img-ys011.didistatic.com/static/cooper_cn/do1_kfJrUTPivjaHXd1QLgmh',
};


function TeamMain() {
  const { teamId } = useParams();
  const [teamDk, setTeamDk] = useState();
  const [suiteData, setSuiteData] = useState({});
  const { pathname } = useLocation();
  const { teamDetail } = useSelector(
    (state) => state.TeamData,
  );
  const { cooperate_type } = teamDetail
  const dispatch = useDispatch();
  const { getAsideTeamList, setToastTeamTrashTip, setUserViewDataRq } = dispatch.GlobalData;
  const { getTeamData, setActiveListTab, setFeatures } = dispatch.TeamData;

  const { activeListTab } = useSelector((state) => state.TeamData);
  const { toastTeamTrashTip, userViewData } = useSelector((state) => state.GlobalData);


  const { role, pinned } = teamDetail;
  const navigate = useNavigate();
  const { suiteList, teamCenterId, suiteCenterFullUrl } = suiteData;

  async function reqDepartmentSuite(id) {
    const res = await reqSuite(id);
    const tempList = [{ title: intl.t('群聊'), suiteSourceType: 'IM', key: '2' }, { title: intl.t('圈子'), suiteSourceType: 'JZD', key: '3' }];
    res.suiteList?.forEach((item) => {
      if (item.suiteSourceType === 'IM') {
        tempList[0].isOpen = true;
      }
      if (item.suiteSourceType === 'JZD') {
        tempList[1].isOpen = true;
      }
      const index = tempList.findIndex((suite) => {
        return item.suiteSourceType === suite.suiteSourceType;
      });
      tempList[index] = { ...tempList[index], ...item };
    });
    setSuiteData({ suiteList: tempList, teamCenterId: res.teamId, suiteCenterFullUrl: res.suiteCenterFullUrl });
  }

  const isMainTab = !(['/trash', '/member', '/setup', '/dynamic'].find((item) => pathname.includes(item)));


  const initData = () => {
    setTeamDk([]);
    setActiveListTab('0');
    setSuiteData({});
  }

  useEffect(() => {
    initData();
  }, [teamId])


  useEffect(() => {
    if (isMainTab === false) {
      setActiveListTab('-1')
      return;
    }
    if (isMainTab && activeListTab === '-1') {
      setActiveListTab('0')
    }
  }, [isMainTab])

  useEffect(() => {
    reqDepartmentSuite(teamId);
    getRelateTeamDk(teamId).then((res) => {
      setTeamDk(res);
    });
  }, [teamId]);

  useEffect(()=>{
    return ()=>{
      setFeatures([]);
    }
  },[])

  const onTabChange = (activeKey) => {
    // const tabBarEle = document.querySelector('.ant-tabs-ink-bar');
    if (activeKey === '0') {
      window.__OmegaEvent('ep_space_team_document_ck', '', {
        platform: 'new',
      });
      // tabBarEle.style.marginLeft = '0px';
    } else if (activeKey === '1') {
      getRelateTeamDk(teamId);
      window.__OmegaEvent('ep_space_team_knowledge_ck', '', {
        platform: 'new',
      });
    }
    // 如果是群聊，圈子，跳出去
    if (Number(activeKey) > 1) {
      let currentItem = suiteList.find((v) => v.key == activeKey);
      if (currentItem) {
        const { suiteSourceType } = currentItem;
        switch (suiteSourceType) {
          case 'IM':
            window.__OmegaEvent('ep_space_team_chat_ck', '', {
              platform: 'new',
            });
            setVisitedSuit(teamId, 'IM');
            // this.setState({ visitedSuiteList: [...this.state.visitedSuiteList, 'IM'] });
            break;
          case 'JZD':
            window.__OmegaEvent('ep_space_team_circle_ck', '', {
              platform: 'new',
            });
            setVisitedSuit(teamId, 'JZD');
            // this.setState({ visitedSuiteList: [...this.state.visitedSuiteList, 'JZD'] });
            break;
          default:
        }
        if (currentItem.url) {
          openNewWindow(currentItem.url);
        } else if (role === TEAM_ADMIN || role === TEAM_OWNER) {
          openNewWindow(suiteCenterFullUrl);
        }
      }
      setActiveListTab(activeListTab);
    } else {
      setActiveListTab(activeKey);
      navigate(`/team-file/${teamId}`);
    }
  }

  const handleRouteChange = (item) => {
    if (!teamId) return;
    if (item.function === TRASH) {
      navigate(`/team-file/${teamId}/trash`);
    }
    if (item.function === MEMBER) {
      window.__OmegaEvent('ep_team_member_ck', '', {
        platform: 'new',
      });
      navigate(`/team-file/${teamId}/member`);
    }
    if (item.function === SETTING) {
      navigate(`/team-file/${teamId}/setup`);
    }
    if (item.function === DYNAMIC) {
      navigate(`/team-file/${teamId}/dynamic`);
    }
  };

  const getSuitePopover = (hasPower, suiteSourceType) => {
    if (hasPower) {
      return <span>
        {intl.t(ADMIN_SUITE_TIP[suiteSourceType])}
        <span
          className='openBtn'
          onClick={() => openNewWindow(suiteCenterFullUrl)}>{intl.t('去开启')}</span>
      </span>;
    }
    return MEMBER_SUITE_TIP[suiteSourceType];
  }

  const hasSuitePower = role === TEAM_ADMIN || role === TEAM_OWNER;

  const refreshDkList = () => {
    getRelateTeamDk(teamId).then((res) => {
      setTeamDk(res);
    });
  }

  const onPinned = (isPinned) => {
    if (isPinned) {
      doUnPinned(teamId).then(() => {
        getAsideTeamList();
        getTeamData(teamId);
        message.success(intl.t('已取消置顶'));
      });
    } else {
      doPinned(teamId).then(() => {
        getAsideTeamList();
        getTeamData(teamId);
        message.success(intl.t('置顶成功'));
      });
    }
  };

  return (
    <>
      <div className={cx('space-tab')}>
        <div
          id="space-tab-title"
          className={cx('space-tab-title')}>
          <div className={cx('space-tab-title-left')}>
            {
              defaultTabs(cooperate_type).map((defaultItem) => {
                return (
                  <div
                    key={defaultItem.tabKey}
                    className={`${cx(
                      'space-tab-title-item',
                      activeListTab === defaultItem.tabKey
                      && 'space-tab-title-item-active',
                    )} space-tab-title-item`}
                    onClick={() => {
                      onTabChange(defaultItem.tabKey);
                    }}
                  >
                    <div className={cx('space-tab-title-item-content')}>
                      {defaultItem.title}
                    </div>
                  </div>
                );
              })
            }
            {(teamDetail.relationTypeTags || []).includes('SPACE_DEPART')
              && (suiteList ?? []).map((v) => {
                return (
                  <div
                    key={v.key}
                    className={`${cx('space-tab-title-item', `${hasSuitePower && v.isOpen ? '' : 'disabled'}`)} space-tab-title-item`}
                    onClick={() => {
                      hasSuitePower && v.isOpen && onTabChange(v.key);
                    }}
                  >
                    <Popover
                      placement='bottom'
                      overlayClassName={`suite-popover ${v.url ? 'hide' : ''}`}
                      content={!v.isOpen && suiteCenterFullUrl && getSuitePopover(hasSuitePower, v.suiteSourceType)}>
                      <div className={cx('space-tab-title-item-content')}>
                        {intl.t(v.title)}
                      </div>
                    </Popover>
                  </div>
                )
              })
            }
          </div>
        </div>

        <div className={cx('space-tab-operation')}>
          {operations.map((operation) => {
            if (operation.function === 'pinned') {
              return (
                <div
                  key={operation.function}
                  className={cx(
                    'space-tab-operation-item',
                  )}
                  onClick={() => {
                    onPinned(pinned);
                  }}
                >
                  <Tooltip
                    title={
                      pinned ? intl.t('取消置顶') : intl.t('置顶')
                    }
                    placement="top"
                    align={{
                      offset: [0, -7],
                    }}
                  >
                    {
                      pinned
                        ? <i className={cx('dk-iconfont', 'dk-icon-quxiaozhiding', 'space-operation')} />
                        : <i className={cx('dk-iconfont', 'dk-icon-zhiding4px', 'space-operation')} />
                    }
                  </Tooltip>
                </div>
              )
            }
            if (operation.function === TRASH) {
              return (
                <Tooltip
                  title={() => <Tips
                    overlayClassName='blue-tip'
                    text={'被删除的内容已进入"当前团队空间回收站"，30天内你可以在这里找回。'}
                    isShow={true}
                    onClose={() => {
                      setToastTeamTrashTip(false);
                      const data = { ...userViewData };
                      data.New_Trash_Team_Close = '-1';
                      if (JSON.stringify(userViewData) === '{}') return;
                      setUserViewDataRq(data);
                    }}
                  />}
                  color='#047FFE'
                  arrowPointAtCenter={true}
                  placement="bottomRight"
                  visible={toastTeamTrashTip}
                  overlayClassName={'new-functions-tooltip'}
                >
                  <div
                    key={operation.function}
                    className={cx(
                      'space-tab-operation-item',
                      {
                        'space-tab-operation-item-active': ((
                          pathname.includes('/trash') && operation.function === TRASH
                        ) || (
                          pathname.includes('/member') && operation.function === MEMBER
                        ) || (
                          pathname.includes('/setup') && operation.function === SETTING
                        ) || (
                          pathname.includes('/dynamic') && operation.function === DYNAMIC
                        )),
                      },
                    )}
                    onClick={() => {
                      handleRouteChange(operation);
                    }}
              >
                    <Tooltip
                      title={operation.title}
                      placement="top"
                      align={{
                        offset: [0, -7],
                      }}
                >
                      {operation.icon}
                    </Tooltip>
                  </div>
                </Tooltip>

              )
            }
            return (
              <div
                key={operation.function}
                className={cx(
                  'space-tab-operation-item',
                  {
                    'space-tab-operation-item-active': ((
                      pathname.includes('/trash') && operation.function === TRASH
                    ) || (
                      pathname.includes('/member') && operation.function === MEMBER
                    ) || (
                      pathname.includes('/setup') && operation.function === SETTING
                    ) || (
                      pathname.includes('/dynamic') && operation.function === DYNAMIC
                    )),
                  },
                )}
                onClick={() => {
                  handleRouteChange(operation);
                }}
              >
                <Tooltip
                  title={operation.title}
                  placement="top"
                  align={{
                    offset: [0, -7],
                  }}
                >
                  {operation.icon}
                </Tooltip>
              </div>
            );
          })}
        </div>
      </div>

      {activeListTab === '0' && <SpaceTeamFiles />}
      {
        activeListTab === '1'
        && <TeamDk
          dks={teamDk}
          spaceId={teamId}
          role={role}
          refreshDkList={refreshDkList}
        />
      }
      {
        pathname.includes('/trash') && <SpaceTrash />
      }
      {
        pathname.includes('/member') && <SpaceMember />
      }
      {
        pathname.includes('/setup') && <SpaceSetting teamCenterId={teamCenterId} />
      }
      {
        pathname.includes('/dynamic') && <SpaceDynamic teamId={teamId} />
      }

    </>
  );
}

export default TeamMain;
