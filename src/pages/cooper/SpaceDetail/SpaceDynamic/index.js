import NoDynamic from '@/assets/icon/empty8.png';
import ErrorTips from '@/components/ErrorTips';
import NoMore from '@/components/NoMore';
import SpinRender from '@/components/SpinRender';
import FileContent from '@/components/common/FileContent';
import useLoadMore from '@/hooks/useLoadMore';
import { getTeamDynamic } from '@/service/cooper/teamDetail';
import { blurTime, openNewWindow } from '@/utils';
import { handleFileClick as goToPath } from '@/utils/file';
import classBind from 'classnames/bind';
import { getLocale, intl } from 'di18n-react';
import { useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const OPT_TYPE = {
  transfer_cooperation: intl.t('转交了所有者权限'),
  move_files: intl.t('移动了'),
  restore: intl.t('恢复了'),
  modify_cooperation: intl.t('编辑了'),
  comment_cooperation: intl.t('评论了'),
  page_publish: intl.t('发布了'),
  page_update_publish: intl.t('更新了'),
  download_file: intl.t('下载了'),
};

function SpaceDynamicList({ teamId }) {
  const { loadMore, loading, list, hasMore } = useLoadMore((args) => getTeamDynamic({ spaceId: teamId, ...args }));

  const jumpTo = (item) => {
    goToPath({
      fileTypeStr: item.resourceTypeStr,
      resourceId: item.resourceId,
      resourceName: item.resourceName?.replace(/§§/g, ''),
      knowledgeId: item.spaceId,
      dkShareType: '',
      pageId: item.resourceId,
      shareId: '',
      shareLink: '',
      teamId: item.spaceId,
      spaceId: item.spaceId,
      filesList: [{ ...item, name: item.resourceName?.replace(/§§/g, ''), id: item.resourceId }],
    });
  };

  const showScope = ({ opType, spaceName }) => {
    const isDk = opType === 'page_publish' || opType === 'page_update_publish';
    if (isDk && spaceName.length > 0) return true;
    return false;
  };

  return (
    <div className={cx('dynamic-list')}>
      <InfiniteScroll
        initialLoad={false}
        pageStart={0}
        loadMore={loadMore}
        hasMore={!loading && hasMore}
        useWindow={false}
        // getScrollParent={() => document.querySelector('.dynamic-list')}
      >
        {list.map((item, index) => {
          return (
            <li
              key={index}
              className={cx('dynamic-item')}>
              <div className={cx('operate-msg')}>
                <img
                  className={cx('avatar')}
                  src={item.userInfo.avatar} />
                <div className={cx('user-name')}>
                  { getLocale() === 'zh-CN' ? item.userInfo.userCn : item.userInfo.userEn}
                </div>
                <div className={cx('operation')}>
                  {showScope(item) && (
                    <>
                      {<span className="">{intl.t('在')}</span>}

                      {
                        <span className={cx('space-name')}>
                          <span
                            onClick={() => {
                              openNewWindow(`/knowledge/${item.spaceId}/home`);
                            }}
                          >
                            {item.spaceName}
                          </span>
                        </span>
                      }
                    </>
                  )}

                  <span className={cx('operation-type')}>
                    {OPT_TYPE[item.opType]}
                  </span>
                </div>
                <div className={cx('operation-time')}>
                  {blurTime(item.operationOn)}
                </div>
              </div>
              <div
                className={cx('file-content')}
                onClick={() => jumpTo(item)}>
                <FileContent
                  overlayClassName={'dynamic-file'}
                  iconImage={item.tinyImage}
                  resourceName={item.resourceName}
                  resourceId={item.resourceId}
                />
              </div>
            </li>
          );
        })}

        <SpinRender loading={loading} />
        {!loading && !list.length && (
          <ErrorTips
            title={intl.t('暂无团队动态')}
            img={NoDynamic} />
        )}

        {!loading && !hasMore && list.length !== 0 && (
          <NoMore />
        )}
      </InfiniteScroll>
    </div>
  );
}

const SpaceDynamic = (props) => {
  const [update, setUpdate] = useState();
  const { teamId } = props;
  useEffect(() => {
    if (!teamId) return;
    setUpdate(false);
    setTimeout(() => {
      setUpdate(true);
    }, 0);
  }, [teamId]);
  return (
    <div className={cx('space-dynamic')}>
      <span className={cx('dynamic-title')}>{intl.t('团队动态')}</span>
      {update && <SpaceDynamicList {...props} />}
    </div>
  );
};
// routes:  /team-file/:teamId/dynamic
export default SpaceDynamic;
