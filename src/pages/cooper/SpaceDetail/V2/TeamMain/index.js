import { DYNAMI<PERSON>, FIL<PERSON>, HOME, KNOWLEDGE, MEMBER, SETTING, TRASH } from '@/constants/space';
import { TEAM_OWNER, noopenerOpen, TEAM_ADMIN } from '@/utils/cooperutils';
import { Dropdown, Menu, Popover, Tooltip, message } from 'antd';
import classBind from 'classnames/bind';
import { getLocale, intl } from 'di18n-react';
import { cloneDeep } from 'lodash-es';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
// import TeamDk from '../TeamDk';
import Tips from '@/components/Tips';
import SpaceMember from '@/pages/cooper/SpaceDetail/SpaceMember';
import SpaceSetting from '@/pages/cooper/SpaceDetail/SpaceSetting';
import SpaceTeamFiles from '@/pages/cooper/SpaceDetail/SpaceTeamFiles';
import SpaceTrash from '@/pages/cooper/SpaceDetail/SpaceTrash';
import { useDispatch, useSelector } from 'react-redux';
// import { openNewWindow } from '@/utils';
import { dkBook, folder, home } from '@/assets/icon/fileIcon';
import cooperConfirm from '@/components/common/CooperConfirm';
import LayoutDK from '@/components/serviceComponents/Layout';
import { handleCloseSources } from '@/constants/omegaSource.js';
import SpaceDynamic from '@/pages/cooper/SpaceDetail/SpaceDynamic';
import { doPinned, doUnPinned } from '@/service/cooper';
import { get } from '@/utils/request';
import DkApi from '@/utils/request/api/DkApi';
import SpaceDetailHome from '../../SpaceDetailHome';
import ConnectDk from '../TeamDk/ConnectDk';
import AddModal from './AddModal';
import CreateDKModal from './CreateDKModal';
import ManageModal from './ManageModal';
import RenameModal from './RenameModal';
import TabMenu from './TabMenu';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

export const ADMIN_SUITE_TIP = {
  IM: intl.t('启用后在此可一键触达指定群聊'),
  JZD: intl.t('启用后在此可一键触达指定圈子'),
};

export const MEMBER_SUITE_TIP = {
  IM: intl.t('请联系团队空间所有者或管理员开启该套件'),
  JZD: intl.t('请联系团队空间所有者或管理员开启该套件'),
};


const operations = [
  {
    function: 'pinned',
    title: intl.t('置顶'),
    icon: (
      <i className={cx('dk-iconfont', 'dk-icon-zhiding4px', 'space-operation')} />
    ),
  },
  {
    function: DYNAMIC,
    title: intl.t('空间动态'),
    icon: (
      <i className={cx('dk-iconfont', 'dk-icon-dongtai', 'space-operation')} />
    ),
  },
  {
    function: MEMBER,
    title: intl.t('成员管理'),
    icon: (
      <i className={cx('dk-iconfont', 'dk-icon-duoren', 'space-operation')} />
    ),
  },
  {
    function: TRASH,
    title: intl.t('团队空间回收站'),
    icon: (
      <i
        className={cx(
          'dk-iconfont',
          'dk-icon-huishouzhan4px',
          'space-operation',
        )}
      />
    ),
  },

  {
    function: SETTING,
    title: intl.t('空间设置'),
    icon: (
      <i className={cx('dk-iconfont', 'dk-icon-shezhi4', 'space-operation')} />
    ),
  },
];

const TabItem = (props) => {
  const { item, activeKey, isShow, removeTab, renameTab, onTabChange } = props;

  const [visible, setVisible] = useState(false);


  return (
    <div
      key={item.tabKey}
      className={`${cx(
        'space-tab-title-item',
        activeKey === item.tabKey && 'space-tab-title-item-active',
        item.function === HOME && 'space-tab-title-item-home',
        item.function !== HOME && item.function !== FILE && 'space-tab-title-item-knowledge',
      )} space-tab-title-item`}
      onClick={() => {
        onTabChange(item);
      }}
      onMouseEnter={() => {
        setVisible(true)
      }}
      onMouseLeave={() => {
        setVisible(false)
      }}
    >
      <TabItemContent
        visible={visible}
        isShow={isShow}
        item={item}
        removeTab={removeTab}
        renameTab={renameTab}
      />
    </div>
  )
}

const DropdownTabItem = (props) => {
  const { item, isShow, removeTab, renameTab, onTabChange } = props;

  const [visible, setVisible] = useState(false);
  return (
    <div
      className={cx('dropdown-tab-item')}
      onClick={() => {
        onTabChange(item);
      }}
      onMouseEnter={() => {
        setVisible(true)
      }}
      onMouseLeave={() => {
        setVisible(false)
      }}
    >
      <TabItemContent
        visible={visible}
        isShow={isShow}
        item={item}
        removeTab={removeTab}
        renameTab={renameTab}
      />
    </div>
  )
}

const TabItemContent = (props) => {
  const { item, visible, removeTab, renameTab } = props;
  const hoverDivRef = useRef(null);
  const hoverIconRef = useRef(null);
  const [showTooltip, setShowTooltip] = useState(false);

  const {
    teamDetail,
  } = useSelector(
    (state) => state.TeamData,
  );

  const { role } = teamDetail;

  const judgeLength = () => {
    if (hoverDivRef.current) {
      return hoverDivRef.current.scrollWidth > hoverDivRef.current.clientWidth;
    }
    return false;
  };

  useEffect(() => {
    if (hoverDivRef.current) {
      hoverDivRef.current.originalWidth = hoverDivRef.current.offsetWidth;
    }
    if (hoverIconRef.current) {
      hoverDivRef.current.display = 'none';
    }
    setShowTooltip(judgeLength());
  }, []);

  useEffect(() => {
    if (item.function === FILE) {
      return;
    }
    if (hoverDivRef.current && hoverIconRef.current) {
      if (visible) {
        // hoverDivRef.current.style.width = `${hoverDivRef.current.originalWidth - 23}px`;
        hoverIconRef.current.style.display = 'block';
      } else {
        // hoverDivRef.current.style.width = `${hoverDivRef.current.originalWidth}px`;
        hoverIconRef.current.style.display = 'none';
      }
    }
  }, [visible]);

  return (
    <div
      className={cx('space-tab-title-item-content')}
    >
      {
        item.function === HOME && (
          <img
            src={home}
            alt='首页'
            className={cx('space-tab-title-item-content-dk')}
          />
        )
      }
      {
        item.function === FILE && (
          <img
            src={folder}
            alt='文件'
            className={cx('space-tab-title-item-content-dk')}
          />
        )
      }
      {
        item.function === KNOWLEDGE && (
          <img
            src={dkBook}
            alt='知识库'
            className={cx('space-tab-title-item-content-dk')}
          />
        )
      }
      <div className={cx('space-tab-title-item-content-text')}>
        {
          showTooltip ? (
            <Tooltip
              title={item.displayName || item.nameCn}
              placement='top'
            >
              <div
                ref={hoverDivRef}
                className={cx('space-tab-title-item-content-name')}
              >
                {item.displayName || item.nameCn}
              </div>
            </Tooltip>
          ) : (
            <div
              ref={hoverDivRef}
              className={cx('space-tab-title-item-content-name')}
            >
              {item.displayName || (getLocale() === 'en-US' ? item.nameEn : item.nameCn)}
            </div>
          )
        }
        {
          ((item.function === KNOWLEDGE) || (item.function === HOME && role === TEAM_OWNER)) && (
            <Popover
              placement='bottom'
              content={
                <TabMenu
                  feature={item}
                  removeTab={removeTab}
                  renameTab={renameTab}
                />
              }
              width={212}
              overlayClassName={'cooper-team-dk-createDk'}
              zIndex={1999}
              destroyTooltipOnHide
            >
              <div
                className={cx('space-tab-title-item-content-icon')}
                style={{ display: 'none' }}
                ref={hoverIconRef}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <i className={cx('dk-iconfont', 'dk-icon-sandian', 'tab-operation')} />
              </div>
            </Popover>
          )
        }
      </div>
    </div>
  )
}

function TeamMain() {
  const { teamId, knowledgeId } = useParams();
  // const [teamDk, setTeamDk] = useState();
  // const [suiteData, setSuiteData] = useState({});
  const { pathname } = useLocation();
  const dispatch = useDispatch();
  const { saveFuncs } = dispatch.TeamData;

  const [displayFeatureView, setDisplayFeatureView] = useState([]);
  const [hideFeatureView, setHideFeatureView] = useState([]);
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [manageModalOpen, setManageModalOpen] = useState(false);
  const [renameModalOpen, setRenameModalOpen] = useState(false);
  const [renameFeature, setRenameFeature] = useState();
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [showConnectModal, setShowConnectModal] = useState(false);
  const [updateDK, setUpdateDK] = useState(true);
  const [isFirstRender, setIsFirstRender] = useState(true);

  const { toastTeamTrashTip, userViewData } = useSelector((state) => state.GlobalData);
  const { getAsideTeamList, setToastTeamTrashTip, setUserViewDataRq } = dispatch.GlobalData;
  const {
    getTeamData,
    setActiveListTab,
    saveCurrentFuns,
    activeListTab,
    setFeatures,
    getFeatures,
  } = dispatch.TeamData;


  const {
    teamDetail,
    features,
    currentFuns,
  } = useSelector(
    (state) => state.TeamData,
  );

  const activeKey = currentFuns?.tabKey;

  const { role, pinned } = teamDetail;
  const navigate = useNavigate();

  const isMainTab = !(['/trash', '/member', '/setup', '/dynamic'].find((item) => pathname.includes(item)));

  let featuresView = useMemo(() => {
    if(isFirstRender) return [];
    return features;
  }, [features, isFirstRender]);

  const handleResize = useCallback(() => {
    setDisplayFeatureView(featuresView);
    if (featuresView.length === 0) return;
    const totalWidth = document.getElementById('space-tab-title')?.clientWidth;
    const remainWidth = totalWidth - 80; // 80是后面的添加按钮的宽度
    const items = document.querySelectorAll('.space-tab-title-item');

    if (items.length === 0) return;

    let count = 0;
    let total = 0;

    for (let i = 0; i < items.length; i++) {
      total = total + items[i].offsetWidth + 2;
      if (total <= remainWidth) {
        count++;
      }
    }

    for (let i = 0; i < items.length; i++) {
      if (i < count) {
        items[i].style.visibility = 'visible';
        items[i].style.flex = `1 1 ${items[i].offsetWidth}px`;
      } else {
        items[i].style.visibility = 'hidden';
        items[i].style.flex = '0 0 0px';
      }
    }

    setHideFeatureView(
      featuresView.filter((feature) => {
        return feature.enable;
      })?.slice(count),
    );
  }, [featuresView]);

  useEffect(() => {
    if (isFirstRender) {
      setIsFirstRender(false);
      return;
    }
    setFeatures([]);
    featuresView = [];
  }, [teamId]);

  useEffect(() => {
    handleResize();
    setTimeout(() => {
      handleResize();
    }, 0);
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [featuresView]);

  const initData = () => {
    setActiveListTab('0');
  }

  useEffect(() => {
    initData();
  }, [teamId]);

  useEffect(() => {
    // features列表更新会及时更新SpaceHeaderActFuns
    updateActFuns();
  }, [pathname, features, isFirstRender]);

  const updateActFuns = () => {
    if (pathname.includes('/knowledge/')) {
      const currDk = featuresView?.find(
        // eslint-disable-next-line
        (item) => item.subjectId == knowledgeId,
      );

      if (!currDk && featuresView.length > 0) {
        message.error(intl.t('空间中不存在该知识库'));
        gotoFirstTab();
        return;
      }
      saveCurrentFuns(currDk);
    } else if (pathname.includes('/home') && !pathname.includes('/knowledge')) {
      const curHome = featuresView?.find(
        // eslint-disable-next-line
        (item) => item.function === HOME && item.enable,
      );

      if (!curHome && featuresView.length > 0) {
        gotoFirstTab();
        return;
      }
      saveCurrentFuns(featuresView?.find((item) => item.function === HOME));
    } else if (pathname.includes('/trash')) {
      saveCurrentFuns(operations.find((item) => item.function === TRASH));
    } else if (pathname.includes('/member')) {
      saveCurrentFuns(operations?.find((item) => item.function === MEMBER));
    } else if (pathname.includes('/dynamic')) {
      saveCurrentFuns(operations?.find((item) => item.function === DYNAMIC));
    } else if (pathname.includes('/setup')) {
      saveCurrentFuns(operations?.find((item) => item.function === SETTING));
    } else if (pathname.includes('/team-file/')) {
      // const curHome = featuresView?.find(
      //   // eslint-disable-next-line
      //   (item) => item.function === HOME && item.enable,
      // );

      // if (curHome) {
      //   gotoFirstTab();
      //   return;
      // }
      saveCurrentFuns(featuresView?.find((item) => item.function === FILE));
    }
  };

  useEffect(() => {
    if (isMainTab === false) {
      setActiveListTab('-1')
      return;
    }
    if (isMainTab && activeListTab === '-1') {
      setActiveListTab('0')
    }
  }, [isMainTab])

  const onTabChange = (item) => {
    if (item.function === HOME) {
      window.__OmegaEvent('ep_team_member_ck', '', {
        platform: 'new',
      });
      navigate(`/team-file/${teamId}/home`);
    } else if (item.function === FILE) {
      window.__OmegaEvent('ep_space_team_document_ck', '', {
        platform: 'new',
      });

      navigate(`/team-file/${teamId}`);
    } else if (item.function === KNOWLEDGE) {
      setUpdateDK(false);
      setTimeout(() => {
        setUpdateDK(true);
      }, 0);
      navigate(`/team-file/${teamId}/knowledge/${item.subjectId}/home`);
    }
  }

  const handleRouteChange = (item) => {
    if (!teamId) return;
    if (item.function === TRASH) {
      navigate(`/team-file/${teamId}/trash`);
    }
    if (item.function === MEMBER) {
      window.__OmegaEvent('ep_team_member_ck', '', {
        platform: 'new',
      });
      navigate(`/team-file/${teamId}/member`);
    }
    if (item.function === SETTING) {
      navigate(`/team-file/${teamId}/setup`);
    }
    if (item.function === DYNAMIC) {
      navigate(`/team-file/${teamId}/dynamic`);
    }
  };

  const onPinned = (isPinned) => {
    if (isPinned) {
      doUnPinned(teamId).then(() => {
        getAsideTeamList();
        getTeamData(teamId);
        message.success(intl.t('已取消置顶'));
      });
    } else {
      doPinned(teamId).then(() => {
        getAsideTeamList();
        getTeamData(teamId);
        message.success(intl.t('置顶成功'));
      });
    }
  };

  const menuItems = () => {
    return (
      <Menu className={cx('space-menu-dropdown')}>
        <Menu.Item
          key={'add'}
          onClick={() => {
            window.__OmegaEvent('ep_team_addtab_ck', '团队空间-添加标签页');
            setAddModalOpen(true);
          }}
        >
          <i className={cx('dk-iconfont', 'dk-icon-icon-test', 'space-dropdown-icon')} />
          <span>
            {intl.t('添加标签页')}
          </span>
        </Menu.Item>
        <Menu.Item
          key={'manage'}
          onClick={() => {
            window.__OmegaEvent('ep_team_managetab_ck', '团队空间-管理标签页');
            setManageModalOpen(true);
          }}
        >
          <i className={cx('dk-iconfont', 'dk-icon-shezhi4', 'space-dropdown-icon')} />
          <span>
            {intl.t('管理标签页')}
          </span>
        </Menu.Item>
      </Menu>
    );
  };

  const renameTab = (feature) => {
    setRenameModalOpen(true);
    setRenameFeature(feature);
  }

  const removeTab = (isHome, feature, source) => {
    const title = isHome ? intl.t('是否关闭首页') : intl.t('是否移除此标签页');
    const content = isHome ? (
      <span>
        {intl.t('“首页”关闭后，空间成员不可访问。当前设置数据会保留，重新开启后可见。')}
      </span>
    ) : (
      <span>
        {intl.t('该标签页将被移除，但数据本身不会删除，你可前往')}
        <span
          onClick={() => {
            noopenerOpen('/knowledge');
          }}
          style={{
            color: '#1A6EFF',
            padding: '0 4px',
            cursor: 'pointer',
          }}
        >
          {intl.t('知识库')}
        </span>
        {intl.t('访问')}
      </span>
    )

    const btnText = isHome ? intl.t('关闭首页') : intl.t('移除标签页');

    cooperConfirm({
      title,
      icon: null,
      content,
      okText: btnText,
      cancelText: intl.t('取消'),
      className: cx('delete-modal'),
      closable: true,
      zIndex: 9999,
      closeIcon: (
        <i
          className={cx('dk-iconfont', 'dk-icon-guanbi', 'confirm-modal-close')}
        />
      ),
      autoFocusButton: null,
      onOk() {
        if (isHome) {
          handleCloseHome(source);
        } else {
          handleCloseTab(feature, source);
        }
      },
    });
  }

  const gotoFirstTab = (result) => {
    const targetFeature = (result || features)?.find((feature) => feature.enable);
    if (targetFeature) {
      onTabChange(targetFeature);
    }
  }

  const gotoLastTab = (result) => {
    onTabChange(result);
  }

  // 开启首页
  const handleEnableHome = () => {
    const cloneFeature = cloneDeep(features);
    const homeFeature = cloneFeature.find((feature) => feature.function === HOME);
    if (homeFeature) {
      const result = cloneFeature.map((feature) => {
        if (feature.function === HOME) {
          feature.enable = true;
        }
        return feature;
      });
      saveFuncs({
        spaceId: teamId,
        functions: result,
      }).then((res) => {
        if (res) {
          gotoFirstTab(result);
        }
      });
    } else {
      cloneFeature.unshift({
        disabled: false,
        function: 'HOME',
        enable: true,
        force: false,
        nameCn: '主页',
        nameEn: 'Home',
        subjectId: null,
        displayName: null,
      });
      saveFuncs({
        spaceId: teamId,
        functions: cloneFeature,
      }).then((res) => {
        if (res) {
          gotoFirstTab(cloneFeature);
        }
      });
    }
  }

  // 关闭首页。 source 是为了区分从哪关的(TabMenu/ManageModal)
  const handleCloseHome = (source) => {
    const cloneFeature = cloneDeep(features);
    const result = cloneFeature.map((feature) => {
      if (feature.function === HOME) {
        feature.enable = false;
      }
      return feature;
    });

    saveFuncs({
      spaceId: teamId,
      functions: result,
    }).then((res) => {
      if (res) {
        if (source === handleCloseSources.MANAGE_MODAL) {
          window.__OmegaEvent('ep_team_managetab_removehonmepage_ck', '团队空间-管理标签页-关闭主页')
        } else if (source === handleCloseSources.TAB_MENU) {
          window.__OmegaEvent('ep_team_tab_closehomepage_ck', '团队空间-标签页-关闭主页')
        }
        if (pathname.includes('/home') && !pathname.includes('/knowledge')) {
          gotoFirstTab(result);
        }
      }
    })
  }

  // 移除标签页
  const handleCloseTab = (feature, source) => {
    const cloneFeature = cloneDeep(features);
    const result = cloneFeature.filter((item) => {
      return item.subjectId !== feature.subjectId;
    });

    get(DkApi.UNBOUND_TEAM_DK.replace(':metisSpaceId', feature.subjectId).replace(':spaceId', teamId)).then(() => {
      // getRelateTeamDk(spaceId);
      if (feature.subjectId === currentFuns.subjectId) {
        gotoFirstTab(result);
      }

      saveFuncs({
        spaceId: teamId,
        functions: result,
      }).then(() => {
        if (source === handleCloseSources.MANAGE_MODAL) {
          window.__OmegaEvent('ep_team_managetab_remove_ck', '团队空间-管理标签页-移除标签页')
        } else if (source === handleCloseSources.TAB_MENU) {
          window.__OmegaEvent('ep_team_tab_remove_ck', '团队空间-标签页-移除标签页')
        }
        message.success(intl.t('标签页移除成功'));
      })
    }).catch((error) => {
      message.error(error.message);
    });
  }

  // 确定重命名
  const handleRename = (name) => {
    const cloneFeature = cloneDeep(features);
    const result = cloneFeature.map((feature) => {
      if (feature.subjectId) {
        if (feature.subjectId === renameFeature.subjectId) {
          feature.displayName = name;
        }
      } else if (renameFeature.function === HOME && feature.function === HOME) {
        feature.displayName = name;
      }
      return feature;
    });
    saveFuncs({
      spaceId: teamId,
      functions: result,
    }).then(() => {
      message.success(intl.t('重命名成功'));
      window.__OmegaEvent('ep_team_managetab_rename_ck', '团队空间-管理标签页-重命名标签页')
    });
  }

  // 拖拽排序
  const handleReorderTab = (before, after) => {
    const cloneFeature = cloneDeep(features);
    const temp = cloneFeature[before];
    cloneFeature[before] = cloneFeature[after];
    cloneFeature[after] = temp;
    setFeatures(cloneFeature);
  }

  const confirmReorderTab = () => {
    const cloneFeature = cloneDeep(features);
    saveFuncs({
      spaceId: teamId,
      functions: cloneFeature,
    }).then(() => {
      window.__OmegaEvent('ep_team_managetab_move_ck', '团队空间-管理标签页-修改排序')
    });
  }

  const dropdownItems = useMemo(() => {
    return (
      <Menu className={cx('space-tab-dropdown', 'space-knowledge-dropdown')}>
        {hideFeatureView?.map((item) => {
          return (
            <Menu.Item
              key={item.tabKey}
              className={cx(
                item.tabKey === activeKey && 'space-tab-dropdown-item',
              )}
              onClick={() => {
                onTabChange(item);
              }}
            >
              <DropdownTabItem
                item={item}
                activeKey={activeKey}
                isShow={activeKey === item.tabKey && item.function !== FILE}
                renameTab={renameTab}
                removeTab={removeTab}
                onTabChange={onTabChange}
              />
            </Menu.Item>
          );
        })}
      </Menu>
    );
  }, [hideFeatureView, activeKey]);

  const closeConnectDk = () => {
    setShowConnectModal(false);
  };

  const closeAndRefresh = (res) => {
    const cloneFeature = cloneDeep(features);
    cloneFeature.push(res);
    setFeatures(cloneFeature);
    setShowConnectModal(false);
    gotoLastTab(res);
  }

  const count = featuresView.filter((feature) => {
    return feature.function === KNOWLEDGE;
  }).length;

  const handleCreate = async (view) => {
    setCreateModalOpen(false);
    getFeatures(teamId).then(() => {
      gotoLastTab(view)
    });
  }

  return (
    <>
      <div className={cx('space-tab')}>
        <div
          id='space-tab-title'
          className={cx('space-tab-title')}
        >
          <div className={cx('space-tab-title-left')}>
            {displayFeatureView?.map((feature) => {
              if (!feature.enable) {
                return null;
              }
              return (
                <TabItem
                  item={feature}
                  activeKey={activeKey}
                  isShow={activeKey === feature.tabKey && feature.function !== FILE}
                  renameTab={renameTab}
                  removeTab={removeTab}
                  onTabChange={onTabChange}
                />
              );
            })}
          </div>

          <div className={cx('space-tab-title-right')}>
            {hideFeatureView?.length > 0 && (
              <Dropdown
                overlay={dropdownItems}
                placement='bottom'
                overlayClassName={cx('space-tab-dropdown', 'menu-dropdown')}
                trigger='click'
              >
                <div
                  className={cx(
                    'space-tab-title-more',
                    hideFeatureView?.findIndex(
                      (item) => item.tabKey === activeKey,
                    ) > -1 && 'space-tab-title-more-active',
                  )}
                >
                  <i
                    className={cx(
                      'dk-iconfont',
                      'dk-icon-xiayitiaopinglun',
                      'space-more',
                    )}
                  />
                </div>
              </Dropdown>
            )}

            {(role === TEAM_OWNER || role === TEAM_ADMIN) && (
              <Dropdown
                overlay={menuItems}
                placement='bottom'
                overlayClassName={cx('space-menu-dropdown', 'menu-dropdown')}
                trigger={['click']}
              >
                <div className={cx('space-tab-title-add')}>
                  <i
                    className={cx(
                      'dk-iconfont',
                      'dk-icon-icon-test',
                      'space-add',
                    )}
                  />
                </div>
              </Dropdown>
            )}
          </div>
        </div>

        <div className={cx('space-tab-operation')}>
          {operations.map((operation) => {
            if (operation.function === 'pinned') {
              return (
                <div
                  key={operation.function}
                  className={cx(
                    'space-tab-operation-item',
                  )}
                  onClick={() => {
                    onPinned(pinned);
                  }}
                >
                  <Tooltip
                    title={
                      pinned ? intl.t('取消置顶') : intl.t('置顶')
                    }
                    placement='top'
                    align={{
                      offset: [0, -7],
                    }}
                  >
                    {
                      pinned
                        ? <i className={cx('dk-iconfont', 'dk-icon-quxiaozhiding', 'space-operation')} />
                        : <i className={cx('dk-iconfont', 'dk-icon-zhiding4px', 'space-operation')} />
                    }
                  </Tooltip>
                </div>
              )
            }
            if (operation.function === TRASH) {
              return (
                <Tooltip
                  title={() => <Tips
                    overlayClassName='blue-tip'
                    text={'被删除的内容已进入"当前团队空间回收站"，30天内你可以在这里找回。'}
                    isShow={true}
                    onClose={() => {
                      setToastTeamTrashTip(false);
                      const data = { ...userViewData };
                      data.New_Trash_Team_Close = '-1';
                      if (JSON.stringify(userViewData) === '{}') return;
                      setUserViewDataRq(data);
                    }}
                  />}
                  color='#047FFE'
                  arrowPointAtCenter={true}
                  placement='bottomRight'
                  visible={toastTeamTrashTip}
                  overlayClassName={'new-functions-tooltip'}
                >
                  <div
                    key={operation.function}
                    className={cx(
                      'space-tab-operation-item',
                      {
                        'space-tab-operation-item-active': ((
                          currentFuns?.function === TRASH && operation.function === TRASH
                        )),
                      },
                    )}
                    onClick={() => {
                      handleRouteChange(operation);
                    }}
                  >
                    <Tooltip
                      title={operation.title}
                      placement='top'
                      align={{
                        offset: [0, -7],
                      }}
                    >
                      {operation.icon}
                    </Tooltip>
                  </div>
                </Tooltip>
              )
            }
            return (
              <div
                key={operation.function}
                className={cx(
                  'space-tab-operation-item',
                  {
                    'space-tab-operation-item-active': ((
                      currentFuns?.function === TRASH && operation.function === TRASH
                    ) || (
                      currentFuns?.function === MEMBER && operation.function === MEMBER
                    ) || (
                      currentFuns?.function === SETTING && operation.function === SETTING
                    ) || (
                      currentFuns?.function === DYNAMIC && operation.function === DYNAMIC
                    )),
                  },
                )}
                onClick={() => {
                  handleRouteChange(operation);
                }}
              >
                <Tooltip
                  title={operation.title}
                  placement='top'
                  align={{
                    offset: [0, -7],
                  }}
                >
                  {operation.icon}
                </Tooltip>
              </div>
            );
          })}
        </div>
      </div>

      {currentFuns?.function === HOME && currentFuns?.enable && <SpaceDetailHome />}
      {currentFuns?.function === FILE && <SpaceTeamFiles />}
      {currentFuns?.function === TRASH && <SpaceTrash />}
      {currentFuns?.function === MEMBER && <SpaceMember />}
      {currentFuns?.function === SETTING && <SpaceSetting />}
      {currentFuns?.function === DYNAMIC && <SpaceDynamic teamId={teamId} />}
      {currentFuns?.function === KNOWLEDGE && (
        <div className={cx('space-knowledge-container')}>
          {updateDK && <LayoutDK />}
        </div>
      )}

      <AddModal
        featuresView={featuresView}
        addModalOpen={addModalOpen}
        role={role}
        setAddModalOpen={setAddModalOpen}
        setManageModalOpen={setManageModalOpen}
        handleEnableHome={handleEnableHome}
        setCreateModalOpen={setCreateModalOpen}
        setShowConnectModal={setShowConnectModal}
      />

      <ManageModal
        featuresView={featuresView}
        manageModalOpen={manageModalOpen}
        removeTab={removeTab}
        renameTab={renameTab}
        role={role}
        setManageModalOpen={setManageModalOpen}
        handleEnableHome={handleEnableHome}
        handleReorderTab={handleReorderTab}
        confirmReorderTab={confirmReorderTab}
      />

      <RenameModal
        name={renameFeature?.displayName || renameFeature?.nameCn}
        renameModalOpen={renameModalOpen}
        handleRename={handleRename}
        setRenameModalOpen={setRenameModalOpen}
      />

      <CreateDKModal
        createModalOpen={createModalOpen}
        setCreateModalOpen={setCreateModalOpen}
        handleCreate={handleCreate}
      />

      {showConnectModal && (
        <ConnectDk
          connectedDkNum={count}
          spaceId={teamId}
          closeModal={closeConnectDk}
          closeAndRefresh={closeAndRefresh}
        />
      )}
    </>
  );
}

export default TeamMain;
