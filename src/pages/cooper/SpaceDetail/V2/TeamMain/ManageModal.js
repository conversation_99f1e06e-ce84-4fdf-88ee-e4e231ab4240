import { dkBook, folder, home } from '@/assets/icon/fileIcon';
import { handleCloseSources } from '@/constants/omegaSource.js';
import { FILE, HOME, KNOWLEDGE } from '@/constants/space';
import { Modal, Tooltip } from 'antd';
import classBind from 'classnames/bind';
import cls from 'classnames';
import { useSelector } from 'react-redux';
import { getLocale, intl } from 'di18n-react';
import { useEffect, useRef, useState } from 'react';
import { TEAM_OWNER, TEAM_ADMIN } from '@/utils/cooperutils';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import ExplainPopover from '@/components/common/ExplainPopover';

import * as styles from './style.module.less';

const cx = classBind.bind(styles);

function NotNormalItem(props) {
  const { feature, idx, renameTab, removeTab, handleReorderTab, confirmReorderTab, canManage, canDrag } = props;
  const ref = useRef(null);
  const hoverDivRef = useRef(null);
  const [showTooltip, setShowTooltip] = useState(false);


  const [, drag] = useDrag({
    type: 'DragDropBox',
    item: { idx },
    isDragging: (monitor) => {
      return idx === monitor.getItem().idx;
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag: canDrag,
  });

  const [, drop] = useDrop({
    accept: 'DragDropBox',
    hover: (item) => {
      if (!ref.current) return;
      let dragIndex = item.idx;
      let hoverIndex = idx;
      if (dragIndex === hoverIndex) return;
      item.idx = hoverIndex;
      handleReorderTab(dragIndex, hoverIndex);
    },
    canDrag: canDrag,
    drop: () => {
      confirmReorderTab();
    },
  });

  useEffect(() => {
    setShowTooltip(judgeLength());
  }, []);

  const judgeLength = () => {
    if (hoverDivRef.current) {
      return hoverDivRef.current.scrollWidth > hoverDivRef.current.clientWidth;
    }
    return false;
  };


  return (
    <div
      className={cx('modal-setting-item')}
      key={feature.tabKey}
      ref={drag(drop(ref))}
    >
      {canDrag && <div className={cx('modal-setting-item-drag')}>
        <i className={cx('dk-iconfont', 'dk-icon-tuozhuaipaixu', 'icon-drag')} />
      </div>}
      <div>
        <img
          alt='logo'
          className={cx('modal-setting-item-logo')}
          src={feature.function === KNOWLEDGE ? dkBook : (feature.function === HOME ? home : folder)}
        />
      </div>
      {
        showTooltip ? (
          <Tooltip
            title={feature.displayName || feature.nameCn}
            placement='top'
          >
            <div
              className={cx('modal-setting-item-name')}
              ref={hoverDivRef}
            >
              {feature.displayName || feature.nameCn}
            </div>
          </Tooltip>
        ) : (
          <div
            className={cx('modal-setting-item-name')}
            ref={hoverDivRef}
          >
            {feature.displayName || feature.nameCn}
          </div>
        )
      }
      {
        feature.function !== FILE && (
          <div
            className={cx('modal-setting-item-icon', {
              disable: !canManage,
            })}
            onClick={() => {
              if (!canManage) return;
              renameTab(feature);
            }}
          >
            <Tooltip
              overlayClassName={cx('dk-ant-tooltip__reset')}
              title={canManage ? intl.t('重命名') : intl.t('你没有权限，可联系空间所有者操作')}
            >
              <i className={cx('dk-iconfont', 'dk-icon-zhongmingming2', 'icon-edit', 'icon-normal')} />
            </Tooltip>
          </div>
        )
      }

      {
        feature.function === KNOWLEDGE && !(feature.tags?.includes('SPACE_DEPART')) && (
          <div
            className={cx('modal-setting-item-icon', {
              disable: !canManage,
            })}
            onClick={() => {
              if (!canManage) return;
              removeTab(false, feature, handleCloseSources.MANAGE_MODAL); // isHome, feature, source
            }}
          >
            <Tooltip
              overlayClassName={cx('dk-ant-tooltip__reset')}
              title={canManage ? intl.t('删除') : intl.t('你没有权限，可联系空间所有者操作')}
            >
              <i className={cx('dk-iconfont', 'dk-icon-huishouzhan4px', 'icon-delete', 'icon-normal')} />
            </Tooltip>
          </div>
        )
      }
    </div>
  )
}

function NormalItem(props) {
  const { feature, removeTab, handleEnableHome,canDrag, role } = props;
  return (
    <div
      key={feature.tabKey}
      className={cx('modal-setting-item')}
    >
      {canDrag && <div className={cx('modal-setting-item-drag')}>
        {/* <i className={cx('dk-iconfont', 'dk-icon-tuozhuaipaixu', 'icon-drag')} /> */}
      </div>}
      <div>
        <img
          alt='logo'
          className={cx('modal-setting-item-logo')}
          src={feature.function === KNOWLEDGE ? dkBook : (feature.function === HOME ? home : folder)}
        />
      </div>
      <div className={cx('modal-setting-item-name')}>
        {feature.displayName || (getLocale() === 'zh-CN' ? feature.nameCn : feature.nameEn)}
      </div>
      {feature.function === HOME && (
        <div
          className={cx('modal-setting-item-icon', {
            disable: role !== TEAM_OWNER,
          })}
          onClick={() => {
            if (role !== TEAM_OWNER) return;
            if (feature.enable) {
              removeTab(true, undefined, handleCloseSources.MANAGE_MODAL); // isHome, feature, source
            } else {
              handleEnableHome();
            }
          }}
        >
          {
            feature.enable
              ? (
                <Tooltip
                  overlayClassName={cx('dk-ant-tooltip__reset')}
                  title={role === TEAM_OWNER ? intl.t('关闭') : intl.t('你没有权限，可联系空间所有者操作')}
                >
                  <i
                    className={cx('dk-iconfont', 'dk-icon-guanbi2', 'icon-view', 'icon-normal')} />
                </Tooltip>
              ) : (
                <Tooltip
                  overlayClassName={cx('dk-ant-tooltip__reset')}
                  title={role === TEAM_OWNER ? intl.t('开启') : intl.t('你没有权限，可联系空间所有者操作')}
                >
                  <i
                    className={cx('dk-iconfont', 'dk-icon-kejian', 'icon-view', 'icon-normal')} />
                </Tooltip>
              )
          }
        </div>
      )}
    </div>
  )
}

function ManageModal(props) {
  const {
    manageModalOpen,
    setManageModalOpen,
    featuresView,
    removeTab,
    renameTab,
    handleEnableHome,
    handleReorderTab,
    confirmReorderTab,
    role,
  } = props;

  const { globalOutsideChain } = useSelector((state) => state.CooperIndex);

  const explainText = <div>
    <p className={cx('explainText-p')}>{intl.t('空间所有者：支持添加、修改、移除所有关联的知识库，支持管理首页')}</p>
    <p className={cx('explainText-p')}>{intl.t('空间管理员：仅支持添加、修改、移除自己关联的知识库')}</p>
    <a
      className={cls('link-explain')}
      target='_blank'
      href={globalOutsideChain?.relate_dk_perm}>{ intl.t('查看详细权限说明')}</a>
  </div>


  const ModalTitle = <div className={cx('title-wrap')}>
    <span className={cx('title-span')}>{intl.t('管理标签页')}</span>
    <ExplainPopover
      text={intl.t('权限说明')}
      content={explainText}
      className={cls('explain-popover-create')}
      getPopupContainer={(e) => e.parentNode}
  />
  </div>


  const canManageFn = (feature) =>  role === TEAM_OWNER || (role === TEAM_ADMIN && feature.isMapperCreator);

  const dragContent = ({ index, feature }) => {
    return <NotNormalItem
      key={index}
      idx={index}
      feature={feature}
      removeTab={removeTab}
      renameTab={renameTab}
      handleEnableHome={handleEnableHome}
      handleReorderTab={handleReorderTab}
      confirmReorderTab={confirmReorderTab}
      canManage={canManageFn(feature)}
      canDrag={role === TEAM_OWNER}
    />
  }


  return (
    <Modal
      width={620}
      centered={true}
      destroyOnClose={true}
      className={cx('modal-setting')}
      title={ModalTitle}
      visible={manageModalOpen}
      onOk={() => {
        setManageModalOpen(false);
      }}
      onCancel={() => {
        setManageModalOpen(false);
      }}
      footer={null}
      closeIcon={
        <i
          className={cx(
            'dk-iconfont',
            'dk-icon-guanbi',
            'modal-setting-close',
          )}
        />
      }
    >
      {
        <div className={cx('modal-setting-content')}>
          {
            featuresView.map((feature, index) => {
              if (feature.function === HOME || feature.function === FILE) {
                return (
                  <NormalItem
                    key={index}
                    feature={feature}
                    removeTab={removeTab}
                    handleEnableHome={handleEnableHome}
                    canManage={canManageFn(feature)}
                    canDrag={role === TEAM_OWNER}
                    role={role}
                  />
                )
              }
              return (
                role === TEAM_OWNER
                  ? <DndProvider>{dragContent({ index, feature })}</DndProvider>
                  : dragContent({ index, feature })
              )
            })
          }
        </div>

      }
    </Modal>
  );
}

export default ManageModal;
