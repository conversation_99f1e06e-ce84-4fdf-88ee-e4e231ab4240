import { addModalHome, addModalHomeEn, addModalKnowledge, addModalKnowledgeEn } from '@/assets/icon';
import { HOME } from '@/constants/space';
import { Button, Modal } from 'antd';
import classBind from 'classnames/bind';
import cls from 'classnames';
import { TEAM_OWNER, TEAM_ADMIN } from '@/utils/cooperutils';
import ExplainPopover from '@/components/common/ExplainPopover';
import { getLocale, intl } from 'di18n-react';
import { useSelector } from 'react-redux';

import * as styles from './style.module.less';

const cx = classBind.bind(styles);

function ManageModal(props) {
  const {
    addModalOpen,
    setAddModalOpen,
    featuresView,
    setManageModalOpen,
    handleEnableHome,
    setCreateModalOpen,
    setShowConnectModal,
    role,
  } = props;
  const curLocale = getLocale();
  const { globalOutsideChain } = useSelector((state) => state.CooperIndex);
  let homeEnabled = false;


  featuresView.forEach((feature) => {
    if (feature.function === HOME) {
      homeEnabled = feature.enable;
    }
  });

  const explainText = <div>
    <p className={cx('explainText-p')}>{intl.t('空间所有者：支持添加、修改、移除所有关联的知识库，支持管理首页')}</p>
    <p className={cx('explainText-p')}>{intl.t('空间管理员：仅支持添加、修改、移除自己关联的知识库')}</p>
    <a
      className={cls('link-explain')}
      target='_blank'
      href={globalOutsideChain?.relate_dk_perm}>{ intl.t('查看详细权限说明')}</a>
  </div>


  const ModalTitle = <div className={cx('title-wrap')}>
    <span className={cx('title-span')}>{intl.t('添加标签页')}</span>
    <ExplainPopover
      text={intl.t('权限说明')}
      content={explainText}
      className={cls('explain-popover-create')}
      getPopupContainer={(e) => e.parentNode}
    />
  </div>

  return (
    <Modal
      width={880}
      style={{ top: 120 }}
      destroyOnClose={true}
      className={cx('modal-setting', 'modal-add')}
      title={ModalTitle}
      visible={addModalOpen}
      onOk={() => {
        setAddModalOpen(false);
      }}
      onCancel={() => {
        setAddModalOpen(false);
      }}
      footer={null}
      closeIcon={
        <i
          className={cx(
            'dk-iconfont',
            'dk-icon-guanbi',
            'modal-setting-close',
          )}
        />
      }
    >
      <div className={cx('modal-setting-cards')}>
        <div className={cx('modal-setting-card')}>
          <div className={cx('modal-setting-card-title')}>
            {intl.t('首页')}
          </div>
          <div className={cx('modal-setting-card-desc')}>
            {intl.t('展示重要信息进行团队透传')}
          </div>
          <div>
            <img
              className={cx('modal-setting-card-pic')}
              alt={intl.t('首页')}
              src={curLocale === 'en-US' ? addModalHomeEn : addModalHome}
            />
          </div>
          <div className={cx('modal-setting-card-btns')}>
            {
              !homeEnabled ? (
                <Button
                  type='primary'
                  disabled={role !== TEAM_OWNER}
                  className={cx('modal-setting-card-btn')}
                  onClick={() => {
                    window.__OmegaEvent('ep_team_addtab_openhomepage_ck', '团队空间-添加标签页-开启首页')
                    setAddModalOpen(false);
                    handleEnableHome();
                  }}
                >
                  <i className={cx('dk-iconfont', 'dk-icon-icon-test', 'space-dropdown-icon')} />
                  {role === TEAM_OWNER ? intl.t('添加') : intl.t('仅空间所有者可操作')}
                </Button>
              ) : (
                <Button
                  type='secondary'
                  disabled={role !== TEAM_OWNER}
                  className={cx('modal-setting-card-btn')}
                  onClick={() => {
                    window.__OmegaEvent('ep_team_addtab_managehomepage_ck', '团队空间-添加标签页-管理首页')
                    setAddModalOpen(false);
                    setManageModalOpen(true);
                  }}
                >
                  {role === TEAM_OWNER ? intl.t('首页已开启，前往管理') : intl.t('仅空间所有者可操作')}
                </Button>
              )
            }
          </div>
        </div>
        <div className={cx('modal-setting-card')}>
          <div className={cx('modal-setting-card-title')}>
            {intl.t('知识库新')}
          </div>
          <div className={cx('modal-setting-card-desc')}>
            {intl.t('用结构化目录形式进行知识沉淀')}
          </div>
          <div>
            <img
              className={cx('modal-setting-card-pic')}
              alt={intl.t('知识库')}
              src={curLocale === 'en-US' ? addModalKnowledgeEn : addModalKnowledge}
            />
          </div>
          <div className={cx('modal-setting-card-btns', 'modal-add-card-btns')}>
            <Button
              type='secondary'
              className={cx('modal-setting-card-btn', 'modal-setting-card-btn-left')}
              onClick={() => {
                window.__OmegaEvent('ep_team_addtab_linknew_ck', '团队空间-添加标签页-关联新知识库')
                setCreateModalOpen(true);
                setAddModalOpen(false);
              }}
            >
              <i
                className={cx('dk-iconfont', 'dk-icon-chaolianjie', 'space-dropdown-icon')}
              />
              {intl.t('关联新知识库')}
            </Button>
            <Button
              type='primary'
              className={cx('modal-setting-card-btn', 'modal-setting-card-btn-right')}
              onClick={() => {
                window.__OmegaEvent('ep_space_team_relatedk_ck', '团队空间-关联知识库')
                setShowConnectModal(true);
                setAddModalOpen(false);
              }}
            >
              <i
                className={cx('dk-iconfont', 'dk-icon-chaolianjie', 'space-dropdown-icon')}
              />
              {intl.t('关联已有知识库')}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}

export default ManageModal;
