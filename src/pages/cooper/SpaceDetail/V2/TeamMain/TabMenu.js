
import { handleCloseSources } from '@/constants/omegaSource.js';
import { HOME } from '@/constants/space';
import { noopenerOpen, TEAM_ADMIN, TEAM_OWNER } from '@/utils/cooperutils';
import { message, Tooltip } from 'antd';
import classBind from 'classnames/bind';
import { intl } from 'di18n-react';
import CopyToClipboard from 'react-copy-to-clipboard';
import { useSelector } from 'react-redux';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

const TabMenu = (props) => {
  const { feature, removeTab, renameTab } = props;
  const { origin } = window.location;

  const {
    teamDetail,
  } = useSelector(
    (state) => state.TeamData,
  );

  const { role } = teamDetail;

  let baseOrigin = '';
  baseOrigin = origin.includes('cooper-vpn') ? 'https://cooper.didichuxing.com' : origin;
  if (process.env.APP_ENV === 'dev') {
    baseOrigin = 'http://localhost:4001';
  }

  return (
    <div>
      {
        feature.function !== HOME && (
          <p
            className={cx('popover-menu')}
            onClick={(e) => {
              window.__OmegaEvent('ep_team_tab_opennew_ck', '团队空间-标签页-在浏览器中新开页签');
              e.stopPropagation();
              noopenerOpen(`/knowledge/${feature.subjectId}/home`);
            }}
          >
            <i className={cx('dk-iconfont', 'dk-icon-liulanqi', 'tab-dropdown-icon')} />
            {intl.t('在浏览器中新开页签')}
          </p>
        )
      }
      {
        feature.function !== HOME && (
          <div
            onClick={(e) => {
              window.__OmegaEvent('ep_team_tab_copylink_ck', '团队空间-标签页-复制链接');
              e.stopPropagation();
            }}
          >
            <CopyToClipboard
              text={`${baseOrigin}/knowledge/${feature.subjectId}/home`}
              onCopy={() => {
                message.success(intl.t('复制成功'));
              }}
            >
              <p className={cx('popover-menu')}>
                <i className={cx('dk-iconfont', 'dk-icon-fuzhilianjie3', 'tab-dropdown-icon')} />
                {intl.t('复制链接')}
                <Tooltip
                  placement='top'
                  overlayStyle={{ zIndex: 9999 }}
                  title={intl.t('知识库原链接，知识库成员可访问')}
                >
                  <i className={cx('dk-iconfont', 'dk-icon-a-tishi2', 'tab-info-icon')} />
                </Tooltip>
              </p>
            </CopyToClipboard>
          </div>

        )
      }
      {
        feature.function !== HOME
        && <Tooltip
          title={(role === TEAM_OWNER || (role === TEAM_ADMIN && feature.isMapperCreator )) ? '' : intl.t('你没有权限，可联系空间所有者操作')}
          placement='top'
          >
          <p
            className={cx('popover-menu', {
              disable: !(role === TEAM_OWNER || (role === TEAM_ADMIN && feature.isMapperCreator )),
            })}
            onClick={(e) => {
              e.stopPropagation();
              if (role === TEAM_OWNER || (role === TEAM_ADMIN && feature.isMapperCreator )) {
                window.__OmegaEvent('ep_team_tab_rename_ck', '团队空间-标签页-重命名标签页');
                renameTab(feature);
              }
            }}
              >
            <i className={cx('dk-iconfont', 'dk-icon-zhongmingming', 'tab-dropdown-icon')} />
            {intl.t('重命名标签页')}
          </p>

        </Tooltip>
        }

      {
        feature.function !== HOME && !(feature.tags?.includes('SPACE_DEPART')) && (
          <Tooltip
            title={(role === TEAM_OWNER || (role === TEAM_ADMIN && feature.isMapperCreator )) ? '' : intl.t('你没有权限，可联系空间所有者操作')}
            placement='top'
          >
            <p
              className={cx('popover-menu', 'popover-menu-delete', {
                disable: !(role === TEAM_OWNER || (role === TEAM_ADMIN && feature.isMapperCreator )),
              })}
              onClick={(e) => {
                e.stopPropagation();
                if (role === TEAM_OWNER || (role === TEAM_ADMIN && feature.isMapperCreator )) {
                  removeTab(false, feature, handleCloseSources.TAB_MENU);
                }
              }}
              >
              <i className={cx('dk-iconfont', 'dk-icon-shanchu3', 'tab-dropdown-icon')} />
              {intl.t('移除')}
            </p>

          </Tooltip>

        )
      }
      {
        feature.function === HOME && role === TEAM_OWNER && (
          <p
            className={cx('popover-menu', 'popover-menu-delete')}
            onClick={(e) => {
              e.stopPropagation();
              removeTab(true, feature, handleCloseSources.TAB_MENU);
            }}
          >
            <i className={cx('dk-iconfont', 'dk-icon-icon_guanbi', 'tab-dropdown-icon')} />
            {intl.t('关闭')}
            <Tooltip
              placement='top'
              overlayStyle={{ zIndex: 9999 }}
              title={intl.t('关闭后数据将保留，支持再次添加')}
            >
              <i className={cx('dk-iconfont', 'dk-icon-a-tishi2', 'tab-info-icon')} />
            </Tooltip>
          </p>
        )
      }
    </div>
  );
}

export default TabMenu;
