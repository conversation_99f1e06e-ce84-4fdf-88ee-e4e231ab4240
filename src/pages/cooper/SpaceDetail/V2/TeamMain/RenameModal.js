import { useState, useEffect, useRef } from 'react';
import { intl } from 'di18n-react';
import { Input, Modal } from 'antd';
import classBind from 'classnames/bind';

import * as styles from './style.module.less';
import { checkInputValue } from '@/utils/cooperutils';

const cx = classBind.bind(styles);

function RenameModal(props) {
  const { name, renameModalOpen, setRenameModalOpen, handleRename } = props;
  const [value, setValue] = useState(name);
  const [errorText, setErrorText] = useState('');
  const inputRef = useRef(null);

  useEffect(() => {
    setValue(name);

    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.setSelectionRange(0, name.length);
      }
    }, 0);
  }, [name]);

  const handleChange = (e) => {
    const v = e.target.value;
    let s = checkInputValue(v);
    setValue(e.target.value);
    setErrorText(s);
  }

  return (
    <Modal
      width={480}
      centered={true}
      destroyOnClose={true}
      className={cx('modal-setting', 'modal-rename')}
      title={intl.t('重命名标签页')}
      visible={renameModalOpen}
      zIndex={9999}
      onOk={() => {
        if (value === '') return;
        setRenameModalOpen(false);
        handleRename(value);
      }}
      onCancel={() => {
        setRenameModalOpen(false);
      }}
      okText={intl.t('确认')}
      cancelText={intl.t('取消')}
      okButtonProps={{
        disabled: value === '' || errorText !== '',
      }}
      closeIcon={
        <i
          className={cx(
            'dk-iconfont',
            'dk-icon-guanbi',
            'modal-setting-close',
          )}
        />
      }
    >
      <div className={cx('modal-setting-cards')}>
        <Input
          ref={inputRef}
          value={value}
          status={value === '' ? 'error' : 'normal'}
          className={cx('rename-input')}
          onChange={handleChange}
          placeholder={intl.t('请输入标签页名称')}
        />
      </div>
      {errorText ? <div className={cx('error-text')}>{errorText}</div> : null}
    </Modal>
  );
}

export default RenameModal;
