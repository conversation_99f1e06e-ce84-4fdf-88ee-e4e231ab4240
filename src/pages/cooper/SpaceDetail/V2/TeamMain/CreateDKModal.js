import { createKnowledge } from '@/service/knowledge/createKnowledgeModal';
import { checkInputValue } from '@/utils/cooperutils';
import { Button, Input, Modal, message } from 'antd';
import classBind from 'classnames/bind';
import { intl } from 'di18n-react';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import * as styles from './style.module.less';

const cx = classBind.bind(styles);

function CreateDKModal(props) {
  const { createModalOpen, setCreateModalOpen, handleCreate } = props;
  const [value, setValue] = useState('');
  const [errorText, setErrorText] = useState('');
  const [confirmLoading, setConfirmLoading] = useState(false);
  const { teamId } = useParams();

  useEffect(() => {
    setValue('');
  }, [createModalOpen]);

  const handleChange = (e) => {
    const v = e.target.value;
    let s = checkInputValue(v, 100);
    setValue(e.target.value);
    setErrorText(s);
  }

  const handleConfirm = () => {
    if (errorText) return;
    window.__OmegaEvent('ep_team_addtab_linknew_confirm_ck', '团队空间-添加标签页-关联创建新知识库')
    setConfirmLoading(true);
    createKnowledge({
      exPicture: '//img-ys011.didistatic.com/static/cooper_yx/do1_jWXD884PuZvJlY6TYLTD',
      metisSpaceName: value,
      allMapperSpaceId: [teamId],
    }).then((res) => {
      message.success(intl.t('message-创建并关联成功'));
      handleCreate({
        displayName: null,
        enable: true,
        force: false,
        nameCn: value,
        nameEn: value,
        subjectId: res.metisSpaceId,
        function: 'KNOWLEDGE',
        tabKey: `KNOWLEDGE-${res.metisSpaceId}`,
      });
    }).catch((error) => {
      message.error(error.message);
    }).finally(() => {
      setConfirmLoading(false);
    });
  }

  return (
    <Modal
      width={370}
      style={{ top: 120 }}
      destroyOnClose={true}
      className={cx('modal-setting', 'modal-create')}
      title={intl.t('创建并关联知识库')}
      visible={createModalOpen}
      zIndex={9999}
      footer={null}
      okText={intl.t('确认')}
      cancelText={intl.t('取消')}
      maskClosable={true}
      onCancel={() => {
        setCreateModalOpen(false);
      }}
      closeIcon={
        <i
          className={cx(
            'dk-iconfont',
            'dk-icon-guanbi',
            'modal-setting-close',
          )}
        />
      }
    >
      <div className={cx('modal-setting')}>
        <div className={cx('modal-create-desc')}>
          {intl.t('创建一个新的知识库，并自动关联到该团队空间')}
        </div>
        <Input
          autoFocus
          value={value}
          className={cx('create-input')}
          onChange={handleChange}
          placeholder={intl.t('请输入知识库名字')}
        />
        {errorText ? <div className={cx('error-text')}>{errorText}</div> : null}
        <div className={cx('modal-create-card-btns')}>
          <Button
            type='primary'
            className={cx('modal-setting-card-btn')}
            disabled={errorText !== '' || value === ''}
            onClick={handleConfirm}
            loading={confirmLoading}
          >
            {intl.t('创建')}
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default CreateDKModal;
