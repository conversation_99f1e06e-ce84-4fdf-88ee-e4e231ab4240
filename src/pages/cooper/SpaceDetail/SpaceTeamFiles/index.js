/* eslint-disable camelcase */
/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-06-08 11:04:30
 * @LastEditTime: 2023-08-07 11:12:53
 * @Description: 个人空间
 * @FilePath: /knowledgeforge/src/pages/cooper/SpaceDetail/SpaceTeamFiles/index.js
 *
 */
import classBind from 'classnames/bind';
import * as styles from './style.module.less';
import CooperSubHead from '@/components/CooperSubHead';
import CooperFilesList from '@/components/CooperFilesList';
import { useEffect, useState } from 'react';
import { intl } from 'di18n-react';
import { getCrumbs } from '@/service/cooper/index';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useLocation } from 'react-router-dom';
import { TEAM_COOPER, TEAM_OPREATION_TYPE } from '@/constants/cooper';
import NewCreate from '@/components/LayoutCooper/Header/NewCreate/index';
import NewUpload from '@/components/LayoutCooper/Header/NewUpload/index';
import USER_VIEW from '@/constants/userView';
import { TEAM_LIST_OPT } from '@/components/OperateMenu/constant';

const cx = classBind.bind(styles);


function SpaceTeamFiles() {
  const params = useParams()
  const location = useLocation();

  const [viewType, setViewType] = useState()
  const [teamId, setTeamId] = useState(Number(params.teamId || 0) || 0)
  const [folderId, setFolderId] = useState(Number(params.foldId || 0) || 0)
  const { teamDetail } = useSelector((state) => state.TeamData);

  const { cooperate_type } = teamDetail

  const [state, setState] = useState({
    isShowSwitchView: true,
    crumbs: [
      {
        path: '/disk',
        breadcrumbName: intl.t('全部文件'),
      },
    ],
  })
  // 和其他的state放在一起，更新其他的属性时，有时序问题导致数据被重置
  const [pageOption, setPageOption] = useState({});

  const { userViewData } = useSelector((s) => s.GlobalData);
  const { setUserViewDataRq } = useDispatch().GlobalData;

  useEffect(() => {
    const { Team_SPACE_FILE = {}} = userViewData;
    const { view: viewLocal, sort: sortLocal } = Team_SPACE_FILE;
    const viewTypeLocal = viewLocal;
    const FoldTreeConfig = sortLocal;
    setViewType(viewTypeLocal)
    setPageOption({ ...FoldTreeConfig })
  }, [userViewData?.Team_SPACE_FILE])


  const switchView = async (type) => {
    setViewType(type);
    const data = { ...userViewData };
    data.Team_SPACE_FILE = {
      ...userViewData.Team_SPACE_FILE,
      view: type,
    }
    if (JSON.stringify(userViewData) === '{}') return;
    setUserViewDataRq(data);
  }

  const getIds = () => {
    const { teamId: spaceId, foldId } = params;
    if (spaceId === undefined) throw new Error('teamId not found')
    return {
      teamId: Number(spaceId) || 0,
      foldId: Number(foldId) || 0,
    };
  }
  const initFiles = async () => {
    const { teamId: spaceId, foldId } = getIds()
    const crumbs = [
      {
        path: `/team-file/${spaceId}`,
        breadcrumbName: intl.t('全部文件'),
      },
    ]

    if (!Number(foldId)) {
      setFolderId(foldId)
      setTeamId(spaceId)
      setState({
        ...state,
        crumbs,
        // currentPerm: teamInfo.permission,
      });

      return;
    }

    if (Number(foldId) !== 0) {
      const cbs = await getCrumbs(foldId)
      cbs?.items && cbs?.items?.slice(1).forEach((c) => {
        if (c.id) {
          crumbs.push({
            path: `/team-file/${spaceId}/${c.id}`,
            breadcrumbName: c.name,
          })
        }
      });
    }
    setFolderId(foldId)
    setTeamId(spaceId)
    setState({
      ...state,
      crumbs,
    })
  }

  const setIsShowSwitchView = (isShow) => {
    setState({
      ...state,
      isShowSwitchView: isShow,
    })
  }
  useEffect(() => {
    initFiles()
  }, [])

  useEffect(() => {
    initFiles()
  }, [location])

  const routes = state.crumbs;
  const isOuter = cooperate_type ? cooperate_type === 'IN_OUT' : null;
  return (
    <div className={cx('team-space-wrap')}>
      <div className={cx('cooper-file-title-bottom')}>
        <CooperSubHead
          title={true}
          routes={routes}
        />
        <div className={cx('action')}>
          <NewUpload isOuter={isOuter} />
          <NewCreate
            source={'team'}
            isOuter={isOuter}/>
        </div>
      </div>
      <CooperFilesList
        folderId={folderId}
        teamId={teamId}
        isTeam={true}
        viewType={viewType}
        switchView={switchView}
        setIsShowSwitchView={setIsShowSwitchView}
        isShowSwitchView={state.isShowSwitchView}
        sortBy={pageOption.sortBy}
        orderAsc={pageOption.orderAsc}
        originFileType={TEAM_COOPER}
        fileOpreationType={TEAM_OPREATION_TYPE}
        isOuter={isOuter}
        location={TEAM_LIST_OPT}
      />
    </div>
  )
}


export default SpaceTeamFiles;
