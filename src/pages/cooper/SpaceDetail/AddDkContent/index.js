import { intl } from 'di18n-react';
import { useLocation, useParams } from 'react-router-dom';
import classBind from 'classnames/bind';
import { useSelector } from 'react-redux';
import ErrorTips from '@/components/ErrorTips';
import CreateKnowledge from '@/components/CreateKnowledge';
import NoDynamic from '@/assets/icon/empty8.png';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

function AddDkContent() {
  const { currentSpaceType } = useSelector((state) => state.TeamData);

  const isInTeamRoute = currentSpaceType === 'TEAM_SPACE';

  return (
    <div className={cx('space-add-dk')}>
      <ErrorTips
        img={NoDynamic}
        title={
          isInTeamRoute
            ? intl.t('为团队空间增加一个知识库，高效清晰沉淀团队内部知识')
            : intl.t('创建知识库进行个人知识管理')
        }
      >
        <div className={cx('create-content')}>
          <CreateKnowledge />
        </div>
      </ErrorTips>
    </div>
  );
}

export default AddDkContent;
