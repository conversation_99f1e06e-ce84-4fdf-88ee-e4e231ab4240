// import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

function SpaceDetail() {
  return (
    <div className={cx('space-detail-home')}>
      <div className={cx('space-detail-left')}>
        <div className={cx('space-detail-announcement', 'space-detail-card')}>
          <div className={cx('space-detail-card-title')}>公告</div>
          <div className={cx('space-detail-card-content')}>
            我是公告
          </div>
        </div>
        <div className={cx('space-detail-recommend', 'space-detail-card')}>
          <div className={cx('space-detail-card-title')}>推荐</div>
        </div>
        <div className={cx('space-detail-knowledge', 'space-detail-card')}>
          <div className={cx('space-detail-card-title')}>知识库</div>
        </div>
      </div>
      <div className={cx('space-detail-right')}>
        <div className={cx('space-detail-latest', 'space-detail-card')}>
          <div className={cx('space-detail-card-title')}>团队动态</div>
        </div>
      </div>
    </div>
  );
}

export default SpaceDetail;
