/* eslint-disable camelcase */
/* eslint-disable max-len */
import PermissionsPop from '@/components/PermissionsPop';
import DropdownCheckbox from '@/components/common/DropdownCheckbox';
import { doSetPermis } from '@/service/cooper/authority';
import { Radio, message, Tooltip } from 'antd';
import classBind from 'classnames/bind';
import { intl, getLocale } from 'di18n-react';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { isDiDiTenant } from '@/utils/entryEnhance';
import * as styles from './index.module.less';
import { IN_OUT } from '@/constants/space'


const cx = classBind.bind(styles);

const teamType = {
  private: {
    label: intl.t('私密-需要验证'),
    detail: intl.t('加入团队需要通过管理员审核'),
  },
  public: {
    label: intl.t('公开-无需验证'),
    detail: intl.t('任何人都可以通过链接加入'),
  },
}

export const teamPermis = function () {
  return [
    {
      label: intl.t('查看'),
      id: 0,
      value: '1',
      perm: 1,
      desc: intl.t('用户可以查看团队中文件、文件夹以及在线协作文档'),
      disabled: true,
    },
    {
      label: intl.t('编辑'),
      id: 1,
      value: '2',
      perm: 2,
      desc: intl.t('用户可以对团队中文件、文件夹进行重命名，针对在线协作文档进行修改编辑'),
    },
    {
      label: intl.t('上传'),
      id: 2,
      value: '8',
      perm: 8,
      desc: intl.t('用户可以在团队中上传文件、新建文件夹和在线协作文档'),
    },
    {
      label: intl.t('分享'),
      id: 3,
      value: '16',
      perm: 16,
      desc: intl.t('用户可以对文件、文件夹进行分享，针对在线协作文档用户可以添加邀请协作者'),
    },
    {
      label: intl.t('下载'),
      id: 4,
      value: '32',
      perm: 32,
      desc: intl.t('用户对团队中所有的文件、文件夹进行下载，针对在线协作文档可以导出'),
    },
  ];
};

function Auth(props) {
  const { teamInfo, oldPermis, oldType } = props;
  const { cooperate_type } = teamInfo
  const [tType, setTtype] = useState('private');
  const [tPermis, setTpermis] = useState([0, 1, 2]);
  const [isNormalMember, setIsNormalMemeber] = useState(teamInfo.role === 0);
  const dispatch = useDispatch();
  const { cooperLinkConf } = useSelector((state) => state.GlobalData);
  const { getTeamData } = dispatch.TeamData;

  useEffect(() => {
    // 先获取旧的权限用于显示
    setTpermis(oldPermis);
    setTtype(oldType);
  }, [oldPermis]);

  const switchType = (type) => {
    if (tType !== type) {
      let p = getPermisCode();
      updateType(teamInfo.id, type, p);
    }
  }

  const goToPermissionsDetails = () => {
    const { space_permission_zh, space_permission_en, out_space_permission_zh, out_space_permission_en } = cooperLinkConf;
    let link = isDiDiTenant()
      ? getLocale() === 'en-US' ? space_permission_en : space_permission_zh
      : getLocale() === 'en-US' ? out_space_permission_en : out_space_permission_zh;
    return link;
  }

  // 获取成员权限code
  const getPermisCode = () => {
    let p = 0;
    tPermis.forEach((v) => {
      p += teamPermis.call(this)[v].perm;
    });
    return p;
  }

  //  修改空间类型
  const updateType = async (teamId, teamType, p) => {
    const data = await doSetPermis(teamId, teamType, p);
    if (data) {
      setTtype(teamType);
      message.success(intl.t('更新成功'));
      getTeamData(teamInfo.id);
    } else {
      message.error(intl.t('空间类型修改失败'));
    }
  }

  const changePermis = async (value, newOptions) => { // eslint-disable-line no-unused-vars
    const p = value[0] === 0 ? value : [0].concat(value);
    setTpermis(p);

    let per = 0;
    p.forEach((v) => {
      per += teamPermis.call(this)[v].perm;
    });

    const data = await doSetPermis(teamInfo.id, tType, per);
    if (data) {
      message.success(intl.t('更新成功'));
      getTeamData(teamInfo.id);
    } else {
      message.error(intl.t('更新失败'));
    }
  };

  // // 更新成员权限
  // const updateMemberAuth = async () => {
  //   let p = getPermisCode();
  //   const data = await doSetPermis(teamInfo.id, tType, p);
  //   if (data) {
  //     message.success(intl.t('更新成功'));
  //     getTeamData(teamInfo.id);
  //   } else {
  //     message.error(intl.t('更新失败'));
  //   }
  // }

  return (
    <div className={cx('auth-management')}>
      <div className={cx('team-type')}>
        <p><span className={cx('dot')} />{intl.t('空间可见性')}</p>
        <div>
          <div
            onClick={() => !isNormalMember && switchType('private')}
            className={isNormalMember ? cx('normal-member') : cx('private')}>
            <Radio
              checked={tType === 'private'}
              className={isNormalMember ? cx('custom-radio') : ''} />
            <span className={cx('label')}>{teamType.private.label}</span>
            <span className={cx('des')}>{teamType.private.detail}</span>
          </div>
          <Tooltip
            // eslint-disable-next-line camelcase
            title={cooperate_type !== IN_OUT ? '' : intl.t('外部空间不支持设为公开')}
            placement='topLeft'
                  >
            <div
              // eslint-disable-next-line camelcase
              onClick={() => cooperate_type !== IN_OUT && !isNormalMember && switchType('public')}
              className={cx({
                'normal-member': isNormalMember,
                public: !isNormalMember,
                // eslint-disable-next-line camelcase
                disabled: cooperate_type === IN_OUT,
              })}
              >
              <Radio
                checked={tType === 'public'}
                className={isNormalMember ? cx('custom-radio') : ''}
                // eslint-disable-next-line camelcase
                disabled={cooperate_type === IN_OUT} />
              <span className={cx('label')}>{teamType.public.label}</span>
              <span className={cx('des')}>{teamType.public.detail}</span>
            </div>
          </Tooltip>
          {/* <div
            onClick={() => !isNormalMember && switchType('public')}
            className={isNormalMember ? cx('normal-member') : cx('public')}>
            <Radio
              checked={tType === 'public'}
              className={isNormalMember ? cx('custom-radio') : ''} />
            <span className={cx('label')}>{teamType.public.label}</span>
            <span className={cx('des')}>{teamType.public.detail}</span>
          </div> */}
        </div>
      </div>
      <div className={cx('team-permis')}>
        <p><span className={cx('dot')} />
          {intl.t('成员初始权限')}
          <PermissionsPop
            entryType={'teamSet'}
            width={371}
            bottomLink={{
              text: intl.t('了解更多团队空间权限'),
              link: goToPermissionsDetails(),
            }}
          />
        </p>
        <div className={cx('team-permis-update')} >
          <DropdownCheckbox
            overlayClassName={cx('team-permis-create')}
            className={isNormalMember ? cx('checkbox-disabled') : ''}
            showDesc={true}
            type={'bg'}
            options={teamPermis.call(this)}
            value={tPermis}
            placement={'left'}
            onChange={(value, newOptions) => changePermis(value, newOptions)}
            changeDisabled={!!isNormalMember}
          />
          {/* {
            (teamInfo.role === TEAM_OWNER || teamInfo.role === TEAM_ADMIN) && (
              <Button
                disabled={tPermis === oldPermis}
                className={cx('auth-update-button')}
                onClick={updateMemberAuth}>{intl.t('更新')}
              </Button>
            )
          } */}
        </div>
      </div>
    </div>
  );
}

export default Auth;
