.auth-management {
  width: 100%;
  padding: 0 4px;

  .team-type {
    margin-top: 9px;
    margin-bottom: 36px;

    >p {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      font-weight: normal;
      line-height: 24px;
      letter-spacing: 0em;
      color: @blueGray-color;
      margin-bottom: 10px;
    }

    .team-private {
      cursor: pointer;
    }

    .team-public {
      cursor: pointer;
    }

    .disabled {
      cursor: not-allowed;

      .label {
        color: #909499;
      }

      :global {
        .ant-radio-wrapper:hover .ant-radio-inner {
          border-color: rgba(34, 42, 53, 0.08) !important;
        }
      }
    }

    .custom-radio {
      pointer-events: none;
      /* 禁止交互事件 */
      opacity: 1;
      /* 设置透明度或直接隐藏 */
    }

    >div {
      padding: 0 13px;
    }

    >div>div {
      margin-bottom: 12px;
      line-height: 22px;
      line-height: 22px;
      font-size: 14px;
      font-family: PingFangSC-Regular;

      .label {
        color: @blueGray-color;
        margin-right: 8px;
      }

      .des {
        font-size: 12px;
        color: #6A707C;
      }
    }
  }

  .team-permis {

    >p {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      font-weight: normal;
      line-height: 24px;
      letter-spacing: 0em;
      color: @blueGray-color;
      margin-bottom: 10px;
    }



    .team-permis-update {
      display: flex;
      flex-direction: row;
      margin-left: 13px;

      .checkbox-disabled {
        cursor: not-allowed;
        pointer-events: none;
        /* 禁止交互事件 */
      }

      :global {
        .dropdown-checkbox {
          width: 372px;

          .dropdown-checkbox__value span {
            padding: 4px 28px 5px 8px;
          }
        }
      }

      .auth-update-button {
        margin-left: 8px;
        border-radius: 4px;
        background: rgba(26, 110, 255, 0.1);
        color: @primary-color-new;
        border-color: transparent;

        &:focus {
          border-color: transparent;
          // color: #2F343C;
        }

        &:hover {
          border-color: transparent;
          color: #1A75FF;
        }
      }

      .auth-update-button[disabled] {
        color: rgba(26, 110, 255, 0.5) !important;
      }

    }
  }

  .required {
    >p {
      position: relative;

      &::after {
        content: '*';
        font-size: 16px;
        color: #ff3f5a;
        position: absolute;
        left: 60px;
        top: 2px;
      }
    }

  }

}

.dot {
  display: inline-block;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: #D8DEE3;

  margin-right: 7px;
  position: relative;
  top: -2px;
}