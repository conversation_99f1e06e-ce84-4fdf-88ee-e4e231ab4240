/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-09-14 10:24:25
 * @LastEditTime: 2023-09-21 15:14:34
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/pages/cooper/SpaceDetail/SpaceSetting/components/Basic/index.js
 *
 */
import classBind from 'classnames/bind';
import { TeamQuota } from '@/components/QuotaLib';
import Rename from '../Rename';
import * as styles from './index.module.less'
import { useDispatch } from 'react-redux';

const cx = classBind.bind(styles);

function Basic(props) {
  const { teamInfo, spaceQuota, usedQuota } = props;
  const dispatch = useDispatch();

  const { getTeamData } = dispatch.TeamData;

  const refresh = (teamId) => {
    getTeamData(teamId);
  }


  return (
    <div className={cx('basic-box')}>
      <Rename {...props}/>
      <div className={cx('basic-quota')}>
        <TeamQuota
          useUp={usedQuota}
          teamId={teamInfo.id}
          total={spaceQuota}
          isOwner={teamInfo.owner}
          ownerLdap={teamInfo.ownerLdap}
          applied={Boolean(teamInfo.quota_bpm_url)}
          bpmUrl={teamInfo.quota_bpm_url}
          success={() => {
            refresh(teamInfo.id);
          }}
        />
      </div>
    </div>
  );
}

export default Basic;
