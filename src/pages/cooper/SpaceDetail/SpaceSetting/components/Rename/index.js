import { intl } from 'di18n-react';
import { useState, useEffect } from 'react';
import classBind from 'classnames/bind';
import { Button, Input, message } from 'antd'
import { CloseCircleFilled } from '@ant-design/icons'
import { checkInputValue } from '@/utils/cooperutils';
import { renameTeam } from '@/service/cooper/reame';
import { useDispatch } from 'react-redux';
import * as styles from './index.module.less'

const cx = classBind.bind(styles);

function Rename(props) {
  const { teamInfo } = props;
  const { name } = teamInfo;
  const [errorText, setErrorText] = useState('');
  const [oldName, setOldName] = useState('');
  // const [isValid, setIsValid] = useState(false);
  // const [confirmLoading, setConfirmLoading] = useState(false);
  // const [isNormalMember, setIsNormalMemeber] = useState(teamInfo.role === 0);
  const dispatch = useDispatch();
  const { getAsideTeamList } = dispatch.GlobalData;
  const { getTeamData } = dispatch.TeamData;

  useEffect(() => {
    setOldName(name);
  }, [teamInfo]);


  const doRename = async () => {
    const { id } = teamInfo;

    const renameFunc = renameTeam;

    try {
      // const { name, errorText } = this.state;
      if (errorText) return;

      await renameFunc(id, oldName.trim()).then(() => {
        message.success(intl.t('更新成功'));
        window.__OmegaEvent('ep_team_setting_rename_ck', '', {
          platform: 'new',
        });
        getAsideTeamList();
        getTeamData(id);
        // setIsValid(false);
      });
    } catch (e) {
      message.error(e.errorMessage);
    }
  };

  const changeName = (e) => {
    const v = e.target.value;
    let s = checkInputValue(v);
    // setIsValid(s === '');
    setErrorText(s);
    setOldName(v);
  };

  return (
    <div className={cx('basic-rename')}>
      <div className={cx('basic-rename-title')}><div className={cx('dot')} />{`${intl.t('空间名称')}`}</div>
      <div className={cx('basic-rename-show')}>
        <Input.TextArea
          className={cx('basic-rename-input')}
          autoSize={{ minRows: 1, maxRows: 8 }}
          value={oldName}
          onChange={changeName}
          maxLength={200}
          readOnly = {teamInfo.role === 0}
          style={teamInfo.role === 0 ? { cursor: 'not-allowed', color: '#bbbbbb' } : {}}
                    // onPressEnter={doRename}
                    // ref={node => (this.inputRef = node)}
          bordered={true}
                   />
        {/* <span>{errorText ? <div className='error-text'>{errorText}</div> : null}</span> */}

        {teamInfo.role !== 0 && (
          <Button
            className={cx('basic-rename-button')}
            onClick={() => doRename()}
          >
            {intl.t('更新')}
          </Button>
        )}

      </div>
      {errorText
        ? <div className={cx('error-text')}>
          <CloseCircleFilled />
          <span className={cx('error-text-content')}>{errorText}</span>
        </div> : null}
    </div>
  );
}

export default Rename;
