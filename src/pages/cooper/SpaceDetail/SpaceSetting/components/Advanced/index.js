import { intl } from 'di18n-react';
import { useState } from 'react';
import classBind from 'classnames/bind';
import { Button } from 'antd';
import { sendEvent } from '@/constants/cooperMirrorEvent'
import deliverTeam1 from '@/components/CooperOperation/DeliverTeam';
import deleteTeam1 from '@/components/CooperOperation/DeleteTeam';
import ApplyOwner from '@/components/CooperOperation/ApplyOwer';
import quitTeam1 from '@/components/CooperOperation/QuitTeam';
import { entryEnhance, isDiDiTenant } from '@/utils/entryEnhance'
// import { IconTeamCenter } from '@/components/LayoutCooper/icons';
import * as styles from './index.module.less'
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
// import { convertLegacyProps } from 'antd/lib/button/button';

const cx = classBind.bind(styles);

function Advanced(props) {
  const { teamInfo, teamCenterId } = props;
  const [showApplyOwner, setShowApplyOwner] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { getTeamData } = dispatch.TeamData;
  const { getAsideTeamList } = dispatch.GlobalData;

  // getAsideTeamList();
  // navigate(`/knowledge/${knowledgeId}/${newPage.pageId}/edit`);
  // }

  const deliverTeam = () => {
    sendEvent('TEAM_SETTING_TRANSFER');
    window.__OmegaEvent('ep_team_setting_transferownership_ck', '', {
      platform: 'new',
    });
    deliverTeam1(teamInfo.id, () => {
      getTeamData(teamInfo.id);
      // navigate(`/team-file/${teamInfo.id}/setup`);
    });
  };

  const deleteTeam = () => {
    sendEvent('TEAM_SETTING_DELETE');
    window.__OmegaEvent('ep_team_setting_delete_ck', '', {
      platform: 'new',
    });
    deleteTeam1(teamInfo.id, teamInfo.name, () => {
      navigate('/team-folder');
      getAsideTeamList();
    });
  };

  const applyOwner = () => {
    setShowApplyOwner(true);
    sendEvent('APPLY_TEAM_OWNER');
    window.__OmegaEvent('ep_team_setting_applytobeowner_ck', '', {
      platform: 'new',
    });
  }

  const quitTeam = () => {
    sendEvent('TEAM_SETTING_QUIT');
    window.__OmegaEvent('ep_team_setting_exit_ck', '', {
      platform: 'new',
    });
    quitTeam1(teamInfo.id, () => {
      navigate('/team-folder');
      getAsideTeamList();
    });
  };

  // 套件管理
  const goTeamCenter = () => {
    window.__OmegaEvent('ep_space_team_set_suitemanage_ck', '', {
      platform: 'new',
    });
    let host = '';
    if (window.location.hostname === 'cooper.didichuxing.com' || window.location.hostname === 'cooper-x.didichuxing.com') {
      host = 'https://team.didichuxing.com/';
    } else if (window.location.hostname.includes('qa.didichuxing.com')) {
      host = 'https://team-qa.intra.xiaojukeji.com/';
    } else host = 'https://team-test.didichuxing.com/';
    window.open(`${host}${teamCenterId}`);
  }

  return (
    <div className={cx('auth-box')}>
      {// 只有所有者可以转交团队和删除团队
        teamInfo.role === 2
          ? <div >
            <div className={cx('auth-ower')} >
              <p className={cx('title')}><span className={cx('dot')} />{intl.t('移交空间所有权')}</p>
              <p className={cx('tip')}>{intl.t('将空间所有权移交给其他用户后，你将自动变为空间管理员')}</p>
              <Button
                className={cx('red-btn')}
                onClick={deliverTeam}>{intl.t('移交空间所有权')}</Button>
            </div>
            {
              teamInfo.cooperate_type !== 'IN_OUT' && <div className={cx('auth-delete')}>
                <p className={cx('title')}><span className={cx('dot')} />{intl.t('删除空间')}</p>
                <p className={cx('tip')}>{intl.t('一旦删除成功，空间内所有内容将进您的回收站，30天后自动彻底删除。如果你删除的内容中有属于其他人的，其所有者将收到通知，请谨慎操作')}</p>
                <Button
                  className={cx('red-btn')}
                  onClick={deleteTeam}>{intl.t('删除空间')}</Button>
              </div>
            }
          </div>
          : null
      }
      {// 管理员和成员可以退出团队、申请团队所有者
        teamInfo.role === 1 || teamInfo.role === 0
          ? <div >
            {isDiDiTenant() && <div className={cx('auth-other')}>
              <p className={cx('title')}><span className={cx('dot')} />{intl.t('申请空间所有权')}</p>
              <p className={cx('tip')}>{intl.t('申请审批通过后即可成为当前团队空间所有者，拥有团队空间内的所有权限')}</p>
              <Button
                className={cx('normal-btn')}
                onClick={applyOwner}>{intl.t('申请空间所有权')}</Button>
            </div>}

            <div className={cx('auth-out')}>
              <p className={cx('title')}><span className={cx('dot')} />{intl.t('退出空间')}</p>
              <p className={cx('tip')}>{intl.t('成功退出当前团队空间后，你在该空间生产的所有内容会留在当前空间')}</p>
              <Button
                className={cx('red-btn')}
                onClick={quitTeam}>{intl.t('退出空间')}</Button>
            </div>
          </div>
          : null
      }
      {entryEnhance(teamCenterId
        && <div className={cx('auth-suite')}>
          <p className={cx('title')}><span className={cx('dot')} />{intl.t('部门套件管理')}</p>
          <p className={cx('tip')}>{intl.t('查看或管理当前部门下的其他套件')}</p>
          <Button
            className={cx('normal-btn')}
            onClick={goTeamCenter}>{intl.t('前往套件管理')}</Button>

        </div>)
      }

      {showApplyOwner && (
        <ApplyOwner
          docId={teamInfo.id}
          onClose={() => {
            sendEvent('APPLY_TEAM_OWNER_CLOSE');
            setShowApplyOwner(false);
          }}
        />
      )}
    </div>
  );
}

export default Advanced;
