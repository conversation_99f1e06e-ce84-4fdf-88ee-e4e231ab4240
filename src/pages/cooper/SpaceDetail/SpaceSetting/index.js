import { intl } from 'di18n-react';
import { useState, useEffect } from 'react';
import classBind from 'classnames/bind';
import { useSelector } from 'react-redux';
import Basic from './components/Basic';
import Auth from './components/Auth';
import Advanced from './components/Advanced';
import { getTeamQuota } from './services';
import { getDefaultPermis } from '@/utils/cooperutils';
import { CooperTabs, CooperTabsPane } from '@/components/common/CooperTabs';
import { reqSuite } from '@/service/cooper/teamSpace';
import * as styles from './index.module.less';

const cx = classBind.bind(styles);

function SpaceSetting() {
  const [chooseListType, setListType] = useState(1);
  const [spaceQuota, setSpaceQuota] = useState(0);
  const [usedQuota, setUsedQuota] = useState(0);
  const { teamDetail } = useSelector((state) => state.TeamData);
  const [suiteData, setSuiteData] = useState({});

  // const { teamCenterId } = props;
  const fetchData = async () => {
    if (teamDetail.id) {
      const quotaDetail = await getTeamQuota(teamDetail?.id);
      setSpaceQuota(quotaDetail.space_quota);
      setUsedQuota(quotaDetail.used_quota);
      // 没有updateTeamInfo，先临时将要显示的used_quota，space_quota两个值传过去用于显示
    }
  }

  async function reqDepartmentSuite(id) {
    const res = await reqSuite(id);
    const tempList = [{ title: intl.t('群聊'), suiteSourceType: 'IM', key: '2' }, { title: intl.t('圈子'), suiteSourceType: 'JZD', key: '3' }];
    res.suiteList?.forEach((item) => {
      if (item.suiteSourceType === 'IM') {
        tempList[0].isOpen = true;
      }
      if (item.suiteSourceType === 'JZD') {
        tempList[1].isOpen = true;
      }
      const index = tempList.findIndex((suite) => {
        return item.suiteSourceType === suite.suiteSourceType;
      });
      tempList[index] = { ...tempList[index], ...item };
    });
    setSuiteData({ suiteList: tempList, teamCenterId: res.teamId, suiteCenterFullUrl: res.suiteCenterFullUrl });
  }

  useEffect(() => {
    fetchData();
    setListType('1');
    reqDepartmentSuite(teamDetail.id);
  }, [teamDetail.id]);


  const LIST_HEADER_DATA = [
    {
      key: '1',
      val: () => intl.t('基本设置'),
    },
    {
      key: '2',
      val: () => intl.t('权限管理'),
    },
    {
      key: '3',
      val: () => intl.t('高级设置'),
    },
  ];

  // 切换tab
  const handleChooseTab = (listType) => {
    setListType(listType);

    if (listType === 2) {
      window.__OmegaEvent('ep_team_setting_authoritymanage_ck', '', {
        platform: 'new',
      });
    }

    // // 节省请求次数
    // if (getCurrentList(chooseListType)?.length !== 0) return

    // let query = getDefaultReqParams(chooseListType);
    // reqFilesData(query.params, query.url)
    //   .then(setListKey)
    //   .then(setCurrentList(chooseListType))
    //   .then(() => { setLoading(false); });
  }

  // const renderTabs = () => {
  //   return (
  //     <div className={cx('list-header')}>
  //       {
  //         LIST_HEADER_DATA.map((value) => {
  //           return (
  //             <div
  //               className={cx('title-item')}
  //               key={value.key}
  //               onClick={() => handleChooseTab(value.key)}
  //             >

  //               <h2
  //                 className={cx({
  //                   'title-item-txt': true,
  //                   'txt-checked': chooseListType == value.key,
  //                 })}>{value.val()}</h2>
  //               {chooseListType == value.key && <div
  //                 className={cx({
  //                   'title-check-flag': true,
  //                   'flag-checked': true,
  //                 })} />}

  //             </div>
  //           )
  //         })
  //       }
  //     </div>
  //   )
  // }

  return (
    <div className={cx('space-setting')}>
      <div className={cx('space-setting-title')}>
        <div className={cx('space-setting-title-text')}>
          {intl.t('设置')}
        </div>
      </div>
      <CooperTabs
        defaultActiveKey={chooseListType}
        activeKey={chooseListType}
        destroyInactiveTabPane={true}
        onChange={handleChooseTab}
        tabsize='large'
      >
        {
          LIST_HEADER_DATA.map((item) => {
            return (
              <CooperTabsPane
                tab={
                  <span>
                    <span>{item.val()}</span>
                  </span>
                }
                key={item.key}
              >
                {
                  item.key === '1' && <Basic
                    teamInfo={teamDetail}
                    usedQuota={usedQuota}
                    spaceQuota={spaceQuota} />
                }
                {
                  item.key === '2' && <Auth
                    teamInfo={teamDetail}
                    oldPermis={getDefaultPermis(teamDetail)}
                    oldType={teamDetail.check ? 'private' : 'public'} />
                }
                {
                  item.key === '3' && (
                    <Advanced
                      teamInfo={teamDetail}
                      teamCenterId={suiteData.teamCenterId}
                    />
                  )
                }
              </CooperTabsPane>
            )
          })
        }
      </CooperTabs>
    </div>
  );
}
// routes: /team-file/:teamId/setup
export default SpaceSetting;
