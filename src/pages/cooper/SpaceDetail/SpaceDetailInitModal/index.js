/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-07-04 15:50:42
 * @LastEditTime: 2023-08-01 12:09:23
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/pages/cooper/SpaceDetail/SpaceDetailInitModal/index.js
 *
 */
import mountAnywhere from '@/utils/mountAnywhere';
import PrimaryModal from '@/components/common/PrimaryModal';
import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import * as styles from './style.module.less';
import React, { useState } from 'react';
import { Button } from 'antd';
import { TeamSpaceInit, TeamSpaceFolder } from '@/assets/icon/index';

const cx = classBind.bind(styles);

const DES_LIST = [
  {
    icon: <i className={cx({ 'dk-icon-shouye': true, 'dk-iconfont': true, icon: true })} />,
    title: intl.t('首页'),
    text: intl.t('面向全体空间成员开放，提供信息流快速了解空间使用情况，存放空间成员学习的知识内容以及知识管理排行等'),
  },
  {
    icon: <img src={TeamSpaceFolder} />,
    title: intl.t('文件'),
    text: intl.t('碎片化的资源管理，支持本地文件线上化能力，内容快速共享协同'),
  },
  {
    icon: <i className={cx({ 'dk-icon-dafenqi': true, 'dk-iconfont': true, icon: true })} />,
    title: intl.t('空间知识库'),
    text: intl.t('结构化的知识管理，知识库就像一本书一样，将多篇不同类型的内容结构化地呈现和管理'),
  },
]

function SpaceDetailInitModalRender(props) {
  const goToSpace = () => {
    // localStorage.setItem('TEAM_SPACE_INIT', '1')
    props.onClose()
  }

  return (
    <PrimaryModal
      title=""
      width={740}
      footer={null}
      selfClassName="create-team-space team-space-init"
      onCancel={goToSpace}
    >
      <div className={cx('create-team-content')}>
        <div className={cx('create-team-content-left')}>
          <div className={cx('title')}>{intl.t('空间功能介绍')}</div>
          {
            DES_LIST.map((item, index) => {
              return (
                <div
                  className={cx('des-list')}
                  key={index}>
                  <div className={cx('title')}>
                    {item.icon}
                    {
                      item.title
                    }
                  </div>
                  <div className={cx('content')}>
                    {item.text}
                  </div>
                </div>
              )
            })
          }
        </div>
        <img
          src={TeamSpaceInit}
          className={cx('create-team-content-right')} />
        <div className={cx('action')}>
          <Button
            type='primary'
            onClick={goToSpace}>{intl.t('进入空间')}</Button>
        </div>
      </div>
    </PrimaryModal>
  )
}
function SpaceDetailInitModal(doneCallback) {
  const Comp = <SpaceDetailInitModalRender doneCallback={doneCallback} />;
  mountAnywhere(Comp);
}
export default SpaceDetailInitModal
