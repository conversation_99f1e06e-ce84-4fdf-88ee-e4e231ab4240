import { useEffect, useRef, useState } from 'react';
import { Modal } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import classBind from 'classnames/bind';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import SpaceHeader from './SpaceHeader';
import TeamMainV1 from './V1/TeamMain';
import TeamMainV2 from './V2/TeamMain';
import TeamDetailSkeleton from '@/components/SkeletonPage/TeamDetail/teamDetailForLoading';
import * as styles from './style.module.less';
import { NEED_APPROVE, NEED_CHANGE_ACCOUNT, NEED_FORBID } from '@/constants/space';
import { Helmet } from 'react-helmet';
import GetHtml from '@/utils/DOMPurify';
import axios from 'axios';
import { get } from '@/utils/request/cooper';
import api from '@/utils/request/api/CooperApi';

// import { KNOWLEDGE } from '@/constants/space';

const cx = classBind.bind(styles);

function SpaceDetail() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [spaceGray, setSpaceGray] = useState(false);
  const [loading, setLoading] = useState(true);
  const [spaceName, setSpaceName] = useState('');
  const { needTeamFileSkeleton } = useSelector((s) => s.GlobalData);
  const { setRenderNewSpaceGuide, setNeedTeamFileSkeleton } = dispatch.GlobalData;
  const cancel = useRef(null);
  const { teamId } = useParams();
  const { pathname } = useLocation();
  const teamIdRef = useRef(null);
  teamIdRef.current = teamId;

  const {
    getTeamData,
    getFeatures,
    setTeamDetail,
    setFeatures,
    setCurrSpaceId,
  } = dispatch.TeamData;

  // const isInTeamRoute = pathname.includes('/team-file/');

  useEffect(() => {
    // window.document.title = `${intl.t('团队空间')} - Cooper`;
    setTeamDetail({});
  }, []);

  useEffect(() => {
    setRenderNewSpaceGuide(spaceGray)
  }, [spaceGray])

  // 团队空间时，获取团队信息
  useEffect(() => {
    if (!teamId) return;
    getTeamData(teamId).then(async (data) => {
      setSpaceName(data.name)
      const { errorCodes } = data;
      if (errorCodes.includes(NEED_APPROVE)) {
        const inviteId = data.links[0].value;
        navigate(`/teams/invite/${inviteId}`, {
          replace: true,
        });
        setTimeout(() => {
          Modal.destroyAll();
        }, 1000);
      } else if (errorCodes.includes(NEED_FORBID)) {
        navigate('/team-file/team-invalid', {
          replace: true,
        });
        setTimeout(() => {
          Modal.destroyAll();
        }, 1000);
      } else if (errorCodes.includes(NEED_CHANGE_ACCOUNT)) {
        console.log('需要切换账号');
      } else {
        setSpaceGray(data.functionGray)
        if (!teamId) return;

        async function fetchData(url) {
          if (cancel.current) {
            cancel.current();
          }

          try {
            const response = await get(url, {
              cancelToken: new axios.CancelToken((c) => {
                cancel.current = c;
              }),
            });
            return response;
          } catch (error) {
          }
        }
        let res = await fetchData(api.TEAM_FUNCTION.replace(':teamId', teamId));
        if (teamIdRef.current != res.spaceId) return;
        const arr = res.functionList
          .filter((item) => item.function !== 'KNOWLEDGE_NEW')
          .map((item) => {
            let tabKey = item.function
            if (item.function === 'KNOWLEDGE') {
              if (item.subjectId) {
                // 此时知识库是已经绑定了的
                tabKey = `${item.function}-${item.subjectId}`
              } else if (item.functionId) {
                // 此时知识库无绑定
                tabKey = `${item.function}-${item.functionId}`
              }
            }
            return {
              ...item,
              tabKey,
            }
          });
        setFeatures(arr); setCurrSpaceId(res.spaceId);
      }
      // if (data.role === TEAM_STRANGER) {
      //   const inviteId = data.links[0].value;
      //   if (inviteId) {
      //     navigate(`/teams/invite/${inviteId}`, {
      //       replace: true,
      //     });
      //     setTimeout(() => {
      //       Modal.destroyAll();
      //     }, 1000);
      //   }
      // } else {
      //   // if (!Aside_Type_Cooper) {
      //   //   setAsideType(COOPER_ASIDE_TYPE.SMALL)
      //   // }
      //   // eslint-disable-next-line no-lonely-if
      //   // if (!TEAM_SPACE_INIT && isInTeamRoute) {
      //   //   SpaceDetailInitModal()
      //   // }

      // }
    }).catch((err) => {
      if (err.errorCode === 501060) {
        navigate('/team-file/team-invalid', {
          replace: true,
        });
      }
    }).finally(() => {
      setLoading(false);
      setNeedTeamFileSkeleton(false);
    })
  }, [pathname]);

  return (
    <div
      className={cx('space-detail-wrap')}
    >
      <Helmet>
        <title>{spaceName ? `${spaceName}-Cooper` : 'Cooper'}</title>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
      </Helmet>
      <SpaceHeader />
      {
        needTeamFileSkeleton ? (
          <div dangerouslySetInnerHTML={{ __html: GetHtml(TeamDetailSkeleton) }} />
        ) : (
          !loading && (
            <div className={cx('space-detail-main')}>
              {
                spaceGray
                  ? <TeamMainV2 />
                  : <TeamMainV1 />
              }
            </div>
          )
        )
      }
    </div>
  );
}


export default SpaceDetail;
