/* eslint-disable max-len */
/* eslint-disable camelcase */
/* eslint-disable eqeqeq */
import addMember from '@/components/CooperOperation/AddMember';
import { teamPermis as permissionOptions } from '@/components/CooperOperation/CreateTeam';
import PermissionsPop from '@/components/PermissionsPop';
import SpinRender from '@/components/SpinRender';
import DropdownCheckbox from '@/components/common/DropdownCheckbox';
import PersonCard from '@/components/common/PersonCard';
import { batchAdmin, batchPermission, batchRemove, changeMemberPermission, deleteMember, getMembers, searchMembers, setManage } from '@/service/cooper/teamMember';
import { deepCopy, getPermis, getUserNameFromCookie } from '@/utils';
import { TEAM_ADMIN, TEAM_MEMBER, TEAM_OWNER } from '@/utils/cooperutils';
import { Button, Checkbox, Input, Popover, message } from 'antd';
import classBind from 'classnames/bind';
import { intl, getLocale } from 'di18n-react';
import { debounce } from 'lodash-es';
import List from 'rc-virtual-list';
import { useCallback, useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import { useSelector } from 'react-redux';
import { isDiDiTenant } from '@/utils/entryEnhance';
import { useParams } from 'react-router-dom';
import * as styles from './index.module.less';
import Tag from '@/components/Tag';

const cx = classBind.bind(styles);

const SpaceMember = () => {
  const { teamDetail } = useSelector((state) => state.TeamData);
  const { tenantId } = useSelector((state) => state.GlobalData)
  const params = useParams();
  const [loading, setLoading] = useState();
  const [hasMore, setHasMore] = useState(true);
  const [pageNumber, setPageNumber] = useState(0);

  const [allChecked, saveAllChecked] = useState(false);
  const [total, saveTotal] = useState(0);
  const [skw, saveSKW] = useState('');
  const [memberLists, saveMemberLists] = useState([]);
  const [batchManage, setBatchManage] = useState(false);
  const [selfUserRole, setSelfUserRole] = useState(null);
  const [batchValue, saveBatchValue] = useState([0]);
  const [batchPlaceholder, saveBatchPlaceholder] = useState(intl.t('请选择'));
  const [popoverOpen, savePopoverOpen] = useState(false);
  const { cooperLinkConf } = useSelector((state) => state.GlobalData);

  const PERM_DEFAULT_LIST = [1, 2, 8, 16, 32];
  const PAGE_SIZE = 15;

  useEffect(() => {
    const handler = () => {
      if (popoverOpen) {
        savePopoverOpen(false);
      }
    };
    window.addEventListener('click', handler);
  }, [popoverOpen]);

  useEffect(() => {
    setLoading(true);
    searchMember(true);
    setSelfUserRole(teamDetail.role);
  }, [teamDetail.role, searchMember]);

  const goToPermissionsDetails = () => {
    const { space_permission_zh, space_permission_en, out_space_permission_zh, out_space_permission_en } = cooperLinkConf;
    let link = isDiDiTenant()
      ? getLocale() === 'en-US' ? space_permission_en : space_permission_zh
      : getLocale() === 'en-US' ? out_space_permission_en : out_space_permission_zh;
    return link;
  }

  const isShowEditBox = useCallback((m, myrole) => {
    if (m.role === intl.t('所有者') || (myrole === TEAM_MEMBER)) { return false; }
    if (m.role === intl.t('管理员') && myrole === TEAM_ADMIN) { return false; }
    return true;
  }, []);

  const formatMembers = useCallback((members, myrole) => {
    const role = [intl.t('成员'), intl.t('管理员'), intl.t('所有者')]; // 初始化操作者角色
    // let myrole = 0;

    // members.forEach((item) => {
    //   if (item.user_name === getUserNameFromCookie()) {
    //     setSelfUserRole(item.role);
    //     myrole = item.role;
    //   }
    // })

    const memberList = members.map((u) => {
      const p = u.permission;
      const pp = [];

      for (let i = 0; i < PERM_DEFAULT_LIST.length; i++) {
        /* eslint-disable no-bitwise */
        if (p & PERM_DEFAULT_LIST[i]) {
          pp.push(i);
        }
        /* eslint-enable no-bitwise */
      }

      const permisString = getPermis(u.permission, true).join('·');

      const m = {
        checked: false,
        id: u.id,
        teamId: u.team_id,
        role: role[u.role],

        // A: 在职，I: 离职，其他：未知
        dimissed: u.hrStatus === 'I',

        name: u.user_name,
        zhName: u[intl.t('user_display_name')],
        avatar: u.avatar,
        email: u.user_email,
        permis: pp,
        perm: u.permission,
        permisString,
        dep: u.dep,
        orgId: u.orgId,
        showOuter: u.showOuter,
        orgName: u.orgName,
      };

      m.editable = isShowEditBox(m, myrole);

      return m;
    });

    return memberList;
  }, []);

  const getSearchList = async (refresh = false, value) => {
    const pageNum = refresh ? 0 : pageNumber;
    let currentData = refresh ? [] : [...memberLists];

    const keyWord = (value !== undefined ? value : skw)?.trim();
    setLoading(true);
    const pageOption = { pageNum, pageSize: PAGE_SIZE };

    if (keyWord && keyWord !== '') {
      saveAllChecked(false);
      const mbList = await searchMembers(params.teamId, {
        q: keyWord,
        // ...pageOption
      });
      const memberList = formatMembers(mbList, teamDetail.role);
      saveMemberLists(memberList);
      setPageNumber(0);
      setHasMore(false);
    } else {
      const { totalCount, members } = await getMembers(params.teamId, pageOption);
      const list = formatMembers(members, teamDetail.role);
      setHasMore((list ?? []).length === PAGE_SIZE);
      saveMemberLists([...currentData, ...list]);
      setPageNumber(pageNum + 1);
      saveTotal(totalCount);
    }
    setLoading(false);
  }

  const refresh = useCallback(() => {
    setLoading(true);
    saveSKW('');
    saveBatchPlaceholder(intl.t('查看'));
    saveBatchValue([0]);
    saveAllChecked(false);
    searchMember(true);
  }, [])

  const searchMember = useCallback(
    debounce((isRefresh, value) => {
      getSearchList(isRefresh, value);
    }, 400),
    [pageNumber],
  );

  const addNewMember = () => {
    window.__OmegaEvent('ep_team_member_add_ck', '', {
      platform: 'new',
    });
    addMember({
      teamInfo: teamDetail,
      memberList: memberLists,
      doneCallback: () => refresh(),
    });
  };

  const onDeleteClick = (item) => {
    deleteMember(params.teamId, item).then(() => {
      message.success(intl.t('删除成功'));
      refresh();
    });
  };

  const onManageClick = (item, role) => {
    setManage(params.teamId, item.id, role)
      .then(() => {
        message.success(role ? intl.t('设置管理员成功') : intl.t('取消管理员成功'));
        refresh();
      });
  };

  const getActions = (item) => {
    const res = [
      {
        label: <span className={cx('remove-member')}>{intl.t('移除成员')}</span>,
        handle: () => {
          onDeleteClick(item);
        },
      },
    ];

    if (selfUserRole === TEAM_OWNER) {
      if (item.role === intl.t('成员')) {
        res.unshift({
          label: intl.t('设为管理员'),
          handle: () => {
            onManageClick(item, true);
          },
        });
      } else {
        res.unshift({
          label: intl.t('取消管理员'),
          handle: () => {
            onManageClick(item, false);
          },
        });
      }
    }

    return res;
  };

  const switchChecked = (id) => {
    const cp = deepCopy(memberLists);

    for (const m of cp) {
      if (m.id === id && m.editable) {
        m.checked = !m.checked;
        break;
      }
    }

    saveMemberLists(cp);
  };
  const onCheckboxGroupChange = (data, checkedValues) => {
    let p = 0;

    for (let i = 0; i < checkedValues.length; i++) {
      p += PERM_DEFAULT_LIST[checkedValues[i]];
    }

    changeMemberPermission(params.teamId, data.id, p).then(() => {
      message.success(intl.t('修改成功'));
    });
  }

  const changeBatchPermis = (value) => {
    const v = value[0] === 0 ? value : [0].concat(value);
    saveBatchValue(v);
    saveBatchPlaceholder('');
  };

  const getBatchActions = (allCheckAdmin) => {
    const res = [];
    const opt = allCheckAdmin
      ? {
        label: intl.t('取消管理员'),
        handle: () => {
          saveBatchPlaceholder(intl.t('取消管理员'));
          saveBatchValue([]);
        },
      }
      : {
        label: intl.t('设为管理员'),
        handle: () => {
          saveBatchPlaceholder(intl.t('设为管理员'));
          saveBatchValue([]);
        },
      };

    if (selfUserRole === TEAM_OWNER) res.push(opt);

    res.push({
      label: <span className={cx('remove-member')}>{intl.t('移除成员')}</span>,
      handle: () => {
        saveBatchPlaceholder(intl.t('移除成员'));
        saveBatchValue([]);
      },
    });
    return res;
  };

  const switchAllCheck = (checked) => {
    const cp = deepCopy(memberLists);
    cp.filter((m) => (m.name + m.zhName).indexOf(skw) !== -1).forEach((c) => {
      if (c.editable) c.checked = checked;
    });

    saveAllChecked(checked);
    saveMemberLists(cp);
  };

  const resetChecked = () => {
    const cp = deepCopy(memberLists);

    for (const m of cp) {
      m.checked = false;
    }

    saveMemberLists(cp);
  }

  // 获取确认修改弹窗内容
  const getPopoverContent = () => {
    let content = null;
    let scene = 0;

    const permission = batchValue.length > 0 && batchValue.map((v) => PERM_DEFAULT_LIST[v]).reduce((x, y) => x + y);
    const names = checkeds.map((c) => c.name);
    const ids = checkeds.map((c) => c.id);
    const roleCode = batchPlaceholder === intl.t('设为管理员') ? 1 : 0;

    if (batchValue.length > 0) {
      const permisDesc = batchValue
        .map((v) => permissionOptions.call(this)[v].label)
        .reduce((x, y) => `${x}/${y}`);

      content = (
        <span>
          {
            intl.t('确定要将以上成员权限修改为{one}吗', {
              one: permisDesc,
            })
          }
        </span>
      );

      scene = 1;
    }
    if (batchPlaceholder === intl.t('移除成员')) {
      content = (
        <span>
          {
            intl.t('确定要将以上成员移除吗?')
          }
        </span>
      );

      scene = 2;
    }
    if (batchPlaceholder === intl.t('设为管理员') || batchPlaceholder === intl.t('取消管理员')) {
      content = (
        <span>
          {
            roleCode === 0 ? intl.t('确定要将以上成员取消管理员?') : intl.t('确定要将以上成员设为管理员?')
          }
        </span>
      );
      scene = 3;
    }

    return (
      <div className={cx('batch-operate-content')}>
        <div className={cx('batch-operate-title')}>
          <i className='dk-iconfont dk-icon-a-tishi2' />
          <span>
            {content}
          </span>
        </div>
        <div className={cx('batch-operate-footer')}>
          <Button
            size='small'
            className={cx('cancel')}
            onClick={() => {
              savePopoverOpen(false);
            }}
          >
            {intl.t('取消')}
          </Button>
          <Button
            size='small'
            type='primary'
            onClick={() => {
              savePopoverOpen(false);

              if (scene === 1) {
                batchPermission(params.teamId, ids, permission, () => {
                  message.success(intl.t('修改成功'));
                  setBatchManage(false);
                  refresh();
                })
              }
              if (scene === 2) {
                batchRemove(params.teamId, ids, () => {
                  message.success(intl.t('移除成功'));
                  setBatchManage(false);
                  refresh();
                })
              }
              if (scene === 3) {
                batchAdmin(params.teamId, ids, roleCode, () => {
                  message.success(roleCode ? intl.t('设置管理员成功') : intl.t('取消管理员成功'));
                  setBatchManage(false);
                  refresh();
                })
              }
            }}
          >
            {intl.t('确认')}
          </Button>
        </div>
      </div>
    )
  }


  const checkeds = memberLists.filter((m) => m.checked);
  const allCheckAdmin = checkeds.length > 0 && !checkeds.find((m) => m.role === intl.t('成员'));

  return (
    <div className={cx('space-member')}>
      <div className={cx('space-member-search')}>
        <Input
          allowClear
          placeholder={intl.t('搜索当前空间成员')}
          className={cx('space-member-input')}
          value={skw}
          addonBefore={
            <i
              className={`${cx('dk-icon-sousuo', 'dk-iconfont')}`}
              style={{ color: '#bbb' }}
            />
          }
          onChange={(e) => {
            const { value } = e.target;
            saveSKW(value);
            searchMember(true, value);
          }}
        />
        {
          (teamDetail.role === TEAM_ADMIN || teamDetail.role === TEAM_OWNER) && !batchManage && (
            <Button
              type='primary'
              icon={<i className='dk-iconfont dk-icon-tianjiachengyuan1' />}
              className='confirm btn'
              onClick={addNewMember}
            >
              {intl.t('添加成员')}
            </Button>
          )
        }
      </div>
      {
        !skw && (
          <div className={cx('space-member-header')}>
            <div className={cx('space-member-title')}>{intl.t('成员')}({total})
              <PermissionsPop
                width={371}
                bottomLink={{
                  text: intl.t('了解更多团队空间权限'),
                  link: goToPermissionsDetails(),
                }}
              />
            </div>
            {
              (teamDetail.role === TEAM_ADMIN || teamDetail.role === TEAM_OWNER) && !batchManage && (
                <div className={cx('space-member-batch-operate')}>
                  <i className='dk-iconfont dk-icon-wendangyangshishezhi' />
                  <span
                    className={cx('batch-operate-text')}
                    onClick={() => {
                      window.__OmegaEvent('ep_team_member_batchmanage_ck', '', {
                        platform: 'new',
                      });
                      setBatchManage(!batchManage);
                      savePopoverOpen(false);
                      resetChecked();
                      switchAllCheck(false);
                      saveBatchValue([0]);
                      saveBatchPlaceholder(intl.t('查看'));
                    }}>
                    {intl.t('批量管理')}
                  </span>
                </div>
              )
            }
          </div>
        )
      }
      <div className={`space-member-lists ${cx('space-member-lists')}`}>
        <InfiniteScroll
          initialLoad={false}
          pageStart={0}
          loadMore={() => searchMember(false)}
          hasMore={!loading && hasMore}
          useWindow={false}
        >
          <List
            data={memberLists}
            itemHeight={60}
            itemKey='id'
          >
            {(m) => {
              let extClsName = '';
              const options = permissionOptions();
              options.forEach((item) => {
                item.checked = false;
                if (String(m.permis).indexOf(item.id) > -1) {
                  item.checked = true;
                }
              });
              const values = options.filter((p) => p.checked).map((p) => p.label);
              const displayText = values.length ? values.join('/') : '';

              return (
                <div
                  className={cx(`item${extClsName}`, {
                    dimissed: m.dimissed,
                    'item-member-team': true,
                    isSelf: getUserNameFromCookie() === m.name,
                  })}
                  key={m.id}
                  onClick={() => {
                    switchChecked(m.id)
                  }}
                >
                  <div className={cx('item-member-team-left')}>
                    {batchManage && (
                      <Checkbox
                        className={cx('item-member-label')}
                        checked={m.checked}
                        disabled={!m.editable} />
                    )}

                    <PersonCard
                      type={0}
                      depWidth={220}
                      avatar={m.avatar}
                      name={m.zhName}
                      department={m.showOuter ? m.orgName : m.dep}
                      mail={m.email}
                    />

                    {m.name === getUserNameFromCookie() && m.orgId === tenantId && (
                      <span className={cx('name-label-me')}>{intl.t('我')}</span>
                    )}

                    {m.dimissed && (
                      <span className={cx('name-label-dimissed')}>{intl.t('已离职')}</span>
                    )}
                    {m.showOuter && <Tag
                      type='out-yellow'
                      text={intl.t('外部')} />}
                  </div>

                  {!batchManage && (
                    <>
                      {m.editable ? (
                        <div className={cx('item-edit')}>
                          <DropdownCheckbox
                            type='sm'
                            options={
                              m.role === intl.t('管理员')
                                ? []
                                : permissionOptions()
                            }
                            placeholder={ m.role === intl.t('管理员') ? intl.t('管理员') : null}
                            actions={getActions(m)}
                            value={String(m.permis)}
                            isSpaceMember='true'
                            onChange={(value) => {
                              onCheckboxGroupChange(m, value);
                            }}
                          />
                        </div>
                      ) : (
                        <div className={cx('item-edit')}>
                          {
                          m.role === intl.t('管理员') ? intl.t('管理员') : (
                            m.role === intl.t('所有者') ? intl.t('所有者') : displayText
                          )
                        }
                        </div>
                      )}
                    </>
                  )}
                  {
                    batchManage && (
                      <>
                        {m.editable ? (
                          <div className={cx('item-edit')}>
                            {m.role === intl.t('管理员') ? intl.t('管理员') : displayText}
                          </div>
                        ) : (
                          <div className={cx('item-edit')}>
                            {
                              m.role === intl.t('管理员') ? intl.t('管理员') : (
                                m.role === intl.t('所有者') ? intl.t('所有者') : displayText
                              )
                            }
                          </div>
                        )}
                      </>
                    )
                }
                </div>
              );
            }}
          </List>
          {
            !hasMore
            && !loading
            && memberLists.length !== 0
            && <div className={cx('bottom-tip')}>
              {intl.t('没有更多了')}
            </div>}

        </InfiniteScroll>
        <SpinRender loading={loading} />
        {
          !loading
          && memberLists.length === 0
          && <div className={cx('empty-tip')}>
            <img
              src={require('@/assets/icon/empty13.png')}
              className={cx('empty-img')}
            />
            <div>
              {`${intl.t('搜索无结果，该用户不在该空间内')}`}
            </div>
          </div>
        }
      </div>
      {
        batchManage && (
          <div className={cx('space-member-footer')}>
            <div className={cx('space-member-footer-content')}>
              <div className={cx('item-selectall')}>
                <Checkbox
                  // indeterminate={indeterminate}
                  checked={allChecked}
                  onClick={() => {
                    switchAllCheck(!allChecked);
                  }}
                />
                <span className={cx('item-selectall-text')}>
                  {
                    intl.t('已选中{one}', {
                      one: `${checkeds.length}/${total}`,
                    })
                  }
                </span>
              </div>

              <div className={cx('batch-detail')}>
                <span className={cx('batch-detail-head')}>{intl.t('批量修改为')}</span>
                <DropdownCheckbox
                  options={permissionOptions()}
                  placeholder={batchPlaceholder}
                  actions={getBatchActions(allCheckAdmin)}
                  value={String(batchValue)}
                  isSpaceMember='true'
                  onChange={(value) => changeBatchPermis(value)}
                />
              </div>

              <div>
                <Button
                  className={cx('cancel')}
                  onClick={() => {
                    setBatchManage(false);
                  }}
                >
                  {intl.t('取消')}
                </Button>
                <Popover
                  trigger='click'
                  placement='topRight'
                  content={getPopoverContent}
                  visible={popoverOpen}
                >
                  <Button
                    type='primary'
                    disabled={checkeds.length === 0 || (batchValue.length === 0 && batchPlaceholder === intl.t('请选择'))}
                    onClick={(e) => {
                      document.body.click();
                      e.stopPropagation();
                      savePopoverOpen(true);
                    }}
                  >
                    {intl.t('确认修改')}
                  </Button>
                </Popover>
              </div>
            </div>
          </div>
        )
      }
    </div>
  );
};
// routes: /team-file/:teamId/member
export default SpaceMember;
