import { intl } from 'di18n-react';
import CooperTrashFilesList from '@/components/CooperTrashFilesList';
import { useParams } from 'react-router-dom';
import classBind from 'classnames/bind';
import { useState, useEffect } from 'react';
import Tips from '@/components/Tips';
import { useSelector, useDispatch } from 'react-redux';
import * as styles from './index.module.less';

const cx = classBind.bind(styles);

const tipsText = '当前团队空间内的文件、文档、文件夹等被删除后会进入该回收站，保留30天后彻底删除不可恢复。';

function SpaceTrash() {
  const params = useParams();
  const { teamDetail } = useSelector((state) => state.TeamData);
  const { userViewData } = useSelector((state) => state.GlobalData);
  const { setUserViewDataRq } = useDispatch().GlobalData;

  const [isShow, setIsShow] = useState(false);


  useEffect(() => {
    const { TEAM_TRASH_CLOSE } = userViewData;
    setIsShow(!TEAM_TRASH_CLOSE)
  }, [userViewData?.TEAM_TRASH_CLOSE])

  const onclose = async () => {
    setIsShow(false);
    const data = { ...userViewData };
    data.TEAM_TRASH_CLOSE = true
    if (JSON.stringify(userViewData) === '{}') return;
    setUserViewDataRq(data);
  };


  return (
    <div className={cx('space-trash')}>
      <div className={cx('space-trash-title')}>{intl.t('回收站')}</div>
      <Tips
        text={tipsText}
        isShow={isShow}
        onClose={onclose}
      />
      <CooperTrashFilesList
        role={teamDetail.role}
        teamId={params.teamId}
        isTeam={true}
      />
    </div>
  );
}

export default SpaceTrash;
