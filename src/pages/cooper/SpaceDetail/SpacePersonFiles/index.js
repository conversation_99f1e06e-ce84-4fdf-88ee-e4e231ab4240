/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-06-08 11:04:30
 * @LastEditTime: 2024-04-01 14:39:12
 * @Description: 个人空间
 * @FilePath: /knowledgeforge/src/pages/cooper/SpaceDetail/SpacePersonFiles/index.js
 *
 */
import classBind from 'classnames/bind';
import * as styles from './style.module.less';
import CooperSubHead from '@/components/CooperSubHead';
import CooperFilesList from '@/components/CooperFilesList';
import { useEffect, useState } from 'react';
import { intl } from 'di18n-react';
import { useSelector, useDispatch } from 'react-redux';
import { getCrumbs } from '@/service/cooper/index';
import { useParams, useLocation } from 'react-router-dom';
import { MY_COOPER, PERSON_FILE_SORT } from '@/constants/cooper';
import NewCreate from '@/components/LayoutCooper/Header/NewCreate/index';
import NewUpload from '@/components/LayoutCooper/Header/NewUpload/index';

const cx = classBind.bind(styles);


function SpacePersonFiles() {
  const params = useParams()
  const location = useLocation();

  const [viewType, setViewType] = useState()
  const [folderId, setFolderId] = useState(Number(params.foldId || 0) || 0)

  const { userViewData } = useSelector((state) => state.GlobalData);
  const { setUserViewDataRq } = useDispatch().GlobalData;

  // 和其他的state放在一起，更新其他的属性时，有时序问题导致数据被重置
  const [pageOption, setPageOption] = useState({});

  const [state, setState] = useState({
    isShowSwitchView: true,
    crumbs: [
      {
        path: '/disk',
        breadcrumbName: intl.t('全部文件'),
      },
    ],
  })

  useEffect(() => {
    const { MY_SPACE_FILE = {}} = userViewData;
    const { view: viewLocal, sort: sortLocal } = MY_SPACE_FILE;
    const viewTypeLocal = viewLocal;
    const FoldTreeConfig = sortLocal;

    setPageOption({
      ...FoldTreeConfig,
    })
    setViewType(viewTypeLocal)
  }, [userViewData])

  const switchView = async (type) => {
    setViewType(type);

    const data = { ...userViewData };
    data.MY_SPACE_FILE = {
      ...userViewData.MY_SPACE_FILE,
      view: type,
    }
    if (JSON.stringify(userViewData) === '{}') return;
    setUserViewDataRq(data);
  }

  const getFoldId = () => { return Number(params.foldId || 0) || 0 }

  const initCrumbs = async () => {
    let id = getFoldId()
    const crumbs = [
      {
        path: '/disk',
        breadcrumbName: intl.t('全部文件'),
      },
    ]; // 非根节点
    if (id !== 0) {
      const cbs = await getCrumbs(id); // 第一个是根节点，需要过滤掉
      cbs && cbs?.items?.slice(1).forEach((c) => crumbs.push({
        path: `/files/${c.id}`,
        breadcrumbName: c.name,
      }));
    }
    setFolderId(id)
    setState({
      ...state,
      crumbs,
    });
  }

  const setIsShowSwitchView = (isShow) => {
    setState({
      ...state,
      isShowSwitchView: isShow,
    })
  }

  useEffect(() => {
    initCrumbs()
  }, [])

  useEffect(() => {
    initCrumbs()
  }, [location])

  const routes = state.crumbs
  return (
    <div className={cx('person-space-wrap')}>
      <div className={cx('cooper-file-title-bottom')}>
        <CooperSubHead
          title={true}
          routes={routes} />
        <div className={cx('action')}>
          <NewUpload />
          <NewCreate source={'personal'}/>
        </div>
      </div>
      <CooperFilesList
        folderId={folderId}
        viewType={viewType}
        switchView={switchView}
        setIsShowSwitchView={setIsShowSwitchView}
        isShowSwitchView={state.isShowSwitchView}
        sortBy={pageOption.sortBy}
        orderAsc={pageOption.orderAsc}
        originFileType={MY_COOPER}
        isTeam={false}
      />
    </div>
  )
}


export default SpacePersonFiles;
