import classBind from 'classnames/bind';
import SpacePersonFiles from '@/pages/cooper/SpaceDetail/SpacePersonFiles';
import SpaceHeader from '../SpaceHeader';
import { useEffect } from 'react';
import { intl } from 'di18n-react';
import * as styles from '@/pages/cooper/SpaceDetail/style.module.less';
import { Helmet } from 'react-helmet';

const cx = classBind.bind(styles);

function PersonDetail() {
  useEffect(() => {
    window.document.title = `${intl.t('个人空间')} - Cooper`;
  }, []);

  return (
    <div className={cx('space-detail-wrap')}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
      </Helmet>
      <SpaceHeader/>

      <div className={cx('space-detail-main')}>
        <SpacePersonFiles />
      </div>
    </div>
  );
}

// routes: /disk
export default PersonDetail;
