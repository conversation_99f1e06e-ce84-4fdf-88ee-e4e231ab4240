/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-06-05 19:58:59
 * @LastEditTime: 2023-09-18 19:00:56
 * @Description: 回收站
 * @FilePath: /knowledgeforge/src/pages/cooper/Trash/index.js
 *
 */
// import { Tabs } from 'antd';
import classBind from 'classnames/bind';
import CooperTrashFilesList from '@/components/CooperTrashFilesList';
import CooperTrashTeamList from '@/components/CooperTrashTeamList';
// import PageTitleContent from '@/components/LayoutCooper/PageTitleContent';
import { CooperTabs, CooperTabsPane } from '@/components/common/CooperTabs';
import { intl } from 'di18n-react';
import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Tips from '@/components/Tips';
import { entryEnhance } from '@/utils/entryEnhance'
import * as styles from './style.module.less';
import { Helmet } from 'react-helmet';


const cx = classBind.bind(styles);

const tipsText = '个人空间内的文件、文档、文件夹等被删除后会进入该回收站，保留30天后彻底删除不可恢复。';
const tipsTextTeam = '个人创建的团队空间被删除后会进入该回收站，保留30天后彻底删除不可恢复。';
function Trash() {
  const [trashClose, setTrashClose] = useState();

  const { userViewData } = useSelector((state) => state.GlobalData);
  const { IsExternalTenant } = useSelector((state) => state.GlobalData);
  const { setUserViewDataRq } = useDispatch().GlobalData;

  const [isShow, setIsShow] = useState(false);
  const [isShowTeam, setIsShowTeam] = useState(false);

  useEffect(() => {
    const { PERSON_TRASH_CLOSE } = userViewData;
    setTrashClose(PERSON_TRASH_CLOSE)
  }, [userViewData?.PERSON_TRASH_CLOSE])

  useEffect(() => {
    window.document.title = `${intl.t('回收站')} - Cooper`;
  }, []);

  // useEffect(() => {
  //   // 多租户下，禁用tab名称
  //   if (IsExternalTenant) {
  //     document.querySelector('.ant-tabs-nav').style.display = 'none'
  //   } else {
  //     document.querySelector('.ant-tabs-nav').style.display = 'block'
  //   }
  // }, [IsExternalTenant]);


  useEffect(() => {
    if (trashClose === undefined) return;
    setIsShow(!(trashClose?.file))
    setIsShowTeam(!(trashClose?.space))
  }, [trashClose])


  const oncloseTips = async (isTeam) => {
    const { PERSON_TRASH_CLOSE } = userViewData;
    let data = { ...userViewData };

    if (isTeam) {
      setIsShowTeam(false)
      data.PERSON_TRASH_CLOSE = {
        ...PERSON_TRASH_CLOSE,
        space: true,
      }
    } else {
      setIsShow(false);
      data.PERSON_TRASH_CLOSE = {
        ...PERSON_TRASH_CLOSE,
        file: true,
      }
    }
    if (JSON.stringify(userViewData) === '{}') return;
    setUserViewDataRq(data);
  };

  return (
    <div className={cx('trash-wrap')}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
      </Helmet>
      {/* <PageTitleContent /> */}
      <div className={cx('trash-list')}>
        <CooperTabs className={cx('trash-tabs')}>
          {entryEnhance(
            <CooperTabsPane
              tab={intl.t('文件回收站')}
              key="file">

              <div className={cx('trash-tabs-content')}>
                <Tips
                  text={tipsText}
                  isShow={isShow}
                  onClose={() => oncloseTips()}
            />
                <CooperTrashFilesList
                  isTeam={false}
            />
              </div>

            </CooperTabsPane>,
          )}
          <CooperTabsPane
            tab={intl.t('空间回收站')}
            key="teamSpace">

            <div className={cx('trash-tabs-content')}>
              <Tips
                text={tipsTextTeam}
                isShow={isShowTeam}
                onClose={() => oncloseTips(true)}
              />
              <CooperTrashTeamList
                isTeam={true}
              />
            </div>

          </CooperTabsPane>
        </CooperTabs>
      </div>
    </div>
  );
}
// routes: /trash
export default Trash;
