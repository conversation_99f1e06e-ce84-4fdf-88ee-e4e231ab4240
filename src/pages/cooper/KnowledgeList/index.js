import { intl } from 'di18n-react';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Tabs, Tooltip } from 'antd';
import classBind from 'classnames/bind';
import { getMyDKList, getCountNumber } from '@/service/knowledge/menhuIndex';
import DKList from '@/components/DKList';
import HelpLink from '@/components/HelpLink';
import PopoverSort from '@/pages/cooper/KnowledgeList/PopoverSort';
import PageTitleContent from '@/components/LayoutCooper/PageTitleContent';
import { TAB_TYPE } from '@/constants/dkList';
import { setLocalData, getLocalData } from '@/utils/localStorage';
import USER_VIEW from '@/constants/userView';
import * as styles from '@/pages/cooper/KnowledgeList/style.module.less';

// 门户
const { TabPane } = Tabs;

const cx = classBind.bind(styles);

const _tabInfo = [
  {
    key: TAB_TYPE.ALL,
    title: intl.t('全部'),
    tip: '',
  },
  {
    key: TAB_TYPE.OWN,
    title: intl.t('我拥有的'),
    tip: intl.t('我是知识库所有者'),
  },
  {
    key: TAB_TYPE.JOIN,
    title: intl.t('我参与的'),
    tip: intl.t('我是知识库成员'),
  },
];

const _sortChoose = {
  [TAB_TYPE.ALL]: [intl.t('sortby-访问时间')],
  [TAB_TYPE.OWN]: [intl.t('sortby-访问时间'), intl.t('sortby-创建时间')],
  [TAB_TYPE.JOIN]: [intl.t('sortby-访问时间'), intl.t('sortby-参与时间')],
};

const KnowledgeList = () => {
  const cookieSortObj = getLocalData('always:USER_VIEW')?.My_Knowledge?.Sort_Type || {};
  const { globalOutsideChain } = useSelector((state) => state.CooperIndex);
  const [initLoading, setInitLoading] = useState(true);
  const [errorData, setErrorData] = useState(false);
  const [tabType, setTabType] = useState(TAB_TYPE.ALL);

  const [DKsort, setDKsort] = useState({
    [TAB_TYPE.ALL]: cookieSortObj[TAB_TYPE.ALL] ?? 0,
    [TAB_TYPE.OWN]: cookieSortObj[TAB_TYPE.OWN] ?? 1,
    [TAB_TYPE.JOIN]: cookieSortObj[TAB_TYPE.JOIN] ?? 1,
  });
  const [listData, setListData] = useState();
  const [countObj, setCountObj] = useState([]);

  useEffect(() => {
    updateData(tabType);
  }, []);

  const updateData = (type, activeSort) => {
    setInitLoading(true);
    const params = {
      type,
      orderType: activeSort ?? DKsort[type],
    };
    getMyDKList(params)
      .then((res) => {
        setListData(res);
      })
      .catch(() => {
        setErrorData(true);
      })
      .finally(() => {
        setInitLoading(false);
      });

    getCountNumber(true).then((res) => {
      setCountObj(res);
    });
  };

  const handleSort = (activeSort) => {
    setInitLoading(true);
    let currView = getLocalData('always:USER_VIEW')?.length > 0
      ? getLocalData('always:USER_VIEW')
      : USER_VIEW;
    let newSort = { ...DKsort, [tabType]: activeSort };
    setDKsort(newSort);
    if (currView?.My_Knowledge?.Sort_Type) {
      currView.My_Knowledge.Sort_Type = newSort;
    }
    updateData(tabType, activeSort);
    setLocalData('always:USER_VIEW', currView);

    let sortValue = _sortChoose[tabType][activeSort];

    switch (tabType) {
      case tabType === TAB_TYPE.OWN:
        window.__OmegaEvent('ep_dkpc_dkhome_owned_sort_ck', '', {
          sort: sortValue,
        });
        break;
      case tabType === TAB_TYPE.JOIN:
        window.__OmegaEvent('ep_dkpc_dkhome_joined_sort_ck', '', {
          sort: sortValue,
        });
        break;
      case tabType === TAB_TYPE.ALL:
        break;

      default:
        break;
    }
  };

  const handleTabChange = (activeKey) => {
    setTabType(activeKey);
    updateData(activeKey);
    setInitLoading(true);

    switch (activeKey) {
      case activeKey == TAB_TYPE.ALL:
        window.__OmegaEvent('ep_dkpc_dkhome_all_ck');
        break;
      case activeKey == TAB_TYPE.OWN:
        window.__OmegaEvent('ep_dkpc_dkhome_owned_ck');
        break;
      case activeKey == TAB_TYPE.JOIN:
        window.__OmegaEvent('ep_dkpc_dkhome_joined_ck');
        break;
      default:
        break;
    }
  };

  return (
    <div className={cx('my-dk-wrap')}>
      <PageTitleContent
        titleRight={
          <HelpLink
            title={intl.t('了解更多知识库用法')}
            jumpUrl={globalOutsideChain?.dk_help || ''}
          />
        }
      />

      <Tabs
        defaultActiveKey={tabType}
        destroyInactiveTabPane={true}
        onChange={handleTabChange}
        className={`dk-tabs-has-fit ${cx('tab-top-title')}`}
        tabBarExtraContent={{
          right: (
            <PopoverSort
              isMyKnowledge={true}
              type={tabType}
              chooseValue={DKsort[tabType]}
              sortChoose={_sortChoose}
              onChange={handleSort}
            />
          ),
        }}
      >
        {_tabInfo.map((item) => {
          return (
            <TabPane
              tab={
                <Tooltip
                  placement="top"
                  title={item.tip}>
                  <span>
                    <span>{item.title}</span>
                    <span> {countObj[item.key]?.count}</span>
                  </span>
                </Tooltip>
              }
              key={item.key}
            >
              <DKList
                type={tabType}
                errorData={errorData}
                initLoading={initLoading}
                dkData={listData}
                isMyKnowledge={true}
              />
            </TabPane>
          );
        })}
      </Tabs>
    </div>
  );
};

export default KnowledgeList;
