import { intl } from 'di18n-react';
import { useEffect, useState } from 'react';
import { Popover } from 'antd';
import classBind from 'classnames/bind';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);

export default ({ sortChoose, type, chooseValue, onChange }) => {
  const [currentDesc, setCurrentDesc] = useState(sortChoose[type][chooseValue]);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    setCurrentDesc(sortChoose[type][chooseValue]);
  }, [type, chooseValue]);

  const onSelect = (v, label) => {
    setCurrentDesc(label);
    onChange(v);
    setVisible(false);
  };

  const itemLis = (v, i) => {
    return (
      <li
        key={i}
        onClick={() => onSelect(i, v)}>
        <span>{v}</span>
        {chooseValue == i && (
          <i className={cx('dk-icon-gou', 'dk-iconfont')} />
        )}
      </li>
    );
  };

  return (
    <Popover
      trigger="click"
      visible={visible}
      placement="bottom"
      overlayInnerStyle={{ minWidth: 140 }}
      onVisibleChange={(v) => {
        !v && setVisible(false);
      }}
      content={
        <ul className={`${cx('recent-dk-sort-popover')}`}>
          {sortChoose[type].map((v, i) => itemLis(v, i))}
        </ul>
      }
      overlayClassName={'dk-ant-popover__reset '}
    >
      <div
        className={`${cx('dk-sort-recent')} ${visible ? cx('visible') : ''}`}
        onClick={() => setVisible(!visible)}
      >
        <i
          className={`dk-icon-paixu3px dk-iconfont ${cx(
            'recent-dk-sort-icon',
          )}`}
        />
        <span>
          {intl.t('按')}
          {currentDesc}
          {intl.t('排序')}
        </span>
      </div>
    </Popover>
  );
};
