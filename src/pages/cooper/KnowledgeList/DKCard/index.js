import { intl } from 'di18n-react';
import { useMemo, useState } from 'react';
import { message, Tooltip } from 'antd';
import classBind from 'classnames/bind';
import { Link } from 'react-router-dom';
import { highlight, isDesktopDC } from '@/utils';
import { DK_FROM_TYPE } from '@/constants/dkList';
import AddQuick from '@/components/CooperOperation/AddQuick';
import RemoveQuick from '@/components/CooperOperation/RemoveQuick';
import * as styles from './style.module.less';
import useDebounceFn from '@/hooks/useDebounceFn';

const cx = classBind.bind(styles);
/**
 * 知识库卡片，用在搜索、知识库聚合页
 * @param {*} param0
 * @returns
 */

const DKCard = ({ dkInfo, onClickCard }) => {
  const [quickObj, setQuickObj] = useState({
    isMarked: dkInfo.markedQuickVisit,
    quickId: dkInfo.quickId || '',
  });
  const gotoDk = (metisSpaceId) => {
    onClickCard && onClickCard(metisSpaceId);
  };

  const { run: doAddClick } = useDebounceFn(() => AddQuick({
    objectType: 'KNOWLEDGE',
    objectId: dkInfo.id,
    sourceAppId: 4,
    sourceId: dkInfo.id,
    doneCallback: (quickId) => {
      setQuickObj({
        isMarked: true,
        quickId: quickId || '',
      });
    },
  }));

  const { run: doRemoveClick } = useDebounceFn(() => RemoveQuick({
    id: quickObj.quickId,
    doneCallback: () => {
      setQuickObj({
        isMarked: false,
        quickId: '',
      });
    },
  }));

  const onQuickVisit = (e, isAdd) => {
    e.preventDefault();
    e.stopPropagation();
    if (isAdd) {
      doAddClick();
    } else {
      doRemoveClick();
    }
  };

  return (
    <li>
      <Link
        target="_blank"
        key={dkInfo.id}
        to={`/knowledge/${dkInfo.id}/home`}
        className={cx('dk-card-wrap', {
          'dk-card-dc': isDesktopDC,
        })}
        onClick={() => gotoDk(dkInfo.id)}
      >
        {dkInfo.knowledgeType === 1 && (
          <div className={cx('department-tag')}>
            <p>{intl.t('部门')}</p>
          </div>
        )}
        <img
          className={cx('dk-card-pic')}
          src={dkInfo.pictureUrl} />
        <div className={cx('dk-card-info')}>
          <div className={cx('dk-card-title')}>
            <div className={cx('title-name')}>{highlight(dkInfo.name)}</div>
            <Tooltip
              align={{
                offset: [0, '-4px'],
              }}
              placement="top"
              title={
                quickObj.isMarked
                  ? intl.t('从"快速访问"移出')
                  : intl.t('添加至"快速访问"')
              }
            >
              {quickObj.isMarked ? (
                <i
                  className={cx(
                    'dk-iconfont',
                    'dk-icon-yichukuaisufangwen3',
                    'has-quick-visit',
                    'quick-visit-icon',
                  )}
                  onClick={(e) => onQuickVisit(e, false)}
                />
              ) : (
                <i
                  className={cx(
                    'dk-iconfont',
                    'dk-icon-kuaisu4',
                    'no-quick-visit',
                    'quick-visit-icon',
                  )}
                  onClick={(e) => onQuickVisit(e, true)}
                />
              )}
            </Tooltip>
          </div>
          <div className={cx('dk-card-from')}>
            {Number(dkInfo.knowledgeKind) === DK_FROM_TYPE.PERSON ? (
              <>
                <div className={cx('from-icon-wrap', 'person-icon')}>
                  <i
                    className={cx('dk-icon-rongqi', 'dk-iconfont', 'from-icon')}
                  />
                </div>
                <div className={cx('from-name')}>
                  {dkInfo?.knowledgeOwner?.name?.chineseName}
                </div>
              </>
            ) : (
              <>
                <div className={cx('from-icon-wrap', 'space-icon')}>
                  <i
                    className={cx(
                      'dk-icon-tuanduikongjian',
                      'dk-iconfont',
                      'from-icon',
                    )}
                  />
                </div>
                <div className={cx('from-name')}>
                  {dkInfo?.knowledgeMixedSpace?.name}
                </div>
              </>
            )}
          </div>
        </div>
      </Link>
    </li>
  );
};

export default DKCard;
