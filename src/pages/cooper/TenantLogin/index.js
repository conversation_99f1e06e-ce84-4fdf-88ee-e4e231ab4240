import classBind from 'classnames/bind';
// import { useSelector } from 'react-redux';
import { useState, useEffect } from 'react';
import Logo from '@/components/LayoutCooper/Aside/Logo';
import { getCombineTenantInfo } from '@/pages/cooper/TenantLogin/service';
// import { history } from '@/model';
import { message } from 'antd';
import { putTenantLoginInfo } from './service';
import { intl } from 'di18n-react';
import { Helmet } from 'react-helmet';
import * as styles from './style.module.less';

const cx = classBind.bind(styles);
// 多租户登录页面
const TenantIndex = () => {
  const [tenantList, setTenantList] = useState([]);
  // const { tenantList } = useSelector((state) => state.GlobalData)

  const jumpToSpace = async (tenantId) => {
    // 发送到后端
    const loginData = await putTenantLoginInfo(tenantId)
    if (loginData) {
      // 跳转到根目录
      window.localStorage.setItem('x-tenant-id', tenantId);
      if (document.referrer) {
        // console.log('document.referrer----', document.referrer);
        window.location.href = document.referrer;
        return;
      }
      window.location.href = window.location.origin
    } else {
      message.error(loginData.message)
    }
  };

  useEffect(async () => {
    const res = await getCombineTenantInfo();
    setTenantList(res?.tenants || []);

    // 获取完数据后删除骨架屏的dom，防止页面抖动
    const rootNode = document.getElementById('root');
    const skeletonRoot = document.getElementById('root-skeleton');
    if (skeletonRoot) {
      skeletonRoot.parentNode.removeChild(skeletonRoot);
      rootNode.style.display = 'flex';
    } else {
      console.log("Element with id 'root' not found.");
    }
  }, []);

  return (
    <div className={cx('tenant-login')}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
      </Helmet>
      <div className={cx('cooper-icon')}>
        <Logo isLarge={true} />
      </div>
      {
        tenantList.length !== 0 && (
          <div className={cx('body')}>
            <div className={cx('body-icon')} />
            <div className={cx('body-select-con')}>
              <div className={cx('select-header')}>
                {intl.t('请选择组织进入')}
              </div>
              <div className={cx('org-scroll')}>
                {
                  tenantList.map((v) => {
                    if (v.tenantId) {
                      return (
                        <div
                          className={cx('org-item')}
                          onClick={() => jumpToSpace(v.tenantId)}>
                          <div className={cx('text')}>{v.tenantName}</div>
                          <span className={cx('arrow')} />
                        </div>
                      )
                    }
                    return null;
                  })
                }
              </div>
            </div>
          </div>
        )
      }
    </div>
  );
};
export default TenantIndex;
// routes: /tenant
