import { intl } from "di18n-react";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { noop } from "lodash-es";
import SearchBox from "../cooper-selectfile-dc/search-box";
import FileList from "../cooper-selectfile-dc/file-list";
import NoResult from "../cooper-selectfile-dc/no-result";
import {
  dynamicGetRecent,
  dynamicGetSearch,
  hideDialog,
  sendFiles,
  dcToast,
  getSelectFromDC,
  getUrlInfo,
  getUrlParam,
} from "../cooper-selectfile-dc/services";
import { iconSpin } from "../cooper-selectfile-dc/icons";
import "./index.less";
import {isBeforeDcVersion415} from '../cooper-selectfile-dc/dc-helper';

const MAX_SELECTS = getUrlParam("max_selects") || 9;
const PRECISION = 10;

const CooperSelectfileDcMobile = () => {
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);

  const SelectedListFromDC = useRef([]);

  const [searchMode, setSearchMode] = useState(false);
  const [recentList, setRecentList] = useState([]);
  const [searchList, setSearchList] = useState([]);
  const [selectedList, setSelectedList] = useState([]);
  const [invalidCount, setInvalidCount] = useState(0);
  const [validUrlArrInit, setValidUrlArrInit] = useState([]);

  const loadFuncs = useRef({
    getRecent: noop,
    getSearch: noop,
  });

  const loadingMore = useRef(false);

  useEffect(() => {
    const announceElement = document.getElementById('cooper-announce');
    if(announceElement) announceElement.style.display = 'none';
  }, [])

  useEffect(() => {
    (async function () {
      try {
        loadFuncs.current.getRecent = dynamicGetRecent();
        const originList = getSelectFromDC();
        SelectedListFromDC.current = originList;
        await loadSelectedList();
        await loadRecent();
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  const handleSearch = useCallback((value, cancelToken) => {
    if (value) {
      dynamicGetSearch.cancelToken = cancelToken;
      loadFuncs.current.getSearch = dynamicGetSearch(value);
      loadSearch();
    } else {
      setSearchMode(false);
    }
  }, []);

  const loadSelectedList = useCallback(async () => {
    const result = await getUrlInfo([...SelectedListFromDC.current]);
    setSelectedList(result);
    setValidUrlArrInit(result.map(it => it.fileUrl));
    setInvalidCount(SelectedListFromDC.current.length - result.length);
  }, []);

  const loadRecent = useCallback(async () => {
    if (!loadingMore.current) {
      loadingMore.current = true;

      try {
        const { isEnd, result } = await loadFuncs.current.getRecent();
        setRecentList(result);
      } finally {
        loadingMore.current = false;
      }
    }
  }, [loadFuncs.current.getRecent])

  const loadSearch = useCallback(async () => {
    if (!loadingMore.current) {
      loadingMore.current = true;

      try {
        const { isEnd, result } = await loadFuncs.current.getSearch();
        setSearchList(result);
        setSearchMode(true);
      } finally {
        loadingMore.current = false;
      }
    }
  }, [loadFuncs.current.getSearch])

  const handleScroll = useCallback((e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;

    if (scrollTop + clientHeight + PRECISION >= scrollHeight) {
      const loadFunc = searchMode ? loadSearch : loadRecent;
      loadFunc();
    }
  }, [loadSearch, loadRecent, searchMode])

  const handleCheck = useCallback((item) =>{
    const idx = selectedList.findIndex((it) => it.fileUrl === item.fileUrl);
    if (idx > -1) {
      selectedList.splice(idx, 1);
    } else if (selectedList.length < MAX_SELECTS - invalidCount) {
      selectedList.push(item);
    } else {
      dcToast('warning', intl.t('最多支持关联{count}个会议文档', {count: MAX_SELECTS}));
    }
    setSelectedList([...selectedList]);
  }, [selectedList, invalidCount])

  const _sendFiles = useCallback(async () => {
    if(isBeforeDcVersion415 && selectedList.length === 0) return;
    setSending(true);
    try { 
      await sendFiles(selectedList, [...SelectedListFromDC.current], validUrlArrInit);
      hideDialog();
    } catch (e) {
      console.log(e);
    } finally {
      setSending(false);
    }
  }, [selectedList, validUrlArrInit])

  const list = searchMode ? searchList : recentList;
  return (
    <div className="cooper-sendfile-dc-mobile" onScroll={handleScroll}>
      {/* <div className="header">
        <div
          className="left"
          onClick={() => {
            hideDialog();
          }}
        >
          {intl.t('取消')}
        </div>
        <div className="title">{intl.t('关联企业内部文档')}</div>
        <div className="right"></div>
      </div> */}
      <div className="search_div">
        <SearchBox onSearch={handleSearch} type="mobile" />
      </div>
      <div className="selector">
        {!loading && list.length > 0 && (
          <React.Fragment>
            <FileList
              isSelectedList={false}
              isMaxCount={selectedList.length >= MAX_SELECTS - invalidCount}
              files={list}
              selectedList={selectedList}
              toggleSelect={handleCheck}
              type="mobile"
            />
          </React.Fragment>
        )}
        {!loading && list.length === 0 && (
          <NoResult isRecentEmpty={!searchMode} />
        )}
      </div>
      <div className="footer">
        <div className="select_box">
        {intl.t('剩余可关联：{selected}个', {
          selected: MAX_SELECTS - invalidCount - selectedList.length,
        })}
        </div>
        <div
          className={`btn ${selectedList.length === 0 && isBeforeDcVersion415 ? "btn-disable" : ""}`}
          onClick={() => !sending && _sendFiles()}
        >
          {sending ? <img src={iconSpin} alt="" /> : intl.t("确定")}
        </div>
      </div>
    </div>
  );
};

export default CooperSelectfileDcMobile;
