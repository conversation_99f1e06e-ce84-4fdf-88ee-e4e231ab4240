/*
 * @Date: 2024-02-28 17:03:05
 * @LastEditors: guanzhong <EMAIL>
 * @LastEditTime: 2024-03-19 17:10:36
 * @FilePath: /knowledgeforge/src/pages/cooper/SpaceInvalid/index.js
 * @Description: 空间失效落地页面
 */
import classBind from 'classnames/bind';
import * as styles from './style.module.less';
import CooperHeaderOld from '@/components/common/OldGlobalHeader';
import InvalidUi from './Invalid-ui/index'
import CopyRight from '@/components/common/CopyRight'
import { Helmet } from 'react-helmet';

const cx = classBind.bind(styles);


const SpaceInvalid = () => {
  return (
    <div className={cx('spaceInvalid')}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
      </Helmet>
      <CooperHeaderOld />
      <InvalidUi />
      <CopyRight />
    </div>
  )
};

// routes: /team-file/team-invalid
export default SpaceInvalid;
