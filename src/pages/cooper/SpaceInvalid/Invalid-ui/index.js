/*
 * @Date: 2024-03-19 17:08:34
 * @LastEditors: guanzhong <EMAIL>
 * @LastEditTime: 2024-03-19 17:42:20
 * @FilePath: /knowledgeforge/src/pages/cooper/SpaceInvalid/Invalid-ui/index.js
 * @Description: 空间失效的展示UI
 */
import classBind from 'classnames/bind';
import * as styles from './style.module.less';
import { SpaceInvalid } from '@/assets/icon/index'
import { intl } from 'di18n-react';


const cx = classBind.bind(styles);

const InvalidUi = () => {
  return (
    <div className={cx('invalid-ui')}>
      <img src={SpaceInvalid} />
      <p>{intl.t('该空间被删除或链接存在问题')}</p>
    </div>
  )
}

export default InvalidUi
