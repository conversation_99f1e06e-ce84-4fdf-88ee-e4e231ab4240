import { isDesktopDC } from '@/utils/index';
import classBind from 'classnames/bind';
import { useSelector } from 'react-redux';
import { isDiDiTenant } from '@/utils/entryEnhance';
import helperFeedback from './FeedbackModal';
import { Tooltip } from 'antd';
import { intl } from 'di18n-react';
import * as styles from './style.module.less';
import { toggleModal } from '@didi/ai-customer-service';

const cx = classBind.bind(styles);


function Feedback({ icon, placement }) {
  const { cooperLinkConf } = useSelector((state) => state.GlobalData);
  // eslint-disable-next-line camelcase, max-len
  const { feedBack_dc } = cooperLinkConf;
  // TODO：判断是否DC
  const open = (e) => {
    window.__OmegaEvent(
      'ep_help_ck',
      '点击进入反馈群',
      {
        platform: 'knowledgeforge',
        tenant: isDiDiTenant() ? 'internal' : 'external',
      },
    );
    if (isDiDiTenant()) {
      toggleModal({
        initialWidth: 380,
        initialHeight: isDesktopDC ? 512 :  600,
        initialZIndex: 100000000
      }, { config: { feedBack_dc } }, e);
    } else {
      helperFeedback();
    }
  }

  return (
    <div className={cx('feedback')}>
      <Tooltip
        placement={placement ?? 'bottomRight'}
        title={isDiDiTenant() ? intl.t('Cooper相关问题，请点击咨询'): intl.t('点击反馈Cooper问题')}
      >
        <div
          onClick={open}
          key={'feedback'}
          className={cx('content')}
        >
          {
            icon || <i className='dk-iconfont dk-icon-lianxikefu'/>
          }

        </div>
      </Tooltip>
    </div>
  );
}

export default Feedback;
