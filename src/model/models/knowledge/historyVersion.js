import * as PageService from '@/service/knowledge/page';
import * as DidocService from '@/service/knowledge/didoc';

const getDefaultState = () => ({
  initLoading: false,
  view: {},
  versionList: [],
  currentVersionIndex: 0,
  historyVersionVisible: false,
  comparisonVersionIndex: '',
  currentContent: '',
});
export default {
  state: getDefaultState(),
  reducers: {
    changeInitLoading(state, initLoading) {
      return {
        ...state,
        initLoading,
      };
    },
    changeView(state, view) {
      return {
        ...state,
        view,
      };
    },
    changeCurrentContent(state, currentContent) {
      return {
        ...state,
        currentContent,
      };
    },
    changeCurrentVersionIndex(state, currentVersionIndex) {
      return {
        ...state,
        currentVersionIndex,
      };
    },
    changeVersionList(state, versionList) {
      return {
        ...state,
        versionList,
      };
    },
    toggleHistoryVersionVisible(state) {
      return {
        ...state,
        currentVersionIndex: 0,
        historyVersionVisible: !state.historyVersionVisible,
      };
    },
    changeComparisonVersion(state, comparisonVersionIndex) {
      return {
        ...state,
        comparisonVersionIndex,
      }
    },
  },
  effects: {
    // 如果在新文档灰度中，获取哪些版本是新数据产生的
    async getVersionList({ pageId, owner, guid, isNewEditor }) {
      if (isNewEditor) {
        const [data, result] = await Promise.all([PageService.getVersionList(pageId, owner), DidocService.getNewVersionList({ guid })]);
        const newVersionList = result?.data;
        if (newVersionList.length > 0) {
          data.forEach((dataItem) => {
            if (newVersionList.some((newItem) => newItem.version == dataItem.version)) {
              dataItem.isNewEditorData = true;
            }
          });
        }
        this.changeVersionList(data);
        return data;
      } else {
        const data = await PageService.getVersionList(pageId, owner);
        this.changeVersionList(data);
        return data;
      }
    },
    async revertVersion(payload) {
      return PageService.revertPage(payload);
    },
    async getCurrentContent(payload) {
      const data = await DidocService.getNewEditorVersionContent(payload);
      if (data) {
        const contentStr = data.content ? JSON.parse(data.content)?.default : null
        this.changeCurrentContent(contentStr);
      }
      return data;
    },
  },
};
