{"暂不支持移动端访问表格": "Mobile access to the sheet is not supported", "我拥有的": "I created", "我参与的": "I joined", "我管理的": "I managed", "浏览": "Opened", "知识库-实体": "repository", "知识库-大写": "Repository", "知识门户": "Knowledge portal", "选择所需知识库类型": "Select repository type", "结构预览": "Structure preview", "该部门已存在一个审批中的部门知识库申请，不可重复创建": "There is already a pending department knowledge application for this department. Duplicate creation is not allowed.", "该部门已存在官方部门知识库，不可重复创建": "The department already has an official department knowledge. Duplicate creation is not allowed.", "选择部门": "Select department", "部门知识库创建成功后会自动提交审核，审核阶段不能使用该知识库，审核通过后该知识库会打上“部门”标签，部门成员实时同步成为知识库成员；审核未通过亦无法使用该部门知识库": "部门知识库创建成功后会自动提交审核，审核阶段不能使用该知识库，审核通过后该知识库会打上“部门”标签，部门成员实时同步成为知识库成员；审核未通过亦无法使用该部门知识库", "请选择部门": "Please select department", "请联系知识库所有者或管理员开启该套件": "Please contact the knowledge owner or admin to enable this suite", "人)": " individuals)", "来源：Cooper团队空间": "From: Cooper Team Space", "来源：组织结构": "From: Organization", "部门知识库的创建能力正在路上！": "The ability to create a department repository is on the way! ", "部门负责人可前往": "The department head can go to ", "升级现有知识为部门知识库": " to upgrade the existing knowledge to the department repository.", "正在路上，敬请期待": "On the way and stay tuned", "敬请期待": "Stay tuned", "欢迎使用知识库": "Welcome to Knowledge", "新建知识库-取消按钮": "Cancel", "新建知识库-创建按钮": "Create", "新建知识库-下一步按钮": "Next", "完善知识库信息": "Improve repository information", "点击刷新": "点击刷新", "页面所有者/管理员已调整页面设置": "The page owner/admin has adjusted the page settings", "页面设置": "Page Setup", "决定页面的正文字号和页面宽度": "Determine the font size and page width", "切换后同步影响编辑态协同方，协同方刷新后展示。发布后影响页面预览态和分享态。": "After switching, the edit mode collaborator is affected synchronously, and the page is displayed after refreshing. After publishing, the preview and share mode are affected.", "创建知识门户": "Create knowledge portal", "已关联团队空间": "Associated with space", "暂无描述": "No description", "你收藏的页面记录会展示在这里！": "Your collection of page records will be displayed here！", "你的修改已自动保存于编辑态中": "Your changes have been automatically saved in the editing state", "阅读样式": "Reading style", "正文字号调整、全宽模式": "Text size adjustment, full width mode", "该样式仅供阅读，不影响页面设置，刷新后还原。": "This style is only for reading, not affect the page settings, restore after refreshing.", "编辑页面-更新于": "Modified ", "创建自定义成员组": "Create custom group", "正文字号": "Font size", "小字14px": "14px", "大字16px": "16px", "页面宽度": "Page width", "窄屏模式": "Narrow Screen", "适合高效阅读": "Suitable for efficient reading", "超宽模式": "Wide Screen", "可展示更多信息": "More information can be displayed", "请输入用户名、D-Chat群组、组织架构名称等": "Search user, D-Chat group and department", "取消收藏成功": "Removed from favorites successfully", "赶紧创建一个知识库，开启你的知识创作之旅": "Creating a knowledge, and start your journey of knowledge creation!", "赶紧创建一个知识门户，开启你的知识创作之旅": "Creating a knowledge portal, and start your journey of knowledge creation!", "你还没有被邀请进入他人的知识{slot0}～": "You have not been invited into other's {slot0}", "你还没有被邀请-知识库": "repository", "你还没有被邀请-知识门户": "knowledge portal", "默认": "<PERSON><PERSON><PERSON>", "切换知识库": "Switch knowledge", "Cooper作为企业办公场景文档协作平台，禁止上传用户隐私数据，请确保存储数据无违法违规内容。": "As a document collaboration platform for enterprise office scenarios, <PERSON> prohibits uploading users' private data. Please ensure that the stored data contains no illegal content.", "搜索页面内容或文件": "Search for page content or files", "搜索页面及知识库": "Search for pages and repository ", "没有最近访问记录": "No recent views", "点击可定位到相关内容": "Click to locate the relevant content", "enter回车可快速查看": "Press enter for a quick view", "如需查看更多知识库内容，请前往PC端": "Please go to PC to view more content.", "，申请后需等待您的直属上级审核。": " extension, which should be approved by your direct supervisor", "你的申请已提交，将由": "Your request has been submitted and will be reviewed by ", "进行审批。审批完成后，你将会收到消息通知": " . once the approval process is complete, you will receive a notification.", "，30天内可以在回收站进行还原": ", and can be restored within 30 days", "更多": "More", "Wiki": "Wiki", "浏览于MM月DD日 HH时mm分": " You opened: MM-DD HH:mm", "还没有收藏文件，快去收藏吧": " You can add important files here", "收藏时间": "Collected time", "成员初始权限": " Permissions", "用户可以查看团队中文件、文件夹以及在线协作文档": "Can view files, folders and docs in the space", "用户可以对团队中文件、文件夹进行重命名，针对在线协作文档进行修改编辑": "Can rename files and edit docs in the space", "用户可以在团队中上传文件、新建文件夹和在线协作文档": "Can upload files and  create docs in the space", "用户可以对文件、文件夹进行分享，针对在线协作文档用户可以邀请协作者": "Can share files and folders in the space", "用户对团队中所有的文件、文件夹进行下载，针对在线协作文档可以导出": "Can download files, folders and docs", "请输入用户名或D-chat群组、Cooper团队、OE组": " Enter username, D-chat group, Cooper team, OE group to search", "用Cooper团队空间，进行团队知识管理": "You can use Space to manage team's knowledge", "沉淀和共享团队内部知识": "Integrate and share team knowledge", "向您分享了文件": "Shared the file with you:", "向您分享了文件夹": "Shared the folder with you:", "word": "Word", "excel": " Excel", "ppt": "PPT", "pdf": "PDF", "视频": " Videos", "图片": "Images", "清除": "Clear", "移动": "Move", "保存到空间": "Save to Team", "上传": "Upload", "下载": "Download", "删除": "Delete", "收藏": "Favorites", "上传文件": "Files", "上传文件1": "upload files", "上传文件{context, onestep}": "File Upload", "上传文件夹": "Folder", "新建文件夹": "Folder", "取消收藏": "Remove from Favorites", "该用户权限已被修改可恢复继承": "Whether to recover inherited permission", "恢复": "Rest<PERSON>", "重命名": "<PERSON><PERSON>", "移动到": "Move to", "分享": "Share", "添加并通知": "Add and notify", "删除团队": "Delete the Team", "退出团队": "Exit the team", "创建": "Create", "创建团队": "Create Team", "权限": "Permissions", "权限管理": "Authority Management", "转交权限": "Transfer Ownership", "添加成员": "Add", "最新动态": "Latest News", "个人空间": "My Space", "{slot0}的个人空间": "{slot0}'s Personal Space", "团队空间": "Team Space", "团队/个人空间": "Space", "团队空间{context, notify}": " team", "我分享的": "Shared by me", "回收站": "Trash", "个人空间扩容": "Extend My Space", "团队空间扩容": "Extend Team Space", "username_zh": "username", "帮助": "Help", "Switch to English": "切换到中文", "注销": "Log Out", "是否删除已选择的文件？": "Are you sure to delete the selected files?", "删除成功": "Deleted successfully", "已收藏": "Added to favorites", "收藏成功": "Added to favorites successfully", "已取消收藏": "Removed from favorites", "您无权限下载": "You have no permission to download", "协作": "Cooperate", "确认": "Confirm", "协作文档暂不支持下载，即将上线敬请期待": "Docs don‘t support batch download, please enter the doc to download", "确认继承团队空间权限吗？": "Are you sure to make it public to all team members", "切换为继承后，团队空间内所有的成员都可以访问文件夹": "Once confirmed, all members in the team space can access the folder.", "确认不继承团队空间权限吗？": "Are you sure to set it as a private folder？", "当前存在分享记录，切换为不继承后，获得文件夹/文件分享链接的用户将无法访问": "The folder and the files in the folder have been shared. Once confirmed, users who had got sharing links will not be able to access them.", "切换为不继承后，仅文件夹管理员和被单独授权的空间成员可以访问文件夹": "Once confirmed, only the folder administrators and the members who are authorized separately can access the folder.", "已修改继承方式": "Inheritance method has been modified successfully", "你没有权限": "You don't have permission", "你无权访问该文件夹": "No permission to access", "该文件夹被单独设置了权限，如需访问，可联系": "The folder is set with separate permissions. You can contact", "Cooper反馈群": "<PERSON> 反馈群【Feedback】", "当前模块发生异常错误，请重新刷新页面，或联系": "An exception error occurred in the current module, please reload page or contact", "的人工客服": "for help.", "，他可在文件夹右侧【...】-【协作】处进行成员权限管理": "，He/She can do it at [...] - [permission setting] on the right side of the folder.", "文件resourceid不能为空": "File resourceid cannot be empty", "扩容申请待审核，详情请查看": "Your request for extension is being approved. Please view more details in", "待审核": "Pending Approval", "团队创建者可申请扩容": "Only the creator can apply", "查看详情": "View more details", "去查看": "View more details", "知道了": "OK", "申请成为团队空间所有者": "Apply to be owner", "个人空间每次可申请扩容": "Every time My Space can be applied for ", "，申请后需等待您的直属上级审核": " extension, which should be approved by your direct supervisor", "每个团队空间每次可申请扩容": "Every time Team Space can be applied for ", "确认申请": "Apply", "申请扩容": "Extend", "登录信息失效，请重新登录": "Login information is invalid, please login again", "校验中": "Checking", "全部上传成功": "All uploaded successfully", "排队中": "Queuing", "上传中": "uploading", "上传成功": "Upload successfully", "上传失败": "Upload failed", "已超过最大容量，请申请扩容后重试。": "The maximum capacity has been exceeded. Please apply for expansion and try again.", "一次性上传文件过多、过大或网络异常可能导致上传失败，建议分批上传或确认网络正常后重新上传。": "Uploading an excessive number of files at one time or an abnormal network may cause the upload to fail. It is recommended to upload them in batches or upload them again after the network is normal.", "已添加": "Added", "用户": "User", "MM月DD日": "MM-DD", "HH时mm分": "HH:mm", "YYYY年MM月DD日 HH时mm分ss秒": "YYYY-MM-DD HH:mm:ss", "YYYY年MM月DD日 HH:mm": "YYYY-MM-DD HH:mm", "MM月DD日 HH:mm": "M-D HH:mm", "关闭": "Close", "首页": "Home", "分享给我": "Share with me", "请在当前团队空间内移动文件，如需跨团队操作请使用“复制”功能": "Please move files within the current team space, if you need to operate across teams, please use the \"copy\" function", "正在复制中，请稍后": "<PERSON><PERSON><PERSON>, please wait", "复制": "Copy", "修改成功": "Modified successfully", "请选择文件夹": "Please select a folder", "移动成功": "Moved successfully", "还没有内容哦": "No content yet", "你可以将文件拖拽到这里，或者": "You can drag and drop files here or", "名称": "Name", "已选中{one}项": "{one} items selected", "已选中{one}": "{one} selected", "没有更多了": "No more left", "加载更多": "Load more", "权限设置": "Permission setting", "复制到": "Copy to", "类型": "Type", "文件夹": "Folder", "Cooper协作文档": "Docs", "氢文档": "D-Doc", "所有者": "Owner", "大小": "Size", "更新时间": "Modified", "编辑": "Edit", "用户已加入【{ teamName }】团队空间，已有该文档的访问权限": "User had joined the [{ teamName }] Team Space, access privileges for this document already exist", "用户已加入该协作文档": "User had joined the Cooper", "暂无消息": "No news", "加载中...": "Loading...", "未处理": "Unhandled", "已处理": "Handled", "全部标记已读": "Read All", "查看": "View", "邀请您加入团队": "Invited you to join the space ", "点赞了协作文档": "Liked the doc", "选择的文件格式不正确": "The selected file format is incorrect", "您没有权限": "You don‘t have permission", "功能暂未开放，您可以尝试拖拽上传": "The function is not open yet. You can try dragging and uploading", "新建": "Create", "匿名文档": "Anonymous Docs", "Cooper协作表格": "Sheets", "Cooper幻灯片": "Slides", "导入为在线文档": "Import as new Docs", "创建文件夹成功": "Created successfully", "请输入文件夹名称": "Please enter a folder name", "位置": "Location", "上传文件到当前目录下": "Upload files to the current directory", "移除成员": "Remove member", "添加成功": "Added successfully", "请输入用户或者组": "Please enter a user or group", "设为管理员": "Is admin", "链接邀请": "Invite <PERSON>", "复制成功": "Copy successfully", "复制链接": "Copy Link", "秒": "s", "小时": "h", "分": "m", "复制已完成": "Replication is complete", "私密-需要验证": "Private - Approval required", "加入团队需要通过管理员审核": "Joining space requires approval", "公开-无需验证": "Public - No approval required", "任何人都可以通过链接加入": "Anyone who has the link can access", "已提交，请等待审批": "Submitted, please wait for approval", "加入成功": "Joined successfully", "创建成功": "Created successfully", "团队名称": "Team Name", "团队已存在": "The team already exists", "已经是第一页": "Already the first page", "已经是最后一页": "Already the last page", "没有更多的图片了": "No more pictures", "没有更多的文本了": "No more text", "上一页": "Previous page", "下一页": "Next page", "上一个": "Previous", "下一个": "Next", "暂不支持此文件预览": "This file preview is not supported at this time", "当前文件已被删除或移动，请进入回收站或搜索进行查找哦": "The file has been deleted or moved. Please search or find it from recycle bin.", "文件": "Files", "将{one}分享给{two}": "Share {one} to {two}", "恢复了文件": "Restored file", "新建在线协作文档": "Create a new online collaboration document", "打开了协作文档": "Opened the Cooper Doc", "修改了协作文档": "Modified collaboration document", "邀请了{one}一起协作在线文档": "Invited {one} to collaborate online documents", "打开预览": "Open Preview", "评论在线协作文档": "Comment on collaboration documents online", "文件夹已被删除": "Folder has been deleted", "收起": "Collapse", "展开全部": "View All", "最近访问": "Recently", "最近编辑": "Last Edited", "搜索结果": "Search Results", "给您分享加密文件": "Share encrypted files with you", "请输入提取密码": "Please enter password to extract", "本表格能力由石墨提供": "This sheet capability is provided by Shi<PERSON>", "分享人": "Sharer", "分享时间": "Shared", "失效时间": "Failure time", "全部文件": "All files", "永久有效": "Permanently Effective", "密码不能为空": "password can not be blank", "未选择任何文件": "No files selected", "已选中{one}个文件/文件夹": "{one} files/folders selected", "定向分享": "Share With Specific People", "全员分享": "Share By Link", "暂无内容": "No content yet", "取消分享": "Cancel sharing", "密码": "Password", "可下载": "Can download", "有效期": "Valid period", "私密": "Private", "公开": "Public", "修改分享": "Edit share", "个月": "Month", "天": "Day", "个月1": " Month", "周1": " Week", "天1": " Day", "请至少添加一个要分享的人或组": "Please add at least one person or group to share", "分享成功，你可以在“": "Sharing successfully, you can view the sharing details in '", "分享成功": "Shared successfully", "”中查看分享详情": "'", "移除": "Remove", "通知": "Notice", "您需对分享内容负责，请确认分享内容及访问权限": "You are responsible for the shared content. Please confirm the shared content and access rights", "分享类型": "Type", "需要密码验证才可以查看分享内容": "Only people with the password can view", "任何人都可通过链接查看分享内容": "Anyone with the link can view", "周": "Week", "访问权限": "Permissions", "创建链接": "Create Link", "链接": "Link ", "复制链接和密码": "Copy Link & Password", "团队动态": "Team dynamics", "暂无动态": "No dynamic yet", "取消置顶": "Remove fom top", "置顶": "Add to Top", "成员": "Member", "动态": "Activity", "设置": "Settings", "申请已发送，请耐心等待审批": "The application has been sent. Please wait for approval", "1. 审批通过（不通过）后，系统会给您发送通知": "1. The system will send you a notice after the approval is passed (or not passed).", "2. 如果{time}后团队所有者未审批，您可以进入该页面重新发起申请": "2. If the owner of the team does not approve after {time}, you can enter this page to re-apply", "3. 您可以联系团队的所有者": "3. You can contact the owner of the team.", "审批": "Approval", "返回首页": "Back to homepage", "申请理由请不要超过30字": "Length of the reason for application cannot exceed 30 characters", "抱歉，您没有权限访问【{team}】团队空间": "Sorry, you have no permission to access [{team}]", "如果需要继续访问该团队空间，请申请访问权限": "If you want to access the team space, please apply for permission", "申请理由（选填）": "Reason for application (Optional)", "请输入申请理由": "Please enter the reason", "申请权限": "Apply for the permission", "确认申请 “{teamName}” 团队空间的访问权限？": "Confirm to apply for access to the team space \"{teamName}\"?", "还没有团队，开始{creation}吧~": "No teams yet, {creation} your team now~ ", "暂无团队，快去{creation}吧": "No teams yet, {creation} your team now", "{count}人": "{count}", "我创建的": "I created", "最近30天访问过的文件": "The files visited in the last 30 days", "移除成功": "Remove successfully", "管理员": "Admin", "取消管理员": "Cancel admin", "批量管理": "Batch manage", "搜索协作成员": "Search members", "全选": "Select", "最多选择1000个": "Up to 1000", "超出选择数量怎么办": "What if over 1000", "批量修改权限为": "<PERSON><PERSON> modify", "已选": "Selected", "当前仅支持一次性选中1000人": "1000 members can be selected at once", "当前最多支持同时选中1000个人": "1000 members can be selected at once", "从空间或父级文件夹继承成员暂不支持移除": "Inheriting members cannot be removed", "搜索成员": "Search team members", "彻底删除": "Delete", "还原": "Recover", "剩余时间": "Remaining", "请先选择新建文件夹的位置": "Select the location of the new folder first.", "正在创建上传任务...": "Creating upload task...", "数量超标": "Exceeded the maximum quantity", "大小超标": "Exceeded the maximum size", "通过校验": "Pass the verification", "已超过最大容量!": "Exceeded the maximum capacity!", "获取上传地址失败!": "Failed to get upload address!Failed to get upload address!", "校验中...": "In checking...", "新增上传任务中...": "Adding upload task...", "当前页面存在未完成的上传任务，您确认要离开此页面么?": "There are unfinished upload tasks on the current page. Are you sure you want to leave this page?", "后续操作保持一致": "Follow up operations are consistent", "保留两者": "Keep both", "是否取消全部文件上传？": "Do you want to discard all file uploads?    ", "继续上传": "Continue uploading", "取消上传": "Cancel uploads", "暂停上传": "Pause upload", "已暂停": "Paused", "已取消": "Cancelled", "重新上传": "Re-upload", "打开目录": "Open the catalog", "用户可以查看在线协作文档": "Users can view collaboration documents online", "用户可以对协作文档进行修改和评论": "Users can modify and comment on collaborative documents", "用户可以将协作文档导出": "Users can export collaboration documents", "您已提交协作文档{slot0}{slot1}权限的申请，由{slot2}进行审批，请耐心等待": "Your application for {slot1} of {slot0} is under approval, which will be approved by {slot2}, please be patient and wait.", "您提交的协作文档{slot0}{slot1}权限的申请已审批{slot2}": "Your application for {slot1} of {slot0} is {slot2}.", "通过": "approved", "拒绝2": "rejected", "因审批人离职或不具备审批权限,你提交的申请协作文档{slot0}{slot1}权限申请已失效。": "Your application for {slot1} of {slot0} is invalid, because that approver has already resigned or doesn't have permission to approve.", "可点击查看其他解决方案": "Click to view other solutions.", "申请者已离职，该申请已失效": "The applicant has resigned and the application is invalid.", "你的文档管理员权限被收回，你无法审批该申请": "You aren't doc administrator and cannot approve the application", "该用户已获得该权限，可前往文档详情查看": "User has obtained the permission, you can view details in doc.", "已被其他管理员同意": "Approved by other administrator", "已被其他管理员拒绝": "Rejected by other administrator", "没有权限审批": "No permission to approve.", "申请已失效": "The application is invalid.", "申请更多权限": "Apply permission", "你在当前{slot0}的权限为{slot1}，申请获得以下权限": "My permission is{slot1}, I can apply for ", "请选择": "please choose", "申请原因": "Reason", "必填，最多200字": "Required, maximum 200 words", "谁是审批人？": "Who is the approver?", "提交申请": "Submit", "文档管理员": "Admin", "文档所有者": "Owner", "可添加协作成员、分享文档": "can add members, share docs", "可添加协作成员、分享文档、设置管理员": "can add members, share docs, set admin", "仅支持申请更多权限，不可降级申请": "Only support applying for more permissions", "提示": "Tips", "你已提交过所有权申请，由{slot0}进行审批。请等待审批结果": "You have applied for doc's ownership, which will be approved by {slot0}, please be patient and wait.", "页面已被删除，3s后为你自动刷新页面": "Page has been deleted,  it will automatically refresh after 3 seconds", "您无法访问该页面，不能申请更多权限": "You can not access this page and can not request additional permissions", "您的申请已提交": "Your application has been submitted.", "申请的权限与你当前的权限一致，请检查后再提交": "Same permission, please check", "请填写申请原因": "Please fill in the reason", "是否提交新申请?": "Are you sure to submit the new application？", "您已经申请了页面的{prePerm}权限，目前还在审批中。是否取消原先申请流程，重新提交{nextPrem}权限的申请？": "Your application for {prePerm} is under approval, which is still under approval. Do you want to resubmit the application for {nextPrem} instead of the original application.", "暂无审批人,建议申请页面所有权": "No approver. it is recommended to apply for page ownership", "暂无审批人，{slot0}": "No approver,{slot0}", "暂无审批人，可联系相关滴滴员工提交申请": "No approver , you may contact the relevant Didi employee to submit an application", "查看其他解决方案": "Click to view other solutions.", "权限设置：": "Permission: ", "邀请您一起协作": "Invited you to collaborate", "已同意": "Approved", "已拒绝": "Rejected", "已由其他管理员同意": "Approved by other administrator", "已由其他管理员拒绝": "Rejected by other administrator", "拒绝": "Reject", "同意": "Approve", "暂无数据": "No data available", "操作记录": "Operation Record", "操作人": "Operator", "操作时间": "Operating time", "操作": "Operation", "目录位置": "Location", "文件位置": "Location", "追加上传": "Additional upload", "正在上传": "Uploading", ":num个文件上传失败": ":num files failed to upload", ":num个文件暂停": ":num files paused", "暂停": "Pause", "取消": "Cancel", "视频预览": "Video preview", "新建了文件夹": "Created a folder", "下载文件": "Download file", "导出协作文档": "Exported the Cooper Doc", "将 {one} 重命名为 {two}": "Renamed {one} to {two}", "移动了 {one}": "Moved the {one}", "移动了 {one} 至 {two}": "Moved the {one} to {two}", "申请": "Apply", "申请协作文档权限": "Apply access to ", "申请理由：": "reason:", "申请协作文档{slot0}的{slot1}权限。": " applied for {slot1} of {slot0}", "申请团队空间权限": "Apply access to ", "协作文档权限": " ", "申请{context, team}": "Applied access to {context, team}", "申请{slot0}协作文档权限": "Applied access to {slot0}", "您已成功加入了": "You have successfully joined the ", "团队空间1": " team space", "协作文档，为您开通了：{permission}权限": " collaboration document, your permission is: { permission }", "您未通过": "You have not applied through ", "团队空间权限申请": " team permissions", "协作文档权限申请": " collaboration permissions", "您所申请的团队空间": "Your extension request for Team Space ", "扩容已经通过审核，容量已由{one}G扩容至{two}G": " has been approved that the space was extended to {two}G", "容量": "Capacity", "您所申请的团队空间 {team} 扩容未通过审核": "Your extension application for team {team} was not apporved", "您所申请的 {personal} 扩容未通过审核": "Your extension application for {personal} was not apporved", " 确定": "Confirm", " 取消": "Cancel", " 替换 ": "Replace", " 好的 ": "OK", "团队空间权限": " ", "创建副本": "Duplicate", "单次文件上传数量不能超过{num}个，大小不能超过{size}，请分批上传或压缩成Zip格式再进行上传": "The number of files uploaded in a time can not exceed {num} and the size in total can not exceed {size}. Please compress files or reselect files before uploading.", "单次文件上传数量不能超过{num}个，请分批上传或压缩成Zip格式再进行上传": "The number of files to be uploaded at a time cannot exceed {num}. Please upload files in batches or Zip format.", "导入文档大小不能超过30MB": "Document imported can not exceed 30MB.", "不再提示": "No more reminder", "反馈": "<PERSON><PERSON><PERSON>", "添加至快速访问": "Add to Quick Access", "移出快速访问": "Remove from Quick Access", "申请已提交": "Application has been submitted.", "已提交所有权申请，你暂时无法提交申请": "has already submitted an ownership application, and you are currently unable to submit another application.", "你已提交过申请，由": "You have submitted an application, and it is currently under review by ", "进行审批。请等待审批结果": " . please wait for the approval result.", "你的申请已提交，将由{slot0}进行审批。审批完成后，你将会收到消息通知": "Your application has been submitted，which will be approved by {slot0}。You will be notified when the approval is complete.", "您的申请已提交到BPM(倚天流程管理平台)中，将由 {one} 进行审批，详情请到BPM中查看": "Your application has been submitted to BPM and will be approved by {one}. Please refer to BPM for details.", "团队所有者可申请扩容": "Team owner can apply for expansion", "协作PPT": "Collaboration PPT", "没有找到匹配结果，尝试其他关键词进行搜索": "No matching results were found, try other keywords to search", "您无权限创建": "You do not have permission to create", "保存": "Save", "正在移动:copyNum个项目": "Moving :copyNum items", "正在复制:copyNum个项目": "Copying :copyNum items", "正在保存:copyNum个项目": "Saving :copyNum items", "保存成功": "Save Successfully", "最近": "Recent", "该文件夹不属于当前团队": "The folder does not belong to the current team", "请在当前团队空间内移动文件": "Please move files within the current team space", "您没有该空间的编辑或上传权限": "You don’t have permission to edit or upload in this space", "该空间层级为空，可新建文件夹": "The space level is empty, you can create a new folder", "团队空间为空，请先创建团队": "The team space is empty, please create a team first", "生成链接失败": "Generate link failed", "修改链接权限成功": "Modify link permission successfully", "文件名称": "File", "需要密码验证才可以查看协作文档内容": "Only people with the password can visit", "任何人都可通过链接查看协作文档内容": "Anyone with the link can visit", "提示：邀请人要对添加的成员负责，请确认添加成员及访问权限": "You are responsible for the shared person, please carefully confirm the permissions", "转换中，请稍后...": "Converting，please wait...", "转让所有者给 {one}": "Transfer ownership to {one}", "@{year} 效能平台部 - 信息平台部": "@{year} EP-IP", "将{src}移动到{dst}": "Move {src} to {dst}", "将{src}复制到{dst}": "Copy {src} to {dst}", "将{src}保存到{dst}": "Save {src} to {dst}", "将{src}上传到{dst}": "Upload {src} to {dst}", "协作文档": "Docs", "协作表格": "Sheets", "其他": "Others", "搜索": "Search", "协作幻灯片": "Slides", "快速访问": "Quick Access", "功能上新": "New features", "常见问题": "FAQ", "帮助文档": "Help center", "客服入口迁移到这里了": "Customer service is here.", "已离职": "Dimission", "已失效": "Invalid", "全部类型": "All types", "幻灯片": "Slides", "创建时间": "Created", "全部": "All", "归我所有": "Owned by me", "打开时间": "Opened", "去清理": "Go to remove", "header-高级搜索-最近浏览": "Recent view", "header-高级搜索-没有最近访问记录": "No recent access records found", "header-高级搜索-搜索知识库和页面": "Search the repository or the page", "header-高级搜索-过滤-选择知识库": "Choose the repository", "header-高级搜索-过滤-所有者": "Owner", "header-高级搜索-过滤-范围": "Range", "header-高级搜索-过滤-全部": "All", "header-高级搜索-过滤-归我所有": "Belong to me", "header-高级搜索-过滤-标签": "Tag", "header-高级搜索-过滤-请输入标签": "Please enter the tag", "header-高级搜索-过滤-浏览时间": "Browsing time", "header-高级搜索-过滤-文件类型": "File type", "header-高级搜索-过滤-pdf": "PDF", "header-高级搜索-过滤-word": "Word", "header-高级搜索-过滤-excel": "Excel", "header-高级搜索-过滤-ppt": "PPT", "header-高级搜索-过滤-视频": "Video", "header-高级搜索-过滤-其他": "Other types", "搜索“{slot0}”页面": "Search \"{slot0}\" page", "message-创建并关联成功": "Created and linked successfully", "message-应用成功": "Applied successfully", "一天内": "Within 1 day", "一周内": "Within 1 week", "一月内": "Within 1 month", "半年内": "Within half a year", "一年内": "Within 1 year", "无": "None", "模板库": "Template", "表格": "Sheets", "文档": "Docs", "全部模板": "All Templates", "选择模板": "<PERSON><PERSON>", "预览": "Preview", "使用": "Create", "使用此模板": "Create", "暂无模板": "There is no English template yet, stay tuned", "推荐模板": "Recommended", "创建文档中...": "Document Creating...", "确定": "Confirm", "氢笔记": "Handy Notes", "实验室": "Lab", "流程图文档": "Flowchart", "我": "Me", "请选择批量操作对象": "Please select files", "刚刚": "Just now", "批量操作": "Multi-select", "目录视图": "Tree View", "列表视图": "List View", "权限说明：": "Permission Description: ", "权限说明": "Permission", "邮件组": "Mail group", "搜索无结果": "No search results", "知识库": "Knowledge", "取消关联": "Disassociate", "解除关联关系": "Disassociation", "没有搜索到您想要的知识库": "No search results", "默认保存在个人空间，暂不支持多人协作": "It is saved in the personal space; Collaboration is not supported.", "名称不能为空": "Name cannot be empty", "名称不能超过{count}个字符": "The name cannot exceed {count} characters", "名称不能包含$'?*:;\"%/|\\等特殊字符": "It can't contain special characters such as %$'?*:;\"/|\\", "请联系团队空间所有者或管理员开启该套件": "Please contact the owner or administrators of the team space to open the tool", "去开启": "open", "启用后在此可一键触达指定圈子": "Once opened, you can access the associated circle here directly.", "启用后在此可一键触达指定群聊": "Once opened, you can access the associated chat here directly.", "启用后在此可一键触达指定团队空间": "Once opened, you can access the associated space here directly. ", "设置谁可以看、谁可以改": "Set who can access", "添加可访问成员": "Add accessible members of the folder", "空间团队成员{memberCount}人": "Team members {memberCount}", "空间所有者": "Team owner", "该文件夹的可访问成员列表": "The list of accessible members for this folder", "空间成员权限变化实时同步": "Synchronize space member permissions", "继承团队空间权限": "Public to all team members", "不继承团队空间权限": "Private to certain members", "自行设置可以访问文件夹内容的成员": "Set who can access folder by myself", "搜索可访问成员": "Search who can access the folder", "暂无搜索结果": "No Search Result", "搜索添加当前团队空间成员": "Search and add team members", "已选择: {num}名成员": "Selected: {num} members", "批量添加": "Batch add", "批量分享": "Batch sharing", "更新日志": "New Features", "您无权限访问": "No permission to access ", "更新权限成功": "Update successfully", "文件夹所有者": "Folder owner", "该链接为他人个人空间的访问地址，您无权访问。可联系分享者获取文件的分享链接进行访问，分享链接获取方式：个人空间列表-【…】更多操作-分享-全员分享-创建链接-复制链接": "This link is the access address of others' personal space. You do not have permission to access it. You can contact the sharer to obtain the sharing link of the file. The way to obtain the sharing link: My Space- [...] more operations - Share - Share By Link - Create Link - Copy Link.", "只读成员": "Viewer", "添加": "Add", "请输入用户名或D-Chat群组、组织架构名称等": " Enter username, D-chat group, Organizational structure name to search", "同时添加包含某个用户的多个群组时，该用户取较高权限（{slot0}成员>只读成员）": "When simultaneously adding a user to multiple groups, the user will have the higher permission level ({slot0} Member > Read-only Member).", "管理员>": "Admin > ", "重置": "Reset", "全部团队空间": "All team spaces", "输入团队空间": "Enter space name", "输入团队空间/知识库名称": "Enter space or repository name", "全部知识库": "All repositories", "输入知识库名": "Enter repository name", "标签": "Label", "知识库-页面-顶部-标签": "tags", "筛选知识库页面": "Filter pages", "请输入标签": "Enter label", "最近浏览时间": "Opened", "最近浏览": "Recently", "没有最近浏览记录": "No browsing history", "未搜索到相关结果，请更换关键词": "No results found. Please change the keyword", "共": "Total ", "条结果": " results", "查看更多": "View more", "请联系知识库所有者": "Please contact the repository owner ", "申请加入。": " to apply for membership", "目录": "Table of contents", "切换为{viewText}视图": "Switch to {viewText} view", "切换为-标签": "tag", "切换为-目录": "catalog", "添加页面、导入、上传本地文件": "Add pages, import and upload local files", "页面": "Pages", "展开目录树": "Expand directory tree", "收起目录树": "Collapse directory tree", "添加{slot0}页面": "Create {slot0} docs", "添加{slot0}表格": "Create {slot0} excel", "子": " ", "导入": "Import as Docs", "上传本地文件": "Upload local Files", "迁入知识库页面": "Move in respository docs", "确认删除": "Confirm", "确认删除-知识库": "the repository", "确认删除-确认删除": "Deleting ", "确认删除-吗？": " ?", "删除后，该页面及其子页面都将一并删除，30天内你仍可在「回收站」中恢复它": "Once deleted, the doc and subdocs will be deleted together, and you can restore them from the Trash with 30 days", "前往帮助文档": "Go to the Help Center.", "wiki迁移帮助文档": " Wiki Migration Help Documentation", "移动成功，已继承{slot0}的成员及权限": "Moved successfully, Members and permissions of {slot0} have been inherited", "新父级页面": " new parent page ", "创建中...": "Creating...", "副本创建失败，您没有权限": "Failed, you don‘t have permission", "没有页面哦，新建页面试试吧~": "No page, try to create a new page~", "确认删除此页面吗？": "Are you sure to delete this page?", "知识库所有者支持跨库移动，非所有者只能在库内移动": "Repository owners can move across the repository, and non-owners can only move within the repository", "添加至\"快速访问\"": "添加至\"快速访问\"", "请输入页面名": "Please enter a page name", "重命名、创建副本、移动到、收藏、删除": "Rename, duplicate, move to, favorites and delete", "添加子页面、导入、上传本地文件": "Add pages, import and upload local files", "搜索{slot0}": "Search for {slot0}", "无匹配的团队空间": "No results", "展开": "Unfold", "恢复后，将与团队空间/父文件夹权限保持一致": "Once restored, it will be consistent with the permissions of the team space/parent folder", "访客链接": "Guset link", "通过开启知识库/页面分享方式获得的链接": "The link is obtained by enabling repository/sharing page", "位置:": "位置:", "请将文件移动至文件夹": "Please move the file to the folder", "不能移动父目录至子目录下": "Cannot move the parent directory into a sub directory", "添加失败，请重试...": "Failed to add member, please try again...", "undefined": "undefined", "部门信息同步中": "Department information syncing", "获取权限失败": "Failed to get permissions", "当前文件夹单独授权": "Separate authorization", "继承自父文件夹": "Inherited from the parent folder", "已添加至": "Added to", "首页-快速访问": "Home-Quick Access", "快速访问数量超出上限": "The upper limit has been reached", "快速访问数量已达到上限20个，请清理后再进行添加": "Added to maximum 20，please remove some before add", "正在复制{copyPercent}，预计{remainingTime}": "Copying {copyPercent}, estimated time remaining: {remainingTime}", "创建团队空间": "Create Team Space", "空间名称": "Space Name", "空间类型": "Space Type", "输入团队名称": "Please enter a space name", "申请 ”{title}“ 团队空间权限": "Applied for access to ”{title}“ ", "删除的文件会进入回收站，30天内可以在回收站进行还原": "The deleted files will be placed in Trash and can be restored within 30 days", "删除的文件会进入": "The deleted files will be placed in", "当前团队空间回收站": " the space's trash", "团队空间回收站": "the space's trash", "30天内可以在回收站进行还原": ", and can be restored within 30 days", "删除的文件会进入空间回收站，30天内可以在回收站进行还原": "The deleted files will be placed in the space's trash and can be restored within 30 days", "打包中...": "Files are packing……", "打包成功": "Files are packed", "打包失败，请重新下载": "Packaging failed, please download again.", "正在导入，请耐心等待...": "Importing, please be patient and wait.", "正在转换，请耐心等待...": "Converting, please be patient and wait.", "个文件": " file(s)", "已从快速访问移出": "Removed from Quick Access", "已分享": "Shared", "文件已被分享给该用户": "The file has been shared with this user", "文件夹图标": "文件夹图标", "恢复内容将展示在历史所在位置，确认恢复内容？": "The restored content will be displayed in the historical location. Are you sure to restore", "由于恢复内容父目录不存在，恢复内容后将移动至根目录，确认恢复内容？": "Since the parent directory of the restored content does not exist, it will be moved to the root directory after recovery. Are you sure to  restored the content?", "内容输入存在问题，请重新输入": "Incorrect content, please enter again", "请刷新列表": "Please refresh the list", "确认恢复内容？": "Are you sure to restore", "确认恢复": "Confirm", "恢复成功，当前已重名，名称后自动加(1)，": "Successfully restored, currently has a duplicate name, automatically adding (1) after the name, ", "前往查看": "Go to view", "恢复成功，": "Restored successfully, ", "确认彻底删除内容？": "Are you sure to completely delete the content?", "删除后内容不支持找回，确认彻底删除内容？": "The deleted content does not support retrieval. Are you sure to completely delete the content?", "文件类型": "File time", "操作者": "Operator", "包含": "Include", "个文件，": " file(s), ", "个文件夹": "个文件夹", "个子页面": "个子页面", "个人空间知识库": "个人空间知识库", "{slot0}知识库": "{slot0}知识库", "新建成功": "Created successfully", "新建失败，请稍后再试": "Failed to create, please try again later", "新建知识库": "Create repository", "知识库名称": "Repository name", "必填，上限100字": "Required. Maximum 100 words", "头像": "Avatar", "简介": "Introduction", "选填，上限1000字": "Optional. Maximum 1000 words", "最多支持{slot0}个字符": "Maximum {slot0} characters", "数据获取失败，请稍后再试": "Failed to get data, please try again later", "暂无知识库": "No repository", "暂无知识库，请创建/关联知识库，让团队成员可以一起沉淀团队知识！": "No repository. Please create or link a repository, and invite teamembers to accumulate knowledge together！", "暂无知识库，快点联系团队所有者创建知识库，一起创作和沉淀团队知识！": "No repository. Please contact team owner to create a repository, and accumulate knowledge together！", "关联已有知识库": "Link existing repository", "创建新的知识库": "Create repository", "创建知识库": "Create repository", "暂无知识库，快点联系团队所有者创建知识库，一起创作和沉淀团队知识": "No repository. Please contact team owner to create a repository, and accumulate knowledge together！", "关联知识库": "Link Repository", "取消关联知识库": "Disassociate", "选择一个你创建的知识库进行关联，成功后团队成员可在团队知识库模块访问该知识库内容": "Please select a repository you created to link. Once linked, team members can access the repository in this team space.", "已选择: {num} 个": "Selected: {num}", "确认关联": "Confirm", "暂无简介": "No introduction", "请搜索并关联知识库": "Search repository", "赶紧创建一个知识门户，帮助团队快速聚合和透传知识": "赶紧创建一个知识门户，帮助团队快速聚合和透传知识", "你还没有被邀请进他人的知识门户": "你还没有被邀请进他人的知识门户", "可前往有管理权限的团队空间创建，在此可进行快速访问": "可前往有管理权限的团队空间创建，在此可进行快速访问", "创建于": "Created on", "最后更新于": "Updated at ", "用协作表格打开": "Open with <PERSON> sheet", "用协作文档打开": "Open with <PERSON> doc", "暂不支持跨空间选择文件，切换空间后需重选文件，是否继续": "File selection across spaces is not support, and reselection is required after you change the space, whether to continue?", "继续": "Continue", "从": "Import files from ", "中导入文件到": " to ", "支持导入文件夹、协作文档和氢文档，导入后将保留原层级关系": "Supports the import of folders, collaboration documents, and hydrogen documents, and retains the original hierarchical relationship after import", "已选：": "Selected: ", "搜索文件夹或文件名称": "Search for a folder or file name", "一次最多可选择50个文件，请分开选择后再导入": "50 files maximum for one time, please import them separately", "联系客服": "Customer Service", "源自": "Derived from ", ", 前往使用": ", go to use", "开启探索之旅": "Start a journey of exploration.", "跳过": "<PERSON><PERSON>", "下一步": "Next", "完成": "Done", "已将页面还原，页面刷新后将展示最新版本。你仍可以在历史记录中找回修改内容。": " has restored the page, after refreshing, the latest version will be displayed. You can still retrieve the modified content from the revision history.", "当前页面已被还原": "Current page has been restored", "刷新页面": "refresh the page", "关闭弹窗": "Close the window", "进入回收站": "Enter the trash", "查看我的知识库": "Check my repository", "当前页面已被删除": "Current page has been deleted", "页面被他人删除，你可以在回收站进行恢复页面。页面内容已被自动保存。": "The page has been deleted by others, which can be restored in trash. The content has been automatically saved.", "页面已经被删除，不可访问": "The page has been deleted and cannot be accessed", "好的": "OK", "刚刚发布了版本": "just published a new version", "刷新页面查看": "refresh the page to see", "副本创建成功，": "Copy created successfully, ", "前往编辑": "go to edit", "副本创建失败": "副本创建失败", "导出成功": "Export successfully", "导出失败": "Failed to export", "切换团队套件": "Switch team suite", "当前知识库": "Current Knowledge", "暂无{slot0}记录": "No {slot0} records available", "团队中心": "Team Center", "无标题页面": "Untitled doc", "直接输入内容，或选取模板进行新建": "Enter the content directly, or select a template to create a new one", "已恢复记录": "Record has been restored.", "⻚面将恢复为": "The page will be restored to the revision history of version ", "的历史记录。当前未发布内容将被覆盖。": " . Any unpublished content will be overwritten.", "确认恢复历史记录吗？": "Are you sure to restore the record?", "历史记录": "Historical record", "恢复至此记录": "Restore to this record", "新增": "新增", "版本": "版本", "发布时间": "发布时间", "发布人": "发布人", "仅看我的": "仅看我的", "请选择版本": "请选择版本", "{historyName}版本": "{historyName}版本", "版本对比功能仍在持续优化中，如遇到问题请加入“知识库反馈群”进行反馈": "版本对比功能仍在持续优化中，如遇到问题请加入“知识库反馈群”进行反馈", "与": "与", "对比": "对比", "修改": "修改", "恢复至": "Restoring to the ", "将会覆盖当前内容。": " version will overwrite the current content", "此⻚面有未发布的内容，恢复至": "This page has unpublished content, restoring to ", "将会丢失未发布内容。": " will lose unpublished content.", "查看未发布的内容": "View unpublished content", "确认恢复至此版本吗？": "Are you sure to restore to this version?", "已恢复内容": "Restored successfully", "发布版本": "Release version", "最新": "Latest", "恢复至此版本": "Rest<PERSON>", "暂无发布版本": "No release version is available", "搜索知识库": "Search for knowledge", "搜索团队空间": "Search for team space", "搜索团队/个人空间": "Search for personal or team space", "当前页面已被删除，即将为您跳转到第一个页面": "The current page has been deleted. You will be redirected to the first page.", "移动端暂不支持展示目录、编辑内容，可前往PC端查看": "The mobile version currently does not support displaying the table of contents or editing content. Please visit the PC version for these features.", "服务器错误，请稍后再试": "Server error, please try again later", "申请成功": "Applied successfully", "你可以申请成为:": "Apply to be: ", "请填写申请原因（选填，最多200字）": "Please fill in the reason for application (optional, Max. 200 words)", "待审批的提醒信息已推送给审批人": "he reminder for pending approval has been sent to the approver.", "已发送申请": "Application sent", "审批结果会以D-Chat消息推送给你": "The result will be sent to you via D-Chat", "名称：": " name: ", "权限：": " permission: ", "当前审批人：": "Current approver: ", "如果": "If the approver does not approve after ", "后审批人未审批，你可以重新进入此页面催促审批": " , you can re-enter this page to urge approval", "审批人超过24小时未审批，你可以点击下方": "If the approver has not approved for more than 24 hours, you can click below ", "催促": "<PERSON><PERSON>", "抱歉，您无权访问{title}": "Sorry, you have no permission to access {title}", "未找到审批人": "Approver not found.", "当前找不到审批人处理你的权限申请，请根据": "Can not find the approver to process your permission application, please ", "帮助手册": "Help manual", "的引导解决访问权限问题": "Bootstrap resolves access permission issues", "更多系统": "More Systems", "桔子学院": "桔子学院", "桔知": "桔知", "已取消置顶": "Removed from top", "置顶成功": "<PERSON>n to top successfully ", "从模版库新建": "Created with template", "新建团队空间": "Create New Space", "新建团队": "Create New Team", "当前网络异常，请检查网络后重试": "Network is abnormal now. Please check the network and try again", "您所申请的": "Your extension application for ", "在": "在", "协作文档中发表了评论：": "协作文档中发表了评论：", "将团队": "Transferred the ownership of space ", "的所有权转交给了您": " to you", "将": "Transferred the ownership of space ", "将新的": "Move", "移动到新的": "to", "的所有者转让给了您": " to you", "已将": "The ownership of space ", "空间的": " of ", "的所有者转让给了": " has been transferred to ", "团队已被团队所有者": " has been deleted by the space owner ", "删除,您在该团队里的文件已被放入": " Your files in the team have been put into ", "回收站将会保留30天。如需使用请联系": "'s trash and will be kept for 30 days. If you want to use the file, please contact ", "在协作表格": "Mention you in Cooper ", "在协作文档": "Mention you in Cooper ", "在氢文档": "Mention you in Didoc ", "中@了你": " ", "在协作": "Mention you in ", "在知识库页面": "在知识库页面", "Cooper作为企业办公场景文档协作平台，禁止上传用户隐私数据，请确保存储数据无违法违规内容。如存储的数据中涉及C3及以上数据，请进入": "<PERSON> prohibits uploading private data, please ensure that the stored data has no illegal content. If what you store involves C3 and above data, please click", "数据加密。": " to apply for data encryption.", "BPM平台申请空间": "BPM platform", "数据安全": "Data Security", "DC反馈群": "DC feedback group", "中文": "中文", "包含:{slot0}": "Include: {slot0}", "标题": "标题", "申请弹窗-描述-知识库": " ", "申请弹窗-描述-页面": " ", "该申请会提交给{text}所有者进行审批；如{text}所有者已离职，会提交给原{text}所有者上级进行审批。审批通过后变更生效": "The application will be submitted to the {text} owner for approval; lf the {text} owner has left the company, it will be submitted to the superior of the original {text} owner for approval. Changes will take effect after approval.", "申请弹窗-知识库": "repository", "申请弹窗-页面": " page", "申请{slot0}所有权": "Apply for the ownership of {slot0}", "申请理由（必填）：": "Application reasons (required):", "请输入申请理由，最多200字": "Please enter application reason within 200 words", "申请所有权弹窗-申请": "Apply", "支持将{text}所有权移交给{slot0}，移交后你将自动变为{text}管理员": "You can transfer the ownership of {text} to {slot0}, and you automatically become an administrator in the {text}.", "支持将-页面": "page", "支持将-知识库": "repository", "支持将-司内用户": "anyone in company", "支持将-知识库内成员": "anyone in repository", "权限移交提示": "Permission transfer notification", "该用户不是知识库内成员，如确定移交，系统会自动将该用户邀请进入该知识库": "The user is not a member of the knowledge. If you proceed with the transfer, the system will automatically send an invitation to that user to join the knowledge.", "确认移交": "Confirm to transfer", "移交成功，已向": "Transferred successfully, and ", "发送D-Chat消息通知": " has been sent a D-chat message", "移交-知识库": "repository", "移交-页面": "page", "移交{slot0}所有权": "Transfer ownership of the {slot0}", "移交": "Transfer", "搜索{slot0}（用户名、邮箱前缀）": "Search {slot0} with name or email prefix", "搜索用户名邮箱前缀-司内": "company users", "搜索用户名邮箱前缀-页面": "page members", "从“快速访问”移出": "Remove from Quick Access", "添加至“快速访问”": "Add to Quick Access", "已复制链接": "<PERSON>d", "已复制密码": "Password Copied", "支持添加当前知识库内的自定义成员组和组织架构，如需在页面中添加自定义成员组，请联系知识库所有者/管理员进行创建": "You can add user-defined member groups and organizational structures in the repository. To add custom membership groups on the page, contact the repository owner/administrator to create them.", "搜索知识库内自定义成员组和组织架构": "Search for custom membership groups and organizational structures in the repository", "推荐": "Recommendation", "自定义成员组": "Custom Group", "组织架构": "Organizational structure", "未搜索到该成员组": "No search results found", "暂无推荐成员组": "No recommended member groups.", "无法重复提交申请": "Cannot submit the application again.", "无法提交申请": "Unable to submit the application.", "联系申请者": "Contact the applicant", "申请了": "Apply for", "页面所有权": " ownership of the repository", "继承知识库权限": "Public to repository members", "知识库成员和被添加的成员均可访问页面": "Both repository members and added ones can access", "不继承知识库权限": "Private to repository members", "仅页面管理员和被添加的成员可访问页面": "Only the admin and added members can access", "可编辑，可管理页面成员": "Editable, can manage page members", "可编辑": "Can edit", "可阅读": "Can read", "无权限成员": "Unauthorized", "页面权限-知识库外受邀成员-移除": "Remove", "该用户将无法访问页面，支持更改": "No permisson to access", "该用户将无法访问页面": "The user will not be able to access the page", "恢复继承": "恢复继承", "该用户权限已被修改，可恢复继承": "The permission of the user has been modified and can be restored", "当前页面单独授权": "authorized separately", "继承自父页面": "Inherit from the parent doc", "知识库所有者": "Repository owner", "页面所有者": "Doc owner", "知识库管理员": "Repository Admin", "页面-权限管理-管理员": "Admin", "页面-权限管理-成员": "Editor", "页面-权限管理-只读成员": "Viewer", "页面-权限管理-无权限成员": "Unauthorized", "是否移除成员": "Are you sure to remove the member", "是否移除成员组": "Are you sure to remove the member group", "已恢复该用户权限继承": "The user's permissions have been restored.", "如修改权限，该用户将不再继承{slot0}权限，支持恢复继承关系": "Once modified, member's permission wii not inherit from the {slot0}, however can be restored.", "如修改权限-知识库": "repository", "如修改权限-父级页面": "parent", "了解成员角色及其权限": "View more about permission", "知识库成员": "Repository member", "知识库外受邀成员": "Outside invited member of repository", "添加成员组": "Add Group", "页面成员": "Member", "角色说明：": "Role Description:", "管理员：所有权限（编辑页面，管理页面成员）": "Administrator: All permissions (edit pages, manage page members)", "只读成员：查看页面": "Read-only member: View the page", "无权限成员：无法访问页面": "No permission member: cannot access the page", "了解页面权限": "View more about permission", "该用户已是页面成员": "The user is already a member of the page", "搜索当前页面成员": "Search in this doc", "同时添加包含某个用户的多个成员组时，该用户取较高权限（成员>只读成员）": "When adding multiple member groups containing a same user, the user takes a higher permission (Editor > Viewer).", "恢复后当前页面权限跟随{slot0}权限自动更改": "After restoration, the current page permissions will automatically follow {slot0} permissions.", "知识库新的": " repository ", "父页面": " parent page ", "了解": "Learn the ", "数据安全分级标准": " standards for data security classification", "页面标签管理": "Tag management", "按Enter键添加标签": "Press Enter to tag", "数据等级C": "Data Level C", "等": " ", "个标签": " tags", "自定义标签：页面成员手动添加的标签。知识库所有者、管理员、页面所有者可管理所有标签；页面「成员」角色只能管理自己添加的标签": "Custom Tags: Tags manually added by page members. Owners, admins, and page owners can manage all tags. The “Member” role on a page can only manage tags they have added themselves.", "系统标签：系统扫描判定的页面数据安全等级，不支持管理": "System Tags: Data security levels determined by system scans, not available for management.", "删除标签": "Delete tag", "上传新版本": "Upload new version", "文件类型不支持预览": "File type is not supported for preview.", "该内容已被删除或失效": "The content has been deleted or becomes invalid", "该内容已被删除或失效，无法继续访问，是否从快速访问列表移出该文件？": "The content has been deleted or becomes invalid and cannot be accessed further. Would you like to remove it from the Quick Access？", "移出“快速访问”": "Remove from Quick Access", "部门": "Department", "知识库页面": "Pages", "Wiki页面": "Wiki", "不限归属": "Owned by anyone", "编辑时间": "Edited", "浏览时间": "Opened", "最近30天编辑过的文件": "Files edited in the last 30 days.", "高级搜索": "Advanced Search", "没有搜索到您想要的内容": "No results", "全新升级，快来体验": "New upgrade, come and experience", "当前空间": "Current Space", "浏览于{slot0}": "Opened: {slot0}", "更新于{slot0}": "Upated: {slot0}", "{slot0}后，获得{slot1}的人都将{slot2}{diffDesc}": "Once {slot0} individuals who have access the {slot1} {slot2}{diffDesc}", "无法": " will no longer be able to ", "可以": " will be able to ", "链接复制成功": "Link copy successfully", "关闭分享": "Close share", "一旦关闭分享，之前获得分享链接的用户将无法访问{shareTypeText}": "Once the share is turned off, users who previously received the share link can't access the {shareTypeText}", "{shareTypeText}已关闭分享": "{shareTypeText} has not been shared. ", "权限变更提示": "Permission update", "确认变更": "Confirm", "下载页面内的附件": "download attachments inside", "查看和添加评论": "view and make comments inside", "更改分享范围": "Change sharing scope", "选择分享“当前页面及子页面”，你为": "Select “Current Page and subpage” to share the subpage of your ", "角色的子页面会同时开启分享": " role at the same time", "下载页面附件": "Can download attachments in docs", "查看并添加划线评论、全文评论": "Can view and make comments", "评论": "评论", "变更成功": "Updated successfully", "变更失败，请稍后再试": "Change failed, please try again later.", "更改分享范围成功": "Changed sharing scope successfully", "{shareTypeText}已开启分享": "{shareTypeText} has been shared.", "仅{shareTypeText}管理员可开启/关闭链接分享": "Only {shareTypeText} admins can enable/disable link sharing", "已开启访客链接分享，获得链接的人可阅读分享内容": "Open, person who gets the visitor link can read the content", "未开启，只有已添加的页面成员可访问该页面": "Closed, only added members can access the docs", "分享范围": "Share scope", "仅当前页面": "Current page only", "当前页面及子页面": "Current page and subpages", "选择分享“仅当前页面”,获得分享链接的用户将无法再访问当前页面的子页面，但不会更改子页面的分享状态": "Select Share “Current page only”, the user who gets the share link will no longer be able to access the subpage of the current page, but will not change the sharing status of the subpage", "链接权限": "Link permission", "知识库安全设置中{disableDesc}权限已关闭": "{disableDesc} permission has been revoked by administrator.", "知识库-安全设置-已关闭-下载": "Attachment download", "知识库-安全设置-已关闭-评论": "Comment", "分享已开启": "Sharing open", "分享已关闭": "Sharing closed", "分享中": "sharing", "区分普通文件预览和页面预览": "区分普通文件预览和页面预览", "文档初始化失败": "文档初始化失败", "退出编辑状态？": "退出编辑状态？", "还未保存公告内容，不保存将会丢失本次编辑内容": "还未保存公告内容，不保存将会丢失本次编辑内容", "保存本次修改": "保存本次修改", "不保存": "OK", "公告": "公告", "请输入公告内容": "请输入公告内容", "管理员可以添加面向全员的公告信息": "管理员可以添加面向全员的公告信息", "设置推荐知识库": "设置推荐知识库", "已选择:{number}/2个": "已选择:{number}/2个", "最多显示2个知识库的内容。知识库必须为当前空间所属的知识库。": "最多显示2个知识库的内容。知识库必须为当前空间所属的知识库。", "空间下暂无知识库，请先添加知识库": "空间下暂无知识库，请先添加知识库", "标题不能为空": "标题不能为空", "请输入内容标题": "请输入内容标题", "链接不能为空": "链接不能为空", "请输入链接地址": "请输入链接地址", "内容推荐": "内容推荐", "暂无推荐内容": "暂无推荐内容", "设置推荐内容": "设置推荐内容", "成员管理": "Members", "搜索当前空间成员": "Search members in this space", "搜索无结果，该用户不在该空间内": "No search results, There is no such person in this space", "已选中{num}/{num}个成员": "Selected: {num}/{num} members", "批量修改为": "<PERSON><PERSON> modify", "确认修改": "Confirm", "确定要将以上成员权限修改为{}吗？": "Are you sure to modify the above members‘ permissions to {} ?", "确定要将以上成员设为管理员？": "Are you sure to set the above members as administrators ?", "确定要将以上成员移除吗？": "Are you sure to remove the above members ?", "定向添加": "Add specific person", "已是知识库成员，通过群组再次添加时不会更改该成员当前知识库权限": "Being a member of the knowledge, re-adding through a group will not alter the current knowledge permissions for that member", "已是页面成员，通过群组再次添加时不会更改该成员当前页面权限": "Being a member of the page, re-adding through a group will not alter the current page permissions for that member.", "已是团队成员，通过群组再次添加时不会更改该成员当前团队空间权限": "If a user is already a member of a team, adding them again through a group will not change his/her space permissions", "同时添加单个用户和包含该用户的群组时，该用户取添加单个用户时设置的权限": "When adding a single user and a group containing that user at the same time,the user takes his/her own permissions", "同时添加包含某个用户的多个群组时，该用户取多个群组权限的并集": "When multiple groups containing a user are added at the same time,the user takes the combination of multiple group permissions", "空间设置": "Settings", "小Di": "小Di", "群聊": "Cha<PERSON>", "云空间": "Space", "圈子": "Circle", "套件管理": "Suite Management", "去启用": "Enable", "未启用": "Disabeld", "没有标签哦，在页面中打标签试试吧~": "No tags, let's tag on docs~", "当前知识库模版": "Template in this repository", "系统模版": "System template", "{slot0} {slot1} 制作": "Made by {slot0} {slot1}", "使用模板": "Apply this template", "确认切换知识库": "Confirm to switch knowledge", "切换后，选中的页面状态将清空，你将需要重新选择。": "After switching, the selected page's state will be cleared, and you will need to choose again.", "确认迁入": "Confirm moving", "确认迁入后，选中的页面及子页面将继承新父级页面的权限，“当前页面单独授权”的成员权限不做调整。迁入页面会从原来位置移除，出现在新的位置。": "After confirming moving, the selected page and its sub-pages will inherit the permissions of the new parent page. Member permissions under 'Current page separate authorization' will not be adjusted. The moving page will be removed from its original location and appear in the new position.", "{slot0}等{slot1}个页面": "{slot0} and total {slot1} page(s)", "确认移动": "Confirm to move", "确认移动后，移动的页面及子页面将继承新父级页面的权限，“当前页面单独授权”的成员权限不做调整。移动页面会从原来位置移除，出现在新的位置。": "After confirming the move, the moved page and subpages will inherit the permissions of the new parent page, and the member permissions of the “current page authorized separately” will not be adjusted. The mobile page is removed from its original location and appears in a new location.", "移动完成，": "Moved successfully, ", "超出知识库页面数量限制": "Exceeds the knowledge base page quantity limit.", "当前最多可以": "The current maximum limit for ", "迁入": "moving", "个页面，所选页面量已超出知识库限制，请调整页面数量后重新": " pages. The selected number of pages exceeds the knowledge base limit. Please adjust the number of pages and ", "迁入至": "Move in under ", "下": " ", "个页面": " pages", "你无权限移动到该页面": "You have no permission to move to this page", "已显示所有结果": "All results shown", "- 已显示所有结果 -": "- All results shown -", "你没有该页面的移动权限": "You have no permission to move this page", "搜索当前知识库页面": "Search the current repository page", "搜索知识库页面": "Search the repository pages", "正在上传{slot0}/{slot1}": "Uploading {slot0}/{slot1}", "{slot0}个文件暂停": "{slot0} files paused", "{slot0}个文件上传成功": "{slot0} files uploaded successfully", "{slot0}个文件上传失败": "{slot0} files failed to upload", "文件夹为空": "文件夹为空", "上传失败，请重试。": "Failed to upload. Please try again.", "文件夹为空，请重新选择文件。": "The folder is empty. Please re-select it", "成功+1": "成功+1", "是否继续上传？": "Do you want to continue uploading?", "文件夹中存在{num}个文档/文件不支持上传，格式如下：{slot}。若选择“继续上传”，系统会过滤不支持格式的文档。": "There are {num} documents or files in the folder that cannot be uploaded, and their format is as follows: {slot}. If you choose “Continue Uploading”, the files and documents in unsupported formats will be automatically filtered by the system.", "终止上传": "Stop Uploading", "支持上传的文件个数为0，请重新选择。": "There is no document type can be uploaded，please select again.", "该位置已经存在名称为{name}的文件，您要用正在上传的文件替换它吗？": "A file with the name {name} already exists in this location. Do you want to replace it with the file being uploaded?", "检测到您上传的文件中存在大文件（>1G），为提升上传体验，推荐您使用同步备份功能。": "It is detected that there are large files (> 1G) in your uploaded files. In order to improve the upload experience, we recommend synchronous backup function to you. ", "使用同步备份": "Use synchronous backup", "文件上传耗时-{slot0}": "文件上传耗时-{slot0}", "性能自定义埋点": "性能自定义埋点", "取消+1": "取消+1", "正在上传文件": "Uploading file", "点击确认后，所有正在上传的文件都将会被取消，确认要关闭进度弹窗吗？": "After clicking confirm, all ongoing file uploads will be canceled. Are you sure you want to close the window?", "正在导入Cooper文档": "Importing Cooper docs", "导入暂不支持中止，点击确认仅会关闭导入弹窗，我们将在后台继续为你导入，仍要关闭导入进度弹窗吗？": "Importing cannot be stopped. Clicking confirm will only close the import window. We will continue importing for you in the background. Do you still want to close the window?", "正在迁入知识库页面": "Moving knowledge pages", "迁入暂不支持中止，点击确认仅会关闭导入弹窗，我们将在后台继续为你移动，仍要关闭导入进度弹窗吗？": "Moving cannot be stopped. Clicking confirm will only close the import window. We will continue moving for you in the background. Do you still want to close the window?", "全部导入完成": "All imports completed", "正在导入": "Importing", "没有结果": "No results", "请输入所有者姓名": "Enter the owner's name", "数据看板": "Data Overview", "链接分享": "URL Share", "创建文件夹": "创建文件夹", "查看帮助": "查看帮助", "退出": "退出", "用户帮助": "用户帮助", "用户反馈": "用户反馈", "保存失败": "Save failure", "保存中...": "Saving...", "撤销": "Undo", "撤销 <br /> Ctrl Z": "Undo <br /> Ctrl Z", "撤销 <br /> ⌘Z": "Undo <br /> ⌘Z", "重做": "Redo", "重做 <br /> Ctrl+Shift+Z": "Redo <br /> Ctrl+Shift+Z", "重做 <br /> ⌘+Shift+Z": "Redo <br /> ⌘+Shift+Z", "格式刷": "Format painter", "取消格式刷": "Unformat painter", "清除格式": "Clear format", "标题 <br /> Control+Shift+Number": "Title <br /> Control+Shift+Number", "字号": "Font size", "加粗": "Bold", "加粗 <br /> Ctrl B": "Bold <br /> Ctrl B", "加粗 <br /> ⌘B": "Bold <br /> ⌘B", "斜体": "Italic", "斜体 <br /> Ctrl I": "Italic <br /> Ctrl I", "斜体 <br /> ⌘I": "Italic <br /> ⌘I", "下划线": "Underline", "下划线 <br/> Ctrl U": "Underline <br/> Ctrl U", "下划线 <br/> ⌘U": "Underline <br/> ⌘U", "删除线": "Strikethrough", "删除线 <br/> Ctrl+Shift+X": "Strikethrough <br/> Ctrl+Shift+X", "删除线 <br/> ⌘+Shift+X": "Strikethrough <br/> ⌘+Shift+X", "文本颜色": "Font color", "默认字色": "Default font color", "文本高亮": "Highlight color", "无背景色": "No fill", "有序列表": "Numbered list", "无序列表": "Bulleted list", "缩进": "Increase indent", "缩进 <br /> Tab": "Increase indent <br /> Tab", "取消缩进": " Decrease indent", "取消缩进 <br /> Shift + Tab": " Decrease indent <br /> Shift + Tab", "对齐": " Align", "任务列表": "Todo list", "编辑器按钮-图片": "Image", "超链接": "Web link", "超链接 <br/> Ctrl K": "Web link <br/> Ctrl K", "超链接 <br/> ⌘K": "Web link <br/> ⌘K", "流程图": "Flowchart", "思维导图": "Mind map", "协作流程图": "Flowchart", "协作思维导图": "Mind Map", "编辑器按钮-文件": "File", "分隔线": "Divider", "代码块": "Code block", "代码块 <br /> ```": "Code block <br /> ```", "折叠块": "Folding block", "引用": "Quote block", "引用 <br /> Ctrl+Shift+U": "Quote block <br /> Ctrl+Shift+U", "引用 <br /> ⌘+Shift+U": "Quote block <br /> ⌘+Shift+U", "公式": "Formulas", "内容": "内容", "导入Wiki压缩包": "Import Wiki", "导入Wiki html页面": "Import Wiki html", "导入Wiki页面": "Import Wiki", "将Wiki页面内容导入知识库": "Import Wiki pages here", "将Wiki html页面内容导入知识库": "Import Wiki html pages here", "导入Cooper文档": "Import Cooper docs", "从Cooper空间选择协作文档": "Import docs from space", "导入Word文件": "Import as Docs", "从本地上传Word文件导入知识库": "Import local word as Docs", "知识库访客链接分享": "Repository visitor link sharing", "知识库所有者和管理员可以开启知识库分享；分享开启后，获得分享链接的任何人只可查看知识库": "Owners and admins can enable knowledge sharing. Once sharing is enabled, anyone with the shared link can only visit the knowledge.", "页面访客链接分享": "Page visitor link sharing", "知识库所有者、管理员、页面所有者可以开启页面分享；分享开启后，获得分享链接的任何人只可查看页面": "Owners, admins, and page owners can enable page sharing. Once sharing is enabled, anyone with the shared link can only visit the page.", "搜索协作文档、知识库文档": "Search for docs and pages", "搜索协作表格": "Search for sheets", "搜索协作幻灯片": "Search for slides", "搜索上传的本地文件、文件夹": "Search for uploaded local files and folders", "附件": "Appendixes", "搜索文档中的附件": "Search for appendixes in Docs", "搜索Wiki页面": "Search for Wiki pages", "成员组": "Group", "导入中": "Importing", "您没有编辑权限，即将跳转到预览页": "You have no access to edit. Will be redirected to the preview page.", "再想想": "Cancel", "开发中，敬请期待！": "开发中，敬请期待！", "我是知识库所有者": "I am the owner of the repository", "我是知识库成员": "I am a member of the repository", "sortby-访问时间": " access time", "sortby-参与时间": " participation time", "sortby-创建时间": " creation time", "了解更多知识库用法": "Learn more about knowledge usage", "按": "Sort by", "排序": " ", "登录成功": "登录成功", "选择门户布局": "选择门户布局", "申请域名": "申请域名", "门户名称重复": "门户名称重复", "门户域名重复": "门户域名重复", "布局预览": "布局预览", "门户名称": "门户名称", "请填写门户名称": "请填写门户名称", "门户域名": "门户域名", "域名是用于成员直接访问门户的网址": "域名是用于成员直接访问门户的网址", "请填写自定义域名后缀": "请填写自定义域名后缀", "新建知识门户": "新建知识门户", "我是知识门户管理员": "我是知识门户管理员", "我是知识门户成员": "我是知识门户成员", "了解更多知识门户用法": "了解更多知识门户用法", "门户配置后台": "Portal Management System", "为团队空间增加一个知识库，高效清晰沉淀团队内部知识": "为团队空间增加一个知识库，高效清晰沉淀团队内部知识", "创建知识库进行个人知识管理": "创建知识库进行个人知识管理", "面向全体空间成员开放，提供信息流快速了解空间使用情况，存放空间成员学习的知识内容以及知识管理排行等": "面向全体空间成员开放，提供信息流快速了解空间使用情况，存放空间成员学习的知识内容以及知识管理排行等", "碎片化的资源管理，支持本地文件线上化能力，内容快速共享协同": "碎片化的资源管理，支持本地文件线上化能力，内容快速共享协同", "空间知识库": "空间知识库", "结构化的知识管理，知识库就像一本书一样，将多篇不同类型的内容结构化地呈现和管理": "结构化的知识管理，知识库就像一本书一样，将多篇不同类型的内容结构化地呈现和管理", "空间功能介绍": "空间功能介绍", "进入空间": "进入空间", "转交了所有者权限": "转交了所有者权限", "移动了": "Move", "恢复了": "Rest<PERSON>", "编辑了": "Edit", "评论了": "Comment", "发布了": "Publish", "更新了": "Update", "下载了": "Download", "暂无团队动态": "No dynamic yet", "空间动态": "Dynamic", "页面已被删除，即将跳转到首页": "The page has been deleted, and you will be redirected to the home.", "已将⻚面恢复至": "The page has been restored to ", "的历史记录，⻚面刷新后将展示最新内容。你仍可以在历史记录中找回修改内容。": " in the revision history. After refreshing the page, the latest content will be displayed. You can still retrieve modified content from the revision history.", "隐藏编写者": "Hide author", "万": "万", "字": "word", "最后修改于": "Last modified in", "查看已发布版本": "View the published version", "查看签批进度": "Check the approval progress", "另存为模板": "Save as a template", "模板保存成功，": "Saved successfully, ", "点击查看": "click here to check", "请输入模板名称": "Please input a template name.", "已发布新版本，并发送通知": "A new version has been published, and a notification has been sent.", "发布后，将向以下用户发送D-Chat通知": "After publication, D-Chat notifications will be sent to the following users", "发布时发送通知": "Send notifications upon publication", "总计：": "Total：", "人，": " individuals, ", "个成员组": " member groups", "全部页面成员": "All page members", "留言": "Leave a message", "@信息通知": "mention notification", "@了以下用户：": "Mentioned the following users: ", "发布并通知": "Publish and notify.", "请搜索通知对象": "Please search for notification recipients", "搜索-无结果": "Search - No results.", "已显示全部结果": "All results displayed.", "发布通知设置": "Publish notification settings", "发布页面时，同步向所选对象发送D-Chat消息通知。此设置仅对你个人的下一次发布生效。": "When publishing a page, a D-Chat message notification is sent synchronously to the selected object. This setting is only valid for your next release.", "将向以下用户发送通知": "Notifications will be sent to the following users", "定向成员": "Specific members", "选填，最多支持200字": "Optional, maximum 200 characters", "是否重新提交发布申请": "Are you going to resubmit the publication application?", "该页面存在审批中的签批流程，是否重新发起申请。一旦重新发起，原申请会被自动作废。": "There is an ongoing approval process on this page. Do you want to initiate a new application? If you proceed, the original application will be automatically voided.", "重新发布": "OK", "无法重复提交发布申请": "Unable to resubmit the publication application.", "该页面存在审批中的签批流程，不能重复提交发布申请。如果要重新提交需先联系原发布申请者（{applicantName}-{applicantEmail}）撤回申请": "There is an ongoing approval process on this page, and it is not possible to resubmit the publication application. If you wish to resubmit, please contact the original applicant ({applicantName}-{applicantEmail}) to withdraw the application first.", "联系原发布者": "Contact the original publisher", "该页面存在审批中的签批流程，不能重复提交发布申请。检测到原发布申请者{applicantName}已离职，可联系流程审批者{approverName}（{approverEmail}）尽快驳回或通过，流程结束后方可重新发起签批流程。": "The page has an ongoing approval process, and it is not possible to resubmit the publication application. It has been detected that the original applicant {applicant<PERSON>ame} has left the company. Please contact the process approver {approver<PERSON><PERSON>} ({approverEmail}) to promptly reject or approve the process. Once the process is concluded, you can initiate the signature process again.", "联系当前审批者": "Contact the current approver", "该页面存在审批中的签批流程，不能重复提交发布申请。检测到原发布申请者{applicantName}和流程审批者{approverName}已离职，请联系审批中心终止流程，流程终止后方可重新发起签批流程": "The page has an ongoing approval process, and it is not possible to resubmit the publication application. It has been detected that the original applicant {applicant<PERSON><PERSON>} and the process approver {approver<PERSON><PERSON>} have left the company. Please contact the approval center to terminate the process. Once the process is terminated, you can initiate the signature process again.", "联系审批中心": "Contact the approval center", "已发布新版本": "Published successfully", "前往分享页面预览": "Go to the sharing page for a preview.", "页面加载中，请等待~": "Loading, please wait~", "正在上传附件，现在发布会导致上传失败，确认要继续发布页面吗？": "Attachment is currently being uploaded. Proceeding with page publication now may result in upload failure. Do you want to continue publishing the page?", "正在上传附件，现在关闭会导致上传失败，确认要继续关闭页面吗？": "Attachments are currently being uploaded. Closing now may result in upload failure. Are you sure you want to proceed with closing the page?", "发布": "Publish", "关闭页面但不发布": "Close the page but not publish", "暂无权限访问": "No access permission currently available", "撤销 <br /> control+z": "撤销 <br /> control+z", "撤销 <br /> ⌘+z": "撤销 <br /> ⌘+z", "重做 <br /> shift+control+z": "重做 <br /> shift+control+z", "重做 <br /> ⇧+⌘+z": "重做 <br /> ⇧+⌘+z", "标题 <br /> shift+control+number": "Title <br /> shift+control+number", "加粗 <br /> control+b": "加粗 <br /> control+b", "加粗 <br /> ⌘+b": "加粗 <br /> ⌘+b", "斜体 <br /> control+i": "斜体 <br /> control+i", "斜体 <br /> ⌘+i": "斜体 <br /> ⌘+i", "引用 <br /> >+空格": "引用 <br /> >+空格", "模版": "Template", "模板保存成功": "Saved successfully", "模板内容不能为空": "The template content cannot be empty.", "模板更新成功": "Updated successfully", "更新模版": "Update", "放弃编辑": "Abandon editing", "退出将不会保存对模版的修改，是否放弃编辑?": "Once exit, the modification of templates will not be saved, whether to abandon editing?", "放弃并退出": "Confirm", "保存模版": "Save template", "更新模板": "Update template", "您是页面成员，{count}s后自动为您跳转": "You are a page member. You will be automatically redirected in {count} seconds.", "您是知识库成员，{count}s后自动为您跳转": "You are a repository member. You will be automatically redirected in {count} seconds.", "分享已失效，不可访问": "The sharing has expired and is no longer accessible.", "页面已被删除，不可访问": "The page has been deleted and is not accessible.", "，前往使用": " Go to use", "首页-{slot0}": "首页-{slot0}", "暂无更多动态": "No more left", "{slot0}时间": "{slot0}", "最新发布": "Latest publish time", "你最近{slot0}的页面记录会展示在这里！": "A record of your most recent {slot0} will be displayed here！", "是否从最近列表中移除?": "是否从最近列表中移除?", "移除后，该内容在最近列表中将不可见。": "移除后，该内容在最近列表中将不可见。", "页面无权限": "No page permissions", "此页面暂未发布，暂不可见": "This page is not yet published and is currently not visible.", "你尚未发布页面": "Your page has not been published yet", "该页面尚未发布": "The page has not been published yet", "继续编辑": "continue editing", "当前网络已断开，请联网后重试": "Network is disconnected. Please reconnect and try again.", "正在导出中，请等待导出完成后再操作": "Exporting in progress. Please wait until the export is complete before proceeding.", "导出中...": "Exporting...", "导出任务超时": "Export task timed out", "下载为Word": "Download as Word", "有未发布内容": "There is unpublished content", "{slot0}万": "{slot0}万", "更新于": "Updated at", "回收站-{slot0}": "回收站-{slot0}", "已恢复至目录中，": "Restored successfully, ", "包含{slot0}个子页面": "Including {slot0} pages", "空空如也": "No deleted records", "确定彻底删除？": "Are you sure to completely delete the content?", "将团队空间知识库转交给空间内其他成员后，你将自动变为知识库管理员": "After transferring the department knowledge to another member within the space, you will automatically become the knowledge admin.", "将个人空间知识库转交给其他司内用户后，该知识库会出现在对方的个人空间下，你将无权限访问": "After transferring the personal knowledge to another user within the company, the knowledge will appear in their personal space, and you will no longer have access to it.", "移交知识库所有权": "Transfer ownership of the repository", "申请知识库所有权": "Apply for the ownership of repository", "申请成功后即可成为知识库所有者，将拥有知识库内的所有权限": "Once applied successfully, you will become the owner of the repository and have all permissions in the repository.", "请求中请稍后": "请求中请稍后", "变更失败": "变更失败", "当前知识库存在分享中的页面，": "There are docs which is accessible to all in this repository.", "知识库-权限-关闭": "revoke permission", "知识库-权限-开启": "empower permission", "知识库-权限-无法": "cannot", "知识库-权限-可以": "can", "{slot0}{slot1}后，获得知识库访客链接、页面访客链接的人将{slot2}{diffDesc}": "{slot0} Once you {slot1}, people who get repository/doc visitor links {slot2} {diffDesc}.", "是否允许获得知识库访客链接、页面访客链接的人下载页面中的附件。": "Whether to allow people who get repository/doc visitor links to download attachments inside.", "是否允许获得知识库访客链接、页面访客链接的人进行评论。": "Whether to allow people who get repository/doc visitor links to make comments.", "安全设置": "Security", "什么是访客链接？": "What is visitor link?", "允许": "Allow", "不允许": "Don’t allow", "转移知识库": "转移知识库", "可将知识库转移至其他团队空间": "可将知识库转移至其他团队空间", "是否确认转移知识库": "是否确认转移知识库", "一旦确认转移，该知识库将从个人空间转移到团队空间": "一旦确认转移，该知识库将从个人空间转移到团队空间", "下，且无法撤销该行为。": "下，且无法撤销该行为。", "确认转移": "确认转移", "转移成功，": "转移成功，", "转移失败，请重新尝试": "转移失败，请重新尝试", "你可以将当前知识库转移至你有管理权限的团队空间。": "你可以将当前知识库转移至你有管理权限的团队空间。", "暂无空间": "暂无空间", "更新成功": "Updated successfully", "更新": "Update", "更新过去时": " updated", "设置-{slot0}": "设置-{slot0}", "基本设置": "Basic", "模板管理": "Template management", "高级设置": "Advanced Setting", "知识库内已存在同名的成员组，请重新命名": "The member group with the same name already exists. Please rename it", "成员组创建后将成员以组的形式添加到知识库，授予一致的角色": "Members will be added to the repository as a custom group, assigned consistent role and permission", "请输入成员组名称": "Please enter group's name", "成员列表": "Member List", "更新并通知": "Update and inform", "取消{slot0}自定义成员组": "Cancel {slot0} a custom group", "取消-自定义成员组-创建": "creating", "取消-自定义成员组-编辑": "editing", "确认退出当前界面吗？一旦退出，不会保存设置。": "Are you sure to exit? Once exit, the settings will not be saved.", "组织架构节点以组的形式被添加到知识{slot0}内，组内成员范围会随员工入转调离情况实时同步": "Organizational structure nodes are added to the {slot0} as groups, and the range of group members is synchronized with the transfer of employees", "添加到-知识-库": "Repository", "添加到-知识-门户": "Knowledge portal", "请输入组织架构节点名称": "Please enter the name of department", "请少选择一些组": "Please select fewer groups.", "该用户已选择": "The user has already been selected", "该用户已是{slot0}成员": "The user is already a member of the {slot0}", "该用户已是-知识库": "repository", "该用户已是-知识门户": "knowledge portal", "搜索当前知识库成员": "Search members in this repository", "未搜索到相关成员和团队，请更换关键词": "Related members and groups are not found, please change the keywords", "删除成员组": "Delete member group", "移除成员组": "Remove member group", "自定义知识库": "Blank repository", "创建空白知识库，开启日常团队协作过程的知识沉淀": "create a blank knowledge base, start the daily process of team collaboration knowledge deposition", "部门知识库": "Department repository", "部门沉淀知识的场所，具备正式和唯一性，成员随部门人员变更实时更新": "the department of precipitation of knowledge, with a formal and unique, real-time membership changes with the department update", "项目知识库": "Project repository", "打通望岳项目，实现项目成员实时同步更新，全流程沉淀项目知识资产": "Through 'WANG-YUE' project, to achieve real-time project members synchronous updates, the whole process precipitation project knowledge assets", "不支持{slot0}等特殊字符": "Can't contain special characters like {slot0}", "名称不能包含{slot0}等特殊字符": "Can't contain special character like {slot0}", "是否解除与团队{slot0}的关联关系？一旦解除成功，该团队将无法访问知识库。": "Do you want to disassociate from {slot0}？Once successfully disarmed, team members will not be able to access the repository.", "确认要移除该成员组吗？一旦移除，该成员组内的成员就无法访问该{name}": "Are you sure to remove this member group? Once removed, members of this member group cannot access the {name}", "知识库及知识库内的所有页面内容": " repository and all page contents in it", "知识门户内容": " knowledge portal contents", "是否解除与知识库{name}的关联关系？一旦解除成功，团队成员将无法访问知识库": "Do you want to disassociate from {name}？Once successfully disarmed, team members will not be able to access the repository.", "是否解除与团队": "Do you want to disassociate from ", "的关联关系？一旦解除成功，该团队将无法访问知识库。你可前往": "? Once removed successfully, the team cannot access the repository. You can go to ", "重新进行关联。": " to re-connect.", "来自：自定义成员组": "From: Custom Group", "来自：Cooper团队": "From: Cooper Teams", "来自：组织架构": "From: Department", "拥有知识库的所有权限": "Own all permissions", "拥有管理和编辑页面、添加和管理成员的权限": "Can manage and edit docs, add and manage members", "仅拥有知识库页面的阅读权限": "Can view and comment docs", "拥有管理和编辑知识库页面的权限": "Can manage and edit docs", "拥有门户管理权限": "拥有门户管理权限", "拥有配置门户和管理成员权限": "拥有配置门户和管理成员权限", "拥有查看知识门户权限": "拥有查看知识门户权限", "模板已更新": "The template has been updated", "已删除模板": "The template has been deleted", "确定删除吗？": "Are you sure to delete?", "模版删除后将无法恢复。已使用模版创建的⻚面不受影响。": "Once the template is deleted, it cannot be restored. Docs created using templates are not affected.", "新建模板": "New template", "制作": "Made by", "重命名模版": "Rename the template", "请稍后，即将为您跳转到第一个页面": "Please wait, you will be redirected to the first page.", "团队空间首页": "团队空间首页", "团队空间文件详情": "团队空间文件详情", "文件详情": "文件详情", "团队添加成员": "团队添加成员", "分享文件夹": "分享文件夹", "仅支持上传zip文件,非zip文件将跳过": "仅支持上传zip文件,非zip文件将跳过", "仅支持上传20M以内的word文件,否则将跳过": "仅支持上传20M以内的word文件,否则将跳过", "最多上传{maxFileCount}个文件，请重新选择后上传": "最多上传{maxFileCount}个文件，请重新选择后上传", "仅支持上传500M以内的文件,否则将跳过": "仅支持上传500M以内的文件,否则将跳过", "普通文件": "普通文件", "请填写门户{slot0}": "请填写门户{slot0}", "最多支持{strLength}个字符": "最多支持{strLength}个字符", "仅支持数字、英文、下划线、中划线": "仅支持数字、英文、下划线、中划线", "D-Chat反馈群": "D-Chat Feedback Group", "下载次数": "Download counts", "公开空间": "Public Space", "私密空间": "Private Space", "今天 {slot0}": "Today {slot0}", "M月d日 hh:mm": "M/d hh:mm", "yyyy年M月d日": "yyyy/M/d", "{slot0}分钟前": "{slot0} minutes ago", "昨天 {slot0}": "Yesterday {slot0}", "yyyy年M月d日 hh:mm": "yyyy/M/d hh:mm", "小于1小时": "小于1小时", "{slot0}小时": "{slot0} hours", "{slot0}天": "{slot0} days", "{slot0}天{slot1}小时": "{slot0} days {slot1} hours", "包含{slot0}个文件，{slot1}个文件夹": "Including {slot0} files and {slot1} folders", "用户可以对文件、文件夹进行分享，针对在线协作文档用户可以添加邀请协作者": "Can share files and folders in the space", "次": " counts", "空间容量": "Space Capacity", "联系空间所有者扩容": "Contact Space Owner", "扩容审核中，前往BPM查看": "Your request for extension is being approved. Please view more details in BPM", "当空间剩余容量低于5G时，会自动开通空间容量申请渠道，空间所有者可以提交申请": "When the remaining space capacity is less than 5G, the space capacity application channel will be automatically opened, and the space owner can submit the application", "输入的转交人必须是团队成员，请核实后再确认提交": "Please note that the new owner must be a team member", "新所有者": "New owner", "请输入新所有者的邮箱前缀": "Please enter the new owner's email prefix", "转交成功会以邮件和D-Chat通知": "The new owner will be notified by email and D-Chat", "是否退出该空间？": "Are you sure to exit this space?", "退出空间后全部文件会保留在空间里": "After exiting the space, all files will remain in the space", "退出成功": "exit successfully", "团队空间{name}一旦删除成功": "Once team {name} is deleted, ", "空间内所有文档将进入文档所有者的个人回收站，无法恢复至团队空间": "All files in team space will be moved to file owners' trash bin, which can not be restored to original team space", "请在下方输入{risk}进行再次确认": "Please enter {risk} to confirm.", "以上风险我已知悉": "I know the risk of above", "空间内所有文档无法进行恢复，请谨慎操作": "all documents in the space cannot be restored, please be careful to do it.", "空间内所有内容将进您的回收站，30 天后自动彻底删除。如果你删除的内容中有属于他人的，其所有者将收到通知，请谨慎操作，空间关联行为较多删除后可还原内容包含：空间所在位置、空间内文件、空间内成员、空间成员权限、空间类型": "All content in the space will be put into your Trash, and will be deleted completely after 30 days. If what you delete belongs to others, the owner will receive a notice. So please be careful to delete the team. If you want to recover the deleted team, The content that can be restored includes: space type, location, member permissions and files in space.", "转交": "Transfer", "移交空间所有权": "Transfer Space Ownership", "将知识库权限移交给其他司内用户后，你将自动变为知识库管理员权限": "After transferring, you automatically become an administrator in the repository.", "将空间所有权移交给其他用户后，你将自动变为空间管理员": "After transferring space ownership of the space to another user, you will automatically become a space administrator.", "删除空间": "Delete Space", "一旦删除成功，空间内所有内容将进您的回收站，30天后自动彻底删除。如果你删除的内容中有属于其他人的，其所有者将收到通知，请谨慎操作": "Once Space is deleted, all content in the space will be put into your Trash, and will be deleted completely after 30 days. If what you delete belongs to others, the owner will receive a notice. ", "申请空间所有权": "Apply to be owner", "申请审批通过后即可成为当前团队空间所有者，拥有团队空间内的所有权限": "After the application is approved, you can become the current team space owner and have all permissions within the team space", "退出空间": "Exit the space", "成功退出当前团队空间后，你在该空间生产的所有内容会留在当前空间": "After successfully exiting the current team space, all content you produce in that space will remain in the current space", "部门套件管理": "Department Suite Management", "查看或管理当前部门下的其他套件": "View or manage other suites under the current department", "前往套件管理": "Go to Suite Management", "用户可以通过链接加入到此团队中，发送邀请链接前请确认邀请对象": "Users can join this team through the link. Please confirm the invitation object before sending the invitation link", "或扫描二维码邀请成员": "Or scan the QR Code to add members", "申请理由": "Reasons", "此申请会提交到空间所有者": "This application will be submitted to the space owner", "此申请会提交到空间原所有者直属上级": "This application will be submitted to the immediate superior of the original owner of the space", "此申请会提交到你的直属上级{apv}进行审批，审批通过后变更生效": "This application will be submitted to your immediate superior {apv} for approval, and the change will take effect once approved", "删除失败": "Failed to delete", "空间已被恢复，请刷新后重试": "The space has been restored. Please refresh and try again", "只查看": "View only", "确认恢复当前空间【可恢复内容包含:空间所在位置、空间内的文件、空间内的成员、空间成员权限、空间类型】": "Confirm the recovery of the current space [The recoverable content includes: location of the space, files in the space, members in the space, member permissions, and space type]", "团队空间还原成功": " restored successfully", "的团队空间正在还原中,请等待...": " The team space is being restored, please wait a moment.", "恢复已失败，请联系Cooper反馈群": "Recovery has failed, please contact <PERSON>Cooper 反馈群【Feedback】” in D-Chat.", "群组": "Group", "查看更多{value}": "View more {value}", "'个人空间'为系统预设空间，不允许创建": "Personal Space is the default space for the system and is not allowed to be created", "来自:": "From: ", "设置管理员成功": "Successfully set the administrator", "取消管理员成功": "Successfully cancel the administrator", "请至少勾选一个成员": "Please check at least one member", "确定要将以上成员权限修改为{one}吗": "Are you sure you want to change the above member permissions to {one}?", "确定要将以上成员移除吗?": "Are you sure you want to remove the above members?", "确定要将以上成员取消管理员?": "Are you sure you want to cancel the administrator of the above members?", "确定要将以上成员设为管理员?": "Are you sure you want to set the above members as administrator", "确认取消分享吗？": " Are you sure you want to cancel sharing?", "取消分享后，该条分享记录将被删除，好友将无法再访问分享内容。": "When you unshare, the sharing history will be deleted and your friends will no longer be able to access the shared content.", "取消成功": "Cancel Successfully", "导入成功": "Imported successfully", "导入失败": "Import failed", "转换成功": "Converted successfully", "转换失败": "Convert failed", "该团队关联知识库已达上限5个": "The team space has been associated with up to 5 repositories", "已关联": "associated", "部门知识库不可关联": "department repository cannot associate", "该知识库已关联": "The repository is associated", "接收人不能是自己": "The recipient cannot be himself", "暂无搜索内容": "No search results", "暂无最近访问记录": "No recently records", "申请空间容量": "Apply for space capacity", "扩容申请中，可前往{bpm}查看": "Your application has been submitted. Please refer to {bpm} for details", "新建数量已达上限5": "You can just associate up to 5 repositories", "转交成功": "Successful transfer", "确认还原": "Confirm restore", "已经存在名称为“{name}”的团队空间，您空间名称会恢复为“{spaceName}”": "A team space with the name “{name}” already exists. Your space name will be restored to “{spaceName}”", "链接及密码": "Link & Password", "可不填": "Not fill", "包含关联知识库内容": "Involve associated repository", "暂无分享内容": "No results", "群组中的人会被打散添加进来，后续进群人员数据不做更新": "People in the group will be broken up and added, and the data of subsequent people in the group will not be updated", "链接地址已经失效": "The address is invalid.", "请联系该链接的分享者": "Please contact the sharer.", "第三方": "Third-party system", "发送文件": "Send files", "搜索云空间文件": "Search docs", "发送成功": "<PERSON><PERSON> successfully", "发送失败，请重试...": "Failed, please try it later...", "普通文件发送后，可被接收者分享或转发": "The files can be shared or forwarded by the receiver", "普通文件及知识库分享页面发送后，可被接受者分享或转发": "The files can be shared or forwarded by the receiver", "发送云文档": "Send docs", "已选 {selected}/{total}": "Selected {selected}/{total}", "没有搜索结果": "No search results", "类型可多选": "Can multiple select", "空间类型修改失败": "Failed to modify space type.", "安全提示": "Safety Tips", "是否允许此功能获取您需要备份的本地文件夹访问权限？": "Allow this feature to gain access to the local folders you need to back up?", "同步盘": "Cloud Drive", "我的收藏": "Favorites", "文件回收站": "File", "空间回收站": "Space", "个人": "Person", "团队": "Team", "知识库1": "Knowl.", "收藏1": "Fav.", "更多1": "More", "当前团队空间内的文件、文档、文件夹等被删除后会进入该回收站，保留30天后彻底删除不可恢复。": "Once the files in the current space are deleted, they will enter the trash and remain for 30 days. After 30 days, they will be completely deleted and cannot be restored.", "个人空间内的文件、文档、文件夹等被删除后会进入该回收站，保留30天后彻底删除不可恢复。": "Once the files in personal space are deleted, they will enter the trash and remain for 30 days. After 30 days, they will be completely deleted and cannot be restored.", "个人创建的团队空间被删除后会进入该回收站，保留30天后彻底删除不可恢复。": "Once the team space you created by yourself is deleted, it will enter the trash and remain for 30 days. After 30 days, they will be completely deleted and cannot be restored.", "Cooper搜索能力强势升级": "Search is better and more powerful", "支持全文内容检索、团队空间检索，新增知识库、Wiki的检索数据来源，筛选能力多样化，提升搜索触达效率和体验！": "You can search with content keywords and the help of diverse filter capabilities. You can search team space,  Knowledge, Wiki here now!", "协作文档/表格升级2.0新版本": "Docs/Sheets upgrade to Version 2.0", "编辑、协作功能强化——支持跨表引用、多级下拉菜单、锁定单元格/表、保存版本等能力。离线缓存能力增强，网络不好也不怕！": "Editing functions are enhanced. It is supported to use IMPORTRANGE, lock sheet/cells and so on. Besides offline caching is provided.", "团队空间有了自己的回收站": "Team space owns its trash bin", "团队空间内被删除的文档会进入文档所在空间内的回收站，30天内支持文档所有者或空间管理员恢复至原位置！": "Once the files in team space are deleted, you can see the deleted files in the team space's trash bin and they can be restored within 30 days.", "知识库移动端消费升级啦": "Better mobile experience to visit Knowledge", "云空间内新增知识库入口，强化了移动端知识消费能力，支持查看目录、收藏和分享知识库页面。": "Knowledge is added in Space on the mobile. You can visit directory in the repository, add pages to Favorites and share pages to others.", "最近、收藏使用场景优化": "More knowledge in Recently and Favorites", "最近访问/编辑新增Wiki和知识库数据、收藏模块新增知识库数据，统一司内知识消费路径，帮助知识库快速寻回。": "Recently and Last Edited include knowledge and Wiki pages. Favorites includes knowledge pages. You can access more knowledge here.", "Cooper新视觉新面貌": "<PERSON> with new face", "新版Cooper平台提供了全新的视觉感受，屏效更高，增加内容露出，小屏电脑体验更加友好！": "Better experience with higher screen efficiency on the small screen computer.", "开启Cooper平台新体验": "Start to experience", "被删除的内容已进入\"当前团队空间回收站\"，30天内你可以在这里找回。": "The deleted files have entered the space trash, you can restore them within 30 days.", "被删除的内容已进入\"回收站\"，30天内你可以在这里找回。": "The deleted files have entered the trash, you can restore them within 30 days.", "恢复失败，请联系Cooper反馈群": "recovery failed, please contact Cooper <PERSON> Group", "类型说明": "Type  Description", "文档：Cooper协作文档、匿名文档、知识库文档": "Docs: <PERSON>, Anonymous Docs; Knowledge Docs", "表格：Cooper协作表格": "Sheets: <PERSON> Sheets", "幻灯片：Cooper协作幻灯片": "Slides: Cooper Slides", "文件：Cooper、知识库上传的本地文件": "Files: Uploaded local files (<PERSON>, Knowledge)", "Wiki页面：Wiki页面": "Wiki: Wiki pages", "文档：Cooper协作文档": "Docs: <PERSON>", "流程图：Cooper协作流程图": "Flowcharts：Cooper Flowcharts", "思维导图：Cooper协作思维导图": "Mind maps：Cooper Mind maps", "文件：本地上传的Doc、Excel、PDF等文件": "Locally uploaded Doc, Excel, PDF and other files", "放大": "Zoom in", "缩小": "Zoom out", "全屏": "Full screen", "下载原图": "Download the original", "首页已开启，前往管理": "Home is opened, go to manage", "添加标签页": "Add <PERSON>", "管理标签页": "Manage Tabs", "展示重要信息进行团队透传": "To display important information for team members", "用结构化目录形式进行知识沉淀": "To precipitate knowledge with structured directory", "知识库新": "Repository", "重命名标签页": "<PERSON><PERSON>", "在浏览器中新开页签": "Open a new tab in browser", "是否关闭首页": "Are you sure to close Home?", "“首页”关闭后，空间成员不可访问。当前设置数据会保留，重新开启后可见。": "After “Home” page is closed, space members cannot access it. All contents will be retained and visible after reopening.", "是否移除此标签页": "Are you sure to remove this tab?", "该标签页将被移除，但数据本身不会删除，你可前往": "This tab will be removed, but the data itself will not be deleted. You can go to  ", "访问": " to access it.", "移除标签页": "Remove", "关闭首页": "Close", "知识库原链接，知识库成员可访问": "The repository’s original link, accessible to all repository’s members", "管理员可添加内容，向空间内成员展示。": "Administrators can add content to display to members in the space.", "编辑首页": "Edit Home", "应用": "Apply", "取消本次编辑将不会保存内容": "Canceling this edit will not save the content", "创建并关联知识库": "Create and link new repository", "创建一个新的知识库，并自动关联到该团队空间": "You can create a new repository, which will be linked with this team space.", "请输入知识库名字": "Please enter repository name", "关联新知识库": "Link a new repository", "云盘存储和在线协作、聚合团队知识内容": "Online storage and collaboration. To aggregate team knowledge", "空间": "Space", "默认模块，不支持取消勾选": "Default selection, unsupported to uncheck", "初始模块": "Initial Module", "关闭后数据将保留，支持再次添加": "Data will be retained after closing and can be added again", "关联成功": "Linked successfully", "空间中不存在该知识库": "There is no such repository in the space", "您没有权限！请联系空间管理员添加上传权限。": "No permission! Please ask space administrator for upload permission in the target location.", "您没有权限! 请联系空间管理员添加目标位置的编辑/上传权限。": "No permission! Please ask space administrator for edit/upload permissions in the target location.", "请输入标签页名称": "Please enter tab name", "开启": "Open", "标签页移除成功": "Removed successfully", "添加首页内容进行展示": "Add homepage content for display", "请输入所有者名称": "Enter the owner's name", "我的个人空间": "My Space", "模块": "Behavior", "开启全新体验": "Start", "空间首页全新上线": "Home of Team is newly launched", "推出团队首页，让重要信息脱颖而出：我们是谁、目标是什么、团队运营方式、正在进行的工作…争做团队仪表盘！": "Team homepage is designed to make important information stand out: who we are, what our goals are, how our team operates, ongoing work, striving to be a team dashboard!", "关联知识库展示升级": "Associated repository's display is upgraded", "同一空间下可直接展示文件及知识库内容，降低两个平台跳转的割裂感，提高内容的快速触达效率，提升使用体验！": "Files and repositories can be displayed in a space, reducing the sense of disconnection between two platforms, improving the efficiency of accessing content, and enhancing user experience!", "空间新增快捷访问区域": "Quick access of Team is newly launched", "支持添加/管理标签页，聚合不同类型知识：知识库（已支持）、文档/表格（远期支持）等，自定义变化标签页排序和名称.": "Tabs of team can aggregate different types of knowledge: repository(already supported), docs/sheets (future feature) , etc., and can be sorted and renamed.", "团队成员协作权限": "Team member collaboration permissions", "所需权限": "Required permissions", "当前位置：编辑/上传/下载": "Current location: Edit/Upload/Download", "目标位置：编辑/上传": "Target location: Edit/Upload", "了解更多团队空间权限": "Learn more about team space permissions", "对应文件管理员": "File administrator", "上传/下载": "Upload/Download", "编辑/上传/下载": "Edit/Upload/Download", "编辑/上传": "Edit/Upload", "新建/上传": "Create/Upload", "查看更多知识库": "View more repositories", "移交页面所有权": "Transfer ownership of the repository", "转让页面所有权": "Transfer ownership of the repository", "已经到底了～": "No more left", "重命名成功": "<PERSON><PERSON> successfully", "包含页面": "Contains pages", "暂不支持访问": "Access not supported", "移动端暂不支持访问知识库回收站、设置、数据看板，如需访问知识库目录及内容，请升级D-Chat至最新版本或前往PC端访问": "Access to the trash, settings, and data dashboard is currently not supported on the mobile platform. If you need to access the directory and content, please upgrade D-Chat to the latest version or visit the PC platform.", "移动端暂不支持访问知识库回收站、设置、数据看板，请前往PC端访问": "Access to the trash, settings, and data dashboard is currently not supported on the mobile platform. Please visit the PC platform.", "退出全屏": "Exit Full Screen", "已是原图": "It's original", "查看原图": "Original image", "该空间已被删除，您对": "The space", "团队空间的权限申请已失效": "has been deleted, permission application is invalid", "该协作文档已被删除，您对": "The collaboration document", "协作文档权限申请已失效": "has been deleted, permission application for is invalid", "，赋予权限：": "，Grant permissions: ", "外部": "External", "内部空间": "Internal space", "外部空间": "External space", "公司内部文档协作空间": "Internal document collaboration space within the company", "与企业外用户文档协作空间": "Collaboration space with external user documents", "外部空间说明": "External space description", "空间可见性": "Space Visibility", "申请创建外部空间": "Apply to create an external space", "外部空间的创建需通过相关审批后方可实现，具体审批流程请见": "The creation of outer space requires approval before it can be implemented. For specific approval procedures, please refer to", "审批说明": "Approval Instructions", "详情请见功能说明": "Please see the function description for details", "一旦创建成功，当前团队空间会成为“外部”团队空间，空间管理员可以邀请企业外的合作伙伴加入空间进行文档协作与分享，": "Once successfully created, the current team space will become an 'external' team space, and the space administrator can invite external partners to join the space for document collaboration and sharing. ", "申请创建": "Apply create", "外部空间不支持设为公开": "External spaces do not support setting to public", "当空间容量不够用时可联系租户管理员进行申请": "Once the space capacity is exhausted, please contact the tenant administrator for application", "团队名称不能为空": "The team name cannot be empty", "输入团队空间名称": "Enter the team space name", "外部联系人需为D-Chat外部联系人后可进行添加": "External contacts need to be D-Chat external contacts before they can be added", "搜索协作文档": "Search for docs", "“外部空间”支持滴滴员工和企业外成员开展在线协作和内容共享。每个空间成员需要对空间内的数据及分享对象负责，请谨慎操作。禁止上传用户隐私数据，同时请确保数据无违法违规内容。": "The External Space supports online collaboration and content sharing among Didi Global employees and external members.Each space member is responsible for the data in the space and all the sharing actions, please operate with caution.Uploading of user privacy data is strictly prohibited, and please ensure that the data doesn't contain any illegal or inappropriate content.", "此空间数据安全等级文件统计如下：": "The data security level statistics for files in this space are as follows:", "{num}等级:{count}个": "{num} level: {count}", "审批状态": "Approval Status", "申请中": "Pending Approval", "申请被驳回": "Application Rejected", "您提交的“开通外部空间申请”": "Your 'External Space Activation Request'", "已审批通过，": " has been approved. The", "创建成功，请开启内外协作吧！": " has been successfully created. Please proceed with internal and external collaboration!", "已被驳回，": " has been rejected.", "驳回原因：": " Reason for rejection: ", "创建失败，如有需求请在Cooper重新创建或在BPM申请页面修改后重新提交。": " creation failed. If you have further needs, please recreate it in Cooper or modify and resubmit on the BPM application page.", "暂不支持通过访客链接访问": "Access via guest links is not supported", "数据安全等级统计": "Data security level statistics", "接收人不能是外部用户": "The recipient cannot be an external user", "你没有权限，3s后自动跳转到“分享给我”": "Sorry, you do not have permission. The page will automatically redirect to the 'Shared with me' list after 3 seconds.", "当前登录账号为": "The current login account is ", "由于文档所在空间的所有者未开通对外分享能力，你可以": "As the owner of this space has not enabled external sharing, you can", "由于该空间的所有者未开通对外分享能力，你无法申请权限": "As the owner of this space has not enabled external sharing, you are unable to apply for permission. ", "你可以联系分享者开启对外分享能力后获得有效的分享链接": "you are unable to apply for permission. You can contact the sharer to enable external sharing ability and obtain effective sharing links", "你可以联系分享者开启空间对外分享能力后获得有效的分享链接": "you are unable to apply for permission. You can contact the sharer to enable external sharing ability and obtain effective sharing links", "输入密码": "Enter password", "请输入密码": "Please enter password", "密码错误": "Password error", "你可以联系分享者邀请你成为成员": "You can contact the sharer to invite you to become a space collaboration member", "申请成为团队空间": "Apply to be a team space", "申请成为协作文档": "Apply to become a docs", "的所有者": "owner", "请填写审批理由，限制200字（选填)": "Please fill in the approval reasons, and limit it to 200 words (optional)", "该空间被删除或链接存在问题": "A problem with the space being removed or linked", "当前为外部空间，只支持创建以下文档": "Here is an external space and you can only create documents as follows", "切换组织": "Switch Organization", "切换为中文": "切换为中文", "退出登录": "Log Out", "切换为英文": "切换为英文", "分享普通文件后，文件可被接受者转发及分享": "After sharing a regular file, the file can be forwarded and shared by the recipient", "暂无团队空间": "There is no team space available now", "外部空间内的文件不支持设为公开": "Documents in external space can not be made public", "全文评论": "full text comments", "添加评论": "Add comment", "用户本人头像": "avatar", "评论全文": "Add comments", "评论字数过多": "Overword review", "enter为换行，shift+enter为发送": "Press 'enter' to newline，Press 'shift+enter' to send", "发送": "send", "已更新": "updated", "回复": "reply", "确认删除此条{slot0}吗？": "Are you sure delete this {slot0}?", "反馈给Cooper": "Fe<PERSON><PERSON> on <PERSON>", "反馈类型": "Type", "使用咨询": "Use Consultation", "Bug缺陷": "Bug", "困惑与建议": "Confusion and suggestions", "使用场景": "Channel", "网页浏览器": "Web browser", "手机移动端": "Mobile", "iPad端": "iPad", "功能模块": "Functional", "文件上传": "Files upload", "协作文档/表格": "Docs/Sheets online", "文件预览": "Files preview", "文件分享": "Files sharing", "文件搜索": "Files search", "权限使用": "Usage of permissions", "其他（可在问题描述中阐述）": "Others (you can explain in 'Problem description')", "问题描述": "Problem description", "请详细描述问题（选填，最多200个字）": "Please detail the problem (optional, maximum of 200 words)", "希望通过什么方式联系你解决": "How can we contact you", "D-Chat": "D-Chat", "邮箱": "Email", "电话": "Telephone", "联系方式": "Contact information", "请输入你的D-Chat用户名称或邮箱前缀": "Please enter your name or email prefix", "请输入你的邮箱地址": "Please enter your Email", "请输入你的电话": "Please enter your telephone", "请说明你的联系方式": "Please state your others contact information", "请输入联系方式": "Please enter your contact information", "解决方式": "Contact information", "提交": "Submit", "请填写": "Please enter", "Cooper相关问题，请点击咨询": "Fe<PERSON><PERSON> on <PERSON>", "点击反馈Cooper问题": "Click to provide feedback on <PERSON>", "反馈已提交，请耐心等待": "Feedback has been submitted, please be patient and wait", "前往容灾版Cooper": "Disaster-Tolerant of Cooper", "故障通知": "Fault Notice", "Cooper切换通知": "Switch Notice", "相关服务出现故障，Cooper平台相关功能受到影响，我们正在全力处理以确保尽快恢复，给您带来不便，敬请谅解。": "There has been a service malfunction that has affected the functions of <PERSON>. We are making every effort to ensure prompt recovery. We apologize for any inconvenience caused.", "服务故障短时间无法恢复，有工作需求的同学可前往使用Cooper容灾版。": "Service malfunction cannot be restored in a short period of time. Who needs to visit docs can use Disaster-Tolerant of Cooper.", "Cooper提示": "Tips", "使用容灾版": "Use", "我知道了": "Got it", "智能客服全新上线，答疑更便捷！": "Cooper AI assistant is coming！", "流程图、思维导图功能上新啦": "Flowchart and Mind Map are coming", "快来体验Cooper全新功能吧！思维导图、流程图助你工作更上一层楼！": "Use flowchart and mind map to help you work more efficiently!", "转交给我的": "Transferred to me", "原所有者:{slot}": "original owner: {slot}", "{solt0}将{solt1}的文件转交给您，共计：": "{solt0} deliver {solt1} files to you，In total:", "{slot0}个团队空间": "{slot0} Team", "{slot0}个知识库": "{slot0} Knowledge", "服务故障暂未恢复，移动端不支持访问，Cooper在网页端提供了容灾通道，请前往网页端访问。": "Service malfunction has not been restored yet, and docs don’t support viewing on the phone. You can visit docs through Disaster-Tolerant of <PERSON> on website.", "前往使用容灾版": "Disaster-Tolerant of Cooper", "选择模板1": "template", "你没有权限，可联系空间所有者操作": "No permission，you can ask space owner for help", "仅空间所有者和空间管理员可以关联知识库，": "Only space owner and administrators can link repositories. ", "查看详细权限说明": "View more permission instructions", "空间所有者：支持添加、修改、移除所有关联的知识库，支持管理首页": "Space owner: Can link repositories and modify, disassociate all repositories linked with space", "空间管理员：仅支持添加、修改、移除自己关联的知识库": "Space administrators: Can link repositories and modify, disassociate repositories linked by themself.", "模版预览图中无法展示流程图，但应用后可正常使用": "Flowchart cannot be displayed in the template preview, but it can be used normally after application.", "找回本地数据": "Retrieve local data", "针对你提交的“成为协作文档": "Your application for doc ", "所有者“的申请，鉴于协作文档被删除，该申请已失效。": "ownership is invalid due to the deletion of the doc.", "所有者“的申请，鉴于目前审批人{name}已离职或不具备审批权限，该申请已失效。暂无审批人可审批你提交的文档所有权申请，可联系相关滴滴员工提交申请。": "ownership is invalid because the approver {name} has resigned or doesn't have approval authority. No approver is available now and you can contact Didi employees to submit the application.", "，申请理由：": "，Reasons：", "点击查看审批人逻辑": "who is the approver ？", "你的申请已提交，由{one}进行审批，请耐心等待。如需查看进度可前往BPM（倚天流程中心）": "It is submitted to {one} for approval. Please be patient and wait. You can check the progress through BPM.", "已有团队空间成员": "Space member", "提交空间所有权的申请，你无法提交。如需申请可先联系": "has submitted the application for doc ownership. You should firstly ask ", "撤销所有权申请后你再尝试申请。": "to revoke the ownership application, then to apply again.", "您的申请已提交，将由 ({one}) 进行审批。": "It is submitted to {one} for approval.", "针对你提交的“成为团队空间": "Your application for team space ", "所有者“的申请，鉴于空间被删除，该申请已失效。": "ownership is invalid due to the deletion of team space.", "你提交的申请已通过, 您已成为空间所有者": "Your application for ownership of this space has been approved. You have the ownership of the space.", "换一换": "Change", "背景说明：": "Background Note：", "存在文件你无权限下载": "No permission to download some files", "存在文件你无权限删除": "No permission to delete some files", "批量操作时，每个文件都需具备下载权限，请逐个检查是否都有下载权限，若无可咨询管理员授权": "You need to have download permission for all selected file, please check one by one. You ask administrators for download permission.", "仅文件所有者/管理员可以删除文件，请逐个检查是否都为文档管理员，若无可咨询空间所有者授予管理员权限，或联系管理员删除": "Only doc's owner or administrator can delete, please check one by one. You can ask space owner for admin permission, or contact administrator to delete", "协作文档/表格/幻灯片/流程图/脑图暂不支持批量下载，请在空间列表更多操作处进行逐个下载": "Docs/sheets/slides/flowchart/mind map don't support batch downloading, please download one by one", "协作文档/表格/幻灯片/流程图/脑图暂不支持批量下载，下载过程会自动过滤": "Docs/sheets/slides/flowchart/mind map  don't support batch downloading, which will be filter when downloading", "已超过最大容量，请在个人空间申请扩容后重试，若无申请入口可通过BPM选择“个人空间扩容”": "Exceed maximum capacity, please apply for personal space expansion and try again. If there is no application portal, you can select 'Personal Space Capacity Expansion' through BPM", "已超过最大容量，请联系空间所有者申请扩容后重试，若空间所有者无申请入口可通过BPM选择“团队空间扩容”": "Exceed maximum capacity, please ask space owner to apply for space expansion and try again.If there is no application portal, you can select 'Team Space Capacity Expansion' through BPM", "文档暂不支持批量下载": "Don't support batch downloading", "存在文档不支持批量下载": "Some docs don't support batch downloading", "成员初始权限仅影响用户首次主动申请加入团队空间时的权限，不能批量修改团队空间内成员权限": "It only affects permissions of users when they firstly join team space, and cannot batch modify the permissions of exist members in team space.", "了解权限与操作对应关系": "Relationship between permissions and operations.", "权限说明1": "Permission description", "如何找管理员？": "How to find the administrator？", "管理员不会授予权限怎么办？": "What if administrator doesn't know how to grant permission?", "暂无权限": "No permission", "如需操作可联系分享者": "You can ask sharer for", "分享者不会授予权限怎么办？": "What if the sharer doesn't know how to grant permission?", "搜索文档标题、正文、知识库名": "Search title of doc/repository, body text", "搜索文档标题、正文1": "Search doc's title and body text", "你没有权限，可咨询管理员授予": "No permission, ask administrator to grant", "搜索文档标题、正文、空间/知识库名": "Search title of doc/space/repository, body text", "搜索流程图": "Search title of flowchart", "搜索思维导图": "Search title of mind map", "暂无权限，可咨询管理员授予编辑权限或自行申请权限": "No permission, ask administrator to grant edit permission or apply for permission on your own", "暂无权限，可咨询管理员授予下载权限或自行申请权限": "No permission, ask administrator to grant download permission or apply for permission on your own", "暂无权限，可咨询管理员授予管理员权限或自行申请权限": "No permission, ask administrator to grant admin permission or apply for permission on your own", "暂无权限，可咨询管理员授予编辑权限": "No permission, ask administrator to grant edit permission", "暂无权限，可咨询管理员授予下载权限": "No permission, ask administrator to grant download permission", "暂无权限，可咨询管理员授予分享权限": "No permission, ask administrator to grant share permission", "你不是文档所在空间的协作成员，不支持移动": "You are not a member of the space where the doc is located, so you have no permission to move.", "你不是文档所在空间的协作成员，不支持复制": "You are not a member of the space where the doc is located, so you have no permission to copy.", "暂无权限，可咨询空间/文档所有者授予文档管理员权限，或联系文档管理员删除": "No permission, ask space owner or doc owner for administrator permission or contact doc administrator to delete.", "如何找空间所有者或文档管理员？": "How to find space owner or doc administrator？", "空间所有者不会授予权限怎么办？": "What if space owner doesn't know how to grant permission?", "你没有权限，可咨询空间所有者授予管理员权限，或联系文件/文件夹管理员删除": "No permission, ask space owner for admin permission or contact file/folder‘s administrator to delete.", "如何找管理员或自行申请权限？": "No permission, ask administrator to grant permission or apply for permission on your own", "你没有权限，可咨询管理员授予${下载}权限或自行申请权限": "No permission, ask administrator to grant ${下载} permission or apply for permission on your own", "你没有权限，可咨询管理员授予${下载}权限": "No permission, ask administrator to grant ${下载} permission", "你没有权限，可咨询管理员授予${管理员}权限或自行申请权限": "No permission, ask administrator to grant ${管理员} permission or apply for permission on your own", "你没有权限，可咨询管理员授予${分享}权限": "No permission, ask administrator to grant ${分享} permission", "你没有权限，可咨询空间/文档所有者授予${文档管理员}权限，或联系文档管理员删除": "No permission, ask space owner or doc owner for administrator permission or contact ${文档管理员} to delete.", "你没有权限，可咨询空间所有者授予${管理员}权限，或联系文件夹管理员删除": "No permission, ask space owner for ${管理员} permission or contact file/folder‘s administrator to delete.", "你没有权限，可咨询管理员授予${编辑}权限或自行申请权限": "No permission, ask administrator to grant ${编辑} permission or apply for permission on your own", "你没有权限，可咨询管理员授予${编辑}权限": "No permission, ask administrator to grant ${编辑} permission", "你没有权限，可咨询分享者": "No permission, ask sharer to ", "你没有权限，可咨询文件所有者": "No permission, ask doc's owner to ", "授予": " grant ", "文件所有者不会授予权限怎么办？": "What if doc's owner doesn't know how to grant permission?", "查看帮助手册": "View help manual", "你没有权限，当前位置需具备": "No permission, Current location Required", "权限，缺少": "Permission, lack of", "权限，可咨询管理员授权": "Permission, the administrator for permission", "下载中，内容如包含敏感信息请及时删除": "Downloading, If it contains sensitive information, please delete promptly", "点击重置，筛选条件变为”不限归属“": "Click 'Reset' , filter condition will change to 'Owned by anyone'", "Cooper推荐使用Chrome V115及以上版本浏览器，兼容性表现优秀。若使用其他浏览器或者较低版本Chrome浏览器，可能存在使用问题，建议切换为Cooper平台推荐的浏览器": "<PERSON> recommends Chrome V115 and above browsers, which have excellent compatibility. If you use other browsers or lower versions of Chrome, functional issues may occur. You'd better switch to recommended browser.", "Edge当前版本过低，使用Cooper平台相关功能时可能存在问题，推荐使用Edge 115及以上版本，兼容性表现优秀": "The current version of Edge is too low, and there may be issues when using Cooper platform related features. It is recommended to use Edge 115 or above, which has excellent compatibility.", "Safari当前版本过低，使用Cooper平台相关功能时可能存在问题，推荐使用Safari 14及以上版本，兼容性表现优秀": "The current version of Safari is too low, and there may be issues when using Cooper platform related features. It is recommended to use Safari 14 or above, which has excellent compatibility.", "Chrome当前版本过低，使用Cooper平台相关功能时可能存在问题，推荐使用Chrome115及以上版本，兼容性表现优秀": "The current version of Chrome is too low, and there may be issues when using Cooper platform related features. It is recommended to use Chrome 115 or above, which has excellent compatibility.", "如何更新浏览器版本或下载最新浏览器": "How to update browser version or download the latest browser?", "如何将Cooper支持的浏览器设置为默认浏览器": "How to set supported browsers as default browsers?", "不再提示1": "No more prompts", "打包成功并开始下载，内容如包含敏感信息请及时删除": "Files are packed and downloading, If it contains sensitive information, please delete promptly", "查看/编辑/上传/下载": "View/Edit/Upload/Download", "查看/上传/下载": "View/Upload/Download", "在新标签页打开": "Open in New Tab", "检查服务可用性": "Check service availability", "Cooper服务检测": "Cooper service detection", "Cooper服务检测中": "Cooper service detection in progress", "Cooper服务检测完成": "Cooper service detection completed", "该检测主要应用于评估能否正常使用Cooper服务，检测结果将助力快速定位Cooper页面访问问题。": "The detection is used to evaluate whether Cooper services can be used normally, which can locate access issues quickly.", "检测结果：": "Detection result: ", "当前网络及服务": "Network and service are ", "正常": "normal", "协作成员": "Collaborator(s)", "我的权限：": "My permission:", "成员权限说明": "Permission", "可查看/评论，不能编辑": "Can read and comment，can't edit", "可查看/评论/编辑，不能分享给他人": "Can read, comment and edit, can't share", "可分享文件夹和本地上传的文件": "Can share folders and files uploaded", "可在文件夹内上传、新建文件": "Can upload and create files", "可查看/下载，不能编辑": "Can read and download, can't edit", "可查看/编辑/评论/下载/分享给他人": "Can read, edit, comment, download and share", "无权限": "Unauthorized", "不可访问": "No permission to access", "可查看，不能编辑": "Can read，can't edit", "可查看/编辑，不能上传/新建/分享给他人": "Can read and edit, can't upload, create and share", "可上传/新建": "Can upload and create files", "可分享上传文件，不能分享协作文档": "Can share folders and files uploaded", "可查看/编辑/上传/下载/分享给他人": "Can read, edit, comment, download and share", "可查看/评论/编辑/下载，管理文档成员": "Can View/Comment/Edit/Download and manage members", "可查看/评论，不能编辑1": "Can View/Comment and can't Edit", "可查看/评论/编辑/下载，不能管理文档成员": "Can View/Comment/Edit/Download and can't manage members", "无法访问文档": "No permisson to access", "了解操作权限": "Learn more about permissions", "搜索添加当前{slot}成员": "Search to add team member", "{slot}成员{memberCount}人": "{slot} Member(s)", "继承{slot}权限": "Public to {slot} members", "团队空间single": "team", "父级文件夹": "parent folder", "不继承{slot}权限": "Private to certain {slot} members", "跟{slot}成员及权限保持一致": "Synchronize {slot} member permissions", "{type}成员及权限独立，不随{slot}变化": "{type}'s members and permissions aren't affected by {slot}", "确认不继承{slot}权限吗？": "Are you sure to set it not affected by {slot}?", "切换为不继承后，仅{type}管理员和'单独授权'的用户可以访问该{type}。": "Once confirmed, only {type} administrators and the members who are authorized separately can access the {type}.", "当前{type}存在分享记录，获得文件夹/文件分享链接的用户将无法访问。": "The {type} and the files in the {type} have been shared. Once confirmed, users who had got sharing links will not be able to access them.", "确认继承{slot}权限吗？": "Are you sure to consistent with {slot} members' permissions?", "{slot}所有者": "{slot} owner", "当前文档单独授权": "Separate authorization", "你不是文档所属空间成员，无法修改权限": "You aren‘t member of space which the doc belongs to，so you cannot modify permissions.", "切换为继承后，{slot}内所有的成员都可以访问{type}": "Once confirmed, all members in {slot} can access the folder.", "你目前正在连接公司VPN，请检查VPN连接情况。若连接正常仍无法访问，建议断开VPN，Cooper支持在公司可信设备下进行访问。": "You are currently connected to VPN, please check connection status. If the connection is normal but you are still unable to access, it is recommended to disconnect VPN. <PERSON> supports visiting by trusted devices in public network.", "当前网络可能存在异常，": "There may be anomalies in the network. ", "请切换至优质网络后重新刷新页面。若切换网络后页面仍无法加载内容，请点击加入": "Please switch to a high-quality network and refresh the page again. If you still can't visit after switching network, please click ", "【Cooper反馈群】": "\"<PERSON> Feedback\" ", "回到上次位置": "Back to last pos", "进行咨询。": "for help.", "重新检测": "Redetect", "开始检测": "Start detection", "停止检测": "Stop detection", "最小化": "Minimize", "编辑前需进行样式转换，将Wiki html页面转为知识库页面，转换后会导致部分样式存在丢失风险（如外部图片、流程图、思维导图等），请确认是否转换？": "Before editing, a style conversion is required to transform the Wiki HTML page into a knowledge base page. This conversion might lead to the risk of losing some styles (such as external images, flowcharts, mind maps, etc.). Please confirm if you want to proceed with the conversion?", "查看wiki格式不兼容知识库的情况": "Check the situation where wiki format is incompatible with the knowledge base.", "样式转换提示": "Style Conversion Prompt", "查看帮助文档": "Help document", "上传压缩包": "Upload Compressed File", "第一步：将wiki空间以html格式导出": "Step One: Export the wiki space in HTML format", "第二步：在此上传wiki导出的html压缩包（.zip格式）": "Step Two: Upload the HTML compressed file (.zip format) exported from the wiki here", "。": ".", "发布至桔子堆": "Publish to iPortal", "将当前内容发布至桔子堆": "Publish current content to iPortal", "将当前发布版本内容发布至桔子堆": "Publish current version to iPortal", "选择作者身份": "What's your identity", "个人的": "Personal", "订阅号": "Subscription Account", "作者为自己，不显示其他协作者": "Publish in personal name", "以订阅号账号发布": "Publish with subscription account name", "选择发布渠道": "Channel", "Way社区": "Way", "交流": "Forum", "资讯": "News", "选择订阅号": "Subscription Accounts", "请选择订阅号": "Please select target subscription account", "选择圈子": "Circle", "请选择圈子": "Please select target Circle", "提交并前往桔子堆发布": "Submit to iPortal", "无担任管理员的订阅号": "No accounts managed by you", "提交中...": "Uploading…", "无桔子堆权限": "No permission to iPortal", "内容不能为空": "The content cannot be empty", "单篇文章字数限制7万": "The word limit for each article is 70,000", "暂未加入任何圈子": "Not join any circle", "提交成功": "Submit successfully", "我已知晓以下注意事项": "I got it", "公网环境暂时不开放此功能": "This function is not available in the public network environment", "系统检测到您当前使用的是非办公网络，": "You are currently using a public network environment, ", "为保障您的文件数据安全，建议连接公司 VPN 后使用": "to ensure data security, please connect to the company's VPN and use", "查看公网环境说明": "View instructions", "下载 VPN 客户端": "Download VPN client", "连接 VPN 后仍然无法打开，请": "After connecting to VPN, it still can't be opened. Please ", "点击此处": "click here", "进行访问": " ", "在公网环境您可以": "Operation in public network environment", "• 在个人空间内上传文件/文件夹、新建文件夹": "• Upload files / folders and create new folders in My Space", "• 查看首页、个人空间、团队空间首页等列表": "• View the home, My Space, Team Space, etc", "• 申请个人空间和团队空间的扩容": "• Apply for expansion of my and team Space", "• 在消息中心中进行快速审批处理": "• Quick processing of approval in message center", "• 搜索文件、文档和文件夹": "• Search for files, documents, and folders", "当前模块{slot}，请参考以下解决方案：": "The current module {slot}. Please refer to the following solution:", "加载失败": "failed to load", "出现问题": "error", "可能是网络抖动，可尝试": "May Be the network jitter, can try ", "如果开启了代理，请关闭代理后重新刷新页面；": "If the network proxy is turned on, close the agent and refresh the page", "浏览器存在缓存，请完全清空浏览器缓存后刷新页面或使用无痕模式访问页面；": "There is a cache in your browser, please refresh the page after completely emptying the browser cache or access the page in traceless mode;", "可能是网络抖动，请点击": "Possible network jitter, please click", "如果以上尝试后仍未解决，请联系": "If this does not resolve after the above attempt, contact", "，我们将为您提供技术支持；": ",We will provide you with technical support;", "你目前正在连接公司VPN，请检查VPN连接情况。若连接正常仍无法访问，建议断开VPN，Cooper支持在公司可信设备下进行访问；": "You are currently connected to the company VPN, please check the VPN connection. If the connection is still unable to access, it is recommended to disconnect VPN, Cooper support in the company under the trusted device access;", "当前网络存在异常，可尝试切换至优质网络后重新刷新页面；": "The current network is abnormal, you can try to switch to high-quality network after re-refresh the page;", "桔子堆长文内容单篇字数上限为7万字，超过字数限制无法对外发布。": "You can only publish within 70,000 words.", "同步至桔子堆的内容以文档最近一次发布版本为准，且不会同步更新。": "What you have published to iPortal will not be affected by changes in Knowledge.", "以下内容同步至桔子堆后，样式可能存在差异，请仔细校验，确认无误后再发布：表格、超链接卡片、任务列表、折叠块、代码块、包含页面。": "Attention to the style before and after publishing：Table, Link, Todo-list, Folding block, Code block, Contains pages.", "以下内容无法直接同步至桔子堆，请自行转为图片格式，以免内容丢失：流程图、思维导图。": "Following format cannot be published and should be convert to image in advance: Flowchart.", "归属于团队空间": "is created in space", "，文档所有者": "，the owner ", "离职时未做所有权交接，现有用户申请该文档访问权限，请评估是否通过该申请": " didn't transfer ownership before resignation. Please evaluate whether the application can be approved", "所在团队空间": " is created in space  ", "为你曾经的下属员工": "which is created by your former subordinate", "创建，该员工离职时未做所有权交接，现有用户申请该文档的访问权限，应信息安全要求，需要你评估是否通过该申请": "the owner didn't transfer ownership before resignation. Please evaluate whether the application can be approved, in accordance with information security requirements.", "已离职，无法审批该文档的访问权限申请，你的下属员工期望获得该文档的访问权限，应信息安全要求，需要你评估是否通过该申请": "has resigned and is unable to process the application. Your subordinate is applying for the access permission In accordance with information security requirements, please evaluate whether the application can be approved", "暂无审批人可审批你提交的【": "No approver is available to process the application for access permission of【", "】文档访问权限申请，可联系相关滴滴员工获取该文档的管理权限后邀请你加入该文档的协作": "】you can contact Didi employees to obtain management permission and cooperate with you in doc.", "邀请您一起协作加密文件": " invite you to collaborate to encrypt files", "不知道密码？": "Don't know the password?", "添加协作者": "Add Collaborators", "搜索当前文档协作者": "Search collaborators", "未开启分享": "Only collaborators visible", "企业员工可访问": "Enterprise employees can access", "仅文档协作者可通过链接访问，非文档协作者打开链接需要申请权限": "Only doc collaborators can access the link, others need to apply for permission.", "获得链接的人都可访问": "Who gets the link within the enterprise can access.", "获得链接和密码的人都可访问": "Who gets the link and password within the enterprise can access.", "通过链接访问的人": "People accessing the link", "将不会成为文档协作者": "won't become collaborator", "将自动成为文档协作者": "will become doc collaborator", "已开启": "Enabled", "查看/编辑": "View/Edit", "查看/下载": "View/Download", "查看/编辑/下载": "View/Edit/Download", "链接分享设置": "Link Sharing", "权限管理1": "Manage member permissions", "链接分享1": "Link Sharing", "联系所有者": "Contact doc owner", "可设置链接分享范围为企业员工可访问，": "Link sharing scope can be set to enterprise employees. ", "为文档协作者设置只读成员（仅查看/评论）、成员（可编辑）角色，": "Set Viewer (view/comment only) or Editor (editable) roles for doc collaborator,", "为文档协作者设置查看/编辑/下载权限，": "Set view/edit/download permissions for doc collaborator. ", "查看详细说明": "Read more detailes", "搜索并添加联系人、DC群、部门": "Search and invite contacts, D-Chat group, department", "“企业员工可访问”的链接设置如下": "Settings of the link", "企业内获得链接的人拥有以下权限": "Who gets the link within the enterprise can access wit permission below", "可查看/评论": "Can View/Comment and can't Edit", "可编辑/下载": "Can View/Comment/Edit/Download", "链接有效期限": "Validity Period", "过期后，重置为“未开启分享”": " expirated, reset to 'Only collaborators visible'", "启用密码": "Require Password", "文档协作者无需密码，企业内其他获得链接的人需密码访问": "Doc collaborators don't require password, while others within the enterprise who obtain links need a password to access.", "外部空间的文档必须设置密码": "Docs in external space must have a password when sharing", "访问者默认成为文档协作者": "Visitors default to becoming doc collaborators", "通过链接快速邀请批量协作者，请开启该设置。如开启，访问者默认成为协作者，初始权限为链接权限": "You can enable the setting to quickly invite bulk collaborators through links. If enabled, visitors becomes collaborators by default, with initial permissions above.", "仅文档管理员可见密码": "Only doc administrator visible", "复制文档链接": "Copy link", "复制文档链接和密码": "Copy doc link and password", "已复制链接和密码": "Link and password Copied", "是否修改设置": "Sure to modify settings?", "链接分享设置已被修改，需确认修改后，以上设置才会生效。": "Link sharing settings have been modified. Once confirmed, settings above will take effect.", "暂不修改": "Cancel", "协作者": "Collaborators", "仅文档管理员可邀请协作者": "Only doc administrators can invite collaborators", "仅文档管理员可修改设置": "Only doc administrators can modify settings", "仅文档管理员可操作": "Only doc administrators can manage collaborators", "发布为司内公开页面": "Publish as internal public page", "发布为司内公开知识库": "Publish as internal public repository", "原“开启分享”功能。轻松将知识库发布成司内公开页面，用于产品帮助中心、更新日志等资料共享，可以将链接嵌入各业务系统。": "The original 'Share' function. Easily publish it as internal public repository for sharing information such as product help center, update logs and other materials, and you can embed links into various business systems.", "示例说明": " Read more example.", "发布生成的链接与文档网址无法互通使用，且该链接内容不支持被公开搜索": "Published link is different from doc URL and doc cannot be publicly searched.", "公司内获得链接的人都可访问（注：该链接与文档网址不一致，且该链接内容不支持被公开搜索）": "Anyone obtains the link within the company can access (Note: the link does not match the doc URL and it cannot be publicly searched)", "仅文档管理员可取消发布": "Only doc administrators can cancel publishing", "仅文档管理员可以发布": "Only doc administrators can publish", "取消发布": "Cancel publishing", "确定取消发布？": "Sure to cancel publishing ?", "一旦取消发布，之前获得分享链接的用户将无法访问。": "Once you cancel publishing, users who previously obtained the sharing link will not be able to access it.", "{shareTypeText}已取消发布": "{shareTypeText} is unpublished", "{shareTypeText}已发布": "{shareTypeText} is published", "复制公开链接": "Copy public link", "已发布": "Published", "若新增子页面，子页面分享状态需手动开启": "If a new subpage is added, the subpage sharing status needs to be manually enabled", "知识库已发布": "Published", "知识库未发布": "Unpublished", "转让文档所有权": "Transfer ownership", "成功转让文档所有者后，你将成为协作成员，继承团队空间的权限，{name} 将成为新的所有者，且拥有所有者全部权限，确认转让?": "After ownership trafer, you will become a collaborative member and inherit the permissions of the team space, and the new owner will have all permissions of the document. Are you sure to transfer?", "成功转让文档所有者后，你将成为受邀成员，有查看、编辑和下载权限，{name} 将成为新的所有者，且拥有所有者全部权限，确认转让?": "After ownership trafer, you will become an invited member with viewing, editing and downloading permissions, and the new owner will have all permissions of the document. Are you sure to transfer?", "转让成功": "Successfully transfer", "· 文档只能转让给团队成员，且有该文档的访问权限。": "· Documents can only be transferred to team members who have permission to access the document.", "· 成功转让文档所有者后，你将成为协作成员，继承团队空间的权限，对方将成为新的所有者，且拥有所有者全部权限。": "· After ownership trafer, you will become a collaborative member and inherit the permissions of the team space, and the new owner will have all permissions of the document.", "· 文档可以转让给任何人": "· Documents can be transferred to anyone", "· 成功转让文档所有者后，你将成为受邀成员，有查看、编辑和下载权限，对方将成为新的所有者，且拥有所有者全部权限。": "· After ownership trafer, you will become an invited member with viewing, editing and downloading permissions, and the new owner will have all permissions of the document.", "转让": "Transfer", "请输入用户或邮箱地址": "Please enter user name or email address", "查看访问记录": "View Access Records", "暂无访问记录": "No access records available", "访问记录": "Access Records", "仅文档协作者可访问，可向文档所有者申请成为文档协作者": "Only doc collaborators can view, you can ask doc owner to be a collaborator", "如何找文档所有者/管理员？": "How to find the doc owner/administrator?", "文档所有者如何邀请你成为协作者？": "How can doc owner invite you to become a collaborator?", "分享给你一个加密文档": " shares with you an encrypted doc", "访问1": "Visit", "迁移Wiki空间": "Import Wiki space", "新建迁移任务": "Start import", "查看迁移进度": "View progress", "反馈咨询": "<PERSON><PERSON><PERSON>", "问卷反馈": "Give feedback", "剩余可关联：{selected}个已选": "Selections left: {selected}", "最多支持关联{count}个会议文档": "You can only associate up to {count} docs", "关联企业内部文档": "Associate docs", "过期后，重置为仅协作者可见": " expirated, reset to Only collaborators visible", "外部文档不能设为所有人访问": "Docs in external space cannot be set Everyone visible", "仅协作者可访问": "Collaborators visible", "非协作者查看链接需申请权限": "Non collaborators need to apply for access permission", "需密码访问": "Password access", "非协作者查看链接需输入密码": "Non collaborators need password to access", "所有人可访问": "Everyone visible", "企业内员工都可通过链接访问": "Who gets the link in the enterprise can access", "链接权限p": "Permission", "链接期限": "Valid period", "密码设置": "Password", "协作设置": "<PERSON><PERSON><PERSON>", "当前文档的链接分享设置为": "The doc's link sharing is set：", "分享者需对分享对象负责，": "You are responsible for people you share,", "请谨慎分享": "please make caution.", "如需获取密码，请": "To obtain the password, please", "查看/评论": "View/Comment", "编辑/下载": "Edit/Download"}