# Cooper

## npm调试

@didi/add-member-search-react为例

1. npm install -g yalc
2. npm代码库执行： npm run build ;  yalc publish
3. 当前代码库执行： yalc add @didi/add-member-search-react@4.***
4. 调试完执行 yalc remove @didi/add-member-search-react@4.***

注意提交代码之前检查package.json中使用的包名


## 更新骨架屏
骨架屏内容放在src/components/SkeletonPage/totalSkeleton.js,第一（html阶段）、二（路由加载阶段）、三阶段（接口加载阶段）共用这份骨架屏代码。

如需更新骨架屏代码，需执行以下命令，重新生成html文件，更新html阶段骨架屏代码
1. 删除index.html中<div id="root-skeleton"></div>里边的内容
2. `npm run skeleton`，为index.html重新生成骨架屏内容
3. 格式化index.html

注：Space/index的骨架屏第三阶段和第一二阶段不一致，代码不共用。


## 本地开发

#### 1. 启动Cooper和知识库两个前端服务

`npm start`
or
`npm run start`
启动test环境

`npm run qa` 启动qa环境

访问[ http://localhost:4001/](http://localhost:4001/)和[http://localhost:4002/knowledge](http://localhost:4002/knowledge)分别访问Cooper前端服务和知识库前端服务

#### 2. 只启动Cooper前端服务

`npm run start:1`

启动完成后，访问 [ http://localhost:4001/](http://localhost:4001/)

#### 3. 只启动知识库前端服务

`npm run start:2`

启动完成后，访问 [http://localhost:4002/knowledge](http://localhost:4002/knowledge)


## 开发规范

1. 一个文件只包含一个 react 组件
2. 组件名称和文件名称保持一致，使用大驼峰的方式命名，如： HeaderExample
3. 组件样式使用 less 编写，文件名称统一命名为 style.module.less
4. 在 js 文件中可以使用 `rfc` 快速生成 react 组件代码片段（需vscode配置ES7+ React/Redux/React-Native snippets插件）
5. 静态变量使用大写
6. 注释另起一行写在代码行的上方，函数注释写在函数上方

> [项目规范] https://cooper.didichuxing.com/docs/document/2199228623121

## 知识库项目介绍

> [项目cooper空间](https://cooper.didichuxing.com/team-file/2199221307986)

## 前端地址

## 好用的工具

1. 获取和存储用户喜好的json  useView.js 注：新加变量需要先在常量中定义再使用

## 开发注意事项

- 配置登录信息，在 src 下新建配置 cookie.js，内容如下：
```js
// cookie取测试环境，使用线上cookie无效
// 此处填入你在 https://cooper-test.didichuxing.com 中 cookie 的 _cooper_username 和 _user_token

export const _username = 'elenachenchen';
export const _usertoken = 'eyJhbGciOiJSUzI1NiJ9.xxx';
export const _usertokenQa = 'eyJhbGciOiJSUzI.XX';
```


## Git提交行数限制

本项目配置了Git pre-commit钩子，用于限制每次提交的代码变更不超过1000行。这有助于保持提交简洁、聚焦，并使代码审查更加高效。

### 自动安装

当你运行`npm install`或`yarn`时，钩子会自动安装。

### 手动安装

如果自动安装未生效，你可以手动安装：

```bash
# 给安装脚本添加执行权限
chmod +x install-hooks.sh

# 运行安装脚本
./install-hooks.sh
```

### 工作原理

这个钩子会在每次提交前检查暂存区中的代码变更行数。如果变更超过1000行，提交将被阻止，并显示错误信息。

如需临时绕过此限制进行大型提交，可使用以下命令：

```bash
git commit --no-verify -m "你的提交信息"
```

不过，建议尽量将大型变更拆分为多个小型提交。
