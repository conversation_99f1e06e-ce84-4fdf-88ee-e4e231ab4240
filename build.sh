#!/bin/sh
export NODE_OPTIONS=--max-old-space-size=4096

source ~/.nvm/nvm.sh
nvm install 12.20.0
nvm use v12.20.0

#Exit the script if an error happens
set -e
#执行指令后,会显示该指令及参数,可加可不加该行
set -x


echo '配置需编译依赖'

# cp ~/.npmrc /home/<USER>/.
chown xiaoju:xiaoju /home/<USER>/.npmrc
chmod 777 /home/<USER>/.npmrc


echo 'npm version:'
npm -v
echo 'node version:'
node -v
echo '安装依赖'
npm install --registry http://npm.intra.xiaojukeji.com

# env=$1
# 创建目录用于存放源码
mkdir -p output/{source,target}

rsync  -av --exclude=node_modules --exclude=dist --exclude=output --exclude=.git . ./output/source/.

echo ""=======================打包$env环境==========================""
npm run build:$env
mv dist output/target/$env

# Dockerfile 需要放在output跟目录
cp output/source/deploy/Dockerfile output/

tar -zcvf web-ui.tar.gz output/*
