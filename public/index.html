<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="google" content="notranslate" />
    <meta property="og:title" content="知识管理平台" />
    <meta property="og:description" content="高效便捷的知识管理平台" />
    <link rel="stylesheet" href="/iconfont.css" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
    <script>
        "https:" === location.protocol && document.write('<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">')
    </script>
    <meta name="theme-color" content="#000" />
    <meta name="description" content="集云盘存储、在线协作、知识结构化沉淀为一体的知识协作平台" />
    <link rel="dns-prefetch" href="https://cooper.didichuxing.com/m/didoceditor/" />
    <link rel="dns-prefetch" href="https://s3-conveyor.didiglobal.com" />
    <link rel="dns-prefetch" href="https://as.xiaojukeji.com" />
    <link rel="dns-prefetch" href="https://sec-aegisfe.didistatic.com" />
    <link rel="dns-prefetch" href="https://img-hxy021.didistatic.com" />
    <link rel="dns-prefetch" href="https://img-ys011.didistatic.com" />
    <link rel="dns-prefetch" href="https://omgup2.xiaojukeji.com" />
    <link rel="dns-prefetch" href="https://water-mark.xiaojukeji.com" />
    <title>Cooper</title>
    <style>
        .loading-wrap {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: center
        }

        .my-spin {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            color: rgba(0, 0, 0, .85);
            font-size: 12px;
            font-variant: tabular-nums;
            line-height: 1.66667;
            list-style: none;
            font-feature-settings: tnum;
            position: absolute;
            display: none;
            color: #047ffe;
            text-align: center;
            vertical-align: middle;
            opacity: 0;
            transition: transform .3s cubic-bezier(.78, .14, .15, .86);
            position: static;
            display: inline-block;
            opacity: 1
        }

        .my-spin-dot-span {
            position: relative;
            display: inline-block;
            font-size: 20px;
            width: 1em;
            height: 1em;
            transform: rotate(0);
            animation: antRotate 1.2s infinite linear
        }

        .my-spin-dot-span>.my-spin-dot-item {
            position: absolute;
            display: block;
            width: 9px;
            height: 9px;
            background-color: #047ffe;
            border-radius: 100%;
            transform: scale(.75);
            transform-origin: 50% 50%;
            opacity: .3;
            animation: antSpinMove 1s infinite linear alternate
        }

        .my-spin-dot-span:nth-child(1) {
            top: 0;
            left: 0
        }

        .my-spin-dot-span :nth-child(2) {
            top: 0;
            right: 0;
            animation-delay: .4s
        }

        .my-spin-dot-span :nth-child(3) {
            bottom: 0;
            right: 0;
            animation-delay: .8s
        }

        .my-spin-dot-span :nth-child(4) {
            left: 0;
            bottom: 0;
            animation-delay: 1.2s
        }

        .my-spin>.my-text-container {
            padding-top: 5px;
            text-shadow: 0 1px 2px #fff;
            color: #222a35;
            font-variant: tabular-nums;
            font-feature-settings: "tnum";
            font-size: 14px
        }

        @keyframes antSpinMove {
            to {
                opacity: 1
            }
        }

        @keyframes antRotate {
            to {
                transform: rotate(360deg)
            }
        }

        html body #root {
            display: none
        }
    </style>
</head>

<body class="didoc2-editor-app__wrapper">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root-skeleton">
        <style id="index-skeleton-style">
            body {
                height: 100%;
                margin: 0;
                font-size: 14px;
                line-height: 22px;
                overflow: auto
            }

            .home-wrap {
                position: relative;
                width: 100%;
                height: 100%;
                padding-bottom: 40px
            }

            .home-wrap .home-main {
                min-width: 667px
            }

            .generic .home-wrap-os-flag,
            .linux .home-wrap-os-flag,
            .mac .home-wrap-os-flag {
                overflow-y: auto
            }

            .right {
                width: 100%;
                min-width: 850px
            }

            .tab-top-title {
                flex: 1
            }

            .tab-top-title .ant-tabs-content-top {
                height: 100%
            }

            .tab-top-title .ant-tabs-nav {
                margin-bottom: 15px
            }

            .tab-top-title .ant-tabs-ink-bar {
                display: none !important
            }

            .tab-top-title .ant-tabs-tab-active {
                position: relative
            }

            .tab-top-title .ant-tabs-tab-active:before {
                content: "";
                width: 24px;
                height: 2px;
                background-color: #047ffe;
                left: calc(50% - 12px);
                bottom: 0;
                z-index: 100;
                position: absolute
            }

            .tab-top-large .ant-tabs-tab {
                font-size: 16px !important;
                line-height: 24px !important;
                padding-top: 4px !important;
                padding-bottom: 6px !important
            }

            .icon-gengduo1 {
                color: rgba(34, 42, 53, .5)
            }

            .v3-operate-menu {
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                width: 24px;
                height: 24px
            }

            .v3-operate-menu:hover {
                background: rgba(0, 0, 0, .05);
                border-radius: 4px
            }

            .v3-quick-access {
                min-width: 667px;
                margin: 0 auto 12px;
                padding: 0 32px
            }

            .v3-quick-access-title {
                font-family: PingFangSC-Medium;
                font-size: 16px;
                font-weight: 400;
                color: #222a35;
                top: 0;
                position: -webkit-sticky;
                position: sticky;
                z-index: 1;
                height: 24px;
                background-color: #fff;
                display: block;
                margin-bottom: 0;
                padding: 0 0 12px;
                box-sizing: content-box
            }

            .container {
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-start
            }

            .recent-activity {
                margin: 0 auto;
                min-width: 667px
            }

            .recent-activity .ant-tabs-nav {
                padding: 0 32px 2px;
                position: -webkit-sticky !important;
                position: sticky !important;
                top: 0;
                z-index: 2
            }

            .file-ellipsis {
                padding-right: 4px;
                display: flex;
                color: #2f343c;
                align-items: center;
                overflow: hidden;
                height: 22px;
                padding-left: 4px;
                width: 100%;
                cursor: pointer
            }

            .file-ellipsis .file-ellipsis-inner-span {
                white-space: nowrap;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                display: inline-block;
                padding: 0 2px;
                height: 22px;
                line-height: 22px;
                color: #222a35
            }

            .file-ellipsis .link-tag {
                font-family: PingFangSC-Medium;
                display: inline-block;
                background: rgba(4, 127, 254, .1);
                border-radius: 4px;
                text-align: center;
                padding: 0 8px;
                color: #047ffe;
                font-size: 22px;
                -webkit-transform: scale(.5);
                transform: scale(.5);
                box-sizing: content-box;
                height: 36px;
                line-height: 36px;
                margin-left: -22px;
                margin-right: -26px
            }

            .file-name-list {
                width: 24px;
                height: 24px;
                vertical-align: middle;
                margin-right: 4px;
                -o-object-fit: cover;
                object-fit: cover;
                -o-object-position: left center;
                object-position: left center;
                overflow: hidden;
                display: inline-block;
                line-height: 1
            }

            .file-name-list svg {
                max-width: 100%;
                max-height: 100%
            }

            .cooper-list-content {
                min-width: 667px;
                margin: -8px auto 0
            }

            .cooper-list-content .folder-tree {
                height: 100%;
                width: 100%;
                position: relative
            }

            .cooper-list-content .tb-header {
                font-size: 14px;
                font-weight: 500;
                color: #656a72;
                position: -webkit-sticky;
                position: sticky;
                top: 36px;
                background-color: #fff;
                padding-left: 29px;
                padding-right: 32px;
                z-index: 4
            }

            .cooper-list-content .tb-header .tb-header-div {
                height: 36px !important;
                line-height: 36px !important;
                border-bottom: 1px solid rgba(34, 42, 53, .08);
                position: relative
            }

            .cooper-list-content .tb-header .tb-header-div .file-name {
                position: relative;
                overflow: visible;
                display: flex;
                align-items: center
            }

            .cooper-list-content .tb-header .tb-header-div .file-handle {
                cursor: pointer;
                height: 26px;
                padding-left: 4px;
                line-height: 24px;
                background-color: #fff;
                border-radius: 4px;
                display: flex;
                align-items: center
            }

            .cooper-list-content .tb-header .tb-header-div .file-checked {
                display: flex;
                align-items: center;
                background-color: rgba(4, 127, 254, .1);
                color: #047ffe
            }

            .cooper-list-content .tb-header .tb-header-div .file-checked .icon-shaixuan2 {
                padding: 2px 2px 0;
                font-size: 14px
            }

            .cooper-list-content .tb-body>li,
            .cooper-list-content .tb-header>.tb-header-div {
                height: 48px;
                line-height: 48px;
                width: 100%;
                white-space: nowrap;
                display: flex;
                align-items: center;
                color: rgba(34, 42, 53, .7)
            }

            .cooper-list-content .tb-body>li>span,
            .cooper-list-content .tb-header>.tb-header-div>span {
                display: inline-block
            }

            .cooper-list-content .tb-body>li>.file-name,
            .cooper-list-content .tb-header>.tb-header-div>.file-name {
                max-width: none;
                min-width: 250px;
                height: 100%;
                display: flex;
                align-items: center;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                flex: 3;
                cursor: pointer;
                padding-right: 12px
            }

            .cooper-list-content .tb-body>li .file-content-box,
            .cooper-list-content .tb-header>.tb-header-div .file-content-box {
                margin-right: 12px;
                width: 100%;
                height: 100%;
                cursor: pointer;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis
            }

            .cooper-list-content .tb-body>li .file-owner-content-box,
            .cooper-list-content .tb-header>.tb-header-div .file-owner-content-box {
                padding-left: 4px
            }

            .cooper-list-content .tb-body>li>.file-address-,
            .cooper-list-content .tb-header>.tb-header-div>.file-address- {
                height: 100%;
                width: 300px;
                min-width: 200px;
                display: flex;
                align-items: center;
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis
            }

            .cooper-list-content .tb-body>li>.file-address- .file-content-box,
            .cooper-list-content .tb-header>.tb-header-div>.file-address- .file-content-box {
                padding: 0 2px;
                height: 22px;
                border-radius: 4px;
                line-height: 22px;
                cursor: pointer;
                width: auto
            }

            .cooper-list-content .tb-body>li>.file-owner,
            .cooper-list-content .tb-body>li>.file-time,
            .cooper-list-content .tb-header>.tb-header-div>.file-owner,
            .cooper-list-content .tb-header>.tb-header-div>.file-time {
                height: 100%;
                width: 130px;
                min-width: 130px;
                display: flex;
                align-items: center;
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                padding-right: 12px
            }

            .cooper-list-content .tb-body>li>.file-owner .file-content-box,
            .cooper-list-content .tb-body>li>.file-time .file-content-box,
            .cooper-list-content .tb-header>.tb-header-div>.file-owner .file-content-box,
            .cooper-list-content .tb-header>.tb-header-div>.file-time .file-content-box {
                cursor: default
            }

            .cooper-list-content .tb-body>li .file-owner,
            .cooper-list-content .tb-header>.tb-header-div .file-owner {
                width: 150px;
                min-width: 150px
            }

            .cooper-list-content .tb-body>li>.file-operate,
            .cooper-list-content .tb-header>.tb-header-div>.file-operate {
                width: 80px;
                height: 100%;
                display: flex;
                justify-content: right;
                align-items: center
            }

            .cooper-list-content .tb-body>li>.file-owner,
            .cooper-list-content .tb-header>.tb-header-div>.file-owner {
                min-width: 150px;
                overflow: hidden
            }

            .cooper-list-content .tb-body>li>.file-address-,
            .cooper-list-content .tb-header>.tb-header-div>.file-address- {
                min-width: 130px
            }

            .cooper-list-content .tb-body {
                padding-left: 27px;
                padding-right: 28px;
                padding-top: 8px
            }

            .cooper-list-content .tb-body>li {
                padding-left: 4px;
                padding-right: 4px
            }

            .cooper-list-content .tb-body>li .file-name {
                color: #222a35
            }

            .cooper-recent-table-loading {
                position: relative;
                margin-top: 16px;
                width: 100%;
                text-align: center;
                font-size: 14px;
                color: rgba(34, 42, 53, .7)
            }

            .v3-card {
                flex: 1;
                height: 46px;
                border-radius: 4px;
                margin: 0 12px 10px 0;
                border: 1px solid #f2f3f3;
                background-color: #fff;
                padding: 12px;
                box-sizing: border-box;
                cursor: pointer;
                display: flex;
                align-items: center;
                position: relative;
                width: calc(25% - 9px);
                min-width: calc(25% - 9px);
                max-width: calc(25% - 9px)
            }

            .v3-card .v3-card-img {
                width: 24px !important;
                height: 24px !important;
                overflow: hidden;
                display: inline-block;
                margin: 0 8px 0 4px
            }

            .v3-card .v3-card-img svg {
                max-width: 100%;
                max-height: 100%
            }

            .v3-card>img {
                width: 24px;
                height: 24px;
                -o-object-fit: cover;
                object-fit: cover;
                margin: 0 8px 0 4px
            }

            .v3-card .v3-card-text- {
                display: inline-block;
                vertical-align: middle;
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                color: #222a35
            }

            .v3-card .operate-tag {
                width: 18px;
                text-align: center;
                display: none
            }

            .v3-card:before {
                display: none;
                font-family: dk-iconfont !important;
                content: "e6af";
                color: #999;
                position: absolute;
                left: 3px;
                top: center;
                font-size: 12px
            }

            .v3-card:hover {
                border: 1px solid #047ffe
            }

            .v3-card:hover .operate-tag {
                display: inline-block
            }

            .v3-card:hover:before {
                display: block
            }

            .global-search-wrap {
                height: 32px;
                line-height: 32px;
                width: 100%;
                position: relative
            }

            .global-search-wrap .search-before {
                display: flex;
                align-items: center;
                background: 0 0;
                height: 24px;
                line-height: 24px;
                color: rgba(34, 42, 53, .3);
                border: none;
                padding-left: 3px;
                font-size: 14px
            }

            .global-search-wrap .search-tag {
                margin-left: 8px;
                padding: 0 4px 0 8px;
                border-radius: 3px;
                background: #ebeef1;
                display: flex;
                align-items: center;
                color: rgba(0, 0, 0, .7)
            }

            .global-search-wrap .search-icon-btn {
                display: block;
                align-items: center;
                line-height: 14px;
                font-size: 12px;
                border: 1px solid rgba(34, 42, 53, .2);
                padding: 2px 6px;
                border-radius: 4px;
                margin-right: 4px;
                color: rgba(0, 0, 0, .5);
                cursor: pointer
            }

            .global-search-wrap .search-icon-btn .search-text {
                margin-right: 5px
            }

            .global-search-wrap .ant-input-affix-wrapper {
                height: 32px
            }

            .global-search-wrap .ant-input-group-addon {
                border-radius: 4px
            }

            .cooper-notify {
                display: inline-block;
                position: relative;
                cursor: pointer;
                text-align: center;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 18px
            }

            .cooper-notify .dk-icon-zhanneixin {
                font-size: 16px;
                color: #6a707c;
                width: 24px;
                height: 24px;
                line-height: 24px
            }

            .cooper-notify-count {
                position: absolute;
                top: -6px;
                left: 10px;
                background-color: #ff563a;
                border: 1px solid #fff;
                color: #fff;
                display: inline-block;
                min-width: 16px;
                height: 16px;
                font-size: 12px;
                line-height: 14px;
                border-radius: 8px;
                text-align: center;
                cursor: pointer;
                padding: 0 4px;
                z-index: 100
            }

            .notify-list-small {
                line-height: 40px;
                text-align: center;
                margin-right: 0;
                margin-top: 5px
            }

            .notify-list-small .dk-icon-zhanneixin {
                font-size: 18px;
                width: 40px;
                height: 40px;
                line-height: 40px
            }

            .icon-user {
                cursor: pointer;
                height: 24px;
                line-height: 24px;
                text-align: center;
                width: 24px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: #6a707c;
                margin-right: 18px;
                background-repeat: no-repeat;
                background-size: 100%;
                background-position: 50%;
                border-radius: 50%;
                background-image: url(https://img-hxy021.didistatic.com/static/iportal/upload/d0aba0cdadd283b84859bb51bd5995d3.jpg)
            }

            .icon-user-small {
                background-image: url(https://img-hxy021.didistatic.com/static/iportal/upload/d0aba0cdadd283b84859bb51bd5995d3.jpg);
                cursor: pointer;
                height: 28px;
                line-height: 40px;
                text-align: center;
                width: 22px;
                border-radius: 4px;
                justify-content: center;
                font-size: 18px;
                color: #6a707c;
                margin-right: 0;
                margin-top: 5px;
                border-radius: 50%;
                background-size: contain
            }

            .icon-shiyanshi-small {
                cursor: pointer;
                height: 40px;
                line-height: 40px;
                text-align: center;
                width: 40px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: #6a707c;
                margin-right: 0;
                font-size: 18px
            }

            .icon-shiyanshi {
                cursor: pointer;
                height: 24px;
                line-height: 24px;
                text-align: center;
                width: 24px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: #6a707c
            }

            .tip-btn {
                position: relative;
                background: rgba(255, 165, 12, .1);
                border-radius: 4px;
                border: .5px solid rgba(255, 165, 12, .18);
                font-family: PingFangSC-Medium;
                font-size: 11px;
                font-weight: 400;
                color: #faa006;
                display: flex;
                align-items: center;
                height: 24px;
                line-height: 24px;
                width: 70px;
                text-align: center;
                cursor: auto;
                overflow: hidden
            }

            .tip-btn-long {
                margin-left: 0
            }

            .tip-btn .cooper-tip-name {
                font-size: 22px;
                -webkit-transform: scale(.5);
                transform: scale(.5);
                width: 100%;
                white-space: nowrap;
                -webkit-transform-origin: left;
                transform-origin: left;
                margin-left: 4px;
                overflow: hidden;
                text-overflow: ellipsis
            }

            .tip-btn>span {
                height: 24px;
                line-height: 24px;
                display: flex;
                align-items: center;
                justify-content: center
            }

            .tip-btn>span>img {
                width: 12px;
                height: 12px;
                margin-left: 2px
            }

            .logo .logo-large {
                width: 158px;
                margin: 10px 16px 0;
                padding: 0 12px
            }

            .logo .logo-small {
                width: 36px;
                margin: 10px 8px;
                position: relative;
                left: 4px
            }

            .aside-large-wrap {
                width: 240px;
                opacity: 1
            }

            .aside-small-wrap {
                width: 64px;
                opacity: 1
            }

            .aside {
                display: flex;
                flex-direction: column;
                padding-top: 10px
            }

            .aside .aside-content {
                position: relative;
                margin-top: 20px;
                flex: 1
            }

            .aside .item-name {
                width: 100%;
                color: #222a35;
                margin-bottom: 2px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding: 0 8px
            }

            .aside-operate .item-name-normal {
                padding: 12px 8px 12px 12px;
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                border-radius: 4px;
                cursor: pointer
            }

            .aside .item-container {
                display: flex;
                align-items: center;
                width: auto
            }

            .aside .item-small-container {
                display: flex;
                align-items: center;
                width: 100%;
                justify-content: space-around;
                flex-direction: column
            }

            .aside .item-small-container>i {
                padding-top: 4px
            }

            .aside .item-small-container>span {
                width: 200%;
                overflow: hidden;
                text-align: center;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding: 0 4px
            }

            .aside .aside-icon {
                color: #6a707c
            }

            .aside .item-text {
                padding-left: 12px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis
            }

            .aside .item-name-active {
                color: #047ffe;
                background-color: #e5f2fe;
                font-weight: 500
            }

            .aside .item-name-active .aside-icon {
                color: #047ffe
            }

            .left-action {
                width: 240px;
                height: 56px;
                align-items: center;
                z-index: 2;
                margin-left: 22px
            }

            .left-action,
            .left-action-small {
                background-color: #f7f9fa;
                display: flex;
                justify-content: flex-start
            }

            .left-action-small {
                width: 64px;
                height: 145px;
                flex-direction: column;
                padding: 19px 0;
                margin-left: 0
            }

            .icon-aside-large {
                font-size: 18px;
                line-height: 20px
            }

            .space-content-wrap {
                width: 100%;
                font-size: 13px;
                line-height: 21px
            }

            .space-content-wrap .space-item {
                width: 100%;
                color: #222a35;
                margin-bottom: 2px
            }

            .space-content-wrap .space-item .wrap {
                width: 100%
            }

            .space-content-wrap .space-item .space-item-normal {
                padding: 7px 12px;
                width: 100%;
                display: inline-block;
                border-radius: 4px
            }

            .space-content-wrap .space-item .space-item-normal .content-wrap {
                display: flex;
                align-items: center;
                justify-content: space-between;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis
            }

            .space-content-wrap .space-item .space-item-normal .content-wrap .left-wrap-Fig {
                display: flex;
                align-items: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis
            }

            .space-content-wrap .space-item .space-item-normal .content-wrap .left-wrap-Fig .icon-dian {
                font-size: 20px;
                line-height: 14px;
                color: #d8dee3
            }

            .space-content-wrap .space-item .space-item-normal .content-wrap .left-wrap-Fig .item-text {
                padding-left: 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis
            }

            .space-content-wrap .space-item .space-item-normal .content-wrap .icon-pin {
                display: none;
                font-size: 16px;
                line-height: 16px;
                padding: 2px;
                color: #6a707c;
                border-radius: 4px
            }

            .large-wrap .aside-large {
                width: 240px
            }

            .large-wrap .aside-large .item-name-normal {
                padding: 7px 24px
            }

            .large-wrap .aside-large .item-name {
                height: 34px
            }

            .large-wrap .aside-large .split-line {
                width: 100%;
                height: 1px;
                background-color: #ebeef1;
                margin-top: 4px;
                margin-bottom: 6px
            }

            .large-wrap .pine-space {
                width: 100%;
                padding: 0 8px
            }

            .large-wrap .pine-space .space-item-nav {
                padding-left: 46px !important
            }

            .aside-small-box {
                width: 64px;
                flex: 1
            }

            .aside-small {
                height: 100%;
                padding: 10px 2px 0 !important
            }

            .item-name {
                margin-bottom: 2px
            }

            .item-name-6 {
                margin-bottom: 6px !important
            }

            .aside-small .item-name-normal {
                height: auto !important;
                padding: 4px 0 !important;
                width: 100%;
                display: flex;
                align-items: center;
                border-radius: 4px;
                cursor: pointer
            }

            .small-name {
                font-size: 22px !important;
                -webkit-transform: scale(.5);
                transform: scale(.5)
            }

            .header-top-left {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-basis: 540px;
                flex-grow: 0;
                flex-shrink: 0
            }

            @media screen and (max-width:1200px) {
                .header-top-left {
                    flex-basis: 445px
                }
            }

            @media screen and (max-width:1020px) {
                .header-top-left {
                    flex-basis: 360px
                }
            }

            .header-warp {
                padding: 0 32px;
                margin: 0 auto;
                min-width: 667px
            }

            .header-warp .header-top {
                margin: 0 auto;
                min-width: 667px;
                height: 72px;
                display: flex;
                align-items: center;
                justify-content: space-between
            }

            .header-warp .header-top .title-opt {
                min-width: 192px;
                text-align: right;
                flex: 1
            }

            .header-warp .header-top .title-opt>button {
                margin-left: 12px
            }

            .header-warp .header-top .title-opt span {
                font-size: 14px
            }

            .header-warp .cooper-customer-service {
                display: flex;
                justify-content: right;
                align-items: center;
                margin-left: 20px
            }

            .header-warp .customer-service-bar {
                line-height: 18px;
                height: 18px;
                margin-right: 10px;
                border-left: 1px solid #e8e9ea
            }

            .header-warp .cooper-helper {
                width: 26px;
                height: 26px;
                line-height: 26px;
                text-align: center;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center
            }

            .header-warp .feedback-with-popup {
                margin-left: 10px
            }

            .header-warp .feedback .content {
                width: 26px;
                height: 26px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                text-align: center
            }

            .header-warp .dk-icon-gongnengshangxin {
                font-size: 18px !important
            }

            .header-warp .dk-icon-lianxikefu {
                font-size: 18px !important
            }

            .space-skeleton-header-search .cooper-customer-service {
                display: flex;
                justify-content: right;
                align-items: center;
                margin-left: 20px
            }

            .space-skeleton-header-search .customer-service-bar {
                line-height: 18px;
                height: 18px;
                margin-right: 10px;
                border-left: 1px solid #e8e9ea
            }

            .space-skeleton-header-search .cooper-helper {
                width: 26px;
                height: 26px;
                line-height: 26px;
                text-align: center;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center
            }

            .space-skeleton-header-search .feedback-with-popup {
                margin-left: 10px
            }

            .space-skeleton-header-search .feedback .content {
                width: 26px;
                height: 26px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                text-align: center
            }

            .space-skeleton-header-search .dk-icon-gongnengshangxin {
                font-size: 18px !important
            }

            .space-skeleton-header-search .dk-icon-lianxikefu {
                font-size: 18px !important
            }

            .layout,
            .layout .left-wrap {
                display: flex;
                height: 100%;
                position: relative
            }

            .layout .left-wrap {
                z-index: 106
            }

            .layout .left-wrap .left {
                height: 100%;
                background-color: #f7f9fa;
                position: relative;
                display: flex;
                transition: all 2s ease;
                overflow-x: hidden;
                overflow-y: overlay;
                overflow-y: auto
            }

            .layout .left-wrap .aside-btn-handle {
                width: 20px;
                height: 100%;
                cursor: pointer;
                position: absolute;
                right: -20px;
                top: 0;
                background-color: transparent
            }

            .layout .right {
                flex: 1;
                overflow: auto;
                display: flex;
                flex-direction: column
            }

            .layout .right .body-wrap {
                flex: 1;
                overflow-y: hidden;
                min-width: 737px
            }

            .aside-operate {
                width: 14px;
                height: 56px;
                border-radius: 64px;
                display: flex;
                border: 1px solid #ebeef1;
                visibility: hidden;
                align-items: center;
                justify-content: center;
                position: relative;
                top: 69px;
                right: 8px;
                background-color: #fff;
                z-index: 999;
                cursor: pointer
            }

            .aside-operate .operate-icon {
                color: rgba(34, 42, 53, .3);
                font-size: 14px
            }

            .v3-cooper-helper {
                cursor: pointer;
                height: 42px;
                line-height: 42px;
                text-align: center;
                width: 42px;
                border-radius: 100%;
                background-color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 1px solid #ebeef1;
                box-shadow: 0 6px 16px 0 rgba(0, 0, 0, .1)
            }

            .v3-cooper-helper .dk-icon-a-bangzhuzhongxin4px {
                font-size: 18px;
                color: #6a707c
            }

            .global-utils-warp {
                position: fixed;
                right: 24px;
                bottom: 40px;
                z-index: 1000
            }

            .cooper-upload-list-up .cooper-upload-list-close {
                display: none
            }

            .cooper-upload-list-up .drag-modal {
                position: fixed;
                top: 0;
                right: 0;
                z-index: 99;
                bottom: 0;
                left: 0;
                background-color: rgba(0, 0, 0, .5);
                font-size: 36px;
                display: none;
                justify-content: center;
                align-items: center;
                color: #fff;
                border: 4px dashed #fff
            }

            .cooper-upload-list-up .cooper-upload-list {
                position: fixed;
                bottom: 12px;
                right: 52px;
                background-color: #fff;
                width: 480px;
                border: 1px solid rgba(148, 160, 176, .14);
                box-sizing: border-box;
                box-shadow: 0 4px 16px 0 rgba(47, 52, 60, .2);
                border-radius: 4px;
                z-index: 20
            }

            .tip-btn-small {
                cursor: auto;
                width: 28px;
                height: 28px;
                background: rgba(255, 165, 12, .1);
                border: .7px solid rgba(255, 165, 12, .18);
                border-radius: 5.6px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 12px
            }

            .tip-btn-small .cooper-tip-name {
                display: none
            }

            .tip-btn-small>span img {
                width: 17px;
                height: 17px;
                margin: 0
            }

            button,
            input,
            optgroup,
            select,
            textarea {
                font-size: 100%;
                line-height: 1.15
            }

            [type=button],
            [type=reset],
            [type=submit],
            button {
                -webkit-appearance: button
            }

            body,
            html {
                width: 100%;
                height: 100%
            }

            *,
            :after,
            :before {
                box-sizing: border-box
            }

            html {
                font-family: sans-serif;
                line-height: 1.15;
                -webkit-text-size-adjust: 100%;
                -ms-text-size-adjust: 100%;
                -ms-overflow-style: scrollbar;
                -webkit-tap-highlight-color: transparent
            }

            body {
                margin: 0;
                color: #222a35;
                font-size: 14px;
                font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Tahoma, Arial, "PingFang SC", "Microsoft YaHei", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                font-variant: tabular-nums;
                line-height: 1.5715;
                background-color: #fff;
                -webkit-font-feature-settings: "tnum";
                font-feature-settings: "tnum"
            }

            input[type=number],
            input[type=password],
            input[type=text],
            textarea {
                -webkit-appearance: none
            }

            dl,
            ol,
            ul {
                margin-top: 0;
                margin-bottom: 1em
            }

            ol ol,
            ol ul,
            ul ol,
            ul ul {
                margin-bottom: 0
            }

            a {
                color: #047ffe;
                text-decoration: none;
                background-color: transparent;
                outline: 0;
                cursor: pointer;
                transition: color .3s;
                -webkit-text-decoration-skip: objects
            }

            img {
                vertical-align: middle;
                border-style: none
            }

            [role=button],
            a,
            area,
            button,
            input:not([type=range]),
            label,
            select,
            summary,
            textarea {
                touch-action: manipulation
            }

            button,
            input,
            optgroup,
            select,
            textarea {
                margin: 0;
                color: inherit;
                font-size: inherit;
                font-family: inherit;
                line-height: inherit
            }

            button,
            input {
                overflow: visible
            }

            button,
            select {
                text-transform: none
            }

            [type=reset],
            [type=submit],
            button,
            html [type=button] {
                -webkit-appearance: button
            }

            .anticon {
                display: inline-block;
                color: inherit;
                font-style: normal;
                line-height: 0;
                text-align: center;
                text-transform: none;
                vertical-align: -.125em;
                text-rendering: optimizelegibility;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale
            }

            .anticon>* {
                line-height: 1
            }

            .anticon svg {
                display: inline-block
            }

            .anticon-spin,
            .anticon-spin:before {
                display: inline-block;
                -webkit-animation: loadingCircle 1s linear infinite;
                animation: loadingCircle 1s linear infinite
            }

            html {
                -wave-shadow-color: #047ffe;
                -bar: 0
            }

            .ant-btn {
                line-height: 1.5715;
                position: relative;
                display: inline-block;
                font-weight: 400;
                white-space: nowrap;
                text-align: center;
                background-image: none;
                box-shadow: 0 2px 0 rgba(0, 0, 0, .015);
                cursor: pointer;
                transition: all .3s cubic-bezier(.645, .045, .355, 1);
                -webkit-user-select: none;
                -ms-user-select: none;
                user-select: none;
                touch-action: manipulation;
                height: 32px;
                padding: 4px 15px;
                font-size: 14px;
                border-radius: 2px;
                color: #222a35;
                border: 1px solid rgba(34, 42, 53, .08);
                background: #fff
            }

            .ant-btn,
            .ant-btn:active,
            .ant-btn:focus {
                outline: 0
            }

            .ant-btn>span {
                display: inline-block
            }

            .ant-btn-primary {
                color: #fff;
                border-color: #047ffe;
                background: #047ffe;
                text-shadow: 0 -1px 0 rgba(0, 0, 0, .12);
                box-shadow: 0 2px 0 rgba(0, 0, 0, .045)
            }

            .ant-btn:before {
                position: absolute;
                top: -1px;
                right: -1px;
                bottom: -1px;
                left: -1px;
                z-index: 1;
                display: none;
                background: #fff;
                border-radius: inherit;
                opacity: .35;
                transition: opacity .2s;
                content: "";
                pointer-events: none
            }

            .ant-input-affix-wrapper {
                position: relative;
                display: inline-block;
                width: 100%;
                min-width: 0;
                padding: 4px 11px;
                color: #222a35;
                font-size: 14px;
                line-height: 1.5715;
                background-color: #fff;
                background-image: none;
                border: 1px solid rgba(34, 42, 53, .08);
                border-radius: 2px;
                transition: all .3s;
                display: inline-flex
            }

            .ant-input-affix-wrapper>input.ant-input {
                padding: 0;
                border: none;
                outline: 0
            }

            .ant-input-affix-wrapper:before {
                width: 0;
                visibility: hidden;
                content: "a0"
            }

            .ant-input-prefix,
            .ant-input-suffix {
                display: flex;
                flex: none;
                align-items: center
            }

            .ant-input-suffix {
                margin-left: 4px
            }

            .ant-input-clear-icon,
            .anticon.ant-input-clear-icon {
                margin: 0;
                color: rgba(0, 0, 0, .25);
                font-size: 12px;
                vertical-align: -1px;
                cursor: pointer;
                transition: color .3s
            }

            .ant-input-clear-icon-hidden,
            .anticon.ant-input-clear-icon-hidden {
                visibility: hidden
            }

            .ant-input {
                box-sizing: border-box;
                margin: 0;
                font-variant: tabular-nums;
                list-style: none;
                -webkit-font-feature-settings: "tnum";
                font-feature-settings: "tnum";
                position: relative;
                display: inline-block;
                width: 100%;
                min-width: 0;
                padding: 4px 11px;
                color: #222a35;
                font-size: 14px;
                line-height: 1.5715;
                background-color: #fff;
                background-image: none;
                border: 1px solid rgba(34, 42, 53, .08);
                border-radius: 2px;
                transition: all .3s
            }

            .ant-input::-webkit-input-placeholder {
                color: #bfbfbf;
                -webkit-user-select: none;
                user-select: none
            }

            .ant-input::placeholder {
                color: #bfbfbf;
                -webkit-user-select: none;
                -ms-user-select: none;
                user-select: none
            }

            .ant-input:placeholder-shown {
                text-overflow: ellipsis
            }

            .ant-input-group {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
                color: #222a35;
                font-size: 14px;
                font-variant: tabular-nums;
                line-height: 1.5715;
                list-style: none;
                -webkit-font-feature-settings: "tnum";
                font-feature-settings: "tnum";
                position: relative;
                display: table;
                width: 100%;
                border-collapse: separate;
                border-spacing: 0
            }

            .ant-input-group-addon,
            .ant-input-group-wrap,
            .ant-input-group>.ant-input {
                display: table-cell
            }

            .ant-input-group-addon,
            .ant-input-group-wrap {
                width: 1px;
                white-space: nowrap;
                vertical-align: middle
            }

            .ant-input-group .ant-input {
                float: left;
                width: 100%;
                margin-bottom: 0;
                text-align: inherit
            }

            .ant-input-group-addon {
                position: relative;
                padding: 0 11px;
                color: #222a35;
                font-weight: 400;
                font-size: 14px;
                text-align: center;
                background-color: #fafafa;
                border: 1px solid rgba(34, 42, 53, .08);
                border-radius: 2px;
                transition: all .3s
            }

            .ant-input-group-addon:first-child,
            .ant-input-group-addon:first-child .ant-select .ant-select-selector,
            .ant-input-group>.ant-input:first-child,
            .ant-input-group>.ant-input:first-child .ant-select .ant-select-selector {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0
            }

            .ant-input-group>.ant-input-affix-wrapper:not(:first-child) .ant-input {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0
            }

            .ant-input-group>.ant-input-affix-wrapper:not(:last-child) .ant-input {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0
            }

            .ant-input-group-addon:first-child {
                border-right: 0
            }

            .ant-input-group-addon:last-child {
                border-left: 0
            }

            .ant-input-group-addon:last-child,
            .ant-input-group-addon:last-child .ant-select .ant-select-selector,
            .ant-input-group>.ant-input:last-child,
            .ant-input-group>.ant-input:last-child .ant-select .ant-select-selector {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0
            }

            .ant-input-group .ant-input-affix-wrapper:not(:last-child) {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0
            }

            .ant-input-group .ant-input-affix-wrapper:not(:first-child),
            .ant-input-search .ant-input-group .ant-input-affix-wrapper:not(:first-child) {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0
            }

            .ant-input-group-wrapper {
                display: inline-block;
                width: 100%;
                text-align: start;
                vertical-align: top
            }

            .ant-skeleton {
                display: table;
                width: 100%
            }

            .ant-skeleton-active .ant-skeleton-avatar,
            .ant-skeleton-active .ant-skeleton-button,
            .ant-skeleton-active .ant-skeleton-image,
            .ant-skeleton-active .ant-skeleton-input,
            .ant-skeleton-active .ant-skeleton-paragraph>li,
            .ant-skeleton-active .ant-skeleton-title {
                position: relative;
                z-index: 0;
                overflow: hidden;
                background: 0 0
            }

            @-webkit-keyframes ant-skeleton-loading {
                0% {
                    transform: translateX(-37.5%)
                }

                to {
                    transform: translateX(37.5%)
                }
            }

            @keyframes ant-skeleton-loading {
                0% {
                    transform: translateX(-37.5%)
                }

                to {
                    transform: translateX(37.5%)
                }
            }

            .ant-skeleton-active .ant-skeleton-avatar:after,
            .ant-skeleton-active .ant-skeleton-button:after,
            .ant-skeleton-active .ant-skeleton-image:after,
            .ant-skeleton-active .ant-skeleton-input:after,
            .ant-skeleton-active .ant-skeleton-paragraph>li:after,
            .ant-skeleton-active .ant-skeleton-title:after {
                position: absolute;
                top: 0;
                right: -150%;
                bottom: 0;
                left: -150%;
                background: linear-gradient(90deg, hsla(0, 0%, 74.5%, .2) 25%, hsla(0, 0%, 50.6%, .24) 37%, hsla(0, 0%, 74.5%, .2) 63%);
                -webkit-animation: ant-skeleton-loading 1.4s ease infinite;
                animation: ant-skeleton-loading 1.4s ease infinite;
                content: ""
            }

            .ant-skeleton-element {
                display: inline-block;
                width: auto
            }

            .ant-skeleton-element .ant-skeleton-button {
                display: inline-block;
                vertical-align: top;
                background: hsla(0, 0%, 74.5%, .2);
                border-radius: 2px;
                width: 64px;
                min-width: 64px;
                height: 32px;
                line-height: 32px
            }

            .ant-skeleton-element .ant-skeleton-button.ant-skeleton-button-round {
                border-radius: 32px
            }

            .ant-spin {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
                color: #222a35;
                font-size: 14px;
                font-variant: tabular-nums;
                line-height: 1.5715;
                list-style: none;
                -webkit-font-feature-settings: "tnum";
                font-feature-settings: "tnum";
                position: absolute;
                display: none;
                color: #047ffe;
                text-align: center;
                vertical-align: middle;
                opacity: 0;
                transition: -webkit-transform .3s cubic-bezier(.78, .14, .15, .86);
                transition: transform .3s cubic-bezier(.78, .14, .15, .86);
                transition: transform .3s cubic-bezier(.78, .14, .15, .86), -webkit-transform .3s cubic-bezier(.78, .14, .15, .86)
            }

            .ant-spin-spinning {
                position: static;
                display: inline-block;
                opacity: 1
            }

            .ant-spin-dot {
                position: relative;
                display: inline-block;
                font-size: 20px;
                width: 1em;
                height: 1em
            }

            .ant-tabs-bottom,
            .ant-tabs-top {
                flex-direction: column
            }

            .ant-tabs-bottom>.ant-tabs-nav,
            .ant-tabs-bottom>div>.ant-tabs-nav,
            .ant-tabs-top>.ant-tabs-nav,
            .ant-tabs-top>div>.ant-tabs-nav {
                margin: 0 0 16px
            }

            .ant-tabs-bottom>.ant-tabs-nav:before,
            .ant-tabs-bottom>div>.ant-tabs-nav:before,
            .ant-tabs-top>.ant-tabs-nav:before,
            .ant-tabs-top>div>.ant-tabs-nav:before {
                position: absolute;
                right: 0;
                left: 0;
                border-bottom: 1px solid rgba(34, 42, 53, .08);
                content: ""
            }

            .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-ink-bar,
            .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-ink-bar,
            .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar,
            .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar {
                height: 2px
            }

            .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-ink-bar-animated,
            .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-ink-bar-animated,
            .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar-animated,
            .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar-animated {
                transition: width .3s, left .3s, right .3s
            }

            .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:after,
            .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:before,
            .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
            .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:before,
            .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:after,
            .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:before,
            .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
            .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                top: 0;
                bottom: 0;
                width: 30px
            }

            .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:before,
            .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:before,
            .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:before,
            .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                left: 0;
                box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, .08)
            }

            .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:after,
            .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
            .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:after,
            .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:after {
                right: 0;
                box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, .08)
            }

            .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar,
            .ant-tabs-top>.ant-tabs-nav:before,
            .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar,
            .ant-tabs-top>div>.ant-tabs-nav:before {
                bottom: 0
            }

            .ant-tabs {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
                color: #222a35;
                font-size: 14px;
                font-variant: tabular-nums;
                line-height: 1.5715;
                list-style: none;
                -webkit-font-feature-settings: "tnum";
                font-feature-settings: "tnum";
                display: flex
            }

            .ant-tabs>.ant-tabs-nav,
            .ant-tabs>div>.ant-tabs-nav {
                position: relative;
                display: flex;
                flex: none;
                align-items: center
            }

            .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap,
            .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap {
                position: relative;
                display: inline-block;
                display: flex;
                flex: auto;
                align-self: stretch;
                overflow: hidden;
                white-space: nowrap;
                -webkit-transform: translate(0);
                transform: translate(0)
            }

            .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap:after,
            .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap:before,
            .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
            .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                position: absolute;
                z-index: 1;
                opacity: 0;
                transition: opacity .3s;
                content: "";
                pointer-events: none
            }

            .ant-tabs>.ant-tabs-nav .ant-tabs-nav-list,
            .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-list {
                position: relative;
                display: flex;
                transition: -webkit-transform .3s;
                transition: transform .3s;
                transition: transform .3s, -webkit-transform .3s
            }

            .ant-tabs>.ant-tabs-nav .ant-tabs-nav-operations,
            .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-operations {
                display: flex;
                align-self: stretch
            }

            .ant-tabs>.ant-tabs-nav .ant-tabs-nav-operations-hidden,
            .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-operations-hidden {
                position: absolute;
                visibility: hidden;
                pointer-events: none
            }

            .ant-tabs>.ant-tabs-nav .ant-tabs-nav-more,
            .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-more {
                position: relative;
                padding: 8px 16px;
                background: 0 0;
                border: 0
            }

            .ant-tabs>.ant-tabs-nav .ant-tabs-nav-more:after,
            .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-more:after {
                position: absolute;
                right: 0;
                bottom: 0;
                left: 0;
                height: 5px;
                -webkit-transform: translateY(100%);
                transform: translateY(100%);
                content: ""
            }

            .ant-tabs-ink-bar {
                position: absolute;
                background: #047ffe;
                pointer-events: none
            }

            .ant-tabs-tab {
                position: relative;
                display: inline-flex;
                align-items: center;
                padding: 12px 0;
                font-size: 16px;
                background: 0 0;
                border: 0;
                outline: 0;
                cursor: pointer
            }

            .ant-tabs-tab-btn,
            .ant-tabs-tab-remove {
                outline: 0;
                transition: all .3s
            }

            .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
                color: #222a35;
                text-shadow: 0 0 .25px currentcolor
            }

            .ant-tabs-tab+.ant-tabs-tab {
                margin: 0 10px 0 32px
            }

            .ant-tabs-content {
                display: flex;
                width: 100%
            }

            .ant-tabs-content-holder {
                flex: auto;
                min-width: 0;
                min-height: 0
            }

            .ant-tabs-tabpane {
                flex: none;
                width: 100%;
                outline: 0
            }

            html body {
                height: 100%;
                margin: 0;
                font-size: 14px;
                line-height: 22px;
                overflow: auto;
                color: #222a35;
                font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Tahoma, Arial, "PingFang SC", "Microsoft YaHei", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                -webkit-font-smoothing: antialiased
            }

            html body p,
            html body ul {
                padding: 0;
                margin: 0
            }

            html body li {
                list-style: none
            }

            html body a {
                text-decoration: none
            }

            html body a,
            html body a:hover {
                color: #222a35
            }

            html body ul {
                -webkit-padding-start: 0;
                padding-inline-start: 0
            }

            html body .dk-iconfont {
                font-weight: 400;
                line-height: 16px
            }

            html body input {
                caret-color: #047ffe
            }

            html body .ant-input {
                border-radius: 0
            }

            html body #root {
                height: 100%
            }

            html body #root-skeleton {
                height: 100%;
                overflow: hidden
            }

            .generic .os-scrollbar,
            .linux .os-scrollbar,
            .mac .os-scrollbar {
                overflow-y: overlay;
                overflow-y: auto
            }

            html body .ant-tabs {
                color: rgba(34, 42, 53, .7);
                position: relative
            }

            html body .ant-tabs .ant-tabs-nav {
                background: #fff
            }

            html body .ant-tabs .ant-tabs-nav:before {
                border-bottom: none
            }

            html body .ant-tabs .ant-tabs-tab {
                font-size: 16px;
                line-height: 38px;
                padding: 0
            }

            html body .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
                font-weight: 500
            }

            html body .ant-tabs .ant-tabs-ink-bar {
                background-color: #047ffe;
                height: 2px !important;
                border-radius: 8px
            }

            html body .ant-btn {
                border-radius: 3px;
                line-height: normal
            }

            html body .ant-btn>i {
                margin-right: 8px !important
            }

            html body .ant-btn-primary {
                background-color: #047ffe;
                border: 1px solid #047ffe;
                color: #fff;
                box-shadow: none
            }

            html body .ant-btn-secondary {
                background-color: #fff;
                border: 1px solid #047ffe;
                color: #047ffe
            }

            html body .ant-input-affix-wrapper {
                border-radius: 6px
            }

            html body .ant-input-clear-icon,
            html body .anticon .ant-input-clear-icon {
                color: rgba(34, 42, 53, .3);
                font-size: 14px
            }

            html body .ant-input-group-wrapper {
                border: 1px solid rgba(34, 42, 53, .1);
                border-radius: 4px
            }

            html body .ant-input-group-wrapper .ant-input-group-addon {
                background: #fff;
                border: none;
                padding: 0 8px 0 9px
            }

            html body .ant-input-group-wrapper .ant-input-affix-wrapper {
                padding: 0;
                border: none;
                box-shadow: none
            }

            html body .ant-input {
                font-size: 14px;
                height: 30px
            }

            html .ant-input::-webkit-input-placeholder {
                color: rgba(34, 42, 53, .5)
            }

            html .ant-input::placeholder {
                color: rgba(34, 42, 53, .5)
            }

            .aside-content .aside-skeleton-box {
                width: 240px;
                padding: 0 8px
            }

            .aside-content .aside-skeleton-box .aside-skeleton-button * {
                min-width: auto !important;
                width: 100% !important;
                height: 100% !important
            }

            .aside-content .aside-skeleton-box .aside-skeleton-box-item {
                width: 100%;
                height: 36px;
                position: relative
            }

            .aside-content .aside-skeleton-box .aside-skeleton-box-item .aside-skeleton-logo {
                position: absolute;
                top: 10px;
                left: 24px;
                width: 16px;
                height: 16px
            }

            .aside-content .aside-skeleton-box .aside-skeleton-box-item .aside-skeleton-title {
                position: absolute;
                top: 11px;
                left: 52px;
                width: 48px;
                height: 14px
            }

            .aside-content .aside-skeleton-box .aside-skeleton-space {
                width: 100%
            }

            .aside-content .aside-skeleton-box .aside-skeleton-space .aside-skeleton-space-item {
                width: 100%;
                height: 35px;
                position: relative
            }

            .aside-content .aside-skeleton-box .aside-skeleton-space .aside-skeleton-space-item .aside-skeleton-symbol {
                display: block;
                position: absolute;
                top: 15.5px;
                left: 52px;
                width: 4px;
                height: 4px
            }

            .aside-content .aside-skeleton-box .aside-skeleton-space .aside-skeleton-space-item .aside-skeleton-title {
                display: block;
                position: absolute;
                top: 11px;
                left: 64px;
                width: 88px;
                height: 13px
            }
        </style>
        <div class="layout">
            <div id="layout-left-small" class="left-wrap left-wrap" style="display:none">
                <div class="left os-scrollbar">
                    <div class="aside aside-small-wrap"><a class="logo" href="/"><img class="logo-img logo-small" src="data:image/png;base64,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" alt=""></a>
                        <div class="aside-small-box os-scrollbar aside-small-box-media">
                            <ul class="aside-small">
                                <li class="item-name item-name-6"><a aria-current="page" class="item-name-normal" href="/">
                                        <div class="item-small-container"><i class="dk-iconfont aside-icon icon-aside-large dk-icon-zhuye4"></i> <span class="small-name"><span>首页</span></span></div>
                                    </a></li>
                                <li class="item-name item-name-6"><a class="item-name-normal" href="/disk">
                                        <div class="item-small-container"><i class="dk-iconfont aside-icon icon-aside-large dk-icon-gerenkongjian"></i> <span class="small-name"><span>个人</span></span></div>
                                    </a></li>
                                <li class="item-name"><a class="item-name-normal" href="/team-folder">
                                        <div class="item-small-container"><i class="dk-iconfont aside-icon icon-aside-large dk-icon-tuanduikongjian4px"></i> <span class="small-name"><span>团队</span></span></div>
                                    </a></li>
                                <li class="item-name item-name-6">
                                    <div class="item-name-normal">
                                        <div class="item-small-container"><i class="dk-iconfont aside-icon icon-aside-large dk-icon-zhishikuxian"></i> <span class="small-name"><span>知识库</span></span></div>
                                    </div>
                                </li>
                                <li class="item-name item-name-6"><a class="item-name-normal" href="/tome">
                                        <div class="item-small-container"><i class="dk-iconfont aside-icon icon-aside-large dk-icon-fenxiang4px"></i> <span class="small-name"><span>分享</span></span></div>
                                    </a></li>
                                <li class="item-name item-name-6"><a class="item-name-normal" href="/favorite">
                                        <div class="item-small-container"><i class="dk-iconfont aside-icon icon-aside-large dk-icon-shoucang4px"></i> <span class="small-name"><span>收藏</span></span></div>
                                    </a></li>
                                <li class="item-name item-name-6"><a class="item-name-normal" href="/trash">
                                        <div class="item-small-container"><i class="dk-iconfont aside-icon icon-aside-large dk-icon-huishouzhan4px"></i> <span class="small-name"><span>回收站</span></span></div>
                                    </a></li>
                                <li class="item-name">
                                    <div class="item-name-normal">
                                        <div class="item-small-container"><i class="dk-iconfont aside-icon icon-aside-large dk-icon-gengduoxitong"></i> <span class="small-name"><span>更多</span></span></div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="left-action left-action-small">
                            <div class="tip-btn tip-btn-small"><span><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAWCSURBVFiFpZdPiFVVHMe/33Nf782kwohRriYXFQSThboIInIRSDIuytQZMSuhRZBB2xDcRLYLoiJQslrUZFEbMQM3LpIMZ2xGISghRREK/zGjjm9873xbnD/33Hfvm3lOh7lzzrv33N/v8zvf3/lziUWWmQOrhzJoI61ehrAaAEBMyfD7WoPH6q9MTSzGLnvtqI8eacwtu//F9t32MwC3UnpQIkgV+/l7Iv8FdCi7L/ulPnP7R759rvm/ge58+eRjtmWHKWyE1bMA6gAQEEj3evwdoVQ0LrSQ8biII6ZmDve9Ovlnz0Cznz/xHFsatsQWWj0sECQgEDTOueNg93Dk/3lSWYAQJF8bXjDCd6rxcP+uM8e7As3uXz2Gdnsb6J3FOgFgCkKAAuRvxLaKUJ1tKa+z7Nv+N6ZGgkWTwwzthbXbYIjyZYDMtzOChmBGsAYwM2CNQI2gMf6ee56+A2OiPfkLhoC122b3D+0tAQnYDSYQWfFyAB4sM+4yxhn2zxUcZ/nFmsnhUlvBDwkRuwNHLRn8Fci8JL4jDcILEBkV80lUSqOC/nIKOqUIysspQdZLbZ3MtFhRAnK5kYwQCRk3k5jmEhPXoWoMuDRp3ogwsZ/kwEDAusSOfQAPlccRJUthaAhk8LVrpzkVc8gQ2eAG1IePojF8FNnghsIzmPQ9xPyLtpPgy0BZkCk4NXlnmugk1945MyufBpgBzFw7ecYEHjQx6GCbwX6WA+WSwVN7WWhc3oDMA/DSxWWP+cgHFZRIyqCF5OyJPre8dHQ+1c6tdOSQT15DCAmMT+yEI3mNhXaaYsofgGFRBB2MccsQqS6S0UWXrsLzwjBmeEfJc6IA523lr/kdoMOMQaEkkrHoLEaPxELHxlq850Y5ztIOW6UZ2wlEoJl2cDOVMQJBPqdSG11GKMTGsEsoKgAyyTuGv9slIBnciKpTftTD7th9QBYqEVl5w8mWL0gymCkBgbw4r2G6lLzXIqgkWYX1v0tAtPqtLAGLClU5vHuzsl1yGe2kWe48UDwZ+sVpbwwn2mFxEP2UTM4/UiVY+6+vKtspiD8IhSrfPuAPcxknS0B3DX7KLApaI+xBAawq9OZVtM9+WPUkdxrBAk2emwTQpn4OfaNkS0ZPXwYx6UCSEJj8XDAXKko45nZGFMCIySWjpy+XgADAigeQJ7+z13k+7iyN5TCDwzCDw0BjeTVTqFWctZD3mZTCXjbdbh0cqNU+oLAknDrhF0T5qJhEDVNHbe17MA+sdQFdGUfr13cAOxdHoTgvmX8AuIyYnm63DqY9CiO0cufULQCfyOePgnyJ7IWxqg9EGACuXR8oj008rMUTW1hCPvM+q4EAYLZp90m8VgTJ4yzE3JqBbuXLl25dBFoz1X07DvkSr8027b5O/5WpMTe29k0LfBoPZMkmWzjCAuCyVcgefQ0Q0D73BTRzPmHwnz7K27ByJ0doV//IREGurkAAcGdszRGQL3RC5Rm6wIxLE7gDBhaH+0bHN1W9VpIslIblTggXEAwF+f3apOLqVlgq5Od4XHL8ocy3LzSE17v57QrE7eNXWi1sonQ11Z5yDuMKXAFGDxUTWXBfr9LVVgubuH38yj0DAcDSHeNnDLAF0k2FYY9QiSoV7TRvHJxuGGDL0h3jZ+bz2dPS2xxbN2SJYzR4qHCsrbKgfP8KAcjiHyM83xg5dXYhX/OOUCiNkVNn+2rZOsqecI5cctKPlqvztvv+8vLJnuirZet6gekZCAC4+eSl+nWup7AHVnNykUNWUSZZf89BzVHYU7/O9dx88lLPfnrtmJbZb55aBVN7n8RWAFm04vKoLeEQbOvd/tHfz9+r7UUBhdL8Yc3jmuNbJLYDgISvWdfHjZcm/liszf8Ai0udeWRpC48AAAAASUVORK5CYII="> <span class="cooper-tip-name">数据安全</span></span></div>
                            <div class="cooper-notify notify-list-small"><i class="dk-iconfont dk-icon-zhanneixin"></i></div><i class="icon-user icon-user-small"></i>
                        </div>
                    </div>
                </div>
                <div class="aside-btn-handle">
                    <div class="aside-operate isZhankai"><i class="dk-iconfont dk-icon-zhedieshouqidejiantou operate-icon"></i></div>
                </div>
            </div>
            <div id="layout-left-large" class="left-wrap left-wrap" style="display:none">
                <div class="left os-scrollbar">
                    <div class="aside aside-large-wrap large-wrap"><a class="logo" href="/"><img class="logo-img logo-large" src="https://img0.didistatic.com/static/cooper_cn/cooper/images/logo_cooper-97326ac6428fcd3fb235680bd6b849f4.svg" alt=""></a>
                        <div class="aside-content">
                            <div class="aside-skeleton-box">
                                <div class="aside-skeleton-box-item">
                                    <div class="aside-skeleton-logo aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="aside-skeleton-title aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="aside-skeleton-box-item">
                                    <div class="aside-skeleton-logo aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="aside-skeleton-title aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="aside-skeleton-box-item">
                                    <div class="aside-skeleton-logo aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="aside-skeleton-title aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="aside-skeleton-space">
                                    <div class="aside-skeleton-space-item">
                                        <div class="aside-skeleton-symbol aside-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-circle"></span></div>
                                        </div>
                                        <div class="aside-skeleton-title aside-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="aside-skeleton-space-item">
                                        <div class="aside-skeleton-symbol aside-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-circle"></span></div>
                                        </div>
                                        <div class="aside-skeleton-title aside-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="aside-skeleton-space-item">
                                        <div class="aside-skeleton-symbol aside-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-circle"></span></div>
                                        </div>
                                        <div class="aside-skeleton-title aside-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="aside-skeleton-space-item">
                                        <div class="aside-skeleton-symbol aside-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-circle"></span></div>
                                        </div>
                                        <div class="aside-skeleton-title aside-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="aside-skeleton-space-item">
                                        <div class="aside-skeleton-symbol aside-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-circle"></span></div>
                                        </div>
                                        <div class="aside-skeleton-title aside-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="aside-skeleton-box-item">
                                    <div class="aside-skeleton-logo aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="aside-skeleton-title aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="aside-skeleton-box-item">
                                    <div class="aside-skeleton-logo aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="aside-skeleton-title aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="aside-skeleton-box-item">
                                    <div class="aside-skeleton-logo aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="aside-skeleton-title aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="aside-skeleton-box-item">
                                    <div class="aside-skeleton-logo aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="aside-skeleton-title aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="aside-skeleton-box-item">
                                    <div class="aside-skeleton-logo aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="aside-skeleton-title aside-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="left-action"><i class="icon-user"></i>
                            <div class="cooper-notify"><i class="dk-iconfont dk-icon-zhanneixin"></i></div>
                            <div class="tip-btn tip-btn-long"><span><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAWCSURBVFiFpZdPiFVVHMe/33Nf782kwohRriYXFQSThboIInIRSDIuytQZMSuhRZBB2xDcRLYLoiJQslrUZFEbMQM3LpIMZ2xGISghRREK/zGjjm9873xbnD/33Hfvm3lOh7lzzrv33N/v8zvf3/lziUWWmQOrhzJoI61ehrAaAEBMyfD7WoPH6q9MTSzGLnvtqI8eacwtu//F9t32MwC3UnpQIkgV+/l7Iv8FdCi7L/ulPnP7R759rvm/ge58+eRjtmWHKWyE1bMA6gAQEEj3evwdoVQ0LrSQ8biII6ZmDve9Ovlnz0Cznz/xHFsatsQWWj0sECQgEDTOueNg93Dk/3lSWYAQJF8bXjDCd6rxcP+uM8e7As3uXz2Gdnsb6J3FOgFgCkKAAuRvxLaKUJ1tKa+z7Nv+N6ZGgkWTwwzthbXbYIjyZYDMtzOChmBGsAYwM2CNQI2gMf6ee56+A2OiPfkLhoC122b3D+0tAQnYDSYQWfFyAB4sM+4yxhn2zxUcZ/nFmsnhUlvBDwkRuwNHLRn8Fci8JL4jDcILEBkV80lUSqOC/nIKOqUIysspQdZLbZ3MtFhRAnK5kYwQCRk3k5jmEhPXoWoMuDRp3ogwsZ/kwEDAusSOfQAPlccRJUthaAhk8LVrpzkVc8gQ2eAG1IePojF8FNnghsIzmPQ9xPyLtpPgy0BZkCk4NXlnmugk1945MyufBpgBzFw7ecYEHjQx6GCbwX6WA+WSwVN7WWhc3oDMA/DSxWWP+cgHFZRIyqCF5OyJPre8dHQ+1c6tdOSQT15DCAmMT+yEI3mNhXaaYsofgGFRBB2MccsQqS6S0UWXrsLzwjBmeEfJc6IA523lr/kdoMOMQaEkkrHoLEaPxELHxlq850Y5ztIOW6UZ2wlEoJl2cDOVMQJBPqdSG11GKMTGsEsoKgAyyTuGv9slIBnciKpTftTD7th9QBYqEVl5w8mWL0gymCkBgbw4r2G6lLzXIqgkWYX1v0tAtPqtLAGLClU5vHuzsl1yGe2kWe48UDwZ+sVpbwwn2mFxEP2UTM4/UiVY+6+vKtspiD8IhSrfPuAPcxknS0B3DX7KLApaI+xBAawq9OZVtM9+WPUkdxrBAk2emwTQpn4OfaNkS0ZPXwYx6UCSEJj8XDAXKko45nZGFMCIySWjpy+XgADAigeQJ7+z13k+7iyN5TCDwzCDw0BjeTVTqFWctZD3mZTCXjbdbh0cqNU+oLAknDrhF0T5qJhEDVNHbe17MA+sdQFdGUfr13cAOxdHoTgvmX8AuIyYnm63DqY9CiO0cufULQCfyOePgnyJ7IWxqg9EGACuXR8oj008rMUTW1hCPvM+q4EAYLZp90m8VgTJ4yzE3JqBbuXLl25dBFoz1X07DvkSr8027b5O/5WpMTe29k0LfBoPZMkmWzjCAuCyVcgefQ0Q0D73BTRzPmHwnz7K27ByJ0doV//IREGurkAAcGdszRGQL3RC5Rm6wIxLE7gDBhaH+0bHN1W9VpIslIblTggXEAwF+f3apOLqVlgq5Od4XHL8ocy3LzSE17v57QrE7eNXWi1sonQ11Z5yDuMKXAFGDxUTWXBfr9LVVgubuH38yj0DAcDSHeNnDLAF0k2FYY9QiSoV7TRvHJxuGGDL0h3jZ+bz2dPS2xxbN2SJYzR4qHCsrbKgfP8KAcjiHyM83xg5dXYhX/OOUCiNkVNn+2rZOsqecI5cctKPlqvztvv+8vLJnuirZet6gekZCAC4+eSl+nWup7AHVnNykUNWUSZZf89BzVHYU7/O9dx88lLPfnrtmJbZb55aBVN7n8RWAFm04vKoLeEQbOvd/tHfz9+r7UUBhdL8Yc3jmuNbJLYDgISvWdfHjZcm/liszf8Ai0udeWRpC48AAAAASUVORK5CYII="> <span class="cooper-tip-name">数据安全</span></span></div>
                        </div>
                    </div>
                </div>
                <div class="aside-btn-handle">
                    <div class="aside-operate"><i class="dk-iconfont dk-icon-zhedieshouqidejiantou operate-icon"></i></div>
                </div>
            </div>
            <style>
                .home-skeleton-wrap {
                    position: relative;
                    width: 100%
                }
            </style>
            <div id="right-home" class="right" style="display:none">
                <div class="header-wrap">
                    <div class="header-warp">
                        <div class="header-top">
                            <div class="header-top-left">
                                <div class="global-search-wrap"><span class="ant-input-group-wrapper"><span class="ant-input-wrapper ant-input-group"><span class="ant-input-group-addon">
                                                <div class="search-before"><i class="dk-icon-sousuo dk-iconfont" style="color:#bbb"></i></div>
                                            </span><span class="ant-input-affix-wrapper"><input autocomplete="off" placeholder="搜索文档标题、正文、空间/知识库名" class="ant-input"> <span class="ant-input-suffix"><span class="ant-input-clear-icon ant-input-clear-icon-hidden" role="button" tabindex="-1"><span role="img" aria-label="close-circle" class="anticon anticon-close-circle"><svg fill-rule="evenodd" viewBox="64 64 896 896" focusable="false" data-icon="close-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                                <path d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"></path>
                                                            </svg> </span></span></span></span><span class="ant-input-group-addon"><span class="search-icon-btn"><span class="search-text">高级搜索</span>⌘+J</span></span></span></span></div>
                            </div>
                            <div class="title-opt"><button type="button" class="ant-btn ant-btn-secondary"><i class="dk-iconfont dk-icon-shangchuan2"></i><span>上传</span></button><button type="button" class="ant-btn ant-btn-primary"><i class="dk-iconfont dk-icon-a-"></i><span>新建</span></button></div>
                            <div class="cooper-customer-service"><span class="customer-service-bar"></span>
                                <div class="newfeature-popup"><span><span class="cooper-customer-service-empty"></span></span></div>
                                <div class="cooper-helper-wrap">
                                    <div class="cooper-helper"><i class="dk-iconfont dk-icon-gongnengshangxin"></i></div>
                                </div>
                                <div class="feedback-with-popup"><span>
                                        <div class="feedback">
                                            <div class="content"><i class="dk-iconfont dk-icon-lianxikefu"></i></div>
                                        </div>
                                    </span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="body-wrap">
                    <style>
                        .banner {
                            width: 100%;
                            height: 160px;
                            padding: 0 32px;
                            margin-bottom: 20px;
                            display: flex;
                            flex-wrap: wrap;
                            gap: 20px;
                            justify-content: flex-start
                        }

                        .banner .banner-item {
                            border-radius: 8px
                        }

                        .banner .banner_left {
                            position: relative;
                            background-color: #f6f7f7;
                            flex: 4 4 300px
                        }

                        .banner .banner_left .banner_left-title {
                            padding-top: 14px
                        }

                        .banner .banner_left .banner-skeleton-title {
                            height: 20px !important
                        }

                        .banner .banner_left .banner-skeleton-title-1 {
                            width: 480px;
                            height: 14px !important
                        }

                        .banner .banner_left .banner-skeleton-title-2 {
                            width: 360px;
                            height: 14px !important
                        }

                        .banner .banner_left .data-item {
                            margin-top: 12px
                        }

                        .banner .banner_right {
                            flex: 1 0 200px;
                            background-color: #f6f7f7;
                            min-width: 200px
                        }

                        .banner .banner_right .banner-skeleton-title-short {
                            width: 100px;
                            height: 20px !important
                        }

                        .banner .banner_right .data-item {
                            margin-top: 12px
                        }

                        .banner .ant-card {
                            padding: 26px 24px;
                            border-radius: 8px;
                            background-color: #f6f7f7
                        }

                        .banner .ant-card-body {
                            padding: 0
                        }

                        .banner .ant-card-head-wrapper {
                            display: flex;
                            align-items: center
                        }

                        .banner .ant-card-head {
                            border-radius: 8px 8px 0 0;
                            border: 0;
                            padding: 0;
                            min-height: 0;
                            height: 22px;
                            line-height: 22px
                        }

                        .banner .ant-card-head-title {
                            padding: 0;
                            color: #297e19;
                            display: inline-block;
                            flex: 1;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            font-weight: 500;
                            font-size: 16px
                        }

                        .banner .ant-card-extra {
                            padding: 0;
                            color: #297e19;
                            font-size: 12px;
                            margin-left: auto;
                            font-weight: 400
                        }

                        .banner .dk-iconfont {
                            margin-left: 4px;
                            font-size: 12px;
                            line-height: 22.5px
                        }

                        .banner .ant-card-bordered {
                            border: 0
                        }

                        .banner .rotating-data-container {
                            margin-top: 20px
                        }

                        .banner .data-item {
                            margin-top: 20px
                        }

                        .faq-item {
                            background-color: #fff;
                            margin-top: 10px;
                            padding: 9px 12px
                        }

                        .banner .banner-skeleton-title {
                            position: absolute;
                            top: 17px;
                            left: 62px;
                            width: 200px;
                            height: 14px !important
                        }

                        .banner .banner-skeleton-title {
                            font-family: PingFangSC-Medium;
                            font-size: 16px;
                            font-weight: 400;
                            color: #222a35;
                            top: 0;
                            position: -webkit-sticky;
                            position: sticky;
                            z-index: 1;
                            height: 24px;
                            display: block;
                            margin-bottom: 0;
                            padding: 0 0 12px;
                            box-sizing: content-box
                        }

                        .banner .banner-skeleton-button * {
                            min-width: auto !important;
                            width: 100% !important;
                            height: 100% !important
                        }
                    </style>
                    <div class="banner">
                        <div class="banner_left banner_example banner-item">
                            <div class="banner_left-title">
                                <div class="data-item">
                                    <div class="banner-skeleton-title banner-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                            </div>
                            <div class="banner_left-text">
                                <div class="data-item">
                                    <div class="banner-skeleton-title banner-skeleton-title-1 banner-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <div class="banner-skeleton-title banner-skeleton-title-2 banner-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="banner_right banner-item">
                            <div class="ant-card ant-card-bordered ant-card-middle" style="width:100%;height:160px">
                                <div class="ant-card-head">
                                    <div class="ant-card-head-wrapper">
                                        <div class="banner-skeleton-title banner-skeleton-title-short banner-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="ant-card-body">
                                    <div class="rotating-data-container">
                                        <div class="data-item">
                                            <div class="banner-skeleton-title banner-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </div>
                                        <div class="data-item">
                                            <div class="banner-skeleton-title banner-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="home-skeleton-wrap">
                        <style>
                            .home-skeleton-wrap {
                                min-width: 667px
                            }

                            .home-skeleton-wrap .quick-access-skeleton {
                                min-width: 667px;
                                margin: 0 auto 8px;
                                padding: 0 32px
                            }

                            .home-skeleton-wrap .quick-access-skeleton-title {
                                font-family: PingFangSC-Medium;
                                font-size: 16px;
                                font-weight: 400;
                                color: #222a35;
                                top: 0;
                                position: -webkit-sticky;
                                position: sticky;
                                z-index: 1;
                                height: 24px;
                                background-color: #fff;
                                display: block;
                                margin-bottom: 0;
                                padding: 0 0 12px;
                                box-sizing: content-box
                            }

                            .home-skeleton-wrap .recent-act-skeleton {
                                margin: 0 auto;
                                min-width: 667px
                            }

                            .home-skeleton-wrap .recent-act-skeleton .ant-tabs-nav {
                                padding: 0 32px 2px !important
                            }

                            .home-skeleton-wrap .recent-act-skeleton .file-skeleton-box {
                                min-width: 667px !important;
                                margin: -8px auto 0 !important
                            }

                            .home-skeleton-wrap .recent-act-skeleton .file-header-skeleton-box {
                                padding-left: 27px !important;
                                padding-right: 32px !important
                            }

                            .home-skeleton-wrap .recent-act-skeleton .file-body-skeleton-box {
                                padding-left: 27px !important;
                                padding-right: 28px !important;
                                padding-top: 8px !important
                            }

                            .quick-access-skeleton-box {
                                width: 100%;
                                display: flex;
                                flex-wrap: wrap;
                                justify-content: flex-start
                            }

                            .quick-access-skeleton-box .quick-access-skeleton-button * {
                                min-width: auto !important;
                                width: 100% !important;
                                height: 100% !important
                            }

                            .quick-access-skeleton-box .quick-access-skeleton-box-item {
                                flex: 1;
                                height: 46px;
                                border-radius: 4px;
                                margin: 0 12px 10px 0;
                                border: 1px solid #f2f3f3;
                                background-color: #fff;
                                padding: 12px;
                                box-sizing: border-box;
                                cursor: pointer;
                                display: flex;
                                align-items: center;
                                width: calc(25% - 9px);
                                min-width: calc(25% - 9px);
                                max-width: calc(25% - 9px);
                                position: relative
                            }

                            .quick-access-skeleton-box .quick-access-skeleton-box-item:nth-child(4n) {
                                margin-right: 0
                            }

                            .quick-access-skeleton-box .quick-access-skeleton-box-item .quick-access-skeleton-logo {
                                position: absolute;
                                top: 14.4px;
                                left: 18.4px;
                                width: 19.2px;
                                height: 19.2px
                            }

                            .quick-access-skeleton-box .quick-access-skeleton-box-item .quick-access-skeleton-title {
                                position: absolute;
                                top: 17px;
                                left: 48px;
                                width: calc(100% - 70px);
                                height: 14px
                            }
                        </style>
                        <div class="home-skeleton-wrap">
                            <div class="quick-access-skeleton">
                                <div class="quick-access-skeleton-title">快速访问</div>
                                <div class="quick-access-skeleton-box">
                                    <div class="quick-access-skeleton-box-item">
                                        <div class="quick-access-skeleton-logo quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="quick-access-skeleton-title quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="quick-access-skeleton-box-item">
                                        <div class="quick-access-skeleton-logo quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="quick-access-skeleton-title quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="quick-access-skeleton-box-item">
                                        <div class="quick-access-skeleton-logo quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="quick-access-skeleton-title quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="quick-access-skeleton-box-item">
                                        <div class="quick-access-skeleton-logo quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="quick-access-skeleton-title quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="quick-access-skeleton-box-item">
                                        <div class="quick-access-skeleton-logo quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="quick-access-skeleton-title quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="quick-access-skeleton-box-item">
                                        <div class="quick-access-skeleton-logo quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="quick-access-skeleton-title quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="quick-access-skeleton-box-item">
                                        <div class="quick-access-skeleton-logo quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="quick-access-skeleton-title quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="quick-access-skeleton-box-item">
                                        <div class="quick-access-skeleton-logo quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="quick-access-skeleton-title quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="quick-access-skeleton-box-item">
                                        <div class="quick-access-skeleton-logo quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="quick-access-skeleton-title quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="quick-access-skeleton-box-item">
                                        <div class="quick-access-skeleton-logo quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="quick-access-skeleton-title quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="quick-access-skeleton-box-item">
                                        <div class="quick-access-skeleton-logo quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="quick-access-skeleton-title quick-access-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="recent-act-skeleton">
                            <div class="tab-skeleton">
                                <div class="ant-tabs ant-tabs-top ant-tabs-middle tab-top-title tab-top-large" tabsize="large">
                                    <div role="tablist" class="ant-tabs-nav">
                                        <div class="ant-tabs-nav-wrap">
                                            <div class="ant-tabs-nav-list" style="transform:translate(0,0)">
                                                <div class="ant-tabs-tab ant-tabs-tab-active">
                                                    <div role="tab" aria-selected="true" class="ant-tabs-tab-btn" tabindex="0" id="rc-tabs-0-tab-1" aria-controls="rc-tabs-0-panel-1"><span><span>最近访问</span></span></div>
                                                </div>
                                                <div class="ant-tabs-tab">
                                                    <div role="tab" aria-selected="false" class="ant-tabs-tab-btn" tabindex="0" id="rc-tabs-0-tab-2" aria-controls="rc-tabs-0-panel-2"><span><span>最近编辑</span></span></div>
                                                </div>
                                                <div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated" style="left:0;width:64px"></div>
                                            </div>
                                        </div>
                                        <div class="ant-tabs-nav-operations ant-tabs-nav-operations-hidden"><button type="button" class="ant-tabs-nav-more" tabindex="-1" aria-hidden="true" aria-haspopup="listbox" aria-controls="rc-tabs-0-more-popup" id="rc-tabs-0-more" aria-expanded="false" style="visibility:hidden;order:1"><span role="img" aria-label="ellipsis" class="anticon anticon-ellipsis"><svg viewBox="64 64 896 896" focusable="false" data-icon="ellipsis" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                        <path d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"></path>
                                                    </svg></span></button></div>
                                    </div>
                                    <div class="ant-tabs-content-holder">
                                        <div class="ant-tabs-content ant-tabs-content-top">
                                            <div role="tabpanel" tabindex="0" aria-hidden="false" class="ant-tabs-tabpane ant-tabs-tabpane-active" id="rc-tabs-0-panel-1" aria-labelledby="rc-tabs-0-tab-1">
                                                <style>
                                                    .file-skeleton-box {
                                                        margin: 0;
                                                        padding: 0;
                                                        min-width: 667px !important;
                                                        margin: -8px auto 0 !important
                                                    }

                                                    .file-skeleton-box .file-header-skeleton-box {
                                                        width: 100%;
                                                        height: 36px;
                                                        margin: 0;
                                                        padding: 0;
                                                        display: flex;
                                                        align-items: center
                                                    }

                                                    .file-skeleton-box .header-inner {
                                                        width: 100%;
                                                        height: 22px;
                                                        position: relative;
                                                        margin: 0;
                                                        padding: 0;
                                                        display: flex;
                                                        align-items: center;
                                                        overflow: hidden
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-button {
                                                        width: 42px;
                                                        height: 14px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-button * {
                                                        min-width: auto !important;
                                                        width: 100% !important;
                                                        height: 100% !important
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-name {
                                                        margin-right: 192px;
                                                        flex: 1;
                                                        min-width: 200px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-owner {
                                                        margin-right: 216px;
                                                        min-width: 42px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-address {
                                                        margin-right: 216px;
                                                        min-width: 42px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-time {
                                                        margin-right: 22px;
                                                        min-width: 42px
                                                    }

                                                    .file-skeleton-box .file-body-skeleton-box {
                                                        width: 100%;
                                                        padding-top: 8px !important
                                                    }

                                                    .file-skeleton-box .body-inner>li {
                                                        width: 100%;
                                                        height: 46px;
                                                        position: relative
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-button * {
                                                        min-width: auto !important;
                                                        width: 100% !important;
                                                        height: 100% !important
                                                    }

                                                    .file-skeleton-box .body-inner li {
                                                        display: flex;
                                                        align-items: center
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-name-logo {
                                                        width: 19.2px;
                                                        height: 19.2px;
                                                        min-width: 19.2px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-name-title {
                                                        height: 14px;
                                                        flex: 1;
                                                        margin-left: 10px;
                                                        top: 17px;
                                                        left: 32px;
                                                        margin-right: 192px;
                                                        min-width: 170px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-name-title .ant-skeleton {
                                                        max-width: 500px !important
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-owner {
                                                        width: 66px;
                                                        height: 14px;
                                                        top: 17px;
                                                        left: 544px;
                                                        margin-right: 192px;
                                                        min-width: 66px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-address {
                                                        width: 66px;
                                                        height: 14px;
                                                        top: 17px;
                                                        left: 736px;
                                                        margin-right: 192px;
                                                        min-width: 66px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-time {
                                                        width: 66px;
                                                        height: 14px;
                                                        top: 17px;
                                                        left: 928px min-width: 66px
                                                    }
                                                </style>
                                                <div class="file-skeleton-box">
                                                    <div class="file-header-skeleton-box">
                                                        <div class="header-inner">
                                                            <div class="file-header-skeleton-name">
                                                                <div class="file-header-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </div>
                                                            <div class="file-header-skeleton-owner file-header-skeleton-button">
                                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                            </div>
                                                            <div class="file-header-skeleton-address file-header-skeleton-button">
                                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                            </div>
                                                            <div class="file-header-skeleton-time file-header-skeleton-button">
                                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="file-body-skeleton-box">
                                                        <div class="body-inner">
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div role="tabpanel" tabindex="-1" aria-hidden="true" class="ant-tabs-tabpane" id="rc-tabs-0-panel-2" aria-labelledby="rc-tabs-0-tab-2" style="display:none"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                #right-person p {
                    margin-top: 0;
                    margin-bottom: 1em
                }

                #right-person .notify-list-small>span {
                    left: 18px;
                    top: 2px
                }

                #right-person :root {
                    --swiper-theme-color: #007aff
                }

                #right-person :root {
                    --swiper-navigation-size: 44px
                }

                #right-person .file-title-skeleton {
                    padding: 0 24px;
                    line-height: 22px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between
                }

                #right-person .file-title-skeleton .title {
                    padding-right: 14px;
                    display: flex;
                    align-items: center
                }

                #right-person .file-title-skeleton .title .title-inner {
                    display: flex;
                    padding: 22px 0 12px
                }

                #right-person .file-title-skeleton .title .title-inner span {
                    font-family: PingFangSC-Medium, PingFang SC;
                    color: #2f343c;
                    font-weight: 500;
                    line-height: 21px;
                    font-size: 16px
                }

                #right-person .file-title-skeleton .action {
                    max-width: 200px;
                    display: flex;
                    align-items: center
                }

                #right-person .file-title-skeleton .action>button:last-child {
                    margin-left: 12px
                }

                #right-person .global-search-wrap .search-tag {
                    margin-left: 8px;
                    margin-bottom: 0 !important;
                    padding: 0 4px 0 8px;
                    border-radius: 3px;
                    background: #ebeef1;
                    display: flex;
                    align-items: center;
                    color: rgba(0, 0, 0, .7)
                }

                #right-person .global-search-wrap .search-tag>span>i {
                    color: #333;
                    font-size: 14px;
                    cursor: pointer
                }

                #right-person .space-skeleton-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px 24px;
                    overflow: hidden
                }

                #right-person .space-skeleton-header .space-skeleton-header-title {
                    height: 20px;
                    width: 160px
                }

                #right-person .space-skeleton-header .space-skeleton-header-search {
                    width: 633px;
                    min-width: 308px;
                    display: flex
                }

                #right-person .space-skeleton-header .space-skeleton-button * {
                    min-width: auto !important;
                    width: 100% !important;
                    height: 100% !important
                }

                #right-person .person-skeleton-box {
                    height: 100%;
                    background-position: 0 0;
                    background-size: 100%;
                    background-repeat: no-repeat;
                    display: flex;
                    flex-direction: column
                }

                #right-person .person-skeleton-box .person-skeleton-body {
                    height: 100%;
                    flex: 1;
                    overflow-y: hidden;
                    display: flex;
                    flex-direction: column;
                    position: relative
                }

                #right-person .person-skeleton-box .person-skeleton-body .person-skeleton-body-wrap {
                    height: 100%;
                    background: #fff;
                    border-radius: 8px;
                    margin: 0 24px
                }

                #right-person .person-skeleton-box .person-skeleton-body .person-skeleton-body-wrap .file-skeleton-box {
                    height: calc(100% - 51px) !important;
                    width: 100% !important;
                    overflow: hidden !important
                }

                #right-person .person-skeleton-box .person-skeleton-body .person-skeleton-body-wrap .file-header-skeleton-box {
                    min-width: 100% !important;
                    padding: 0 24px !important
                }

                #right-person .person-skeleton-box .person-skeleton-body .person-skeleton-body-wrap .file-body-skeleton-box {
                    height: calc(100% - 40px) !important;
                    min-width: 100% !important;
                    width: -webkit-max-content !important;
                    width: max-content !important;
                    overflow: hidden !important;
                    padding: 8px 24px 0 24px !important
                }

                #right-person .dk-icon-rongqi:before {
                    content: "e74e"
                }
            </style>
            <div id="right-person" class="right" style="display:none">
                <div class="header-wrap"></div>
                <div class="body-wrap">
                    <div class="person-skeleton-box">
                        <div class="space-skeleton-header">
                            <div class="space-skeleton-header-title space-skeleton-button">
                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                            </div>
                            <div class="space-skeleton-header-search">
                                <div class="global-search-wrap"><span class="ant-input-group-wrapper"><span class="ant-input-wrapper ant-input-group"><span class="ant-input-group-addon">
                                                <div class="search-before"><i class="dk-icon-sousuo dk-iconfont" style="color:#bbb"></i>
                                                    <p class="search-tag">当前空间 <span style="cursor:pointer;height:20px"><i class="dk-icon-guanbi dk-iconfont" style="font-size:20px"></i></span></p>
                                                </div>
                                            </span><span class="ant-input-affix-wrapper"><input autocomplete="off" placeholder="搜索文档标题、正文1" class="ant-input"> <span class="ant-input-suffix"><span class="ant-input-clear-icon ant-input-clear-icon-hidden" role="button" tabindex="-1"><span role="img" aria-label="close-circle" class="anticon anticon-close-circle"><svg fill-rule="evenodd" viewBox="64 64 896 896" focusable="false" data-icon="close-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                                <path d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"></path>
                                                            </svg> </span></span></span></span><span class="ant-input-group-addon"><span class="search-icon-btn"><span class="search-text">高级搜索</span>⌘+J</span></span></span></span></div>
                                <div class="cooper-customer-service"><span class="customer-service-bar"></span>
                                    <div class="newfeature-popup"><span><span class="cooper-customer-service-empty"></span></span></div>
                                    <div class="cooper-helper-wrap">
                                        <div class="cooper-helper"><i class="dk-iconfont dk-icon-gongnengshangxin"></i></div>
                                    </div>
                                    <div class="feedback-with-popup"><span>
                                            <div class="feedback">
                                                <div class="content"><i class="dk-iconfont dk-icon-lianxikefu"></i></div>
                                            </div>
                                        </span></div>
                                </div>
                            </div>
                        </div>
                        <div class="person-skeleton-body">
                            <div class="person-skeleton-body-wrap">
                                <div class="file-title-skeleton">
                                    <div class="title">
                                        <div class="title-inner"><span>全部文件</span></div>
                                    </div>
                                    <div class="action"><button type="button" class="ant-btn ant-btn-secondary"><i class="dk-iconfont dk-icon-shangchuan2"></i><span>上传</span></button><button type="button" class="ant-btn ant-btn-primary"><i class="dk-iconfont dk-icon-a-"></i><span>新建</span></button></div>
                                </div>
                                <style>
                                    .file-skeleton-box {
                                        margin: 0;
                                        padding: 0;
                                        min-width: 667px !important;
                                        margin: -8px auto 0 !important
                                    }

                                    .file-skeleton-box .file-header-skeleton-box {
                                        width: 100%;
                                        height: 36px;
                                        margin: 0;
                                        padding: 0;
                                        display: flex;
                                        align-items: center
                                    }

                                    .file-skeleton-box .header-inner {
                                        width: 100%;
                                        height: 22px;
                                        position: relative;
                                        margin: 0;
                                        padding: 0;
                                        display: flex;
                                        align-items: center;
                                        overflow: hidden
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-button {
                                        width: 42px;
                                        height: 14px
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-button * {
                                        min-width: auto !important;
                                        width: 100% !important;
                                        height: 100% !important
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-name {
                                        margin-right: 192px;
                                        flex: 1;
                                        min-width: 200px
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-owner {
                                        margin-right: 216px;
                                        min-width: 42px
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-address {
                                        margin-right: 216px;
                                        min-width: 42px
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-time {
                                        margin-right: 22px;
                                        min-width: 42px
                                    }

                                    .file-skeleton-box .file-body-skeleton-box {
                                        width: 100%;
                                        padding-top: 8px !important
                                    }

                                    .file-skeleton-box .body-inner>li {
                                        width: 100%;
                                        height: 46px;
                                        position: relative
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-button * {
                                        min-width: auto !important;
                                        width: 100% !important;
                                        height: 100% !important
                                    }

                                    .file-skeleton-box .body-inner li {
                                        display: flex;
                                        align-items: center
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-name-logo {
                                        width: 19.2px;
                                        height: 19.2px;
                                        min-width: 19.2px
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-name-title {
                                        height: 14px;
                                        flex: 1;
                                        margin-left: 10px;
                                        top: 17px;
                                        left: 32px;
                                        margin-right: 192px;
                                        min-width: 170px
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-name-title .ant-skeleton {
                                        max-width: 500px !important
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-owner {
                                        width: 66px;
                                        height: 14px;
                                        top: 17px;
                                        left: 544px;
                                        margin-right: 192px;
                                        min-width: 66px
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-address {
                                        width: 66px;
                                        height: 14px;
                                        top: 17px;
                                        left: 736px;
                                        margin-right: 192px;
                                        min-width: 66px
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-time {
                                        width: 66px;
                                        height: 14px;
                                        top: 17px;
                                        left: 928px min-width: 66px
                                    }
                                </style>
                                <div class="file-skeleton-box">
                                    <div class="file-header-skeleton-box">
                                        <div class="header-inner">
                                            <div class="file-header-skeleton-name">
                                                <div class="file-header-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </div>
                                            <div class="file-header-skeleton-owner file-header-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-header-skeleton-address file-header-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-header-skeleton-time file-header-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-body-skeleton-box">
                                        <div class="body-inner">
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="right-team" class="right" style="display:none">
                <div class="header-wrap">
                    <div class="header-warp">
                        <div class="header-top">
                            <div class="header-top-left">
                                <div class="global-search-wrap"><span class="ant-input-group-wrapper"><span class="ant-input-wrapper ant-input-group"><span class="ant-input-group-addon">
                                                <div class="search-before"><i class="dk-icon-sousuo dk-iconfont" style="color:#bbb"></i>
                                                    <p class="search-tag">全部团队空间 <span style="cursor:pointer;height:20px"><i class="dk-icon-guanbi dk-iconfont" style="font-size:20px"></i></span></p>
                                                </div>
                                            </span><span class="ant-input-affix-wrapper"><input autocomplete="off" placeholder="搜索文档标题、正文、空间/知识库名" class="ant-input"> <span class="ant-input-suffix"><span class="ant-input-clear-icon ant-input-clear-icon-hidden" role="button" tabindex="-1"><span role="img" aria-label="close-circle" class="anticon anticon-close-circle"><svg fill-rule="evenodd" viewBox="64 64 896 896" focusable="false" data-icon="close-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                                <path d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"></path>
                                                            </svg> </span></span></span></span><span class="ant-input-group-addon"><span class="search-icon-btn"><span class="search-text">高级搜索</span>⌘+J</span></span></span></span></div>
                            </div>
                            <div class="title-opt"><button type="button" class="ant-btn ant-btn-primary"><i class="dk-iconfont dk-icon-a-"></i><span>新建团队空间</span></button></div>
                            <div class="cooper-customer-service"><span class="customer-service-bar"></span>
                                <div class="newfeature-popup"><span><span class="cooper-customer-service-empty"></span></span></div>
                                <div class="cooper-helper-wrap">
                                    <div class="cooper-helper"><i class="dk-iconfont dk-icon-gongnengshangxin"></i></div>
                                </div>
                                <div class="feedback-with-popup"><span>
                                        <div class="feedback">
                                            <div class="content"><i class="dk-iconfont dk-icon-lianxikefu"></i></div>
                                        </div>
                                    </span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <style>
                    body {
                        height: 100%;
                        margin: 0;
                        font-size: 14px;
                        line-height: 22px;
                        overflow: auto
                    }

                    .home-wrap {
                        position: relative;
                        width: 100%;
                        height: 100%;
                        padding-bottom: 40px
                    }

                    .home-wrap .home-main {
                        min-width: 667px
                    }

                    .generic .home-wrap-os-flag,
                    .linux .home-wrap-os-flag,
                    .mac .home-wrap-os-flag {
                        overflow-y: auto
                    }

                    .right {
                        width: 100%;
                        min-width: 850px
                    }

                    .tab-top-title {
                        flex: 1
                    }

                    .tab-top-title .ant-tabs-content-top {
                        height: 100%
                    }

                    .tab-top-title .ant-tabs-nav {
                        margin-bottom: 15px
                    }

                    .tab-top-title .ant-tabs-ink-bar {
                        display: none !important
                    }

                    .tab-top-title .ant-tabs-tab-active {
                        position: relative
                    }

                    .tab-top-title .ant-tabs-tab-active:before {
                        content: "";
                        width: 24px;
                        height: 2px;
                        background-color: #047ffe;
                        left: calc(50% - 12px);
                        bottom: 0;
                        z-index: 100;
                        position: absolute
                    }

                    .tab-top-large .ant-tabs-tab {
                        font-size: 16px !important;
                        line-height: 24px !important;
                        padding-top: 4px !important;
                        padding-bottom: 6px !important
                    }

                    .icon-gengduo1 {
                        color: rgba(34, 42, 53, .5)
                    }

                    .v3-operate-menu {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        width: 24px;
                        height: 24px
                    }

                    .v3-operate-menu:hover {
                        background: rgba(0, 0, 0, .05);
                        border-radius: 4px
                    }

                    .v3-quick-access {
                        min-width: 667px;
                        margin: 0 auto 12px;
                        padding: 0 32px
                    }

                    .v3-quick-access-title {
                        font-family: PingFangSC-Medium;
                        font-size: 16px;
                        font-weight: 400;
                        color: #222a35;
                        top: 0;
                        position: -webkit-sticky;
                        position: sticky;
                        z-index: 1;
                        height: 24px;
                        background-color: #fff;
                        display: block;
                        margin-bottom: 0;
                        padding: 0 0 12px;
                        box-sizing: content-box
                    }

                    .container {
                        width: 100%;
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: flex-start
                    }

                    .recent-activity {
                        margin: 0 auto;
                        min-width: 667px
                    }

                    .recent-activity .ant-tabs-nav {
                        padding: 0 32px 2px;
                        position: -webkit-sticky !important;
                        position: sticky !important;
                        top: 0;
                        z-index: 2
                    }

                    .file-ellipsis {
                        padding-right: 4px;
                        display: flex;
                        color: #2f343c;
                        align-items: center;
                        overflow: hidden;
                        height: 22px;
                        padding-left: 4px;
                        width: 100%;
                        cursor: pointer
                    }

                    .file-ellipsis .file-ellipsis-inner-span {
                        white-space: nowrap;
                        vertical-align: middle;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: inline-block;
                        padding: 0 2px;
                        height: 22px;
                        line-height: 22px;
                        color: #222a35
                    }

                    .file-ellipsis .link-tag {
                        font-family: PingFangSC-Medium;
                        display: inline-block;
                        background: rgba(4, 127, 254, .1);
                        border-radius: 4px;
                        text-align: center;
                        padding: 0 8px;
                        color: #047ffe;
                        font-size: 22px;
                        -webkit-transform: scale(.5);
                        transform: scale(.5);
                        box-sizing: content-box;
                        height: 36px;
                        line-height: 36px;
                        margin-left: -22px;
                        margin-right: -26px
                    }

                    .file-name-list {
                        width: 24px;
                        height: 24px;
                        vertical-align: middle;
                        margin-right: 4px;
                        -o-object-fit: cover;
                        object-fit: cover;
                        -o-object-position: left center;
                        object-position: left center;
                        overflow: hidden;
                        display: inline-block;
                        line-height: 1
                    }

                    .file-name-list svg {
                        max-width: 100%;
                        max-height: 100%
                    }

                    .cooper-list-content {
                        min-width: 667px;
                        margin: -8px auto 0
                    }

                    .cooper-list-content .folder-tree {
                        height: 100%;
                        width: 100%;
                        position: relative
                    }

                    .cooper-list-content .tb-header {
                        font-size: 14px;
                        font-weight: 500;
                        color: #656a72;
                        position: -webkit-sticky;
                        position: sticky;
                        top: 36px;
                        background-color: #fff;
                        padding-left: 29px;
                        padding-right: 32px;
                        z-index: 4
                    }

                    .cooper-list-content .tb-header .tb-header-div {
                        height: 36px !important;
                        line-height: 36px !important;
                        border-bottom: 1px solid rgba(34, 42, 53, .08);
                        position: relative
                    }

                    .cooper-list-content .tb-header .tb-header-div .file-name {
                        position: relative;
                        overflow: visible;
                        display: flex;
                        align-items: center
                    }

                    .cooper-list-content .tb-header .tb-header-div .file-handle {
                        cursor: pointer;
                        height: 26px;
                        padding-left: 4px;
                        line-height: 24px;
                        background-color: #fff;
                        border-radius: 4px;
                        display: flex;
                        align-items: center
                    }

                    .cooper-list-content .tb-header .tb-header-div .file-checked {
                        display: flex;
                        align-items: center;
                        background-color: rgba(4, 127, 254, .1);
                        color: #047ffe
                    }

                    .cooper-list-content .tb-header .tb-header-div .file-checked .icon-shaixuan2 {
                        padding: 2px 2px 0;
                        font-size: 14px
                    }

                    .cooper-list-content .tb-body>li,
                    .cooper-list-content .tb-header>.tb-header-div {
                        height: 48px;
                        line-height: 48px;
                        width: 100%;
                        white-space: nowrap;
                        display: flex;
                        align-items: center;
                        color: rgba(34, 42, 53, .7)
                    }

                    .cooper-list-content .tb-body>li>span,
                    .cooper-list-content .tb-header>.tb-header-div>span {
                        display: inline-block
                    }

                    .cooper-list-content .tb-body>li>.file-name,
                    .cooper-list-content .tb-header>.tb-header-div>.file-name {
                        max-width: none;
                        min-width: 250px;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        flex: 3;
                        cursor: pointer;
                        padding-right: 12px
                    }

                    .cooper-list-content .tb-body>li .file-content-box,
                    .cooper-list-content .tb-header>.tb-header-div .file-content-box {
                        margin-right: 12px;
                        width: 100%;
                        height: 100%;
                        cursor: pointer;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis
                    }

                    .cooper-list-content .tb-body>li .file-owner-content-box,
                    .cooper-list-content .tb-header>.tb-header-div .file-owner-content-box {
                        padding-left: 4px
                    }

                    .cooper-list-content .tb-body>li>.file-address-,
                    .cooper-list-content .tb-header>.tb-header-div>.file-address- {
                        height: 100%;
                        width: 300px;
                        min-width: 200px;
                        display: flex;
                        align-items: center;
                        flex: 1;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis
                    }

                    .cooper-list-content .tb-body>li>.file-address- .file-content-box,
                    .cooper-list-content .tb-header>.tb-header-div>.file-address- .file-content-box {
                        padding: 0 2px;
                        height: 22px;
                        border-radius: 4px;
                        line-height: 22px;
                        cursor: pointer;
                        width: auto
                    }

                    .cooper-list-content .tb-body>li>.file-owner,
                    .cooper-list-content .tb-body>li>.file-time,
                    .cooper-list-content .tb-header>.tb-header-div>.file-owner,
                    .cooper-list-content .tb-header>.tb-header-div>.file-time {
                        height: 100%;
                        width: 130px;
                        min-width: 130px;
                        display: flex;
                        align-items: center;
                        flex: 1;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        padding-right: 12px
                    }

                    .cooper-list-content .tb-body>li>.file-owner .file-content-box,
                    .cooper-list-content .tb-body>li>.file-time .file-content-box,
                    .cooper-list-content .tb-header>.tb-header-div>.file-owner .file-content-box,
                    .cooper-list-content .tb-header>.tb-header-div>.file-time .file-content-box {
                        cursor: default
                    }

                    .cooper-list-content .tb-body>li .file-owner,
                    .cooper-list-content .tb-header>.tb-header-div .file-owner {
                        width: 150px;
                        min-width: 150px
                    }

                    .cooper-list-content .tb-body>li>.file-operate,
                    .cooper-list-content .tb-header>.tb-header-div>.file-operate {
                        width: 80px;
                        height: 100%;
                        display: flex;
                        justify-content: right;
                        align-items: center
                    }

                    .cooper-list-content .tb-body>li>.file-owner,
                    .cooper-list-content .tb-header>.tb-header-div>.file-owner {
                        min-width: 150px;
                        overflow: hidden
                    }

                    .cooper-list-content .tb-body>li>.file-address-,
                    .cooper-list-content .tb-header>.tb-header-div>.file-address- {
                        min-width: 130px
                    }

                    .cooper-list-content .tb-body {
                        padding-left: 27px;
                        padding-right: 28px;
                        padding-top: 8px
                    }

                    .cooper-list-content .tb-body>li {
                        padding-left: 4px;
                        padding-right: 4px
                    }

                    .cooper-list-content .tb-body>li .file-name {
                        color: #222a35
                    }

                    .cooper-recent-table-loading {
                        position: relative;
                        margin-top: 16px;
                        width: 100%;
                        text-align: center;
                        font-size: 14px;
                        color: rgba(34, 42, 53, .7)
                    }

                    .v3-card {
                        flex: 1;
                        height: 46px;
                        border-radius: 4px;
                        margin: 0 12px 10px 0;
                        border: 1px solid #f2f3f3;
                        background-color: #fff;
                        padding: 12px;
                        box-sizing: border-box;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        position: relative;
                        width: calc(25% - 9px);
                        min-width: calc(25% - 9px);
                        max-width: calc(25% - 9px)
                    }

                    .v3-card .v3-card-img {
                        width: 24px !important;
                        height: 24px !important;
                        overflow: hidden;
                        display: inline-block;
                        margin: 0 8px 0 4px
                    }

                    .v3-card .v3-card-img svg {
                        max-width: 100%;
                        max-height: 100%
                    }

                    .v3-card>img {
                        width: 24px;
                        height: 24px;
                        -o-object-fit: cover;
                        object-fit: cover;
                        margin: 0 8px 0 4px
                    }

                    .v3-card .v3-card-text- {
                        display: inline-block;
                        vertical-align: middle;
                        flex: 1;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        color: #222a35
                    }

                    .v3-card .operate-tag {
                        width: 18px;
                        text-align: center;
                        display: none
                    }

                    .v3-card:before {
                        display: none;
                        font-family: dk-iconfont !important;
                        content: "e6af";
                        color: #999;
                        position: absolute;
                        left: 3px;
                        top: center;
                        font-size: 12px
                    }

                    .v3-card:hover {
                        border: 1px solid #047ffe
                    }

                    .v3-card:hover .operate-tag {
                        display: inline-block
                    }

                    .v3-card:hover:before {
                        display: block
                    }

                    .global-search-wrap {
                        height: 32px;
                        line-height: 32px;
                        width: 100%;
                        position: relative
                    }

                    .global-search-wrap .search-before {
                        display: flex;
                        align-items: center;
                        background: 0 0;
                        height: 24px;
                        line-height: 24px;
                        color: rgba(34, 42, 53, .3);
                        border: none;
                        padding-left: 3px;
                        font-size: 14px
                    }

                    .global-search-wrap .search-tag {
                        margin-left: 8px;
                        padding: 0 4px 0 8px;
                        border-radius: 3px;
                        background: #ebeef1;
                        display: flex;
                        align-items: center;
                        color: rgba(0, 0, 0, .7)
                    }

                    .global-search-wrap .search-icon-btn {
                        display: block;
                        align-items: center;
                        line-height: 14px;
                        font-size: 12px;
                        border: 1px solid rgba(34, 42, 53, .2);
                        padding: 2px 6px;
                        border-radius: 4px;
                        margin-right: 4px;
                        color: rgba(0, 0, 0, .5);
                        cursor: pointer
                    }

                    .global-search-wrap .search-icon-btn .search-text {
                        margin-right: 5px
                    }

                    .global-search-wrap .ant-input-affix-wrapper {
                        height: 32px
                    }

                    .global-search-wrap .ant-input-group-addon {
                        border-radius: 4px
                    }

                    .cooper-notify {
                        display: inline-block;
                        position: relative;
                        cursor: pointer;
                        text-align: center;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 18px
                    }

                    .cooper-notify .dk-icon-zhanneixin {
                        font-size: 16px;
                        color: #6a707c;
                        width: 24px;
                        height: 24px;
                        line-height: 24px
                    }

                    .cooper-notify-count {
                        position: absolute;
                        top: -6px;
                        left: 10px;
                        background-color: #ff563a;
                        border: 1px solid #fff;
                        color: #fff;
                        display: inline-block;
                        min-width: 16px;
                        height: 16px;
                        font-size: 12px;
                        line-height: 14px;
                        border-radius: 8px;
                        text-align: center;
                        cursor: pointer;
                        padding: 0 4px;
                        z-index: 100
                    }

                    .notify-list-small {
                        line-height: 40px;
                        text-align: center;
                        margin-right: 0;
                        margin-top: 5px
                    }

                    .notify-list-small .dk-icon-zhanneixin {
                        font-size: 18px;
                        width: 40px;
                        height: 40px;
                        line-height: 40px
                    }

                    .icon-user {
                        cursor: pointer;
                        height: 24px;
                        line-height: 24px;
                        text-align: center;
                        width: 24px;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 16px;
                        color: #6a707c;
                        margin-right: 18px;
                        background-repeat: no-repeat;
                        background-size: 100%;
                        background-position: 50%;
                        border-radius: 50%;
                        background-image: url(https://img-hxy021.didistatic.com/static/iportal/upload/d0aba0cdadd283b84859bb51bd5995d3.jpg)
                    }

                    .icon-user-small {
                        background-image: url(https://img-hxy021.didistatic.com/static/iportal/upload/d0aba0cdadd283b84859bb51bd5995d3.jpg);
                        cursor: pointer;
                        height: 28px;
                        line-height: 40px;
                        text-align: center;
                        width: 22px;
                        border-radius: 4px;
                        justify-content: center;
                        font-size: 18px;
                        color: #6a707c;
                        margin-right: 0;
                        margin-top: 5px;
                        border-radius: 50%;
                        background-size: contain
                    }

                    .icon-shiyanshi-small {
                        cursor: pointer;
                        height: 40px;
                        line-height: 40px;
                        text-align: center;
                        width: 40px;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 16px;
                        color: #6a707c;
                        margin-right: 0;
                        font-size: 18px
                    }

                    .icon-shiyanshi {
                        cursor: pointer;
                        height: 24px;
                        line-height: 24px;
                        text-align: center;
                        width: 24px;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 16px;
                        color: #6a707c
                    }

                    .tip-btn {
                        position: relative;
                        background: rgba(255, 165, 12, .1);
                        border-radius: 4px;
                        border: .5px solid rgba(255, 165, 12, .18);
                        font-family: PingFangSC-Medium;
                        font-size: 11px;
                        font-weight: 400;
                        color: #faa006;
                        display: flex;
                        align-items: center;
                        height: 24px;
                        line-height: 24px;
                        width: 70px;
                        text-align: center;
                        cursor: auto;
                        overflow: hidden
                    }

                    .tip-btn-long {
                        margin-left: 0
                    }

                    .tip-btn .cooper-tip-name {
                        font-size: 22px;
                        -webkit-transform: scale(.5);
                        transform: scale(.5);
                        width: 100%;
                        white-space: nowrap;
                        -webkit-transform-origin: left;
                        transform-origin: left;
                        margin-left: 4px;
                        overflow: hidden;
                        text-overflow: ellipsis
                    }

                    .tip-btn>span {
                        height: 24px;
                        line-height: 24px;
                        display: flex;
                        align-items: center;
                        justify-content: center
                    }

                    .tip-btn>span>img {
                        width: 12px;
                        height: 12px;
                        margin-left: 2px
                    }

                    .logo .logo-large {
                        width: 158px;
                        margin: 10px 16px 0;
                        padding: 0 12px
                    }

                    .logo .logo-small {
                        width: 36px;
                        margin: 10px 8px;
                        position: relative;
                        left: 4px
                    }

                    .aside-large-wrap {
                        width: 240px;
                        opacity: 1
                    }

                    .aside-small-wrap {
                        width: 64px;
                        opacity: 1
                    }

                    .aside {
                        display: flex;
                        flex-direction: column;
                        padding-top: 10px
                    }

                    .aside .aside-content {
                        position: relative;
                        margin-top: 20px;
                        flex: 1
                    }

                    .aside .item-name {
                        width: 100%;
                        color: #222a35;
                        margin-bottom: 2px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        padding: 0 8px
                    }

                    .aside-operate .item-name-normal {
                        padding: 12px 8px 12px 12px;
                        width: 100%;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        border-radius: 4px;
                        cursor: pointer
                    }

                    .aside .item-container {
                        display: flex;
                        align-items: center;
                        width: auto
                    }

                    .aside .item-small-container {
                        display: flex;
                        align-items: center;
                        width: 100%;
                        justify-content: space-around;
                        flex-direction: column
                    }

                    .aside .item-small-container>i {
                        padding-top: 4px
                    }

                    .aside .item-small-container>span {
                        width: 200%;
                        overflow: hidden;
                        text-align: center;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        padding: 0 4px
                    }

                    .aside .aside-icon {
                        color: #6a707c
                    }

                    .aside .item-text {
                        padding-left: 12px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis
                    }

                    .aside .item-name-active {
                        color: #047ffe;
                        background-color: #e5f2fe;
                        font-weight: 500
                    }

                    .aside .item-name-active .aside-icon {
                        color: #047ffe
                    }

                    .left-action {
                        width: 240px;
                        height: 56px;
                        align-items: center;
                        z-index: 2;
                        margin-left: 22px
                    }

                    .left-action,
                    .left-action-small {
                        background-color: #f7f9fa;
                        display: flex;
                        justify-content: flex-start
                    }

                    .left-action-small {
                        width: 64px;
                        height: 145px;
                        flex-direction: column;
                        padding: 19px 0;
                        margin-left: 0
                    }

                    .icon-aside-large {
                        font-size: 18px;
                        line-height: 20px
                    }

                    .space-content-wrap {
                        width: 100%;
                        font-size: 13px;
                        line-height: 21px
                    }

                    .space-content-wrap .space-item {
                        width: 100%;
                        color: #222a35;
                        margin-bottom: 2px
                    }

                    .space-content-wrap .space-item .wrap {
                        width: 100%
                    }

                    .space-content-wrap .space-item .space-item-normal {
                        padding: 7px 12px;
                        width: 100%;
                        display: inline-block;
                        border-radius: 4px
                    }

                    .space-content-wrap .space-item .space-item-normal .content-wrap {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis
                    }

                    .space-content-wrap .space-item .space-item-normal .content-wrap .left-wrap-Fig {
                        display: flex;
                        align-items: center;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis
                    }

                    .space-content-wrap .space-item .space-item-normal .content-wrap .left-wrap-Fig .icon-dian {
                        font-size: 20px;
                        line-height: 14px;
                        color: #d8dee3
                    }

                    .space-content-wrap .space-item .space-item-normal .content-wrap .left-wrap-Fig .item-text {
                        padding-left: 4px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis
                    }

                    .space-content-wrap .space-item .space-item-normal .content-wrap .icon-pin {
                        display: none;
                        font-size: 16px;
                        line-height: 16px;
                        padding: 2px;
                        color: #6a707c;
                        border-radius: 4px
                    }

                    .large-wrap .aside-large {
                        width: 240px
                    }

                    .large-wrap .aside-large .item-name-normal {
                        padding: 7px 24px
                    }

                    .large-wrap .aside-large .item-name {
                        height: 34px
                    }

                    .large-wrap .aside-large .split-line {
                        width: 100%;
                        height: 1px;
                        background-color: #ebeef1;
                        margin-top: 4px;
                        margin-bottom: 6px
                    }

                    .large-wrap .pine-space {
                        width: 100%;
                        padding: 0 8px
                    }

                    .large-wrap .pine-space .space-item-nav {
                        padding-left: 46px !important
                    }

                    .aside-small-box {
                        width: 64px;
                        flex: 1
                    }

                    .aside-small {
                        height: 100%;
                        padding: 10px 2px 0 !important
                    }

                    .item-name {
                        margin-bottom: 2px
                    }

                    .item-name-6 {
                        margin-bottom: 6px !important
                    }

                    .aside-small .item-name-normal {
                        height: auto !important;
                        padding: 4px 0 !important;
                        width: 100%;
                        display: flex;
                        align-items: center;
                        border-radius: 4px;
                        cursor: pointer
                    }

                    .small-name {
                        font-size: 22px !important;
                        -webkit-transform: scale(.5);
                        transform: scale(.5)
                    }

                    .header-top-left {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-basis: 540px;
                        flex-grow: 0;
                        flex-shrink: 0
                    }

                    @media screen and (max-width:1200px) {
                        .header-top-left {
                            flex-basis: 445px
                        }
                    }

                    @media screen and (max-width:1020px) {
                        .header-top-left {
                            flex-basis: 360px
                        }
                    }

                    .header-warp {
                        padding: 0 32px;
                        margin: 0 auto;
                        min-width: 667px
                    }

                    .header-warp .header-top {
                        margin: 0 auto;
                        min-width: 667px;
                        height: 72px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between
                    }

                    .header-warp .header-top .title-opt {
                        min-width: 192px;
                        text-align: right;
                        flex: 1
                    }

                    .header-warp .header-top .title-opt>button {
                        margin-left: 12px
                    }

                    .header-warp .header-top .title-opt span {
                        font-size: 14px
                    }

                    .header-warp .cooper-customer-service {
                        display: flex;
                        justify-content: right;
                        align-items: center;
                        margin-left: 20px
                    }

                    .header-warp .customer-service-bar {
                        line-height: 18px;
                        height: 18px;
                        margin-right: 10px;
                        border-left: 1px solid #e8e9ea
                    }

                    .header-warp .cooper-helper {
                        width: 26px;
                        height: 26px;
                        line-height: 26px;
                        text-align: center;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        justify-content: center
                    }

                    .header-warp .feedback-with-popup {
                        margin-left: 10px
                    }

                    .header-warp .feedback .content {
                        width: 26px;
                        height: 26px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 4px;
                        text-align: center
                    }

                    .header-warp .dk-icon-gongnengshangxin {
                        font-size: 18px !important
                    }

                    .header-warp .dk-icon-lianxikefu {
                        font-size: 18px !important
                    }

                    .space-skeleton-header-search .cooper-customer-service {
                        display: flex;
                        justify-content: right;
                        align-items: center;
                        margin-left: 20px
                    }

                    .space-skeleton-header-search .customer-service-bar {
                        line-height: 18px;
                        height: 18px;
                        margin-right: 10px;
                        border-left: 1px solid #e8e9ea
                    }

                    .space-skeleton-header-search .cooper-helper {
                        width: 26px;
                        height: 26px;
                        line-height: 26px;
                        text-align: center;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        justify-content: center
                    }

                    .space-skeleton-header-search .feedback-with-popup {
                        margin-left: 10px
                    }

                    .space-skeleton-header-search .feedback .content {
                        width: 26px;
                        height: 26px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 4px;
                        text-align: center
                    }

                    .space-skeleton-header-search .dk-icon-gongnengshangxin {
                        font-size: 18px !important
                    }

                    .space-skeleton-header-search .dk-icon-lianxikefu {
                        font-size: 18px !important
                    }

                    .layout,
                    .layout .left-wrap {
                        display: flex;
                        height: 100%;
                        position: relative
                    }

                    .layout .left-wrap {
                        z-index: 106
                    }

                    .layout .left-wrap .left {
                        height: 100%;
                        background-color: #f7f9fa;
                        position: relative;
                        display: flex;
                        transition: all 2s ease;
                        overflow-x: hidden;
                        overflow-y: overlay;
                        overflow-y: auto
                    }

                    .layout .left-wrap .aside-btn-handle {
                        width: 20px;
                        height: 100%;
                        cursor: pointer;
                        position: absolute;
                        right: -20px;
                        top: 0;
                        background-color: transparent
                    }

                    .layout .right {
                        flex: 1;
                        overflow: auto;
                        display: flex;
                        flex-direction: column
                    }

                    .layout .right .body-wrap {
                        flex: 1;
                        overflow-y: hidden;
                        min-width: 737px
                    }

                    .aside-operate {
                        width: 14px;
                        height: 56px;
                        border-radius: 64px;
                        display: flex;
                        border: 1px solid #ebeef1;
                        visibility: hidden;
                        align-items: center;
                        justify-content: center;
                        position: relative;
                        top: 69px;
                        right: 8px;
                        background-color: #fff;
                        z-index: 999;
                        cursor: pointer
                    }

                    .aside-operate .operate-icon {
                        color: rgba(34, 42, 53, .3);
                        font-size: 14px
                    }

                    .v3-cooper-helper {
                        cursor: pointer;
                        height: 42px;
                        line-height: 42px;
                        text-align: center;
                        width: 42px;
                        border-radius: 100%;
                        background-color: #fff;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border: 1px solid #ebeef1;
                        box-shadow: 0 6px 16px 0 rgba(0, 0, 0, .1)
                    }

                    .v3-cooper-helper .dk-icon-a-bangzhuzhongxin4px {
                        font-size: 18px;
                        color: #6a707c
                    }

                    .global-utils-warp {
                        position: fixed;
                        right: 24px;
                        bottom: 40px;
                        z-index: 1000
                    }

                    .cooper-upload-list-up .cooper-upload-list-close {
                        display: none
                    }

                    .cooper-upload-list-up .drag-modal {
                        position: fixed;
                        top: 0;
                        right: 0;
                        z-index: 99;
                        bottom: 0;
                        left: 0;
                        background-color: rgba(0, 0, 0, .5);
                        font-size: 36px;
                        display: none;
                        justify-content: center;
                        align-items: center;
                        color: #fff;
                        border: 4px dashed #fff
                    }

                    .cooper-upload-list-up .cooper-upload-list {
                        position: fixed;
                        bottom: 12px;
                        right: 52px;
                        background-color: #fff;
                        width: 480px;
                        border: 1px solid rgba(148, 160, 176, .14);
                        box-sizing: border-box;
                        box-shadow: 0 4px 16px 0 rgba(47, 52, 60, .2);
                        border-radius: 4px;
                        z-index: 20
                    }

                    .tip-btn-small {
                        cursor: auto;
                        width: 28px;
                        height: 28px;
                        background: rgba(255, 165, 12, .1);
                        border: .7px solid rgba(255, 165, 12, .18);
                        border-radius: 5.6px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-bottom: 12px
                    }

                    .tip-btn-small .cooper-tip-name {
                        display: none
                    }

                    .tip-btn-small>span img {
                        width: 17px;
                        height: 17px;
                        margin: 0
                    }

                    button,
                    input,
                    optgroup,
                    select,
                    textarea {
                        font-size: 100%;
                        line-height: 1.15
                    }

                    [type=button],
                    [type=reset],
                    [type=submit],
                    button {
                        -webkit-appearance: button
                    }

                    body,
                    html {
                        width: 100%;
                        height: 100%
                    }

                    *,
                    :after,
                    :before {
                        box-sizing: border-box
                    }

                    html {
                        font-family: sans-serif;
                        line-height: 1.15;
                        -webkit-text-size-adjust: 100%;
                        -ms-text-size-adjust: 100%;
                        -ms-overflow-style: scrollbar;
                        -webkit-tap-highlight-color: transparent
                    }

                    body {
                        margin: 0;
                        color: #222a35;
                        font-size: 14px;
                        font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Tahoma, Arial, "PingFang SC", "Microsoft YaHei", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                        font-variant: tabular-nums;
                        line-height: 1.5715;
                        background-color: #fff;
                        -webkit-font-feature-settings: "tnum";
                        font-feature-settings: "tnum"
                    }

                    input[type=number],
                    input[type=password],
                    input[type=text],
                    textarea {
                        -webkit-appearance: none
                    }

                    dl,
                    ol,
                    ul {
                        margin-top: 0;
                        margin-bottom: 1em
                    }

                    ol ol,
                    ol ul,
                    ul ol,
                    ul ul {
                        margin-bottom: 0
                    }

                    a {
                        color: #047ffe;
                        text-decoration: none;
                        background-color: transparent;
                        outline: 0;
                        cursor: pointer;
                        transition: color .3s;
                        -webkit-text-decoration-skip: objects
                    }

                    img {
                        vertical-align: middle;
                        border-style: none
                    }

                    [role=button],
                    a,
                    area,
                    button,
                    input:not([type=range]),
                    label,
                    select,
                    summary,
                    textarea {
                        touch-action: manipulation
                    }

                    button,
                    input,
                    optgroup,
                    select,
                    textarea {
                        margin: 0;
                        color: inherit;
                        font-size: inherit;
                        font-family: inherit;
                        line-height: inherit
                    }

                    button,
                    input {
                        overflow: visible
                    }

                    button,
                    select {
                        text-transform: none
                    }

                    [type=reset],
                    [type=submit],
                    button,
                    html [type=button] {
                        -webkit-appearance: button
                    }

                    .anticon {
                        display: inline-block;
                        color: inherit;
                        font-style: normal;
                        line-height: 0;
                        text-align: center;
                        text-transform: none;
                        vertical-align: -.125em;
                        text-rendering: optimizelegibility;
                        -webkit-font-smoothing: antialiased;
                        -moz-osx-font-smoothing: grayscale
                    }

                    .anticon>* {
                        line-height: 1
                    }

                    .anticon svg {
                        display: inline-block
                    }

                    .anticon-spin,
                    .anticon-spin:before {
                        display: inline-block;
                        -webkit-animation: loadingCircle 1s linear infinite;
                        animation: loadingCircle 1s linear infinite
                    }

                    html {
                        -wave-shadow-color: #047ffe;
                        -bar: 0
                    }

                    .ant-btn {
                        line-height: 1.5715;
                        position: relative;
                        display: inline-block;
                        font-weight: 400;
                        white-space: nowrap;
                        text-align: center;
                        background-image: none;
                        box-shadow: 0 2px 0 rgba(0, 0, 0, .015);
                        cursor: pointer;
                        transition: all .3s cubic-bezier(.645, .045, .355, 1);
                        -webkit-user-select: none;
                        -ms-user-select: none;
                        user-select: none;
                        touch-action: manipulation;
                        height: 32px;
                        padding: 4px 15px;
                        font-size: 14px;
                        border-radius: 2px;
                        color: #222a35;
                        border: 1px solid rgba(34, 42, 53, .08);
                        background: #fff
                    }

                    .ant-btn,
                    .ant-btn:active,
                    .ant-btn:focus {
                        outline: 0
                    }

                    .ant-btn>span {
                        display: inline-block
                    }

                    .ant-btn-primary {
                        color: #fff;
                        border-color: #047ffe;
                        background: #047ffe;
                        text-shadow: 0 -1px 0 rgba(0, 0, 0, .12);
                        box-shadow: 0 2px 0 rgba(0, 0, 0, .045)
                    }

                    .ant-btn:before {
                        position: absolute;
                        top: -1px;
                        right: -1px;
                        bottom: -1px;
                        left: -1px;
                        z-index: 1;
                        display: none;
                        background: #fff;
                        border-radius: inherit;
                        opacity: .35;
                        transition: opacity .2s;
                        content: "";
                        pointer-events: none
                    }

                    .ant-input-affix-wrapper {
                        position: relative;
                        display: inline-block;
                        width: 100%;
                        min-width: 0;
                        padding: 4px 11px;
                        color: #222a35;
                        font-size: 14px;
                        line-height: 1.5715;
                        background-color: #fff;
                        background-image: none;
                        border: 1px solid rgba(34, 42, 53, .08);
                        border-radius: 2px;
                        transition: all .3s;
                        display: inline-flex
                    }

                    .ant-input-affix-wrapper>input.ant-input {
                        padding: 0;
                        border: none;
                        outline: 0
                    }

                    .ant-input-affix-wrapper:before {
                        width: 0;
                        visibility: hidden;
                        content: "a0"
                    }

                    .ant-input-prefix,
                    .ant-input-suffix {
                        display: flex;
                        flex: none;
                        align-items: center
                    }

                    .ant-input-suffix {
                        margin-left: 4px
                    }

                    .ant-input-clear-icon,
                    .anticon.ant-input-clear-icon {
                        margin: 0;
                        color: rgba(0, 0, 0, .25);
                        font-size: 12px;
                        vertical-align: -1px;
                        cursor: pointer;
                        transition: color .3s
                    }

                    .ant-input-clear-icon-hidden,
                    .anticon.ant-input-clear-icon-hidden {
                        visibility: hidden
                    }

                    .ant-input {
                        box-sizing: border-box;
                        margin: 0;
                        font-variant: tabular-nums;
                        list-style: none;
                        -webkit-font-feature-settings: "tnum";
                        font-feature-settings: "tnum";
                        position: relative;
                        display: inline-block;
                        width: 100%;
                        min-width: 0;
                        padding: 4px 11px;
                        color: #222a35;
                        font-size: 14px;
                        line-height: 1.5715;
                        background-color: #fff;
                        background-image: none;
                        border: 1px solid rgba(34, 42, 53, .08);
                        border-radius: 2px;
                        transition: all .3s
                    }

                    .ant-input::-webkit-input-placeholder {
                        color: #bfbfbf;
                        -webkit-user-select: none;
                        user-select: none
                    }

                    .ant-input::placeholder {
                        color: #bfbfbf;
                        -webkit-user-select: none;
                        -ms-user-select: none;
                        user-select: none
                    }

                    .ant-input:placeholder-shown {
                        text-overflow: ellipsis
                    }

                    .ant-input-group {
                        box-sizing: border-box;
                        margin: 0;
                        padding: 0;
                        color: #222a35;
                        font-size: 14px;
                        font-variant: tabular-nums;
                        line-height: 1.5715;
                        list-style: none;
                        -webkit-font-feature-settings: "tnum";
                        font-feature-settings: "tnum";
                        position: relative;
                        display: table;
                        width: 100%;
                        border-collapse: separate;
                        border-spacing: 0
                    }

                    .ant-input-group-addon,
                    .ant-input-group-wrap,
                    .ant-input-group>.ant-input {
                        display: table-cell
                    }

                    .ant-input-group-addon,
                    .ant-input-group-wrap {
                        width: 1px;
                        white-space: nowrap;
                        vertical-align: middle
                    }

                    .ant-input-group .ant-input {
                        float: left;
                        width: 100%;
                        margin-bottom: 0;
                        text-align: inherit
                    }

                    .ant-input-group-addon {
                        position: relative;
                        padding: 0 11px;
                        color: #222a35;
                        font-weight: 400;
                        font-size: 14px;
                        text-align: center;
                        background-color: #fafafa;
                        border: 1px solid rgba(34, 42, 53, .08);
                        border-radius: 2px;
                        transition: all .3s
                    }

                    .ant-input-group-addon:first-child,
                    .ant-input-group-addon:first-child .ant-select .ant-select-selector,
                    .ant-input-group>.ant-input:first-child,
                    .ant-input-group>.ant-input:first-child .ant-select .ant-select-selector {
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0
                    }

                    .ant-input-group>.ant-input-affix-wrapper:not(:first-child) .ant-input {
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0
                    }

                    .ant-input-group>.ant-input-affix-wrapper:not(:last-child) .ant-input {
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0
                    }

                    .ant-input-group-addon:first-child {
                        border-right: 0
                    }

                    .ant-input-group-addon:last-child {
                        border-left: 0
                    }

                    .ant-input-group-addon:last-child,
                    .ant-input-group-addon:last-child .ant-select .ant-select-selector,
                    .ant-input-group>.ant-input:last-child,
                    .ant-input-group>.ant-input:last-child .ant-select .ant-select-selector {
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0
                    }

                    .ant-input-group .ant-input-affix-wrapper:not(:last-child) {
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0
                    }

                    .ant-input-group .ant-input-affix-wrapper:not(:first-child),
                    .ant-input-search .ant-input-group .ant-input-affix-wrapper:not(:first-child) {
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0
                    }

                    .ant-input-group-wrapper {
                        display: inline-block;
                        width: 100%;
                        text-align: start;
                        vertical-align: top
                    }

                    .ant-skeleton {
                        display: table;
                        width: 100%
                    }

                    .ant-skeleton-active .ant-skeleton-avatar,
                    .ant-skeleton-active .ant-skeleton-button,
                    .ant-skeleton-active .ant-skeleton-image,
                    .ant-skeleton-active .ant-skeleton-input,
                    .ant-skeleton-active .ant-skeleton-paragraph>li,
                    .ant-skeleton-active .ant-skeleton-title {
                        position: relative;
                        z-index: 0;
                        overflow: hidden;
                        background: 0 0
                    }

                    @-webkit-keyframes ant-skeleton-loading {
                        0% {
                            transform: translateX(-37.5%)
                        }

                        to {
                            transform: translateX(37.5%)
                        }
                    }

                    @keyframes ant-skeleton-loading {
                        0% {
                            transform: translateX(-37.5%)
                        }

                        to {
                            transform: translateX(37.5%)
                        }
                    }

                    .ant-skeleton-active .ant-skeleton-avatar:after,
                    .ant-skeleton-active .ant-skeleton-button:after,
                    .ant-skeleton-active .ant-skeleton-image:after,
                    .ant-skeleton-active .ant-skeleton-input:after,
                    .ant-skeleton-active .ant-skeleton-paragraph>li:after,
                    .ant-skeleton-active .ant-skeleton-title:after {
                        position: absolute;
                        top: 0;
                        right: -150%;
                        bottom: 0;
                        left: -150%;
                        background: linear-gradient(90deg, hsla(0, 0%, 74.5%, .2) 25%, hsla(0, 0%, 50.6%, .24) 37%, hsla(0, 0%, 74.5%, .2) 63%);
                        -webkit-animation: ant-skeleton-loading 1.4s ease infinite;
                        animation: ant-skeleton-loading 1.4s ease infinite;
                        content: ""
                    }

                    .ant-skeleton-element {
                        display: inline-block;
                        width: auto
                    }

                    .ant-skeleton-element .ant-skeleton-button {
                        display: inline-block;
                        vertical-align: top;
                        background: hsla(0, 0%, 74.5%, .2);
                        border-radius: 2px;
                        width: 64px;
                        min-width: 64px;
                        height: 32px;
                        line-height: 32px
                    }

                    .ant-skeleton-element .ant-skeleton-button.ant-skeleton-button-round {
                        border-radius: 32px
                    }

                    .ant-spin {
                        box-sizing: border-box;
                        margin: 0;
                        padding: 0;
                        color: #222a35;
                        font-size: 14px;
                        font-variant: tabular-nums;
                        line-height: 1.5715;
                        list-style: none;
                        -webkit-font-feature-settings: "tnum";
                        font-feature-settings: "tnum";
                        position: absolute;
                        display: none;
                        color: #047ffe;
                        text-align: center;
                        vertical-align: middle;
                        opacity: 0;
                        transition: -webkit-transform .3s cubic-bezier(.78, .14, .15, .86);
                        transition: transform .3s cubic-bezier(.78, .14, .15, .86);
                        transition: transform .3s cubic-bezier(.78, .14, .15, .86), -webkit-transform .3s cubic-bezier(.78, .14, .15, .86)
                    }

                    .ant-spin-spinning {
                        position: static;
                        display: inline-block;
                        opacity: 1
                    }

                    .ant-spin-dot {
                        position: relative;
                        display: inline-block;
                        font-size: 20px;
                        width: 1em;
                        height: 1em
                    }

                    .ant-tabs-bottom,
                    .ant-tabs-top {
                        flex-direction: column
                    }

                    .ant-tabs-bottom>.ant-tabs-nav,
                    .ant-tabs-bottom>div>.ant-tabs-nav,
                    .ant-tabs-top>.ant-tabs-nav,
                    .ant-tabs-top>div>.ant-tabs-nav {
                        margin: 0 0 16px
                    }

                    .ant-tabs-bottom>.ant-tabs-nav:before,
                    .ant-tabs-bottom>div>.ant-tabs-nav:before,
                    .ant-tabs-top>.ant-tabs-nav:before,
                    .ant-tabs-top>div>.ant-tabs-nav:before {
                        position: absolute;
                        right: 0;
                        left: 0;
                        border-bottom: 1px solid rgba(34, 42, 53, .08);
                        content: ""
                    }

                    .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-ink-bar,
                    .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-ink-bar,
                    .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar,
                    .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar {
                        height: 2px
                    }

                    .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-ink-bar-animated,
                    .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-ink-bar-animated,
                    .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar-animated,
                    .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar-animated {
                        transition: width .3s, left .3s, right .3s
                    }

                    .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                    .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                    .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                    .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                    .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                    .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                    .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                    .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                        top: 0;
                        bottom: 0;
                        width: 30px
                    }

                    .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                    .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                    .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                    .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                        left: 0;
                        box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, .08)
                    }

                    .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                    .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                    .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                    .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:after {
                        right: 0;
                        box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, .08)
                    }

                    .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar,
                    .ant-tabs-top>.ant-tabs-nav:before,
                    .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar,
                    .ant-tabs-top>div>.ant-tabs-nav:before {
                        bottom: 0
                    }

                    .ant-tabs {
                        box-sizing: border-box;
                        margin: 0;
                        padding: 0;
                        color: #222a35;
                        font-size: 14px;
                        font-variant: tabular-nums;
                        line-height: 1.5715;
                        list-style: none;
                        -webkit-font-feature-settings: "tnum";
                        font-feature-settings: "tnum";
                        display: flex
                    }

                    .ant-tabs>.ant-tabs-nav,
                    .ant-tabs>div>.ant-tabs-nav {
                        position: relative;
                        display: flex;
                        flex: none;
                        align-items: center
                    }

                    .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap,
                    .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap {
                        position: relative;
                        display: inline-block;
                        display: flex;
                        flex: auto;
                        align-self: stretch;
                        overflow: hidden;
                        white-space: nowrap;
                        -webkit-transform: translate(0);
                        transform: translate(0)
                    }

                    .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                    .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                    .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                    .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                        position: absolute;
                        z-index: 1;
                        opacity: 0;
                        transition: opacity .3s;
                        content: "";
                        pointer-events: none
                    }

                    .ant-tabs>.ant-tabs-nav .ant-tabs-nav-list,
                    .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-list {
                        position: relative;
                        display: flex;
                        transition: -webkit-transform .3s;
                        transition: transform .3s;
                        transition: transform .3s, -webkit-transform .3s
                    }

                    .ant-tabs>.ant-tabs-nav .ant-tabs-nav-operations,
                    .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-operations {
                        display: flex;
                        align-self: stretch
                    }

                    .ant-tabs>.ant-tabs-nav .ant-tabs-nav-operations-hidden,
                    .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-operations-hidden {
                        position: absolute;
                        visibility: hidden;
                        pointer-events: none
                    }

                    .ant-tabs>.ant-tabs-nav .ant-tabs-nav-more,
                    .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-more {
                        position: relative;
                        padding: 8px 16px;
                        background: 0 0;
                        border: 0
                    }

                    .ant-tabs>.ant-tabs-nav .ant-tabs-nav-more:after,
                    .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-more:after {
                        position: absolute;
                        right: 0;
                        bottom: 0;
                        left: 0;
                        height: 5px;
                        -webkit-transform: translateY(100%);
                        transform: translateY(100%);
                        content: ""
                    }

                    .ant-tabs-ink-bar {
                        position: absolute;
                        background: #047ffe;
                        pointer-events: none
                    }

                    .ant-tabs-tab {
                        position: relative;
                        display: inline-flex;
                        align-items: center;
                        padding: 12px 0;
                        font-size: 16px;
                        background: 0 0;
                        border: 0;
                        outline: 0;
                        cursor: pointer
                    }

                    .ant-tabs-tab-btn,
                    .ant-tabs-tab-remove {
                        outline: 0;
                        transition: all .3s
                    }

                    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
                        color: #222a35;
                        text-shadow: 0 0 .25px currentcolor
                    }

                    .ant-tabs-tab+.ant-tabs-tab {
                        margin: 0 10px 0 32px
                    }

                    .ant-tabs-content {
                        display: flex;
                        width: 100%
                    }

                    .ant-tabs-content-holder {
                        flex: auto;
                        min-width: 0;
                        min-height: 0
                    }

                    .ant-tabs-tabpane {
                        flex: none;
                        width: 100%;
                        outline: 0
                    }

                    html body {
                        height: 100%;
                        margin: 0;
                        font-size: 14px;
                        line-height: 22px;
                        overflow: auto;
                        color: #222a35;
                        font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Tahoma, Arial, "PingFang SC", "Microsoft YaHei", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                        -webkit-font-smoothing: antialiased
                    }

                    html body p,
                    html body ul {
                        padding: 0;
                        margin: 0
                    }

                    html body li {
                        list-style: none
                    }

                    html body a {
                        text-decoration: none
                    }

                    html body a,
                    html body a:hover {
                        color: #222a35
                    }

                    html body ul {
                        -webkit-padding-start: 0;
                        padding-inline-start: 0
                    }

                    html body .dk-iconfont {
                        font-weight: 400;
                        line-height: 16px
                    }

                    html body input {
                        caret-color: #047ffe
                    }

                    html body .ant-input {
                        border-radius: 0
                    }

                    html body #root {
                        height: 100%
                    }

                    html body #root-skeleton {
                        height: 100%;
                        overflow: hidden
                    }

                    .generic .os-scrollbar,
                    .linux .os-scrollbar,
                    .mac .os-scrollbar {
                        overflow-y: overlay;
                        overflow-y: auto
                    }

                    html body .ant-tabs {
                        color: rgba(34, 42, 53, .7);
                        position: relative
                    }

                    html body .ant-tabs .ant-tabs-nav {
                        background: #fff
                    }

                    html body .ant-tabs .ant-tabs-nav:before {
                        border-bottom: none
                    }

                    html body .ant-tabs .ant-tabs-tab {
                        font-size: 16px;
                        line-height: 38px;
                        padding: 0
                    }

                    html body .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
                        font-weight: 500
                    }

                    html body .ant-tabs .ant-tabs-ink-bar {
                        background-color: #047ffe;
                        height: 2px !important;
                        border-radius: 8px
                    }

                    html body .ant-btn {
                        border-radius: 3px;
                        line-height: normal
                    }

                    html body .ant-btn>i {
                        margin-right: 8px !important
                    }

                    html body .ant-btn-primary {
                        background-color: #047ffe;
                        border: 1px solid #047ffe;
                        color: #fff;
                        box-shadow: none
                    }

                    html body .ant-btn-secondary {
                        background-color: #fff;
                        border: 1px solid #047ffe;
                        color: #047ffe
                    }

                    html body .ant-input-affix-wrapper {
                        border-radius: 6px
                    }

                    html body .ant-input-clear-icon,
                    html body .anticon .ant-input-clear-icon {
                        color: rgba(34, 42, 53, .3);
                        font-size: 14px
                    }

                    html body .ant-input-group-wrapper {
                        border: 1px solid rgba(34, 42, 53, .1);
                        border-radius: 4px
                    }

                    html body .ant-input-group-wrapper .ant-input-group-addon {
                        background: #fff;
                        border: none;
                        padding: 0 8px 0 9px
                    }

                    html body .ant-input-group-wrapper .ant-input-affix-wrapper {
                        padding: 0;
                        border: none;
                        box-shadow: none
                    }

                    html body .ant-input {
                        font-size: 14px;
                        height: 30px
                    }

                    html .ant-input::-webkit-input-placeholder {
                        color: rgba(34, 42, 53, .5)
                    }

                    html .ant-input::placeholder {
                        color: rgba(34, 42, 53, .5)
                    }

                    .aside-content .aside-skeleton-box {
                        width: 240px;
                        padding: 0 8px
                    }

                    .aside-content .aside-skeleton-box .aside-skeleton-button * {
                        min-width: auto !important;
                        width: 100% !important;
                        height: 100% !important
                    }

                    .aside-content .aside-skeleton-box .aside-skeleton-box-item {
                        width: 100%;
                        height: 36px;
                        position: relative
                    }

                    .aside-content .aside-skeleton-box .aside-skeleton-box-item .aside-skeleton-logo {
                        position: absolute;
                        top: 10px;
                        left: 24px;
                        width: 16px;
                        height: 16px
                    }

                    .aside-content .aside-skeleton-box .aside-skeleton-box-item .aside-skeleton-title {
                        position: absolute;
                        top: 11px;
                        left: 52px;
                        width: 48px;
                        height: 14px
                    }

                    .aside-content .aside-skeleton-box .aside-skeleton-space {
                        width: 100%
                    }

                    .aside-content .aside-skeleton-box .aside-skeleton-space .aside-skeleton-space-item {
                        width: 100%;
                        height: 35px;
                        position: relative
                    }

                    .aside-content .aside-skeleton-box .aside-skeleton-space .aside-skeleton-space-item .aside-skeleton-symbol {
                        display: block;
                        position: absolute;
                        top: 15.5px;
                        left: 52px;
                        width: 4px;
                        height: 4px
                    }

                    .aside-content .aside-skeleton-box .aside-skeleton-space .aside-skeleton-space-item .aside-skeleton-title {
                        display: block;
                        position: absolute;
                        top: 11px;
                        left: 64px;
                        width: 88px;
                        height: 13px
                    }

                    #common-team-skeleton .cooper-notify-count {
                        position: absolute;
                        top: -6px;
                        left: 10px;
                        background-color: #ff563a;
                        border: 1px solid #fff;
                        color: #fff;
                        display: inline-block;
                        min-width: 16px;
                        height: 16px;
                        font-size: 12px;
                        line-height: 14px;
                        border-radius: 8px;
                        text-align: center;
                        cursor: pointer;
                        padding: 0 4px;
                        z-index: 100
                    }

                    #common-team-skeleton .notify-list-small>span {
                        left: 18px;
                        top: 2px
                    }

                    #common-team-skeleton .global-search-wrap .search-tag {
                        margin-left: 8px;
                        padding: 0 4px 0 8px;
                        border-radius: 3px;
                        background: #ebeef1;
                        display: flex;
                        align-items: center;
                        color: rgba(0, 0, 0, .7)
                    }

                    #common-team-skeleton .header-warp .header-top .title-opt>button {
                        width: unset !important
                    }

                    #common-team-skeleton .global-search-wrap .search-tag>span>i {
                        color: #333;
                        font-size: 14px;
                        cursor: pointer
                    }

                    #common-team-skeleton .team-skeleton-box {
                        width: 100%;
                        padding: 0 32px
                    }

                    #common-team-skeleton .team-skeleton-box .team-skeleton-button * {
                        min-width: auto !important;
                        width: 100% !important;
                        height: 100% !important
                    }

                    #common-team-skeleton .team-skeleton-box .team-skeleton-box-title {
                        z-index: 1;
                        width: 64px;
                        height: 16px;
                        margin-top: 4px;
                        margin-bottom: 20px
                    }

                    #common-team-skeleton .team-skeleton-box .team-skeleton-box-list {
                        width: 100%;
                        display: grid;
                        gap: 16px;
                        grid-template-columns: repeat(auto-fill, minmax(332px, 1fr));
                        padding-bottom: 32px
                    }

                    #common-team-skeleton .team-skeleton-box .team-skeleton-box-list .team-skeleton-box-item {
                        background-color: #f6f7f7;
                        position: relative;
                        border-radius: 8px;
                        padding: 0;
                        height: 92px
                    }

                    #common-team-skeleton .team-skeleton-box .team-skeleton-box-list .team-skeleton-box-item .team-skeleton-bi-logo {
                        display: block;
                        position: absolute;
                        top: 18.4px;
                        left: 18.4px;
                        width: 19.2px;
                        height: 19.2px
                    }

                    #common-team-skeleton .team-skeleton-box .team-skeleton-box-list .team-skeleton-box-item .team-skeleton-bi-name {
                        display: block;
                        position: absolute;
                        top: 21px;
                        left: 48px;
                        width: 120px;
                        height: 14px
                    }

                    #common-team-skeleton .team-skeleton-box .team-skeleton-box-list .team-skeleton-box-item .team-skeleton-bi-info {
                        display: block;
                        position: absolute;
                        top: 60px;
                        left: 18px;
                        height: 12px;
                        width: 220px
                    }

                    #common-team-skeleton .team-skeleton-wrap {
                        width: 100%;
                        height: 100%;
                        position: relative;
                        overflow-y: auto
                    }

                    #common-team-skeleton .dk-icon-tuanduikongjian:before {
                        content: "e751"
                    }

                    #common-team-skeleton .dk-icon-tuanduikongjian:before {
                        content: "e751"
                    }
                </style>
                <div id="common-team-skeleton">
                    <div class="team-skeleton-wrap">
                        <div class="team-skeleton-box">
                            <div class="team-skeleton-box-title team-skeleton-button">
                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                            </div>
                            <div class="team-skeleton-box-list">
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="team-skeleton-box">
                            <div class="team-skeleton-box-title team-skeleton-button">
                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                            </div>
                            <div class="team-skeleton-box-list">
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="team-skeleton-box">
                            <div class="team-skeleton-box-title team-skeleton-button">
                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                            </div>
                            <div class="team-skeleton-box-list">
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                                <div class="team-skeleton-box-item">
                                    <div class="team-skeleton-bi-logo team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-name team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                    <div class="team-skeleton-bi-info team-skeleton-button">
                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                #right-team-detail html body .ant-btn-secondary {
                    background-color: #fff;
                    border: 1px solid #047ffe;
                    color: #047ffe
                }

                #right-team-detail .global-search-wrap .search-tag {
                    margin-left: 8px !important;
                    padding: 0 4px 0 8px;
                    border-radius: 3px;
                    background: #ebeef1;
                    display: flex;
                    align-items: center;
                    color: rgba(0, 0, 0, .7);
                    margin: 0;
                    box-sizing: border-box;
                    margin-block-start: 1em;
                    margin-block-end: 1em;
                    margin-inline-start: 0;
                    margin-inline-end: 0;
                    line-height: 24px;
                    font-size: 14px;
                    font-weight: 400;
                    text-align: center;
                    white-space: nowrap
                }

                #right-team-detail .file-title-skeleton {
                    padding: 0 24px;
                    line-height: 22px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between
                }

                #right-team-detail .file-title-skeleton .title {
                    padding-right: 14px;
                    display: flex;
                    align-items: center
                }

                #right-team-detail .file-title-skeleton .title .title-inner {
                    display: flex;
                    padding: 22px 0 12px
                }

                #right-team-detail .file-title-skeleton .title .title-inner span {
                    font-family: PingFangSC-Medium, PingFang SC;
                    color: #2f343c;
                    font-weight: 500;
                    line-height: 21px;
                    font-size: 16px
                }

                #right-team-detail .file-title-skeleton .action {
                    max-width: 200px;
                    display: flex;
                    align-items: center
                }

                #right-team-detail .file-title-skeleton .action>button:last-child {
                    margin-left: 12px
                }

                #right-team-detail .space-skeleton-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px 24px;
                    overflow: hidden
                }

                #right-team-detail .space-skeleton-header .space-skeleton-header-title {
                    height: 20px;
                    width: 160px
                }

                #right-team-detail .space-skeleton-header .space-skeleton-header-search {
                    width: 633px;
                    min-width: 308px;
                    display: flex
                }

                #right-team-detail .space-skeleton-header .space-skeleton-button * {
                    min-width: auto !important;
                    width: 100% !important;
                    height: 100% !important
                }

                #right-team-detail .td-skeleton-box {
                    height: 100%;
                    background-position: 0 0;
                    background-size: 100%;
                    background-repeat: no-repeat;
                    display: flex;
                    flex-direction: column
                }

                #right-team-detail .td-skeleton-box .td-skeleton-button * {
                    min-width: auto !important;
                    width: 100% !important;
                    height: 100% !important
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body {
                    flex: 1;
                    overflow-y: hidden;
                    display: flex;
                    flex-direction: column;
                    position: relative
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body .td-skeleton-bar {
                    margin: 0 24px 16px;
                    display: flex;
                    align-items: center;
                    height: 32px;
                    justify-content: normal
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body .td-skeleton-bar .td-skeleton-bar-tabs {
                    flex: 1;
                    display: flex;
                    justify-content: normal;
                    align-items: center;
                    height: 32px
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body .td-skeleton-bar .td-skeleton-bar-tabs .tab {
                    margin-right: 2px;
                    position: relative;
                    width: 114px;
                    height: 100%
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body .td-skeleton-bar .td-skeleton-bar-tabs .tab .icon {
                    position: absolute;
                    left: 10px;
                    top: 8px;
                    width: 16px;
                    height: 16px
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body .td-skeleton-bar .td-skeleton-bar-tabs .tab .title {
                    position: absolute;
                    left: 30px;
                    top: 9.5px;
                    width: 70px;
                    height: 13px
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body .td-skeleton-bar .td-skeleton-bar-icons {
                    display: flex
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body .td-skeleton-bar .td-skeleton-bar-icons-item {
                    color: #6a707c;
                    margin-left: 16px;
                    cursor: pointer;
                    width: 24px;
                    height: 24px;
                    text-align: center;
                    line-height: 24px
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body .td-skeleton-body-wrap {
                    height: 100%;
                    background: #fff;
                    border-radius: 8px;
                    margin: 0 24px
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body .td-skeleton-body-wrap .file-skeleton-box {
                    height: calc(100% - 51px) !important;
                    width: 100% !important;
                    overflow: hidden !important
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body .td-skeleton-body-wrap .file-header-skeleton-box {
                    min-width: 100% !important;
                    padding: 0 24px !important
                }

                #right-team-detail .td-skeleton-box .td-skeleton-body .td-skeleton-body-wrap .file-body-skeleton-box {
                    height: calc(100% - 40px) !important;
                    min-width: 100% !important;
                    width: -webkit-max-content !important;
                    width: max-content !important;
                    overflow: hidden !important;
                    padding: 8px 24px 0 !important
                }
            </style>
            <div id="right-team-detail" class="right" style="display:none">
                <div class="header-wrap"></div>
                <div class="body-wrap">
                    <div class="td-skeleton-box">
                        <div class="space-skeleton-header">
                            <div class="space-skeleton-header-title space-skeleton-button">
                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                            </div>
                            <div class="space-skeleton-header-search">
                                <div class="global-search-wrap"><span class="ant-input-group-wrapper"><span class="ant-input-wrapper ant-input-group"><span class="ant-input-group-addon">
                                                <div class="search-before"><i class="dk-icon-sousuo dk-iconfont" style="color:#bbb"></i>
                                                    <p class="search-tag">当前空间 <span style="cursor:pointer;height:20px"><i class="dk-icon-guanbi dk-iconfont" style="font-size:20px"></i></span></p>
                                                </div>
                                            </span><span class="ant-input-affix-wrapper"><input autocomplete="off" placeholder="搜索文档标题、正文、知识库名" class="ant-input"> <span class="ant-input-suffix"><span class="ant-input-clear-icon ant-input-clear-icon-hidden" role="button" tabindex="-1"><span role="img" aria-label="close-circle" class="anticon anticon-close-circle"><svg fill-rule="evenodd" viewBox="64 64 896 896" focusable="false" data-icon="close-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                                <path d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"></path>
                                                            </svg> </span></span></span></span><span class="ant-input-group-addon"><span class="search-icon-btn"><span class="search-text">高级搜索</span>⌘+J</span></span></span></span></div>
                                <div class="cooper-customer-service"><span class="customer-service-bar"></span>
                                    <div class="newfeature-popup"><span><span class="cooper-customer-service-empty"></span></span></div>
                                    <div class="cooper-helper-wrap">
                                        <div class="cooper-helper"><i class="dk-iconfont dk-icon-gongnengshangxin"></i></div>
                                    </div>
                                    <div class="feedback-with-popup"><span>
                                            <div class="feedback">
                                                <div class="content"><i class="dk-iconfont dk-icon-lianxikefu"></i></div>
                                            </div>
                                        </span></div>
                                </div>
                            </div>
                        </div>
                        <div class="td-skeleton-body">
                            <div class="td-skeleton-bar">
                                <div class="td-skeleton-bar-tabs">
                                    <div class="tab">
                                        <div class="icon td-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="title td-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="tab">
                                        <div class="icon td-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="title td-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="tab">
                                        <div class="icon td-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="title td-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                    <div class="tab">
                                        <div class="icon td-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                        </div>
                                        <div class="title td-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="td-skeleton-bar-icons">
                                    <div class="td-skeleton-bar-icons-item"><i class="dk-iconfont dk-icon-zhiding4px space-operation"></i></div>
                                    <div class="td-skeleton-bar-icons-item"><i class="dk-iconfont dk-icon-dongtai space-operation"></i></div>
                                    <div class="td-skeleton-bar-icons-item"><i class="dk-iconfont dk-icon-duoren space-operation"></i></div>
                                    <div class="td-skeleton-bar-icons-item"><i class="dk-iconfont dk-icon-huishouzhan4px space-operation"></i></div>
                                    <div class="td-skeleton-bar-icons-item"><i class="dk-iconfont dk-icon-shezhi4 space-operation"></i></div>
                                </div>
                            </div>
                            <div class="td-skeleton-body-wrap">
                                <div class="file-title-skeleton">
                                    <div class="title">
                                        <div class="title-inner"><span>全部文件</span></div>
                                    </div>
                                    <div class="action"><button type="button" class="ant-btn ant-btn-secondary"><i class="dk-iconfont dk-icon-shangchuan2"></i><span>上传</span></button><button type="button" class="ant-btn ant-btn-primary"><i class="dk-iconfont dk-icon-a-"></i><span>新建</span></button></div>
                                </div>
                                <style>
                                    .file-skeleton-box {
                                        margin: 0;
                                        padding: 0;
                                        min-width: 667px !important;
                                        margin: -8px auto 0 !important
                                    }

                                    .file-skeleton-box .file-header-skeleton-box {
                                        width: 100%;
                                        height: 36px;
                                        margin: 0;
                                        padding: 0;
                                        display: flex;
                                        align-items: center
                                    }

                                    .file-skeleton-box .header-inner {
                                        width: 100%;
                                        height: 22px;
                                        position: relative;
                                        margin: 0;
                                        padding: 0;
                                        display: flex;
                                        align-items: center;
                                        overflow: hidden
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-button {
                                        width: 42px;
                                        height: 14px
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-button * {
                                        min-width: auto !important;
                                        width: 100% !important;
                                        height: 100% !important
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-name {
                                        margin-right: 192px;
                                        flex: 1;
                                        min-width: 200px
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-owner {
                                        margin-right: 216px;
                                        min-width: 42px
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-address {
                                        margin-right: 216px;
                                        min-width: 42px
                                    }

                                    .file-skeleton-box .header-inner .file-header-skeleton-time {
                                        margin-right: 22px;
                                        min-width: 42px
                                    }

                                    .file-skeleton-box .file-body-skeleton-box {
                                        width: 100%;
                                        padding-top: 8px !important
                                    }

                                    .file-skeleton-box .body-inner>li {
                                        width: 100%;
                                        height: 46px;
                                        position: relative
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-button * {
                                        min-width: auto !important;
                                        width: 100% !important;
                                        height: 100% !important
                                    }

                                    .file-skeleton-box .body-inner li {
                                        display: flex;
                                        align-items: center
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-name-logo {
                                        width: 19.2px;
                                        height: 19.2px;
                                        min-width: 19.2px
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-name-title {
                                        height: 14px;
                                        flex: 1;
                                        margin-left: 10px;
                                        top: 17px;
                                        left: 32px;
                                        margin-right: 192px;
                                        min-width: 170px
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-name-title .ant-skeleton {
                                        max-width: 500px !important
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-owner {
                                        width: 66px;
                                        height: 14px;
                                        top: 17px;
                                        left: 544px;
                                        margin-right: 192px;
                                        min-width: 66px
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-address {
                                        width: 66px;
                                        height: 14px;
                                        top: 17px;
                                        left: 736px;
                                        margin-right: 192px;
                                        min-width: 66px
                                    }

                                    .file-skeleton-box .body-inner .file-body-skeleton-time {
                                        width: 66px;
                                        height: 14px;
                                        top: 17px;
                                        left: 928px min-width: 66px
                                    }
                                </style>
                                <div class="file-skeleton-box">
                                    <div class="file-header-skeleton-box">
                                        <div class="header-inner">
                                            <div class="file-header-skeleton-name">
                                                <div class="file-header-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </div>
                                            <div class="file-header-skeleton-owner file-header-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-header-skeleton-address file-header-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-header-skeleton-time file-header-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-body-skeleton-box">
                                        <div class="body-inner">
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                </div>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                #right-share-fromme .ant-tabs-bottom,
                #right-share-fromme .ant-tabs-top {
                    flex-direction: column
                }

                #right-share-fromme .ant-tabs-bottom>.ant-tabs-nav,
                #right-share-fromme .ant-tabs-bottom>div>.ant-tabs-nav,
                #right-share-fromme .ant-tabs-top>.ant-tabs-nav,
                #right-share-fromme .ant-tabs-top>div>.ant-tabs-nav {
                    margin: 0 0 16px
                }

                #right-share-fromme .ant-tabs-bottom>.ant-tabs-nav:before,
                #right-share-fromme .ant-tabs-bottom>div>.ant-tabs-nav:before,
                #right-share-fromme .ant-tabs-top>.ant-tabs-nav:before,
                #right-share-fromme .ant-tabs-top>div>.ant-tabs-nav:before {
                    position: absolute;
                    right: 0;
                    left: 0;
                    border-bottom: 1px solid rgba(34, 42, 53, .08);
                    content: ""
                }

                #right-share-fromme .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-ink-bar,
                #right-share-fromme .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-ink-bar,
                #right-share-fromme .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar,
                #right-share-fromme .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar {
                    height: 2px
                }

                #right-share-fromme .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-ink-bar-animated,
                #right-share-fromme .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-ink-bar-animated,
                #right-share-fromme .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar-animated,
                #right-share-fromme .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar-animated {
                    transition: width .3s, left .3s, right .3s
                }

                #right-share-fromme .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-fromme .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-fromme .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-fromme .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-fromme .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-fromme .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-fromme .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-fromme .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                    top: 0;
                    bottom: 0;
                    width: 30px
                }

                #right-share-fromme .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-fromme .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-fromme .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-fromme .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                    left: 0;
                    box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, .08)
                }

                #right-share-fromme .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-fromme .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-fromme .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-fromme .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:after {
                    right: 0;
                    box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, .08)
                }

                #right-share-fromme .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar,
                #right-share-fromme .ant-tabs-top>.ant-tabs-nav:before,
                #right-share-fromme .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar,
                #right-share-fromme .ant-tabs-top>div>.ant-tabs-nav:before {
                    bottom: 0
                }

                #right-share-fromme .ant-tabs {
                    box-sizing: border-box;
                    margin: 0;
                    padding: 0;
                    color: #222a35;
                    font-size: 14px;
                    font-variant: tabular-nums;
                    line-height: 1.5715;
                    list-style: none;
                    -webkit-font-feature-settings: "tnum";
                    font-feature-settings: "tnum";
                    display: flex
                }

                #right-share-fromme .ant-tabs>.ant-tabs-nav,
                #right-share-fromme .ant-tabs>div>.ant-tabs-nav {
                    position: relative;
                    display: flex;
                    flex: none;
                    align-items: center
                }

                #right-share-fromme .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap,
                #right-share-fromme .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap {
                    position: relative;
                    display: inline-block;
                    display: flex;
                    flex: auto;
                    align-self: stretch;
                    overflow: hidden;
                    white-space: nowrap;
                    -webkit-transform: translate(0);
                    transform: translate(0)
                }

                #right-share-fromme .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-fromme .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-fromme .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-fromme .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                    position: absolute;
                    z-index: 1;
                    opacity: 0;
                    transition: opacity .3s;
                    content: "";
                    pointer-events: none
                }

                #right-share-fromme .ant-tabs>.ant-tabs-nav .ant-tabs-nav-list,
                #right-share-fromme .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-list {
                    position: relative;
                    display: flex;
                    transition: -webkit-transform .3s;
                    transition: transform .3s;
                    transition: transform .3s, -webkit-transform .3s
                }

                #right-share-fromme .ant-tabs>.ant-tabs-nav .ant-tabs-nav-operations,
                #right-share-fromme .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-operations {
                    display: flex;
                    align-self: stretch
                }

                #right-share-fromme .ant-tabs>.ant-tabs-nav .ant-tabs-nav-operations-hidden,
                #right-share-fromme .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-operations-hidden {
                    position: absolute;
                    visibility: hidden;
                    pointer-events: none
                }

                #right-share-fromme .ant-tabs>.ant-tabs-nav .ant-tabs-nav-more,
                #right-share-fromme .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-more {
                    position: relative;
                    padding: 8px 16px;
                    background: 0 0;
                    border: 0
                }

                #right-share-fromme .ant-tabs>.ant-tabs-nav .ant-tabs-nav-more:after,
                #right-share-fromme .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-more:after {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    height: 5px;
                    -webkit-transform: translateY(100%);
                    transform: translateY(100%);
                    content: ""
                }

                #right-share-fromme .ant-tabs-ink-bar {
                    position: absolute;
                    background: #047ffe;
                    pointer-events: none
                }

                #right-share-fromme .ant-tabs-tab {
                    position: relative;
                    display: inline-flex;
                    align-items: center;
                    padding: 12px 0;
                    font-size: 16px;
                    background: 0 0;
                    border: 0;
                    outline: 0;
                    cursor: pointer
                }

                #right-share-fromme .ant-tabs-tab-btn,
                #right-share-fromme .ant-tabs-tab-remove {
                    outline: 0;
                    transition: all .3s
                }

                #right-share-fromme .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
                    color: #222a35;
                    text-shadow: 0 0 .25px currentcolor
                }

                #right-share-fromme .ant-tabs-tab+.ant-tabs-tab {
                    margin: 0 10px 0 32px
                }

                #right-share-fromme .ant-tabs-content {
                    display: flex;
                    width: 100%
                }

                #right-share-fromme .ant-tabs-content-holder {
                    flex: auto;
                    min-width: 0;
                    min-height: 0
                }

                #right-share-fromme .ant-tabs-tabpane {
                    flex: none;
                    width: 100%;
                    outline: 0
                }

                #right-share-fromme .ant-tabs {
                    color: rgba(34, 42, 53, .7);
                    position: relative
                }

                #right-share-fromme .ant-tabs .ant-tabs-nav {
                    background: #fff
                }

                #right-share-fromme .ant-tabs .ant-tabs-nav:before {
                    border-bottom: none
                }

                #right-share-fromme .ant-tabs .ant-tabs-tab {
                    font-size: 16px;
                    line-height: 38px;
                    padding: 0
                }

                #right-share-fromme .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
                    font-weight: 500
                }

                #right-share-fromme .ant-tabs .ant-tabs-ink-bar {
                    background-color: #047ffe;
                    height: 2px !important;
                    border-radius: 8px
                }

                #right-share-fromme .tab-top-title {
                    flex: 1
                }

                #right-share-fromme .tab-top-title .ant-tabs-content-top {
                    height: 100%
                }

                #right-share-fromme .tab-top-title .ant-tabs-nav {
                    margin-bottom: 15px
                }

                #right-share-fromme .tab-top-title .ant-tabs-ink-bar {
                    display: none !important
                }

                #right-share-fromme .tab-top-title .ant-tabs-tab-active {
                    position: relative
                }

                #right-share-fromme .tab-top-title .ant-tabs-tab-active:before {
                    content: "";
                    width: 24px;
                    height: 2px;
                    background-color: #047ffe;
                    left: calc(50% - 12px);
                    bottom: 0;
                    z-index: 100;
                    position: absolute
                }

                #right-share-fromme .tab-top-large .ant-tabs-tab {
                    font-size: 16px !important;
                    line-height: 24px !important;
                    padding-top: 4px !important;
                    padding-bottom: 6px !important
                }

                #right-share-fromme .share-skeleton-box {
                    margin-top: -20px;
                    display: flex;
                    flex-direction: column;
                    height: 100%
                }

                #right-share-fromme .share-skeleton-box .share-skeleton-title-wrap {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 18px;
                    padding: 0 32px
                }

                #right-share-fromme .share-skeleton-box .share-skeleton-body-wrap {
                    padding: 0 32px;
                    height: 100%
                }

                #right-share-fromme .share-skeleton-box .share-skeleton-body-wrap .ant-tabs-nav {
                    margin: 0 0 16px !important
                }

                #right-share-fromme .share-skeleton-box .skeleton-fromme .file-skeleton-box {
                    height: 100%;
                    overflow-x: hidden;
                    margin-top: -12px;
                    margin-bottom: 12px
                }

                #right-share-fromme .share-skeleton-box .skeleton-fromme .file-body-skeleton-box {
                    padding-top: 8px !important
                }

                #right-share-fromme .dk-icon-fenxiang1:before {
                    content: "e752"
                }

                #right-share-fromme .dk-icon-zhanneixin:before {
                    content: "e6f1"
                }

                #right-share-fromme .dk-icon-tuanduikongjian4px:before {
                    content: "e6f5"
                }
            </style>
            <div id="right-share-fromme" class="right" style="display:none">
                <div class="header-wrap">
                    <div class="header-warp">
                        <div class="header-top">
                            <div class="header-top-left">
                                <div class="global-search-wrap"><span class="ant-input-group-wrapper"><span class="ant-input-wrapper ant-input-group"><span class="ant-input-group-addon">
                                                <div class="search-before"><i class="dk-icon-sousuo dk-iconfont" style="color:#bbb"></i></div>
                                            </span><span class="ant-input-affix-wrapper"><input autocomplete="off" placeholder="搜索文档标题、正文、空间/知识库名" class="ant-input"> <span class="ant-input-suffix"><span class="ant-input-clear-icon ant-input-clear-icon-hidden" role="button" tabindex="-1"><span role="img" aria-label="close-circle" class="anticon anticon-close-circle"><svg fill-rule="evenodd" viewBox="64 64 896 896" focusable="false" data-icon="close-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                                <path d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"></path>
                                                            </svg> </span></span></span></span><span class="ant-input-group-addon"><span class="search-icon-btn"><span class="search-text">高级搜索</span>⌘+J</span></span></span></span></div>
                            </div>
                            <div class="cooper-customer-service">
                                <div class="newfeature-popup"><span><span class="cooper-customer-service-empty"></span></span></div>
                                <div class="cooper-helper-wrap">
                                    <div class="cooper-helper"><i class="dk-iconfont dk-icon-gongnengshangxin"></i></div>
                                </div>
                                <div class="feedback-with-popup"><span>
                                        <div class="feedback">
                                            <div class="content"><i class="dk-iconfont dk-icon-lianxikefu"></i></div>
                                        </div>
                                    </span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="body-wrap">
                    <div class="share-skeleton-box">
                        <div class="share-skeleton-title-wrap"></div>
                        <div class="share-skeleton-body-wrap skeleton-fromme">
                            <div class="tab-skeleton">
                                <div class="ant-tabs ant-tabs-top ant-tabs-middle tab-top-title tab-top-large" tabsize="large">
                                    <div role="tablist" class="ant-tabs-nav">
                                        <div class="ant-tabs-nav-wrap">
                                            <div class="ant-tabs-nav-list" style="transform:translate(0,0)">
                                                <div class="ant-tabs-tab">
                                                    <div role="tab" aria-selected="false" class="ant-tabs-tab-btn" tabindex="0" id="rc-tabs-0-tab-1" aria-controls="rc-tabs-0-panel-1"><span><span>分享给我</span></span></div>
                                                </div>
                                                <div class="ant-tabs-tab ant-tabs-tab-active">
                                                    <div role="tab" aria-selected="true" class="ant-tabs-tab-btn" tabindex="0" id="rc-tabs-0-tab-2" aria-controls="rc-tabs-0-panel-2"><span><span>我分享的</span></span></div>
                                                </div>
                                                <div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated" style="left:96px;width:64px"></div>
                                            </div>
                                        </div>
                                        <div class="ant-tabs-nav-operations ant-tabs-nav-operations-hidden"><button type="button" class="ant-tabs-nav-more" tabindex="-1" aria-hidden="true" aria-haspopup="listbox" aria-controls="rc-tabs-0-more-popup" id="rc-tabs-0-more" aria-expanded="false" style="visibility:hidden;order:1"><span role="img" aria-label="ellipsis" class="anticon anticon-ellipsis"><svg viewBox="64 64 896 896" focusable="false" data-icon="ellipsis" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                        <path d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"></path>
                                                    </svg></span></button></div>
                                    </div>
                                    <div class="ant-tabs-content-holder">
                                        <div class="ant-tabs-content ant-tabs-content-top">
                                            <div role="tabpanel" tabindex="-1" aria-hidden="true" class="ant-tabs-tabpane" id="rc-tabs-0-panel-1" aria-labelledby="rc-tabs-0-tab-1" style="display:none"></div>
                                            <div role="tabpanel" tabindex="0" aria-hidden="false" class="ant-tabs-tabpane ant-tabs-tabpane-active" id="rc-tabs-0-panel-2" aria-labelledby="rc-tabs-0-tab-2">
                                                <style>
                                                    .file-skeleton-box {
                                                        margin: 0;
                                                        padding: 0;
                                                        min-width: 667px !important;
                                                        margin: -8px auto 0 !important
                                                    }

                                                    .file-skeleton-box .file-header-skeleton-box {
                                                        width: 100%;
                                                        height: 36px;
                                                        margin: 0;
                                                        padding: 0;
                                                        display: flex;
                                                        align-items: center
                                                    }

                                                    .file-skeleton-box .header-inner {
                                                        width: 100%;
                                                        height: 22px;
                                                        position: relative;
                                                        margin: 0;
                                                        padding: 0;
                                                        display: flex;
                                                        align-items: center;
                                                        overflow: hidden
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-button {
                                                        width: 42px;
                                                        height: 14px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-button * {
                                                        min-width: auto !important;
                                                        width: 100% !important;
                                                        height: 100% !important
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-name {
                                                        margin-right: 192px;
                                                        flex: 1;
                                                        min-width: 200px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-owner {
                                                        margin-right: 216px;
                                                        min-width: 42px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-address {
                                                        margin-right: 216px;
                                                        min-width: 42px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-time {
                                                        margin-right: 22px;
                                                        min-width: 42px
                                                    }

                                                    .file-skeleton-box .file-body-skeleton-box {
                                                        width: 100%;
                                                        padding-top: 8px !important
                                                    }

                                                    .file-skeleton-box .body-inner>li {
                                                        width: 100%;
                                                        height: 46px;
                                                        position: relative
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-button * {
                                                        min-width: auto !important;
                                                        width: 100% !important;
                                                        height: 100% !important
                                                    }

                                                    .file-skeleton-box .body-inner li {
                                                        display: flex;
                                                        align-items: center
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-name-logo {
                                                        width: 19.2px;
                                                        height: 19.2px;
                                                        min-width: 19.2px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-name-title {
                                                        height: 14px;
                                                        flex: 1;
                                                        margin-left: 10px;
                                                        top: 17px;
                                                        left: 32px;
                                                        margin-right: 192px;
                                                        min-width: 170px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-name-title .ant-skeleton {
                                                        max-width: 500px !important
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-owner {
                                                        width: 66px;
                                                        height: 14px;
                                                        top: 17px;
                                                        left: 544px;
                                                        margin-right: 192px;
                                                        min-width: 66px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-address {
                                                        width: 66px;
                                                        height: 14px;
                                                        top: 17px;
                                                        left: 736px;
                                                        margin-right: 192px;
                                                        min-width: 66px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-time {
                                                        width: 66px;
                                                        height: 14px;
                                                        top: 17px;
                                                        left: 928px min-width: 66px
                                                    }
                                                </style>
                                                <div class="file-skeleton-box">
                                                    <div class="file-header-skeleton-box">
                                                        <div class="header-inner">
                                                            <div class="file-header-skeleton-name">
                                                                <div class="file-header-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </div>
                                                            <div class="file-header-skeleton-owner file-header-skeleton-button">
                                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                            </div>
                                                            <div class="file-header-skeleton-address file-header-skeleton-button">
                                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                            </div>
                                                            <div class="file-header-skeleton-time file-header-skeleton-button">
                                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="file-body-skeleton-box">
                                                        <div class="body-inner">
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                #right-share-tome .ant-tabs-bottom,
                #right-share-tome .ant-tabs-top {
                    flex-direction: column
                }

                #right-share-tome .ant-tabs-bottom>.ant-tabs-nav,
                #right-share-tome .ant-tabs-bottom>div>.ant-tabs-nav,
                #right-share-tome .ant-tabs-top>.ant-tabs-nav,
                #right-share-tome .ant-tabs-top>div>.ant-tabs-nav {
                    margin: 0 0 16px
                }

                #right-share-tome .ant-tabs-bottom>.ant-tabs-nav:before,
                #right-share-tome .ant-tabs-bottom>div>.ant-tabs-nav:before,
                #right-share-tome .ant-tabs-top>.ant-tabs-nav:before,
                #right-share-tome .ant-tabs-top>div>.ant-tabs-nav:before {
                    position: absolute;
                    right: 0;
                    left: 0;
                    border-bottom: 1px solid rgba(34, 42, 53, .08);
                    content: ""
                }

                #right-share-tome .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-ink-bar,
                #right-share-tome .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-ink-bar,
                #right-share-tome .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar,
                #right-share-tome .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar {
                    height: 2px
                }

                #right-share-tome .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-ink-bar-animated,
                #right-share-tome .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-ink-bar-animated,
                #right-share-tome .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar-animated,
                #right-share-tome .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar-animated {
                    transition: width .3s, left .3s, right .3s
                }

                #right-share-tome .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-tome .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-tome .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-tome .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-tome .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-tome .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-tome .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-tome .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                    top: 0;
                    bottom: 0;
                    width: 30px
                }

                #right-share-tome .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-tome .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-tome .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-tome .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                    left: 0;
                    box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, .08)
                }

                #right-share-tome .ant-tabs-bottom>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-tome .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-tome .ant-tabs-top>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-tome .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-nav-wrap:after {
                    right: 0;
                    box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, .08)
                }

                #right-share-tome .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar,
                #right-share-tome .ant-tabs-top>.ant-tabs-nav:before,
                #right-share-tome .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar,
                #right-share-tome .ant-tabs-top>div>.ant-tabs-nav:before {
                    bottom: 0
                }

                #right-share-tome .ant-tabs {
                    box-sizing: border-box;
                    margin: 0;
                    padding: 0;
                    color: #222a35;
                    font-size: 14px;
                    font-variant: tabular-nums;
                    line-height: 1.5715;
                    list-style: none;
                    -webkit-font-feature-settings: "tnum";
                    font-feature-settings: "tnum";
                    display: flex
                }

                #right-share-tome .ant-tabs>.ant-tabs-nav,
                #right-share-tome .ant-tabs>div>.ant-tabs-nav {
                    position: relative;
                    display: flex;
                    flex: none;
                    align-items: center
                }

                #right-share-tome .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap,
                #right-share-tome .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap {
                    position: relative;
                    display: inline-block;
                    display: flex;
                    flex: auto;
                    align-self: stretch;
                    overflow: hidden;
                    white-space: nowrap;
                    -webkit-transform: translate(0);
                    transform: translate(0)
                }

                #right-share-tome .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-tome .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap:before,
                #right-share-tome .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,
                #right-share-tome .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
                    position: absolute;
                    z-index: 1;
                    opacity: 0;
                    transition: opacity .3s;
                    content: "";
                    pointer-events: none
                }

                #right-share-tome .ant-tabs>.ant-tabs-nav .ant-tabs-nav-list,
                #right-share-tome .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-list {
                    position: relative;
                    display: flex;
                    transition: -webkit-transform .3s;
                    transition: transform .3s;
                    transition: transform .3s, -webkit-transform .3s
                }

                #right-share-tome .ant-tabs>.ant-tabs-nav .ant-tabs-nav-operations,
                #right-share-tome .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-operations {
                    display: flex;
                    align-self: stretch
                }

                #right-share-tome .ant-tabs>.ant-tabs-nav .ant-tabs-nav-operations-hidden,
                #right-share-tome .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-operations-hidden {
                    position: absolute;
                    visibility: hidden;
                    pointer-events: none
                }

                #right-share-tome .ant-tabs>.ant-tabs-nav .ant-tabs-nav-more,
                #right-share-tome .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-more {
                    position: relative;
                    padding: 8px 16px;
                    background: 0 0;
                    border: 0
                }

                #right-share-tome .ant-tabs>.ant-tabs-nav .ant-tabs-nav-more:after,
                #right-share-tome .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-more:after {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    height: 5px;
                    -webkit-transform: translateY(100%);
                    transform: translateY(100%);
                    content: ""
                }

                #right-share-tome .ant-tabs-ink-bar {
                    position: absolute;
                    background: #047ffe;
                    pointer-events: none
                }

                #right-share-tome .ant-tabs-tab {
                    position: relative;
                    display: inline-flex;
                    align-items: center;
                    padding: 12px 0;
                    font-size: 16px;
                    background: 0 0;
                    border: 0;
                    outline: 0;
                    cursor: pointer
                }

                #right-share-tome .ant-tabs-tab-btn,
                #right-share-tome .ant-tabs-tab-remove {
                    outline: 0;
                    transition: all .3s
                }

                #right-share-tome .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
                    color: #222a35;
                    text-shadow: 0 0 .25px currentcolor
                }

                #right-share-tome .ant-tabs-tab+.ant-tabs-tab {
                    margin: 0 10px 0 32px
                }

                #right-share-tome .ant-tabs-content {
                    display: flex;
                    width: 100%
                }

                #right-share-tome .ant-tabs-content-holder {
                    flex: auto;
                    min-width: 0;
                    min-height: 0
                }

                #right-share-tome .ant-tabs-tabpane {
                    flex: none;
                    width: 100%;
                    outline: 0
                }

                #right-share-tome .ant-tabs {
                    color: rgba(34, 42, 53, .7);
                    position: relative
                }

                #right-share-tome .ant-tabs .ant-tabs-nav {
                    background: #fff
                }

                #right-share-tome .ant-tabs .ant-tabs-nav:before {
                    border-bottom: none
                }

                #right-share-tome .ant-tabs .ant-tabs-tab {
                    font-size: 16px;
                    line-height: 38px;
                    padding: 0
                }

                #right-share-tome .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
                    font-weight: 500
                }

                #right-share-tome .ant-tabs .ant-tabs-ink-bar {
                    background-color: #047ffe;
                    height: 2px !important;
                    border-radius: 8px
                }

                #right-share-tome .tab-top-title {
                    flex: 1
                }

                #right-share-tome .tab-top-title .ant-tabs-content-top {
                    height: 100%
                }

                #right-share-tome .tab-top-title .ant-tabs-nav {
                    margin-bottom: 15px
                }

                #right-share-tome .tab-top-title .ant-tabs-ink-bar {
                    display: none !important
                }

                #right-share-tome .tab-top-title .ant-tabs-tab-active {
                    position: relative
                }

                #right-share-tome .tab-top-title .ant-tabs-tab-active:before {
                    content: "";
                    width: 24px;
                    height: 2px;
                    background-color: #047ffe;
                    left: calc(50% - 12px);
                    bottom: 0;
                    z-index: 100;
                    position: absolute
                }

                #right-share-tome .tab-top-large .ant-tabs-tab {
                    font-size: 16px !important;
                    line-height: 24px !important;
                    padding-top: 4px !important;
                    padding-bottom: 6px !important
                }

                #right-share-tome .share-skeleton-box {
                    margin-top: -20px;
                    display: flex;
                    flex-direction: column;
                    height: 100%
                }

                #right-share-tome .share-skeleton-box .share-skeleton-title-wrap {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 18px;
                    padding: 0 32px
                }

                #right-share-tome .share-skeleton-box .share-skeleton-body-wrap {
                    padding: 0 32px;
                    height: 100%
                }

                #right-share-tome .share-skeleton-box .share-skeleton-body-wrap .share-skeleton-tab-bottom {
                    line-height: 20px;
                    overflow: hidden;
                    color: #2f343c;
                    font-weight: 500;
                    float: left;
                    max-width: 155px;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    text-align: center;
                    font-family: PingFangSC-Medium, PingFang SC;
                    padding: 0 5px;
                    padding-left: 0;
                    font-size: 12px
                }

                #right-share-tome .share-skeleton-box .share-skeleton-body-wrap .ant-tabs-nav {
                    margin: 0 0 16px !important
                }

                #right-share-tome .share-skeleton-box .skeleton-tome .file-header-skeleton-box {
                    margin: 4px auto 0 !important
                }

                #right-share-tome .share-skeleton-box .skeleton-tome .file-body-skeleton-box {
                    padding-top: 8px !important;
                    padding-bottom: 24px !important
                }

                #right-share-tome .dk-icon-fenxiang1:before {
                    content: "e752"
                }

                #right-share-tome .dk-icon-zhanneixin:before {
                    content: "e6f1"
                }

                #right-share-tome .dk-icon-tuanduikongjian4px:before {
                    content: "e6f5"
                }
            </style>
            <div id="right-share-tome" class="right" style="display:none">
                <div class="header-wrap">
                    <div class="header-warp">
                        <div class="header-top">
                            <div class="header-top-left">
                                <div class="global-search-wrap"><span class="ant-input-group-wrapper"><span class="ant-input-wrapper ant-input-group"><span class="ant-input-group-addon">
                                                <div class="search-before"><i class="dk-icon-sousuo dk-iconfont" style="color:#bbb"></i></div>
                                            </span><span class="ant-input-affix-wrapper"><input autocomplete="off" placeholder="搜索文档标题、正文、空间/知识库名" class="ant-input"> <span class="ant-input-suffix"><span class="ant-input-clear-icon ant-input-clear-icon-hidden" role="button" tabindex="-1"><span role="img" aria-label="close-circle" class="anticon anticon-close-circle"><svg fill-rule="evenodd" viewBox="64 64 896 896" focusable="false" data-icon="close-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                                <path d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"></path>
                                                            </svg> </span></span></span></span><span class="ant-input-group-addon"><span class="search-icon-btn"><span class="search-text">高级搜索</span>⌘+J</span></span></span></span></div>
                            </div>
                            <div class="cooper-customer-service">
                                <div class="newfeature-popup"><span><span class="cooper-customer-service-empty"></span></span></div>
                                <div class="cooper-helper-wrap">
                                    <div class="cooper-helper"><i class="dk-iconfont dk-icon-gongnengshangxin"></i></div>
                                </div>
                                <div class="feedback-with-popup"><span>
                                        <div class="feedback">
                                            <div class="content"><i class="dk-iconfont dk-icon-lianxikefu"></i></div>
                                        </div>
                                    </span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="body-wrap">
                    <div class="share-skeleton-box">
                        <div class="share-skeleton-title-wrap"></div>
                        <div class="share-skeleton-body-wrap skeleton-tome">
                            <div class="tab-skeleton">
                                <div class="ant-tabs ant-tabs-top ant-tabs-middle tab-top-title tab-top-large" tabsize="large">
                                    <div role="tablist" class="ant-tabs-nav">
                                        <div class="ant-tabs-nav-wrap">
                                            <div class="ant-tabs-nav-list" style="transform:translate(0,0)">
                                                <div class="ant-tabs-tab ant-tabs-tab-active">
                                                    <div role="tab" aria-selected="true" class="ant-tabs-tab-btn" tabindex="0" id="rc-tabs-0-tab-1" aria-controls="rc-tabs-0-panel-1"><span><span>分享给我</span></span></div>
                                                </div>
                                                <div class="ant-tabs-tab">
                                                    <div role="tab" aria-selected="false" class="ant-tabs-tab-btn" tabindex="0" id="rc-tabs-0-tab-2" aria-controls="rc-tabs-0-panel-2"><span><span>我分享的</span></span></div>
                                                </div>
                                                <div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated" style="left:0;width:64px"></div>
                                            </div>
                                        </div>
                                        <div class="ant-tabs-nav-operations ant-tabs-nav-operations-hidden"><button type="button" class="ant-tabs-nav-more" tabindex="-1" aria-hidden="true" aria-haspopup="listbox" aria-controls="rc-tabs-0-more-popup" id="rc-tabs-0-more" aria-expanded="false" style="visibility:hidden;order:1"><span role="img" aria-label="ellipsis" class="anticon anticon-ellipsis"><svg viewBox="64 64 896 896" focusable="false" data-icon="ellipsis" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                        <path d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"></path>
                                                    </svg></span></button></div>
                                    </div>
                                    <div class="ant-tabs-content-holder">
                                        <div class="ant-tabs-content ant-tabs-content-top">
                                            <div role="tabpanel" tabindex="0" aria-hidden="false" class="ant-tabs-tabpane ant-tabs-tabpane-active" id="rc-tabs-0-panel-1" aria-labelledby="rc-tabs-0-tab-1">
                                                <div class="share-skeleton-tab-bottom">全部文件</div>
                                                <style>
                                                    .file-skeleton-box {
                                                        margin: 0;
                                                        padding: 0;
                                                        min-width: 667px !important;
                                                        margin: -8px auto 0 !important
                                                    }

                                                    .file-skeleton-box .file-header-skeleton-box {
                                                        width: 100%;
                                                        height: 36px;
                                                        margin: 0;
                                                        padding: 0;
                                                        display: flex;
                                                        align-items: center
                                                    }

                                                    .file-skeleton-box .header-inner {
                                                        width: 100%;
                                                        height: 22px;
                                                        position: relative;
                                                        margin: 0;
                                                        padding: 0;
                                                        display: flex;
                                                        align-items: center;
                                                        overflow: hidden
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-button {
                                                        width: 42px;
                                                        height: 14px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-button * {
                                                        min-width: auto !important;
                                                        width: 100% !important;
                                                        height: 100% !important
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-name {
                                                        margin-right: 192px;
                                                        flex: 1;
                                                        min-width: 200px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-owner {
                                                        margin-right: 216px;
                                                        min-width: 42px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-address {
                                                        margin-right: 216px;
                                                        min-width: 42px
                                                    }

                                                    .file-skeleton-box .header-inner .file-header-skeleton-time {
                                                        margin-right: 22px;
                                                        min-width: 42px
                                                    }

                                                    .file-skeleton-box .file-body-skeleton-box {
                                                        width: 100%;
                                                        padding-top: 8px !important
                                                    }

                                                    .file-skeleton-box .body-inner>li {
                                                        width: 100%;
                                                        height: 46px;
                                                        position: relative
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-button * {
                                                        min-width: auto !important;
                                                        width: 100% !important;
                                                        height: 100% !important
                                                    }

                                                    .file-skeleton-box .body-inner li {
                                                        display: flex;
                                                        align-items: center
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-name-logo {
                                                        width: 19.2px;
                                                        height: 19.2px;
                                                        min-width: 19.2px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-name-title {
                                                        height: 14px;
                                                        flex: 1;
                                                        margin-left: 10px;
                                                        top: 17px;
                                                        left: 32px;
                                                        margin-right: 192px;
                                                        min-width: 170px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-name-title .ant-skeleton {
                                                        max-width: 500px !important
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-owner {
                                                        width: 66px;
                                                        height: 14px;
                                                        top: 17px;
                                                        left: 544px;
                                                        margin-right: 192px;
                                                        min-width: 66px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-address {
                                                        width: 66px;
                                                        height: 14px;
                                                        top: 17px;
                                                        left: 736px;
                                                        margin-right: 192px;
                                                        min-width: 66px
                                                    }

                                                    .file-skeleton-box .body-inner .file-body-skeleton-time {
                                                        width: 66px;
                                                        height: 14px;
                                                        top: 17px;
                                                        left: 928px min-width: 66px
                                                    }
                                                </style>
                                                <div class="file-skeleton-box">
                                                    <div class="file-header-skeleton-box">
                                                        <div class="header-inner">
                                                            <div class="file-header-skeleton-name">
                                                                <div class="file-header-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </div>
                                                            <div class="file-header-skeleton-owner file-header-skeleton-button">
                                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                            </div>
                                                            <div class="file-header-skeleton-address file-header-skeleton-button">
                                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                            </div>
                                                            <div class="file-header-skeleton-time file-header-skeleton-button">
                                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="file-body-skeleton-box">
                                                        <div class="body-inner">
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </li>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div role="tabpanel" tabindex="-1" aria-hidden="true" class="ant-tabs-tabpane" id="rc-tabs-0-panel-2" aria-labelledby="rc-tabs-0-tab-2" style="display:none"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                #right-favorite .favorite-wrap {
                    height: 100%
                }

                #right-favorite .page-title-wrap {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 18px;
                    padding: 0 32px
                }

                #right-favorite .page-title-wrap .title-name {
                    font-size: 16px;
                    line-height: 24px;
                    font-weight: 500;
                    color: #222a35;
                    display: inline-flex;
                    align-items: center;
                    line-height: 34px
                }

                #right-favorite .ant-table-column-sorter {
                    margin-left: 4px;
                    color: #bfbfbf;
                    font-size: 0;
                    transition: color .3s
                }

                #right-favorite .ant-table-column-sorter-down,
                #right-favorite .ant-table-column-sorter-up {
                    font-size: 11px
                }

                #right-favorite .favorite-skeleton-box {
                    height: 100%
                }

                #right-favorite .favorite-skeleton-box .favorite-skeleton-header {
                    display: flex;
                    justify-content: left;
                    align-items: center;
                    padding: 0 32px
                }

                #right-favorite .favorite-skeleton-box .favorite-skeleton-header .title-name {
                    font-size: 16px;
                    line-height: 24px;
                    font-weight: 500;
                    color: #222a35;
                    display: inline-flex;
                    align-items: center;
                    line-height: 34px
                }

                #right-favorite .favorite-skeleton-box .favorite-skeleton-body {
                    padding: 0 32px 0
                }

                #right-favorite .global-search-wrap .search-tag {
                    margin-left: 8px;
                    padding: 0 4px 0 8px;
                    border-radius: 3px;
                    background: #ebeef1;
                    display: flex;
                    align-items: center;
                    color: rgba(0, 0, 0, .7)
                }

                #right-favorite .global-search-wrap .search-tag>span>i {
                    color: #333;
                    font-size: 14px;
                    cursor: pointer
                }
            </style>
            <div id="right-favorite" class="right" style="display:none">
                <div class="header-wrap">
                    <div class="header-warp">
                        <div class="header-top">
                            <div class="header-top-left">
                                <div class="global-search-wrap"><span class="ant-input-group-wrapper"><span class="ant-input-wrapper ant-input-group"><span class="ant-input-group-addon">
                                                <div class="search-before"><i class="dk-icon-sousuo dk-iconfont" style="color:#bbb"></i>
                                                    <p class="search-tag">收藏-input <span style="cursor:pointer;height:20px"><i class="dk-icon-guanbi dk-iconfont" style="font-size:20px"></i></span></p>
                                                </div>
                                            </span><span class="ant-input-affix-wrapper"><input autocomplete="off" placeholder="搜索文档标题、正文1" class="ant-input"> <span class="ant-input-suffix"><span class="ant-input-clear-icon ant-input-clear-icon-hidden" role="button" tabindex="-1"><span role="img" aria-label="close-circle" class="anticon anticon-close-circle"><svg fill-rule="evenodd" viewBox="64 64 896 896" focusable="false" data-icon="close-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                                <path d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"></path>
                                                            </svg> </span></span></span></span><span class="ant-input-group-addon"><span class="search-icon-btn"><span class="search-text">高级搜索</span>⌘+J</span></span></span></span></div>
                            </div>
                            <div class="cooper-customer-service">
                                <div class="newfeature-popup"><span><span class="cooper-customer-service-empty"></span></span></div>
                                <div class="cooper-helper-wrap">
                                    <div class="cooper-helper"><i class="dk-iconfont dk-icon-gongnengshangxin"></i></div>
                                </div>
                                <div class="feedback-with-popup"><span>
                                        <div class="feedback">
                                            <div class="content"><i class="dk-iconfont dk-icon-lianxikefu"></i></div>
                                        </div>
                                    </span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="body-wrap">
                    <div class="favorite-skeleton-box">
                        <div class="favorite-skeleton-header" style="margin-bottom:18px">
                            <div class="title-name">我的收藏</div>
                        </div>
                        <div class="favorite-skeleton-body">
                            <style>
                                .file-skeleton-box {
                                    margin: 0;
                                    padding: 0;
                                    min-width: 667px !important;
                                    margin: -8px auto 0 !important
                                }

                                .file-skeleton-box .file-header-skeleton-box {
                                    width: 100%;
                                    height: 36px;
                                    margin: 0;
                                    padding: 0;
                                    display: flex;
                                    align-items: center
                                }

                                .file-skeleton-box .header-inner {
                                    width: 100%;
                                    height: 22px;
                                    position: relative;
                                    margin: 0;
                                    padding: 0;
                                    display: flex;
                                    align-items: center;
                                    overflow: hidden
                                }

                                .file-skeleton-box .header-inner .file-header-skeleton-button {
                                    width: 42px;
                                    height: 14px
                                }

                                .file-skeleton-box .header-inner .file-header-skeleton-button * {
                                    min-width: auto !important;
                                    width: 100% !important;
                                    height: 100% !important
                                }

                                .file-skeleton-box .header-inner .file-header-skeleton-name {
                                    margin-right: 192px;
                                    flex: 1;
                                    min-width: 200px
                                }

                                .file-skeleton-box .header-inner .file-header-skeleton-owner {
                                    margin-right: 216px;
                                    min-width: 42px
                                }

                                .file-skeleton-box .header-inner .file-header-skeleton-address {
                                    margin-right: 216px;
                                    min-width: 42px
                                }

                                .file-skeleton-box .header-inner .file-header-skeleton-time {
                                    margin-right: 22px;
                                    min-width: 42px
                                }

                                .file-skeleton-box .file-body-skeleton-box {
                                    width: 100%;
                                    padding-top: 8px !important
                                }

                                .file-skeleton-box .body-inner>li {
                                    width: 100%;
                                    height: 46px;
                                    position: relative
                                }

                                .file-skeleton-box .body-inner .file-body-skeleton-button * {
                                    min-width: auto !important;
                                    width: 100% !important;
                                    height: 100% !important
                                }

                                .file-skeleton-box .body-inner li {
                                    display: flex;
                                    align-items: center
                                }

                                .file-skeleton-box .body-inner .file-body-skeleton-name-logo {
                                    width: 19.2px;
                                    height: 19.2px;
                                    min-width: 19.2px
                                }

                                .file-skeleton-box .body-inner .file-body-skeleton-name-title {
                                    height: 14px;
                                    flex: 1;
                                    margin-left: 10px;
                                    top: 17px;
                                    left: 32px;
                                    margin-right: 192px;
                                    min-width: 170px
                                }

                                .file-skeleton-box .body-inner .file-body-skeleton-name-title .ant-skeleton {
                                    max-width: 500px !important
                                }

                                .file-skeleton-box .body-inner .file-body-skeleton-owner {
                                    width: 66px;
                                    height: 14px;
                                    top: 17px;
                                    left: 544px;
                                    margin-right: 192px;
                                    min-width: 66px
                                }

                                .file-skeleton-box .body-inner .file-body-skeleton-address {
                                    width: 66px;
                                    height: 14px;
                                    top: 17px;
                                    left: 736px;
                                    margin-right: 192px;
                                    min-width: 66px
                                }

                                .file-skeleton-box .body-inner .file-body-skeleton-time {
                                    width: 66px;
                                    height: 14px;
                                    top: 17px;
                                    left: 928px min-width: 66px
                                }
                            </style>
                            <div class="file-skeleton-box">
                                <div class="file-header-skeleton-box">
                                    <div class="header-inner">
                                        <div class="file-header-skeleton-name">
                                            <div class="file-header-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </div>
                                        <div class="file-header-skeleton-owner file-header-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                        <div class="file-header-skeleton-address file-header-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                        <div class="file-header-skeleton-time file-header-skeleton-button">
                                            <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="file-body-skeleton-box">
                                    <div class="body-inner">
                                        <li>
                                            <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-address file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-time file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-address file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-time file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-address file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-time file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-address file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-time file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-address file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-time file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-address file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-time file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-address file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-time file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-address file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                            <div class="file-body-skeleton-time file-body-skeleton-button">
                                                <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                            </div>
                                        </li>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                #right-trash .trash-skeleton-box {
                    height: 100%;
                    position: relative;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column
                }

                #right-trash .trash-skeleton-box .trash-skeleton-wrap {
                    flex: 1;
                    overflow: hidden;
                    padding: 0 32px
                }

                #right-trash .trash-skeleton-box .trash-skeleton-wrap .trash-skeleton-show-tip .ant-tabs-nav {
                    padding: 0 0 16px !important;
                    margin: 0 !important
                }

                #right-trash .trash-skeleton-box .trash-skeleton-wrap .trash-skeleton-show-tip .file-skeleton-box {
                    min-height: 100% !important;
                    margin-top: -12px !important
                }

                #right-trash .trash-skeleton-box .trash-skeleton-wrap .trash-skeleton-show-tip .file-body-skeleton-box {
                    height: calc(100% - 68px) !important;
                    min-width: 100% !important;
                    padding-top: 8px !important
                }

                #right-trash .trash-skeleton-box .trash-skeleton-wrap .trash-skeleton-unshow-tip .ant-tabs-nav {
                    padding: 0 !important;
                    margin: 0 0 16px !important
                }

                #right-trash .trash-skeleton-box .trash-skeleton-wrap .trash-skeleton-unshow-tip .file-skeleton-box {
                    height: 100% !important;
                    margin-top: 40px !important
                }

                #right-trash .trash-skeleton-box .trash-skeleton-wrap .trash-skeleton-unshow-tip .file-body-skeleton-box {
                    height: calc(100% - 68px) !important;
                    min-width: 100% !important;
                    padding-top: 8px !important
                }
            </style>
            <div id="right-trash" class="right" style="display:none">
                <div class="header-wrap">
                    <div class="header-warp">
                        <div class="header-top">
                            <div class="header-top-left">
                                <div class="global-search-wrap"><span class="ant-input-group-wrapper"><span class="ant-input-wrapper ant-input-group"><span class="ant-input-group-addon">
                                                <div class="search-before"><i class="dk-icon-sousuo dk-iconfont" style="color:#bbb"></i></div>
                                            </span><span class="ant-input-affix-wrapper"><input autocomplete="off" placeholder="搜索文档标题、正文、空间/知识库名" class="ant-input"> <span class="ant-input-suffix"><span class="ant-input-clear-icon ant-input-clear-icon-hidden" role="button" tabindex="-1"><span role="img" aria-label="close-circle" class="anticon anticon-close-circle"><svg fill-rule="evenodd" viewBox="64 64 896 896" focusable="false" data-icon="close-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                                <path d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"></path>
                                                            </svg> </span></span></span></span><span class="ant-input-group-addon"><span class="search-icon-btn"><span class="search-text">高级搜索</span>⌘+J</span></span></span></span></div>
                            </div>
                            <div class="cooper-customer-service">
                                <div class="newfeature-popup"><span><span class="cooper-customer-service-empty"></span></span></div>
                                <div class="cooper-helper-wrap">
                                    <div class="cooper-helper"><i class="dk-iconfont dk-icon-gongnengshangxin"></i></div>
                                </div>
                                <div class="feedback-with-popup"><span>
                                        <div class="feedback">
                                            <div class="content"><i class="dk-iconfont dk-icon-lianxikefu"></i></div>
                                        </div>
                                    </span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="body-wrap">
                    <div class="trash-skeleton-box">
                        <div class="trash-skeleton-wrap">
                            <div class="trash-skeleton-show-tip">
                                <div class="tab-skeleton">
                                    <div class="ant-tabs ant-tabs-top ant-tabs-middle tab-top-title tab-top-large" tabsize="large">
                                        <div role="tablist" class="ant-tabs-nav">
                                            <div class="ant-tabs-nav-wrap">
                                                <div class="ant-tabs-nav-list" style="transform:translate(0,0)">
                                                    <div class="ant-tabs-tab ant-tabs-tab-active">
                                                        <div role="tab" aria-selected="true" class="ant-tabs-tab-btn" tabindex="0" id="rc-tabs-0-tab-1" aria-controls="rc-tabs-0-panel-1"><span><span>文件回收站</span></span></div>
                                                    </div>
                                                    <div class="ant-tabs-tab">
                                                        <div role="tab" aria-selected="false" class="ant-tabs-tab-btn" tabindex="0" id="rc-tabs-0-tab-2" aria-controls="rc-tabs-0-panel-2"><span><span>空间回收站</span></span></div>
                                                    </div>
                                                    <div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated" style="left:0;width:80px"></div>
                                                </div>
                                            </div>
                                            <div class="ant-tabs-nav-operations ant-tabs-nav-operations-hidden"><button type="button" class="ant-tabs-nav-more" tabindex="-1" aria-hidden="true" aria-haspopup="listbox" aria-controls="rc-tabs-0-more-popup" id="rc-tabs-0-more" aria-expanded="false" style="visibility:hidden;order:1"><span role="img" aria-label="ellipsis" class="anticon anticon-ellipsis"><svg viewBox="64 64 896 896" focusable="false" data-icon="ellipsis" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                            <path d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"></path>
                                                        </svg></span></button></div>
                                        </div>
                                        <div class="ant-tabs-content-holder">
                                            <div class="ant-tabs-content ant-tabs-content-top">
                                                <div role="tabpanel" tabindex="0" aria-hidden="false" class="ant-tabs-tabpane ant-tabs-tabpane-active" id="rc-tabs-0-panel-1" aria-labelledby="rc-tabs-0-tab-1">
                                                    <style>
                                                        .file-skeleton-box {
                                                            margin: 0;
                                                            padding: 0;
                                                            min-width: 667px !important;
                                                            margin: -8px auto 0 !important
                                                        }

                                                        .file-skeleton-box .file-header-skeleton-box {
                                                            width: 100%;
                                                            height: 36px;
                                                            margin: 0;
                                                            padding: 0;
                                                            display: flex;
                                                            align-items: center
                                                        }

                                                        .file-skeleton-box .header-inner {
                                                            width: 100%;
                                                            height: 22px;
                                                            position: relative;
                                                            margin: 0;
                                                            padding: 0;
                                                            display: flex;
                                                            align-items: center;
                                                            overflow: hidden
                                                        }

                                                        .file-skeleton-box .header-inner .file-header-skeleton-button {
                                                            width: 42px;
                                                            height: 14px
                                                        }

                                                        .file-skeleton-box .header-inner .file-header-skeleton-button * {
                                                            min-width: auto !important;
                                                            width: 100% !important;
                                                            height: 100% !important
                                                        }

                                                        .file-skeleton-box .header-inner .file-header-skeleton-name {
                                                            margin-right: 192px;
                                                            flex: 1;
                                                            min-width: 200px
                                                        }

                                                        .file-skeleton-box .header-inner .file-header-skeleton-owner {
                                                            margin-right: 216px;
                                                            min-width: 42px
                                                        }

                                                        .file-skeleton-box .header-inner .file-header-skeleton-address {
                                                            margin-right: 216px;
                                                            min-width: 42px
                                                        }

                                                        .file-skeleton-box .header-inner .file-header-skeleton-time {
                                                            margin-right: 22px;
                                                            min-width: 42px
                                                        }

                                                        .file-skeleton-box .file-body-skeleton-box {
                                                            width: 100%;
                                                            padding-top: 8px !important
                                                        }

                                                        .file-skeleton-box .body-inner>li {
                                                            width: 100%;
                                                            height: 46px;
                                                            position: relative
                                                        }

                                                        .file-skeleton-box .body-inner .file-body-skeleton-button * {
                                                            min-width: auto !important;
                                                            width: 100% !important;
                                                            height: 100% !important
                                                        }

                                                        .file-skeleton-box .body-inner li {
                                                            display: flex;
                                                            align-items: center
                                                        }

                                                        .file-skeleton-box .body-inner .file-body-skeleton-name-logo {
                                                            width: 19.2px;
                                                            height: 19.2px;
                                                            min-width: 19.2px
                                                        }

                                                        .file-skeleton-box .body-inner .file-body-skeleton-name-title {
                                                            height: 14px;
                                                            flex: 1;
                                                            margin-left: 10px;
                                                            top: 17px;
                                                            left: 32px;
                                                            margin-right: 192px;
                                                            min-width: 170px
                                                        }

                                                        .file-skeleton-box .body-inner .file-body-skeleton-name-title .ant-skeleton {
                                                            max-width: 500px !important
                                                        }

                                                        .file-skeleton-box .body-inner .file-body-skeleton-owner {
                                                            width: 66px;
                                                            height: 14px;
                                                            top: 17px;
                                                            left: 544px;
                                                            margin-right: 192px;
                                                            min-width: 66px
                                                        }

                                                        .file-skeleton-box .body-inner .file-body-skeleton-address {
                                                            width: 66px;
                                                            height: 14px;
                                                            top: 17px;
                                                            left: 736px;
                                                            margin-right: 192px;
                                                            min-width: 66px
                                                        }

                                                        .file-skeleton-box .body-inner .file-body-skeleton-time {
                                                            width: 66px;
                                                            height: 14px;
                                                            top: 17px;
                                                            left: 928px min-width: 66px
                                                        }
                                                    </style>
                                                    <div class="file-skeleton-box">
                                                        <div class="file-header-skeleton-box">
                                                            <div class="header-inner">
                                                                <div class="file-header-skeleton-name">
                                                                    <div class="file-header-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                </div>
                                                                <div class="file-header-skeleton-owner file-header-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-header-skeleton-address file-header-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                                <div class="file-header-skeleton-time file-header-skeleton-button">
                                                                    <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="file-body-skeleton-box">
                                                            <div class="body-inner">
                                                                <li>
                                                                    <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                </li>
                                                                <li>
                                                                    <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                </li>
                                                                <li>
                                                                    <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                </li>
                                                                <li>
                                                                    <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                </li>
                                                                <li>
                                                                    <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                </li>
                                                                <li>
                                                                    <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                </li>
                                                                <li>
                                                                    <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                </li>
                                                                <li>
                                                                    <div class="file-body-skeleton-name-logo file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element"><span class="ant-skeleton-button ant-skeleton-button-square"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-name-title file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-owner file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-address file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                    <div class="file-body-skeleton-time file-body-skeleton-button">
                                                                        <div class="ant-skeleton ant-skeleton-element ant-skeleton-active"><span class="ant-skeleton-button ant-skeleton-button-round"></span></div>
                                                                    </div>
                                                                </li>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div role="tabpanel" tabindex="-1" aria-hidden="true" class="ant-tabs-tabpane" id="rc-tabs-0-panel-2" aria-labelledby="rc-tabs-0-tab-2" style="display:none"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="loading-wrap" id="origin-loading" style="display:none">
            <div class="my-spin" id="myLoadingContainer"><span class="my-spin-dot-span"><i class="my-spin-dot-item"></i> <i class="my-spin-dot-item"></i> <i class="my-spin-dot-item"></i> <i class="my-spin-dot-item"></i></span>
                <div class="my-text-container"><img src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" /></div>
            </div>
        </div>
    </div>
    <div id="root"></div>
    <script type="text/javascript">
        function getLocaleFromScratch() {
            /**
             * 将 string 转成 param(即 key, value 对象)。
             * @param {*} srcStr 待转化字符串
             * @param {*} splitter pair 和 pair 的分隔符
             * @param {*} sepKeyValue 一个 pair 中 key 和 value 的分隔符
             * @returns
             */
            function parseString(srcStr, splitter, sepKeyValue) {
                let params = {};
                let pairs = srcStr.split(splitter);
                pairs.forEach(function(pair) {
                    if (pair) {
                        // '' false
                        let keyValue = pair.split(sepKeyValue);
                        let key = keyValue[0].trim();
                        let value = decodeURIComponent(keyValue[1].trim());
                        if (key) {
                            params[key] = value; // 后面会覆盖前面
                        }
                    }
                });
                return params;
            }

            function getUrlLang() {
                let queryString = location.search?.split("?")[1] || "";
                let urlParams = parseString(queryString, "&", "=");
                let urlLang = urlParams["lang"] || urlParams["local_language"] || "";
                return urlLang;
            }

            function getCookieLang() {
                let cookieParams = parseString(document.cookie, ";", "=");
                let cookieLang =
                    cookieParams["lang"] || cookieParams["local_language"] || "";
                return cookieLang;
            }

            function getBrowserLang() {
                let browserLocale = navigator.language || "";
                let browserLang = browserLocale.split(/_|-/)[0]; // 参考：https://ant.design/docs/react/i18n-cn
                switch (browserLang) {
                    case "en":
                        return "en-US";
                    case "zh":
                        return "zh-CN";
                    default:
                        return "zh-CN";
                }
            }
            return getUrlLang() || getCookieLang() || getBrowserLang() || "zh-CN";
        }

        function processIntl() {
            const enDict = {
                首页: "Home",
                个人: "Person",
                团队: "Team",
                知识库: "Knowl.",
                分享: "Share",
                收藏: "Fav.",
                回收站: "Trash",
                更多: "More",
                "搜索文档标题、正文、知识库名": "Search title of doc/repository, body text",
                "搜索文档标题、正文、空间/知识库名": "Search title of doc/space/repository, body text",
                "搜索文档标题、正文1": "Search doc's title and body text",
                高级搜索: "Advanced Search",
                上传: "Upload",
                新建: "Create",
                快速访问: "Quick Access",
                最近访问: "Recently",
                最近编辑: "Last Edited",
                全部文件: "All files",
                当前空间: "Current Space",
                全部团队空间: "All team spaces",
                新建团队空间: "Create New Space",
                分享给我: "Share with me",
                我分享的: "Shared by me",
                "收藏-input": "Favorites",
                我的收藏: "Favorites",
                文件回收站: "File",
                空间回收站: "Space",
                数据安全: "Data Security",
                常见问题: "FAQ"
            };
            const zhDict = {
                首页: "首页",
                个人: "个人",
                团队: "团队",
                知识库: "知识库",
                分享: "分享",
                收藏: "收藏",
                回收站: "回收站",
                更多: "更多",
                "搜索文档标题、正文、知识库名": "搜索文档标题、正文、知识库名",
                "搜索文档标题、正文、空间/知识库名": "搜索文档标题、正文、空间/知识库名",
                "搜索文档标题、正文1": "搜索文档标题、正文",
                高级搜索: "高级搜索",
                上传: "上传",
                新建: "新建",
                快速访问: "快速访问",
                最近访问: "最近访问",
                最近编辑: "最近编辑",
                全部文件: "全部文件",
                当前空间: "当前空间",
                全部团队空间: "全部团队空间",
                新建团队空间: "新建团队空间",
                分享给我: "分享给我",
                我分享的: "我分享的",
                "收藏-input": "收藏",
                我的收藏: "我的收藏",
                文件回收站: "文件回收站",
                空间回收站: "空间回收站",
                数据安全: "数据安全",
                常见问题: "常见问题"
            };

            function replaceTextByDict(node, dict) {
                if (node.nodeType === Node.TEXT_NODE) {
                    // 如果是文本节点，检查文本内容是否包含目标文本
                    let k = node.nodeValue.trim();
                    if (Object.keys(dict).includes(k)) {
                        node.nodeValue = dict[k];
                    }
                } else if (node.nodeType === Node.ELEMENT_NODE) {
                    if (node.tagName === "INPUT" && node.hasAttribute("placeholder")) {
                        let k = node.getAttribute("placeholder").trim();
                        if (Object.keys(dict).includes(k)) {
                            node.setAttribute("placeholder", dict[k]);
                        }
                    } else {
                        // 递归遍历其子节点
                        for (let childNode of node.childNodes) {
                            replaceTextByDict(childNode, dict);
                        }
                    }
                }
            }

            let locale = getLocaleFromScratch();
            let rootElem = document.getElementById("root-skeleton");
            replaceTextByDict(rootElem, locale === "en-US" ? enDict : zhDict);
        }

        function useLoadingSkeleton() {
            let loadingDiv = document.getElementById("origin-loading");
            if (loadingDiv) {
                loadingDiv.style.display = "flex";
            }
        }

        function matchPath(path, url) {
            const normalizedPath = path.replace(/\/+$/, "");
            const normalizedUrl = url.replace(/\/+$/, "");

            const pathParts = normalizedPath.split("/");
            const urlParts = normalizedUrl.split("/");

            if (pathParts.length !== urlParts.length) {
                return false;
            }

            for (let i = 0; i < pathParts.length; i++) {
                const pathPart = pathParts[i];
                const urlPart = urlParts[i];

                if (pathPart.startsWith(":")) {
                    // Dynamic field, continue to next part
                    continue;
                } else if (pathPart !== urlPart) {
                    // Exact match required for fixed parts
                    return false;
                }
            }
            return true;
        }

        function processSkeletonStyleByPath() {
            const path2Key = {
                "/tenant": undefined,
                "/teams/invite/:inviteId": undefined,
                "/team-file/team-invalid": undefined,
                "/team-file/team-forbid/:type": undefined,
                "/shares/:shareid": undefined,
                "/send-file": undefined,
                "/select-file-mobile": undefined,
                "/select-file":  undefined,
                "/knowledge/:knowledgeId/home": undefined,
                "/knowledge/:knowledgeId/setUp": undefined,
                "/knowledge/:knowledgeId/recycleBin": undefined,
                "/knowledge/:knowledgeId/dataBoard": undefined,
                "/knowledge/:knowledgeId/:pageId": undefined,
                "/knowledge/:knowledgeId/:pageId/edit": undefined,
                "/knowledge/:knowledgeId/template/:templateId/edit": undefined,
                "/knowledge": undefined,
                "/knowledge/": undefined,
                "/knowledge/snapshoot/:snapshootId/:version": undefined,
                "/knowledge/errorPage": undefined,
                "/knowledge/expire": undefined,
                "/knowledge/create": undefined,
                "/knowledge/recent": undefined,
                "/knowledge/portalList": undefined,
                "/knowledge/star": undefined,
                "/knowledge/share/book/:shareId": undefined,
                "/knowledge/share/book/:shareId/:pageId": undefined,
                "/knowledge/share/page/:shareId": undefined,
                "/knowledge/view/page/:shareId": undefined,
                "/knowledge/view/:knowledgeId/:pageId": undefined,
                "/library": undefined,
                "/dk": undefined,
                "/team-file/:teamId/trash": undefined,
                "/team-file/:teamId/member": undefined,
                "/team-file/:teamId/dynamic": undefined,
                "/team-file/:teamId/setup": undefined,
                "/team-file/:teamId/knowledge/:knowledgeId/home": undefined,
                "/team-file/:teamId/knowledge/:knowledgeId/setUp": undefined,
                "/team-file/:teamId/knowledge/:knowledgeId/recycleBin": undefined,
                "/team-file/:teamId/knowledge/:knowledgeId/dataBoard": undefined,
                "/team-file/:teamId/knowledge/:knowledgeId/:pageId": undefined,
                "/team-file/:teamId/knowledge/:knowledgeId/:pageId/edit": undefined,
                "/team-file/:teamId/knowledge/:knowledgeId/template/:templateId/edit": undefined,
                "/videopreview/:type/:id/:shareId": undefined,
                "/preview": undefined,
                "/note": undefined,
                "/note/:number": undefined,
                "/note/:number/:number2": undefined,
                "/": "home",
                "/disk": "disk",
                "/team-folder": "team-folder",
                "/tome": "tome",
                "/favorite": "favorite",
                "/trash": "trash",
                "/fromme": "fromme",
                "/tome/:shareId/:foldId": "tome",
                "/team-file/:teamId": "team-file",
                "/team-file/:teamId/home": "team-file",
                "/team-file/:teamId/:foldId": "team-file",
                "/files/:foldId": "files",
            };
            const pathArr = Object.keys(path2Key);
            const pathConfig = {
                // nthChildLi用于查找当前url在左侧小型导航栏的图标是第几个，activeIconClass是这些图标激活时的类名，inactive同理，rightBoxId是此url对应的右侧的box的id
                home: {
                    nthChildLi: 1,
                    inactiveIconClass: "dk-icon-zhuye4",
                    activeIconClass: "dk-icon-zhuye-mian",
                    rightBoxId: "right-home",
                },
                disk: {
                    nthChildLi: 2,
                    inactiveIconClass: "dk-icon-gerenkongjian",
                    activeIconClass: "dk-icon-rongqi",
                    rightBoxId: "right-person",
                },
                files: {
                    nthChildLi: 2,
                    inactiveIconClass: "dk-icon-gerenkongjian",
                    activeIconClass: "dk-icon-rongqi",
                    rightBoxId: "right-person",
                },
                "team-file": {
                    nthChildLi: 3,
                    inactiveIconClass: "dk-icon-tuanduikongjian4px",
                    activeIconClass: "dk-icon-tuanduikongjian",
                    rightBoxId: "right-team-detail",
                },
                "team-folder": {
                    nthChildLi: 3,
                    inactiveIconClass: "dk-icon-tuanduikongjian4px",
                    activeIconClass: "dk-icon-tuanduikongjian",
                    rightBoxId: "right-team",
                },
                tome: {
                    nthChildLi: 5,
                    inactiveIconClass: "dk-icon-fenxiang4px",
                    activeIconClass: "dk-icon-fenxiang1",
                    rightBoxId: "right-share-tome",
                },
                fromme: {
                    nthChildLi: 5,
                    inactiveIconClass: "dk-icon-fenxiang4px",
                    activeIconClass: "dk-icon-fenxiang1",
                    rightBoxId: "right-share-fromme",
                },
                favorite: {
                    nthChildLi: 6,
                    inactiveIconClass: "dk-icon-shoucang4px",
                    activeIconClass: "dk-icon-yishoucang1",
                    rightBoxId: "right-favorite",
                },
                trash: {
                    nthChildLi: 7,
                    inactiveIconClass: "dk-icon-huishouzhan4px",
                    activeIconClass: "dk-icon-huishouzhan1",
                    rightBoxId: "right-trash",
                },
            };

            const pathname = window.location?.pathname?.toLowerCase() || "";
            let matchedKey = "home"; //没有匹配的路由，走home，走home的skeleton。未做的骨架屏，已在path2Key中体现为undefined
            for (const p of pathArr) {
                if (matchPath(p, pathname)) {
                    matchedKey = path2Key[p];
                    break;
                }
            }

            if (!matchedKey) {
                useLoadingSkeleton();
                return;
            }

            const config = pathConfig[matchedKey];

            // process left aside style
            let asideType;
            try {
                asideType = JSON.parse(
                    localStorage.getItem("dk-asideType") || "{}"
                )?.value;
            } catch (e) {
                console.warn(
                    "in index.html, localStorage.getItem(dk-asideType), error:",
                    e
                );
            }
            let asideElem;
            switch (asideType) {
                case 2:
                    asideElem = document.getElementById("layout-left-small");
                    if (asideElem) {
                        asideElem.style.display = "flex";

                        let ulEle = asideElem.querySelector(".aside-small");
                        let liEle = ulEle.querySelector(
                            `li:nth-child(${config.nthChildLi})`
                        );
                        let aEle = liEle.querySelector("a");
                        aEle.classList.add("item-name-active", "active");
                        let iEle = aEle.querySelector("i");
                        iEle.classList.remove(config.inactiveIconClass);
                        iEle.classList.add(config.activeIconClass);
                    }
                    break;
                case 1:
                default:
                    asideElem = document.getElementById("layout-left-large");
                    if (asideElem) {
                        asideElem.style.display = "flex";
                    }
                    break;
            }

            // process right skeleton style
            let rightBox = document.getElementById(`${config.rightBoxId}`);
            if (rightBox) {
                rightBox.style.display = "flex";
            }

            processIntl();
        }

        processSkeletonStyleByPath();
    </script>
    <script type="text/javascript">
        window.uploadCount = {
            sum: 0,
            cancel: 0,
            success: 0,
        };
        try {
            new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    if (/js-wartermark/.test(entry.id)) return;
                    if (/s3|flow_chart/.test(entry.url)) return;
                    if (
                        entry.element?.className.includes("publish-icon") ||
                        entry.element?.className.includes("emptyIcon")
                    )
                        return;
                    if (entry.element === null) return;
                    let className =
                        entry.element?.className ||
                        entry.element?.parentNode?.className ||
                        entry.element?.parentNode?.parentNode?.className;
                    if (className?.indexOf(" ") !== -1)
                        className = className.split(" ")?.[0];
                    if (document.querySelector(`#knowledge_editor_box ${className}`))
                        return;
                    console.log("LCP candidate:", entry.startTime, entry);
                    performance && performance.mark("largest-contentful-paint");
                }
            }).observe({
                type: "largest-contentful-paint",
                buffered: true
            });
        } catch (error) {}
    </script>
    <script type="text/javascript">
        function getUserNameFromCookie() {
            const m = document.cookie?.match(/username=([^;]*)/);
            return decodeURIComponent(m ? m[1] : "");
        }
        let env = "prod";

        // https://omega.xiaojukeji.com/omegabook/sdk/webLite/
        window.Omega = window.Omega || {
            appKey: env === "prod" ? "omega960d913d18" : "omega2e0624685b",
            autoResourceError: true, // 打开静态资源监控
            // autoClick: false,
            autoPosition: false,
            hashRouterEnable: true,
            browserRouterEnable: false,
            autoSendPageView: false,
            fastLoad: true,
            crashMonitor: { // 页面崩溃
                type: 'onload',
                crashTime: 18 // s
            },
            // jankMonitor: {  // 页面卡顿 - 持续监测，对性能有影响；
            //   type: 'onload', // onevent 在页面触发’click’或’keydown’后的某段时间内进行检测。
            // },
            autoWhiteScreenMonitor: {
              container: '#root', // 白屏监控的目标根节点
              childrenDepth: 6,  // 监听深度 默认为2 监听到根节点下一层
              durationSeconds: 20000, // 白屏持续时间 单位ms
            },
            // jsErrorFilters: [   
            //   { message: (lcainfo)|(sse) } //过滤无用的错误信息
            // ],
            // autoPerformance: false, //注意关闭性能上报，避免和npm中引入的性能监控 重复上报
            userName: getUserNameFromCookie(),
            lcpFilter(dom) {
                return /js-wartermark/.test(dom.id);
            },
        };
    </script>
    <script>
        const userAgent = navigator.userAgent,
            isIpad = /macintosh|mac os x/i.test(userAgent) && window.screen.height > window.screen.width && !userAgent.match(/(iPhone\sOS)\s([\d_]+)/) && userAgent.match(/(iPad).*OS\s([\d_]+)/);
        isIpad && (window.document.body.style.overflow = "hidden")
    </script>
    <script type="text/javascript" crossorigin="Anonymous" src="https://tracker.didistatic.com/static/tracker/latest3x/omega.min.js"></script>
    <script src="https://img-hxy021.didistatic.com/static/star/orange.sdk.v0.10.0.min.js"></script>
    <script type="text/javascript" src="https://img-hxy021.didistatic.com/static/ep_static/dc-h5-js-sdk-0.0.64.js"></script>
</body>

</html>