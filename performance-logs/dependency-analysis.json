{"timestamp": "2025-07-15T12:25:30.498Z", "largestPackages": [{"name": "antd", "size": 48673111, "version": "4.21.7"}, {"name": "@ant-design/icons", "size": 15559402, "version": "4.8.1"}, {"name": "sass", "size": 6401214, "version": "1.89.2"}, {"name": "webpack", "size": 5968535, "version": "5.100.1"}, {"name": "swiper", "size": 4756184, "version": "6.4.5"}, {"name": "eslint", "size": 4450169, "version": "7.32.0"}, {"name": "less", "size": 3829927, "version": "4.3.0"}, {"name": "@didi/didoc-mark-link", "size": 3159537, "version": "1.3.6"}, {"name": "html2canvas", "size": 3093767, "version": "1.0.0"}, {"name": "@didi/didoc-editor-menubar", "size": 3048659, "version": "1.3.7"}, {"name": "react-dom", "size": 2988155, "version": "17.0.2"}, {"name": "babel-plugin-enhance-log", "size": 2752666, "version": "0.3.0"}, {"name": "@didi/didoc-upload-file-node", "size": 2314943, "version": "1.3.2"}, {"name": "video-react", "size": 2172604, "version": "0.10.9"}, {"name": "webpack-bundle-analyzer", "size": 1933299, "version": "4.10.2"}, {"name": "@didi/didoc-core", "size": 1823400, "version": "2.2.7"}, {"name": "jschardet", "size": 1364271, "version": "2.3.0"}, {"name": "@didi/didoc-node-orderlist", "size": 1198819, "version": "1.0.6"}, {"name": "@didi/didoc-plugin-image", "size": 1193568, "version": "1.5.3"}, {"name": "cache-loader", "size": 1174789, "version": "4.1.0"}], "duplicateDependencies": [], "potentiallyUnusedDependencies": ["@babel/plugin-transform-runtime", "@babel/runtime-corejs3", "@didi/didoc-extensions", "@didi/didoc-plugin-formatbrush", "@didi/halo-client-web", "@rematch/updated", "ajv", "ajv-keywords", "babel-plugin-enhance-log", "chalk", "core-js", "fs-extra", "js-cookie", "normalize.css", "prosemirror-commands", "prosemirror-gapcursor", "prosemirror-history", "prosemirror-<PERSON><PERSON><PERSON>", "prosemirror-markdown", "prosemirror-model", "prosemirror-state", "prosemirror-tables", "prosemirror-utils", "prosemirror-view", "redux", "resize-observer-polyfill", "schema-utils", "tiny-svg", "url-parse", "video-react", "workbox-webpack-plugin"], "totalPackages": 151, "totalSize": 154053746}