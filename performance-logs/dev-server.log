
> knowledge-forge@0.1.0 start
> APP_ENV=dev node build/start.js

node:internal/modules/cjs/loader:451
      throw err;
      ^

Error: Cannot find module '/Users/<USER>/work/knowledgeforge/node_modules/react-refresh/index.js'. Please verify that the package.json has a valid "main" entry
    at tryPackage (node:internal/modules/cjs/loader:443:19)
    at Module._findPath (node:internal/modules/cjs/loader:711:18)
    at Module._resolveFilename (node:internal/modules/cjs/loader:1126:27)
    at Function.resolve (node:internal/modules/helpers:188:19)
    at Object.<anonymous> (/Users/<USER>/work/knowledgeforge/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/utils/injectRefreshLoader.js:16:47)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Module.require (node:internal/modules/cjs/loader:1231:19) {
  code: 'MODULE_NOT_FOUND',
  path: '/Users/<USER>/work/knowledgeforge/node_modules/react-refresh/package.json',
  requestPath: 'react-refresh'
}

Node.js v18.20.6
