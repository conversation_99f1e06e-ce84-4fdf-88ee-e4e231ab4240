# 编译时间优化执行计划

## 1. 现状统计（基于实际数据）

### 1.1 编译性能数据（采集自 performance-logs/cold-start.log）
- 最新一次（2025-07-15 15:26:56）：
  - ColdStart: 127s
  - Start1: 71s
  - Start2: 55s
  - Build: 98s
- 近6次平均值：
  - ColdStart: 115.8s
  - Start1: 62.3s
  - Start2: 55.5s
  - Build: 98s

### 1.2 依赖与包体积
- node_modules 体积：752MB
- 主要业务包体积（dist/static/js）：
  - cooper~knowledge.c2216bc5.chunk.js：2.5M
  - splitChunk.didi~a0016b22.df600bf7.chunk.js：1.8M
  - 其他主包/分包均在数百KB~1M+不等
- 主要样式包体积（dist/static/css）：
  - cooper~knowledge.dbaca1f6.chunk.css：968K
  - vendors~cooper~knowledge.631fc260.chunk.css：571K

### 1.3 构建环境
- Node.js: v12.22.12
- npm: 6.14.16
- webpack: 4.44.2
- webpack-bundle-analyzer: 4.10.2
- CPU核心数：8

### 1.4 构建各阶段耗时分析（speed-measure-webpack-plugin）
- General output time: 1分42.8秒（102.8秒）
- Plugins:
  - TerserPlugin: 9.7秒
  - OptimizeCssAssetsWebpackPlugin: 7.068秒
  - HtmlWebpackPlugin: 0.409秒
  - ModuleNotFoundPlugin: 0.072秒
  - ScriptExtHtmlWebpackPlugin: 0.067秒
  - MiniCssExtractPlugin: 0.052秒
  - DefinePlugin: 0.049秒
  - ProvidePlugin: 0.036秒
- Loaders:
  - mini-css-extract-plugin, css-loader, postcss-loader, less-loader, style-resources-loader: 62.95秒（478模块）
  - css-loader, postcss-loader, less-loader, style-resources-loader: 62.94秒（478模块）
  - modules with no loaders: 62.56秒（5621模块）
  - thread-loader, babel-loader: 56.66秒（833模块）
  - url-loader: 20.02秒（294模块）
  - mini-css-extract-plugin, css-loader: 2.72秒（6模块）
  - css-loader: 2.5秒（7模块）
  - html-webpack-plugin: 0.03秒（2模块）

#### 主要耗时瓶颈分析
- **最大瓶颈：样式相关 Loader（css/less/postcss/style-resources）**，单次链路高达 62.95 秒，且有多条类似链路，远超 JS 压缩和转译。
- **第二大瓶颈：babel-loader + thread-loader**，JS/JSX 转译耗时 56.66 秒。
- **第三大瓶颈：url-loader**，静态资源处理耗时 20.02 秒。
- **TerserPlugin** 虽然单次 9.7 秒，但不是主要瓶颈。
- 其他插件和 loader 耗时极低，无需优化。

#### 优化建议（按优先级）
1. **样式处理链路（css/less/postcss/style-resources）**
   - 合并/精简样式文件，减少模块数量。
   - 升级 less-loader、css-loader、postcss-loader 到最新版本。
   - 开启/优化缓存（如 cache-loader，或升级到 webpack5 的持久化缓存）。
   - 减少全局样式依赖，按需引入样式。
   - 检查是否有重复处理。
2. **babel-loader + thread-loader**
   - 精简 babel 配置，减少插件和 polyfill。
   - 只转译必要目录，排除 node_modules。
   - 升级 babel 相关依赖。
   - 检查 thread-loader worker 数量与 CPU 核心数匹配。
3. **url-loader**
   - 合理设置 limit，避免大文件 base64。
   - 静态资源可考虑 CDN 或外链，减少打包体积。
4. **TerserPlugin/OptimizeCssAssetsWebpackPlugin**
   - 确保开启 parallel。
   - 生产环境可关闭 sourceMap。
   - 升级插件版本。

---

## 2. 优化计划

### 2.1 短期计划（低成本、见效快）
- [x] 1. thread-loader worker 数量根据 CPU 自动分配（当前为2，建议8）
- [x] 2. babel-loader 保持 cacheDirectory: true，关闭 cacheCompression
- [x] 3. TerserPlugin/MiniCssExtractPlugin 开启并行
- [x] 4. 样式处理优化（添加 thread-loader 和 cache-loader）
- [x] 5. 生产环境关闭 sourceMap
- [x] 6. 优化静态资源处理（8KB以下转base64）
- [x] 7. 智能代码分割策略
- [ ] 8. 持续采集 cold-start.log，优化前后对比
- [ ] 9. CI/CD 层面缓存 node_modules

#### 已完成说明：
- 已将 thread-loader worker 数量设置为8。
- babel-loader 已配置 cacheDirectory: true，cacheCompression: false。
- TerserPlugin 已开启 parallel: true，MiniCssExtractPlugin 并行参数已注释说明（v4不支持，v5可用）。
- 已引入 speed-measure-webpack-plugin 并包裹生产构建配置，可监控各阶段耗时。
- 为 CSS/LESS/SCSS 添加了 thread-loader 和 cache-loader。
- 生产环境默认关闭 sourceMap，开发环境保留。
- 优化了 asset 处理，8KB以下文件转为base64。
- 按库类型优化了 splitChunks 配置（framework、ui、utils、editor）。

### 2.2 中期计划（中等成本、显著效果）
- [x] 1. Webpack Resolve 深度优化
- [x] 2. Babel 配置精细化调优
- [x] 3. 样式处理缓存优化
- [x] 4. 模块忽略优化（IgnorePlugin）
- [x] 5. 快速开发模式配置
- [x] 6. ESLint 条件启用优化
- [x] 7. 性能分析和监控工具

#### 已完成说明：
- **Resolve 优化**: 添加常用库别名，限制扩展名查找，关闭 symlinks
- **Babel 优化**: 启用 React 自动运行时，精确配置目标浏览器，关闭调试模式
- **样式缓存**: 缓存全局样式文件列表，避免重复 glob 操作
- **模块忽略**: 使用 IgnorePlugin 忽略 moment/dayjs 的 locale 文件
- **快速开发**: 创建 webpack.dev.fast.js，极速启动配置
- **ESLint 优化**: 条件启用，支持缓存和多线程，只检查修改文件
- **分析工具**: 依赖分析脚本、Webpack 预热、综合性能测试

### 2.3 长期计划（高成本、需大改动）
- [x] 1. 升级 webpack 至 v5，启用持久化缓存（filesystem cache）
- [ ] 2. 检查并升级相关 loader/plugin 以兼容 webpack5
- [ ] 3. 精简polyfill，按需引入 antd 及其他大库
- [ ] 4. 依赖库升级/替换，减少 node_modules 体积
- [x] 5. 引入 speed-measure-webpack-plugin 监控各阶段耗时（已完成，后续可持续优化）
- [ ] 6. 模块联邦（Module Federation）架构
- [ ] 7. CDN 外部依赖优化
- [ ] 8. 微前端架构改造

---

## 3. 新增优化手段详解

### 3.1 Webpack Resolve 深度优化
```javascript
resolve: {
  alias: {
    '@': paths.appSrc,
    // 添加常用库别名，减少解析时间
    'react': require.resolve('react'),
    'react-dom': require.resolve('react-dom'),
    'antd': require.resolve('antd'),
    'lodash-es': require.resolve('lodash-es'),
  },
  // 限制文件扩展名查找，提升解析速度
  extensions: ['.js', '.jsx', '.json'],
  // 优化 symlinks 处理
  symlinks: false,
  // 缓存模块解析结果
  cacheWithContext: false,
}
```

### 3.2 Babel 配置精细化调优
```json
{
  "presets": [
    ["@babel/preset-react", {"runtime": "automatic", "development": false}],
    ["@babel/preset-env", {
      "modules": false,
      "debug": false,
      "targets": {"browsers": [">0.2%", "Chrome >= 72", "Safari >= 12"]},
      "exclude": ["transform-typeof-symbol"]
    }]
  ]
}
```

### 3.3 样式处理缓存优化
- 为 CSS/LESS/SCSS 添加 cache-loader 缓存层
- 缓存全局样式文件列表，避免重复 glob 操作
- 生产环境关闭 sourceMap

### 3.4 模块忽略优化
```javascript
new webpack.IgnorePlugin({
  resourceRegExp: /^\.\/locale$/,
  contextRegExp: /moment$/,
}),
```

### 3.5 快速开发模式
- 创建 `webpack.dev.fast.js` 专用配置
- 使用最快的 devtool (`eval`)
- 关闭所有优化选项
- 简化样式处理链路

### 3.6 新增命令
```bash
npm run start:fast      # 极速开发模式
npm run analyze:deps    # 依赖分析
npm run warmup         # Webpack 预热
npm run test:performance # 综合性能测试
```

## 4. 进度追踪

| 日期 | 优化项 | 负责人 | 状态 | 备注 |
|------|--------|--------|------|------|
| 2025-07-15 | thread-loader/babel-loader/TerserPlugin优化 |  | 已完成 | 基础优化 |
| 2025-07-15 | 样式处理优化（thread-loader + cache-loader） |  | 已完成 | 针对最大瓶颈 |
| 2025-07-15 | Webpack Resolve 深度优化 |  | 已完成 | 模块解析提速 |
| 2025-07-15 | Babel 配置精细化调优 |  | 已完成 | 减少转译开销 |
| 2025-07-15 | 快速开发模式 |  | 已完成 | 极速启动 |
| 2025-07-15 | 分析和监控工具 |  | 已完成 | 性能追踪 |

## 4. 效果评估
- 优化前后 cold-start.log 对比，评估各项耗时变化。
- 记录每次优化的具体措施与效果。

---

> 本计划为持续优化文档，建议每次优化后补充进度与效果。
