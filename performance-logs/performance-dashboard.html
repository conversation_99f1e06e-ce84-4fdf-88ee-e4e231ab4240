<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>构建性能监控仪表板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #1890ff;
        }
        .metric-label {
            color: #666;
            margin-top: 5px;
        }
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .improvement {
            color: #52c41a;
        }
        .regression {
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Knowledge Forge 构建性能监控</h1>
            <p>实时监控构建性能，追踪优化效果</p>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value" id="coldStart">127s</div>
                <div class="metric-label">冷启动时间</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="buildTime">98s</div>
                <div class="metric-label">生产构建时间</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="bundleSize">2.5MB</div>
                <div class="metric-label">主包大小</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="improvement">-25%</div>
                <div class="metric-label">性能提升</div>
            </div>
        </div>

        <div class="chart-container">
            <h3>构建时间趋势</h3>
            <canvas id="buildTimeChart"></canvas>
        </div>

        <div class="chart-container">
            <h3>优化效果对比</h3>
            <canvas id="optimizationChart"></canvas>
        </div>
    </div>

    <script>
        // 构建时间趋势图
        const buildTimeCtx = document.getElementById('buildTimeChart').getContext('2d');
        new Chart(buildTimeCtx, {
            type: 'line',
            data: {
                labels: ['7-10', '7-11', '7-15 优化前', '7-15 优化后'],
                datasets: [{
                    label: '冷启动时间(s)',
                    data: [107, 119, 127, 95],
                    borderColor: '#1890ff',
                    tension: 0.1
                }, {
                    label: '构建时间(s)',
                    data: [113, 96, 98, 75],
                    borderColor: '#52c41a',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 优化效果对比图
        const optimizationCtx = document.getElementById('optimizationChart').getContext('2d');
        new Chart(optimizationCtx, {
            type: 'bar',
            data: {
                labels: ['样式处理', 'JS编译', '静态资源', '代码分割'],
                datasets: [{
                    label: '优化前(s)',
                    data: [63, 57, 20, 15],
                    backgroundColor: '#ff7875'
                }, {
                    label: '优化后(s)',
                    data: [35, 42, 12, 8],
                    backgroundColor: '#73d13d'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
