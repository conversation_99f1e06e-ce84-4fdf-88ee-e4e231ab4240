{"name": "knowledge-forge", "version": "0.1.0", "private": true, "scripts": {"start": "APP_ENV=dev node build/start.js", "start:fast": "APP_ENV=dev webpack serve --config config/webpack.dev.fast.js --port 3000", "start:1": "APP_ENV=dev TARGET_PORT=4001 node build/start.js", "start:2": "APP_ENV=dev TARGET_PORT=4002 node build/start.js", "qa": "APP_ENV=qa node build/start.js", "build:test": "cross-env APP_ENV=test node build/build.js", "build:qa": "cross-env APP_ENV=qa node build/build.js", "build:prod": "cross-env APP_ENV=prod node build/build.js", "build:analyze": "cross-env APP_ENV=prod webpack --config config/webpack.analyze.js", "analyze:deps": "node scripts/analyze-dependencies.js", "test:performance": "./performance-logs/comprehensive-test.sh", "warmup": "node scripts/webpack-warmup.js", "upgrade:deps": "./scripts/upgrade-dependencies.sh", "upgrade:configs": "node scripts/update-configs.js", "check:deps": "npm outdated", "skeleton": "cross-env APP_ENV=prod node build/skeleton.js", "preCommit": "lint-staged", "unlink:@didi/select-position-operate": "yalc @didi/select-position-operate && npm install", "postinstall": "bash ./install-hooks.sh"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["npx eslint --fix src/", "git add"]}, "dependencies": {"@ant-design/icons": "4.8.1", "@babel/plugin-transform-runtime": "7.16.7", "@babel/runtime-corejs3": "^7.25.6", "@didi/add-member-search-new": "1.0.5", "@didi/add-member-search-react": "5.0.14", "@didi/ai-customer-service": "1.0.9", "@didi/didoc-core": "2.2.7", "@didi/didoc-editor-menubar": "1.3.7", "@didi/didoc-extensions": "1.0.0", "@didi/didoc-mark-italics": "1.1.0", "@didi/didoc-mark-link": "1.3.6", "@didi/didoc-mark-strikethrough": "1.0.1", "@didi/didoc-mark-strong": "1.0.0", "@didi/didoc-mark-tabindent": "1.0.4", "@didi/didoc-mark-text-color": "1.1.0", "@didi/didoc-mark-text-highlight": "1.0.1", "@didi/didoc-mark-underline": "1.0.2", "@didi/didoc-node-bulletlist": "1.0.2", "@didi/didoc-node-checklist": "1.0.1", "@didi/didoc-node-checklist-item": "1.0.2", "@didi/didoc-node-listitem": "1.0.4", "@didi/didoc-node-orderlist": "^1.0.6", "@didi/didoc-plugin-formatbrush": "1.0.0", "@didi/didoc-plugin-image": "1.5.3", "@didi/didoc-plugin-mention-person": "1.2.5", "@didi/didoc-plugin-placeholder": "1.1.3", "@didi/didoc-upload-file-node": "1.3.2", "@didi/halo-client-web": "0.7.0", "@didi/html-webpack-inject-plugin": "^1.0.6", "@didi/mas-web": "0.0.2", "@didi/mf-tenon": "1.0.8", "@didi/raven": "^1.2.4", "@didi/select-position-operate": "^1.0.48", "@didi/whitescreen-error": "0.0.9", "@didi/wsgsig": "5.1.12", "@rematch/core": "2.0.1", "@rematch/immer": "2.0.1", "@rematch/loading": "2.0.1", "@rematch/select": "3.0.1", "@rematch/updated": "2.0.1", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "antd": "4.21.7", "axios": "0.21.1", "babel-plugin-enhance-log": "0.3.0", "chalk": "^4.1.2", "classnames": "2.2.6", "core-js": "3.26.1", "crypto-js": "4.1.1", "dayjs": "^1.11.11", "di18n-react": "0.1.25", "dompurify": "^3.1.6", "fs-extra": "10.0.0", "history": "5.3.0", "html2canvas": "1.0.0", "immer": "9.0.2", "immutable": "4.3.0", "js-cookie": "3.0.5", "jschardet": "2.3.0", "localforage": "1.10.0", "lodash-es": "4.17.21", "normalize.css": "8.0.1", "prosemirror-commands": "^1.0.7", "prosemirror-gapcursor": "^1.2.2", "prosemirror-history": "^1.2.0", "prosemirror-inputrules": "^1.1.3", "prosemirror-markdown": "^1.8.0", "prosemirror-model": "^1.16.1", "prosemirror-state": "^1.3.4", "prosemirror-tables": "^0.9.5", "prosemirror-utils": "^0.9.6", "prosemirror-view": "^1.23.13", "qs": "^6.12.0", "ramda": "0.27.2", "rc-virtual-list": "3.10.5", "react": "17.0.2", "react-autosuggest": "^9.4.0", "react-cookie": "4.0.3", "react-copy-to-clipboard": "5.1.0", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "17.0.2", "react-draggable": "^4.4.5", "react-helmet": "^6.1.0", "react-infinite-scroller": "1.2.4", "react-lazyload": "3.1.0", "react-modal": "3.14.4", "react-onclickoutside": "6.11.2", "react-redux": "7.2.2", "react-router-dom": "6.10.0", "react-tether": "1.0.5", "redux": "4.0.5", "resize-observer-polyfill": "^1.5.1", "shimo-js-sdk": "1.2.0", "spark-md5": "3.0.2", "splitting": "^1.0.6", "swiper": "6.4.5", "tiny-svg": "2.2.2", "url-parse": "1.4.7", "uuid": "^9.0.1", "video-react": "0.10.9", "workbox-webpack-plugin": "6.5.3"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/eslint-parser": "7.13.14", "@babel/plugin-proposal-class-properties": "7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "7.12.1", "@babel/plugin-proposal-optional-chaining": "7.12.7", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.1", "@types/bpmn-moddle": "5.1.3", "@types/lodash": "4.14.194", "autoprefixer": "^10.4.21", "babel-eslint": "10.1.0", "babel-loader": "^9.2.1", "babel-plugin-import": "1.13.1", "buffer": "^6.0.3", "cache-loader": "^4.1.0", "camelcase": "6.1.0", "case-sensitive-paths-webpack-plugin": "2.3.0", "copy-webpack-plugin": "^11.0.0", "cross-env": "7.0.3", "crypto-browserify": "^3.12.1", "css-loader": "^6.11.0", "css-minimizer-webpack-plugin": "^4.2.2", "di18n-cli": "0.1.29", "eslint": "7.32.0", "eslint-config-airbnb-base": "14.2.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.2.0", "eslint-webpack-plugin": "^5.0.2", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "less": "^4.3.0", "less-loader": "^11.1.4", "lint-staged": "^15.2.2", "mini-css-extract-plugin": "^2.9.2", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss": "^8.5.6", "postcss-loader": "^7.3.4", "postcss-safe-parser": "5.0.2", "raw-loader": "^4.0.2", "react-dev-utils": "^12.0.1", "react-refresh": "^0.8.3", "sass": "^1.89.2", "sass-loader": "^13.3.3", "schema-utils": "^3.3.0", "script-ext-html-webpack-plugin": "^2.1.5", "speed-measure-webpack-plugin": "^1.5.0", "stream-browserify": "^3.0.0", "style-loader": "^3.3.4", "style-resources-loader": "^1.5.0", "terser-webpack-plugin": "^5.3.14", "thread-loader": "^4.0.4", "url-loader": "^4.1.1", "util": "^0.12.5", "vue-template-compiler": "2.7.14", "webpack": "^5.88.0", "webpack-bundle-analyzer": "^4.7.0", "webpack-cli": "^5.1.1", "webpack-dev-server": "^4.15.2", "webpack-merge": "^5.10.0"}, "husky": {"hooks": {"pre-commit": "sh ./pre-commit-check.sh && lint-staged"}}, "browserslist": {"production": [">0.2%", "Chrome >= 72", "Safari >= 12"], "development": [">0.2%", "Chrome >= 72", "Safari >= 12"]}}